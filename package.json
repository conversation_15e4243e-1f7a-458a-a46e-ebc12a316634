{"name": "fe-abtest-new", "version": "0.1.0", "private": true, "scripts": {"dev": "export NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --open --config", "push": "node src/config/push.js", "test": "vue-cli-service serve --mode test --open", "serve": "vue-cli-service serve --open", "build": "vue-cli-service build", "build:test": "vue-cli-service build", "build:online": "vue-cli-service build", "lintcss": "stylelint src/static/css/*.less", "lintjs": "eslint --ext .js,.vue src test/unit/specs test/e2e/specs", "lint": "vue-cli-service lint", "dll": "rm -rf dll/*;export NODE_ENV=production;node ./node_modules/webpack/bin/webpack.js --config config/webpack.config.dll.js"}, "dependencies": {"@antv/data-set": "^0.11.8", "@antv/g2": "^4.1.21", "@antv/g2-plugin-slider": "^2.1.1", "@antv/g6": "^5.0.45", "@sentry/browser": "^5.29.2", "@sentry/integrations": "^5.29.2", "@znzt_fe/abtest-sdk": "^1.0.6", "add-asset-html-webpack-plugin": "^3.1.3", "axios": "^0.19.2", "browserslist": "^4.14.0", "caniuse-lite": "^1.0.30001119", "core-js": "^3.6.4", "cos-js-sdk-v5": "^1.2.20", "dayjs": "^1.11.10", "echarts": "^5.1.2", "el-select-v2": "^1.4.6", "element-ui": "^2.15.3", "highlight.js": "^11.2.0", "js-cookie": "^2.2.0", "jsoneditor": "^9.5.3", "lodash": "^4.17.21", "nprogress": "^0.2.0", "qs": "^6.11.2", "sql-formatter": "^4.0.2", "svg-sprite-loader": "^6.0.11", "swiper": "^5.4.5", "tdesign-vue": "^1.9.6", "uuid": "^8.3.2", "vue": "^2.6.11", "vue-awesome-swiper": "^4.1.1", "vue-clipboard2": "^0.3.1", "vue-codemirror": "^4.0.6", "vue-highlightjs": "^1.3.3", "vue-infinite-scroll": "^2.0.2", "vue-markdown": "^2.2.4", "vue-router": "^3.1.5", "vue-simple-uploader": "^0.7.4", "vue-watermask": "^1.0.22", "vuedraggable": "^2.24.3", "vuex": "^3.1.0", "webpack-cli": "^4.0.0-beta.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.2.0", "@vue/cli-plugin-eslint": "~4.2.0", "@vue/cli-plugin-router": "~4.2.0", "@vue/cli-service": "~4.2.0", "@vue/eslint-config-standard": "^5.1.0", "@vue/test-utils": "^2.4.6", "babel-eslint": "^10.0.3", "babel-jest": "^29.7.0", "element-theme-chalk": "^2.15.3", "eslint": "^6.7.2", "eslint-plugin-html": "^6.0.2", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^11.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.1.2", "html-webpack-plugin": "^3.2.0", "jest": "^29.7.0", "less": "^3.0.4", "less-loader": "^5.0.0", "lint-staged": "^9.5.0", "style-resources-loader": "^1.3.3", "stylelint": "^13.2.0", "stylelint-config-standard": "^20.0.0", "stylelint-webpack-plugin": "^1.2.3", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-jest": "^3.0.7", "vue-slick-carousel": "^1.0.6", "vue-template-compiler": "^2.6.11"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "jest": {"moduleFileExtensions": ["js", "vue", "json"], "transform": {"^.+\\.vue$": "vue-jest", "^.+\\.js$": "babel-jest"}}}