/**
 * @file dll 文件配置
*/
'use strict';
const path = require('path');
const webpack = require('webpack');
const resource = require('./resourceArr');
const paths = require('./paths');
const TerserPlugin = require('terser-webpack-plugin');
module.exports = {
    entry: {
        vender: resource.vender,
        lib: resource.lib
    },
    output: {
        path: paths.appDll,
        filename: '[name].[chunkhash:7].dll.js',
        sourceMapFilename: '[name].map',
        pathinfo: true,
        library: '[name]_[chunkhash]_library'
    },
    mode: process.env.NODE_ENV,
    optimization: {
        minimize: true,
        minimizer: [
            new TerserPlugin({
                terserOptions: {
                    parse: {
                        ecma: 8,
                    },
                    compress: {
                        ecma: 5,
                        warnings: false,
                        comparisons: false,
                        inline: 2,
                        drop_debugger: false,
                    },
                    mangle: {
                        safari10: true,
                    },
                    output: {
                        ecma: 5,
                        comments: false,
                        ascii_only: true // eslint-disable-line
                    },
                },
                parallel: true,
                cache: true,
                sourceMap: true
            })
        ],
    },
    plugins: [
        new webpack.DllPlugin({
            context: __dirname,
            name: '[name]_[chunkhash]_library',
            path: path.resolve(paths.appDll, '[name]-manifest.json')
        })
    ]
};
