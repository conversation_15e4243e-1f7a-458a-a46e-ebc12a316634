/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-10-30 17:12:18
 * @LastEditors: zhuyue
 * @LastEditTime: 2021-09-28 15:14:04
 * @Description: AB实验平台路由
 */

import VisualEditor from '@/views/experiment-list/visual-editor/index';
import Frame from '@/views/frame';
import PermissionBusiness from '@/views/permission/business';
import Permission from '@/views/permission/index';
import PermissionRequest from '@/views/permission/request';
import TrafficControlList from '@/views/traffic-control/list';
import ServiceLine from '@views/app-manage/service_line/index';
import ExperimentExtendDetail from '@views/experiment-extend/detail';
import ExperimentExtendList from '@views/experiment-extend/list';
import AddExperiment from '@views/experiment-list/add';
import PublishExperiment from '@views/experiment-list/publish';
import DiagnosisExp from '@views/experiment-list/diagnosis';
import EditExperiment from '@views/experiment-list/edit';
import ExperimentList from '@views/experiment-list/index';
import ExpReport from '@views/experiment-list/report';
import FeatureDetail from '@views/feature/components/feature';
import FeatureReport from '@views/feature/components/feature-report';
import Feature from '@views/feature/index';
import FeatureManage from '@views/feature/version-manage/version-history';
import MetricList from '@views/metric/metric-list/index';
import Muexc from '@views/mutu-exclu-group/index';
import CrowdEdit from '@views/sys-manage/crowd/components/crowd-edit';
import CrowdList from '@views/sys-manage/crowd/index';
import FilterArg from '@views/system-manage/filter-arg/index';
import OuterFilterApi from '@views/system-manage/filter-arg/outer-filter';
import TestTask from '@views/task-manage/task-list/index';
import TestView from '@views/task-manage/task-list/test-task-detial';
import WhiteList from '@views/white-list/index';
import GroupWhiteList from '@views/white-list/group-whitelist.vue';
import Feedback from '@views/feedback/index';
import FeedbackView from '@views/feedback/detail';
import PersonalCenter from '@/views/personal-center/index';

export default [
  {
    path: '/personal-center', // 个人中心
    name: 'personalCenter',
    component: PersonalCenter,
    meta: {
      title: '个人中心',
      hideMenu: true // 隐藏左侧菜单
    }
  },
  {
    path: '/exp-manage/list', // 实验列表
    name: 'ExperimentList',
    component: ExperimentList,
    meta: {
      title: '实验列表'
    }
  },
  {
    path: '/exp-manage/list/add', // 新增实验
    name: 'addExp',
    component: AddExperiment,
    meta: {
      title: '配置实验'
    }
  },
  {
    path: '/exp-manage/list/publish', // 新增实验
    name: 'publishExp',
    component: PublishExperiment,
    meta: {
      title: '发布实验'
    }
  },
  {
    path: '/exp-manage/list/edit', // 编辑实验
    name: 'editExp',
    component: EditExperiment,
    meta: {
      title: '编辑实验'
    }
  },
  {
    path: '/exp-manage/visual-editor', // 可视化编辑
    name: 'VisualEditor',
    component: VisualEditor,
    meta: {
      title: '可视化编辑'
    }
  },
  {
    path: '/exp-manage/muexc', // 互斥组
    name: 'Muexc',
    component: Muexc,
    meta: {
      title: '互斥组'
    }
  },
  {
    path: '/exp-manage/white', // 白名单
    name: 'White',
    component: WhiteList,
    meta: {
      title: '白名单列表',
      keepAlive: true
    }
  },
  {
    path: '/exp-manage/group-white-list', // 白名单清单
    name: 'GroupWhiteList',
    component: GroupWhiteList,
    meta: {
      title: '白名单清单'
    }
  },
  {
    path: '/exp-manage/list/report', // 实验报告
    name: 'ExpReport',
    component: ExpReport,
    meta: {
      title: '实验报告'
    }
  },
  {
    path: '/exp-manage/extend', // 实验扩展分析
    name: 'ExperimentExtend',
    component: ExperimentExtendList,
    meta: {
      title: '实验扩展分析'
    }
  },
  {
    path: '/exp-manage/extend-detail', // 实验扩展分析
    name: 'ExperimentExtendDetail',
    component: ExperimentExtendDetail,
    meta: {
      title: '实验扩展分析'
    }
  },
  {
    path: '/feat/list', // 固化列表
    name: 'FeatureList',
    component: Feature,
    meta: {
      title: '固化列表'
    }
  },
  {
    path: '/feat/list/detail', // 固化详情
    name: 'FeatureListDetail',
    component: FeatureDetail,
    meta: {
      title: '固化详情'
    }
  },
  {
    path: '/feat/list/manage', // 版本管理
    name: 'FeatureManage',
    component: FeatureManage,
    meta: {
      title: '固化版本管理'
    }
  },
  {
    path: '/metric/list', // 指标列表列表
    name: 'MetricList',
    component: MetricList,
    meta: {
      title: '指标列表'
    }
  },
  {
    path: '/sys-manage/crowd', // 系统管理-用户分群
    name: 'CrowdList',
    component: CrowdList,
    meta: {
      title: '分群列表',
      keepAlive: true
    }
  },
  {
    path: '/sys-manage/crowd/crowd-edit', // 系统管理-用户分群
    name: 'CrowdEdit',
    component: CrowdEdit,
    meta: {
      title: '配置用户分群'
    }
  },
  //诊断实验
  {
    path: '/exp-manage/diagnosis', // 诊断实验
    name: 'DiagnosisExp',
    component: DiagnosisExp,
    meta: {
      title: '诊断实验'
    }
  },
  {
    path: '/task/list', // 实验任务
    name: 'TestTask',
    component: TestTask,
    meta: {
      title: '实验任务'
    }
  },
  {
    path: '/task/list/testView', //  子任务查看
    name: 'TestView',
    component: TestView,
    meta: {
      title: '实验任务明细'
    }
  },
  {
    path: '/system/manage/filterArg', //  过滤参数
    name: 'FilterArg',
    component: FilterArg,
    meta: {
      title: '过滤参数列表',
      keepAlive: true
    }
  },
  {
    path: '/system/app/configManager', //  业务线管理
    name: 'appManager',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/system/intimeManager', //  实时管理
    name: 'appManager',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/admin/easyQuery', //  快速查询
    name: 'EasyQuery',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/admin/task/restartManager', // 任务重启管理
    name: 'RestartManager',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/costs/center', // 费用中心
    name: 'CostManager',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/costs/monitor', // 成本监控
    name: 'CostMonitor',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/costs/budgetManager', //  预算管理
    name: 'BudgetManager',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/system/manage/outerfilterapi', //  过滤参数
    name: 'OuterFilterApi',
    component: OuterFilterApi,
    meta: {
      title: '过滤参数列表'
    }
  },
  {
    path: '/app/manage/serviceLine', //  业务线
    name: 'ServiceLine',
    component: ServiceLine,
    meta: {
      title: '业务线列表'
    }
  },
  {
    path: '/app/manage/feature-report', //  固化报告
    name: 'FeatureReport',
    component: FeatureReport,
    meta: {
      title: '固化报告'
    }
  },
  {
    path: '/permission',
    name: 'permission',
    component: Permission,
    meta: {
      title: '权限申请',
      hideMenu: true
    }
  },
  {
    path: '/permission/request',
    name: 'permission-request',
    component: PermissionRequest,
    meta: {
      title: '权限申请',
      hideMenu: true
    }
  },
  {
    path: '/permission/business',
    name: 'permission-business',
    component: PermissionBusiness,
    meta: {
      title: '业务线接入',
      hideMenu: true
    }
  },
  {
    path: '/traffic-control/exp/list',
    name: 'traffic-control-exp-list',
    component: TrafficControlList,
    meta: {
      title: '流量控制实验列表'
    }
  },
  {
    path: '/costs/budgetManager', //  预算管理
    name: 'BudgetManager',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/system/feedback', //  过滤参数
    name: 'Feedback',
    component: Feedback,
    meta: {
      title: '问题反馈列表'
    }
  },
  {
    path: '/system/feedback/view', //  过滤参数
    name: 'FeedbackView',
    component: FeedbackView,
    meta: {
      title: '问题反馈列表'
    }
  },
  {
    path: '/app-group-manage', // 成本监控
    name: 'AppGrooupManage',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/system-manage/permission-manage', // 权限管理
    name: 'PermissionManage',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/system-manage/process-manage', // 流程中心管理
    name: 'ProcessManage',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/data-center/my-data', // 我的数据
    name: 'MyData',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/data-center/app-data', // 业务线数据
    name: 'AppData',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/exp-label', // 标签管理
    name: 'ExpLabel',
    component: Frame,
    meta: {
      title: ''
    }
  },
  {
    path: '/frame/*', // 通配符路由匹配
    name: 'FrameWildcard',
    component: Frame,
    meta: {
      title: ''
    }
  }
];
