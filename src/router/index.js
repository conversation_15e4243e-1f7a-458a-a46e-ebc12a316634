/*
 * @Author: wang<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-02-29 10:46:08
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-02-01 21:06:48
 * @Description: 前端路由
 */
import Vue from 'vue';
import VueRouter from 'vue-router';
import Authority from '@components/common/no-authority/Index.vue';
import Layout from '@views/Layout.vue';
const NOT_FOUND = resolve => require(['@components/common/404/not-found.vue'], resolve);
const LOGIN = resolve => require(['@components/common/login/login.vue'], resolve);
import Demo from '@/views/experiment-list/visual-editor/demo';
import Demo2 from '@/views/experiment-list/visual-editor/demo2';
import ABtestRoutes from '@router/abtest';

Vue.use(VueRouter);

const routes = [
  // {
  //   path: '/',
  //   name: 'Home',
  //   redirect: '/exp/list',
  // },
  {
    path: '/',
    component: Layout,
    name: 'fe-abtest-new',
    redirect: '/exp-manage/list',
    // redirect: '/test',
    children: [...ABtestRoutes]
  },
  // {
  //   path: '/material',
  //   name: 'material',
  //   component: Layout,
  //   redirect: '/material/wxfilelist',
  //   children: [...Wxfile]
  // },
  {
    path: '/no-authority',
    name: 'authority',
    component: Authority,
    meta: {
      title: '没权限'
    }
  },
  {
    path: '/404',
    name: 'notFound',
    component: NOT_FOUND,
    meta: {
      title: '404'
    }
  },
  {
    path: '/login',
    name: 'login',
    component: LOGIN,
    meta: {
      title: '开发环境种cookie用'
    }
  },
  // {
  //   path: '/customer-service',
  //   name: 'account',
  //   component: Layout,
  //   redirect: '/customer-service/account/number',
  //   children: [...ServiceAccount]
  // }
  {
    path: '/demo', // 可视化编辑
    name: 'demo',
    component: Demo,
    meta: {
      title: '可视化编辑-抓取页面'
    }
  },
  {
    path: '/test', // 可视化编辑
    name: 'test',
    component: Demo2,
    meta: {
      title: '可视化编辑-抓取页面2'
    }
  }
];

const router = new VueRouter({
  mode: 'hash',
  base: '/fe-abtest-new',
  routes
});

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
};

// 路由切换时需要处理的逻辑
router.beforeEach((to, from, next) => {
  next();
});

export default router;
