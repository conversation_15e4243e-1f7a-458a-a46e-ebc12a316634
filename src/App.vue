<!--
 * @Author: wanglijuan01
 * @Date: 2020-02-29 10:46:08
 * @LastEditors: wanglijuan01
 * @LastEditTime: 2020-08-26 20:30:38
 * @Description: vue入口文件
 -->
<template>
  <div id="app">
    <transition name="fade" mode="out-in">
      <router-view v-if="isRouterAlive"></router-view>
    </transition>
    <KeFu></KeFu>
  </div>
</template>
<script>
import { localStorage } from '@/common/utils';
import KeFu from './views/kefu/index.vue';
export default {
  provide() {
    return {
      reload: this.reload
    };
  },
  components: { KeFu },
  data() {
    return {
      isRouterAlive: true,
      isChrome: navigator.userAgent.indexOf('Chrome') > -1 // 是否是谷歌
    };
  },
  created() {
    this.queryAuthority();
    const { isChrome } = this;
    if (isChrome) {
      window.onresize = this.$throttle(() => {
        this.$bus.$emit('resize');
      }, 300);
    }
    window.addEventListener('message', (event) => {
      const { data = {} } = event;
      const { name, action } = data;
      if (action === 'navigateTo') {
        this.$router.push({
          name
        });
      }
    });
  },
  methods: {
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(function () {
        this.isRouterAlive = true;
      });
    },
    queryAuthority() {
      this.$service.get('USER_INFO').then((res) => {
        this.$store.commit('setUserInfo', res);
        this.uname = res.uname;
        localStorage.setItem('uname', res.uname);
      });
    }
  }
};
</script>
<style lang="less" scoped></style>
