/*
 * @FileName: Store
 * @Author: Leinov
 * @Date: 2019-12-19 11:46:13
 * @LastEditTime: 2021-02-23 20:36:45
 * @Description: description your file
 * @FilePath: \wxcservice\src\store\index.js
 */
import Vue from 'vue';
import Vuex from 'vuex';
// import state from './state';
// import * as mutations from './mutation';
// import * as getters from './getters';
import messages from './modules/new-message-module/index';
Vue.use(Vuex);

export default new Vuex.Store({
  // state,
  // mutations,
  // getters,
  // actions: {},
  modules: {
    messages
  }
});

//window.state = state;
