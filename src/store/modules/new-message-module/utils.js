import { v4 as uuidv4 } from 'uuid';

// 推送实验初始值
export const pushData = () => {
  return {
    tabName: uuidv4(),
    isPush: true,
    pushWay: [],
    pushPlatform: ['android', 'ios'],
    pushTitle: '',
    pushContent: '',
    pushDesc: '',
    afterAction: 1,
    frequency: 1,
    pushTimeWay: 1,
    isRepeat: false,
    pushTime: new Date(new Date().toLocaleDateString()).getTime(),
    pushCycle: 'day',
    pushCycleValue: [1],
    pushDay: [new Date().getTime(), new Date().getTime()],
    fields: [
      {
        name: '',
        value: ''
      }
    ],
    notice: {
      way: [1, 2],
      important: 4,
      clear: false,
      mark: 1
    }
  };
};

// 可视化实验初始值
export const getVisual = () => {
  return {
    // id: uuidv4(),
    changes: [],
    changeNum: 0
  };
};
