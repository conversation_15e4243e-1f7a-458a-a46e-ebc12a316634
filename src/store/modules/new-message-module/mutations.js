/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-19 10:43:44
 * @LastEditors: huyew<PERSON>
 * @LastEditTime: 2021-06-25 14:43:49
 */

import Vue from 'vue';
import _ from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { pushData, getVisual } from './utils';
import { localStorage } from '@common/utils';
import router from '@/router/index';

// 编辑实验的时候处理实验指标的数据
export const setMetricData1 = (state, data) => {
  state.metricData = data;
  //debugger;
  formatAddMetricData1(state, data);
};
// 当在编辑实验的时候，处理指标数据
const formatAddMetricData1 = (state, data) => {
  const selectedMetric = []; // 选中的id
  for (let i = 0, len = state.addData.indicators.length; i < len; i++) {
    const curr = state.addData.indicators[i];
    selectedMetric.push(curr.indId); // select选中的
    //availablTableData.push(curr); // 展示的列表
  }
  state.addData.selectedMetric = selectedMetric;
};
// 新建实验的时候需要的指标数据
export const setMetricData = (state, data) => {
  state.metricData = data;
  //debugger;
  formatAddMetricData(state, data);
};
// 当在添加实验的时候，处理指标数据
const formatAddMetricData = (state, data) => {
  const selectedMetric = []; // 选中的id
  //const availablTableData = []; //列表展示的
  for (let i = 0, len = state.metricData.length; i < len; i++) {
    const curr = state.metricData[i];
    // 必看指标&使用中
    if (curr.isMust === 1 && curr.status === 1) {
      // 如果是必看指标需要在新建实验的时候直接选中
      selectedMetric.push(curr.id); // select选中的
      //availablTableData.push(curr); // 展示的列表
    }
  }
  state.addData.selectedMetric = selectedMetric;
};
export const delMetricTableData = (state, data) => {
  const selectData = state.addData.selectedMetric;
  //debugger;
  for (let i = 0, len = selectData.length; i < len; i++) {
    const curr = selectData[i];
    if (curr == data) {
      //debugger;
      selectData.splice(i, 1);
      break;
    }
  }
};
// 设置用户信息
export const setUserInfo = (state, data) => {
  state.userInfo = data;
  if (data.roleInfo && data.roleInfo.length) {
    state.afterLoginCallBacks.forEach(cb => {
      cb();
    });
  } else {
    state.afterLoginCallBacks = [];
    router.push({ path: '/permission' });
  }
};
// 设置app登录校验
export const setAfterLoginCallBacks = (state, cb) => {
  state.afterLoginCallBacks.push(cb);
};
// 添加实验版本
export const addVersion = (state, payload = {}) => {
  const versionNum = state.addData.versionList.length;
  const copyVersion = _.cloneDeep(state.addData.versionList[versionNum - 1]);
  const copyFeature = copyVersion.featureList;
  const newFeature = [];

  copyFeature.forEach((feat, index) => {
    newFeature.push({
      featureId: 0,
      keyName: feat.keyName,
      keyType: feat.keyType,
      keyValue: ''
    });
  });
  const version = {
    flow: 0,
    relationIds: [],
    fixedName: '实验版本',
    displayName: `实验版本${versionNum - 1}`,
    description: '',
    picUrl: '',
    id: 0,
    versionId: 0,
    featureList: newFeature,
    whitelist: [
      {
        // cuid: '',
        whitelistId: ''
      }
    ],
    mywhitelist: []
    // ...pushData(),
    // ...getVisual()
  };
  state.addData.versionList.push(version);
  payload.callback && payload.callback(version);
};
// 删除实验版本
export const deleteVersion = (state, { index }) => {
  const versions = state.addData.versionList;
  versions.splice(index, 1);
};

// 添加参数
export const addParams = (state, data) => {
  const versions = state.addData.versionList;
  versions.forEach((v, index) => {
    v.featureList.push({
      featureId: 0,
      keyName: '',
      keyType: 1,
      keyValue: ''
    });
  });
};
// 改变参数类型
export const changeParamType = (state, { index, type }) => {
  const versions = state.addData.versionList;
  versions.forEach((v, idx) => {
    if (idx !== 0) {
      v.featureList[index].keyType = type;
      v.featureList[index].keyValue = '';
    }
  });
};
// 改变参数值
export const changeKeyName = (state, { index, value }) => {
  const versions = state.addData.versionList;

  versions.forEach((v, idx) => {
    if (idx !== 0) {
      v.featureList[index].keyName = value;
    }
  });
};
// 删除参数
export const deleteParms = (state, { index }) => {
  const versions = state.addData.versionList;
  versions.forEach((v, idx) => {
    v.featureList.splice(index, 1);
  });
};
// 设置过滤条件
export const setFilterList = (state, data) => {
  state.filerList = data;
};
export const setFilterList1 = (state, data) => {
  state.filerList = data;
  handelFilter(state, data);
};
const handelFilter = (state, data) => {
  state.addData.filterRule.forEach(rule => {
    rule.filterList.forEach(list => {
      for (let i = 0, len = data.filterList.length; i < len; i++) {
        const current = data.filterList[i];
        if (
          list.filterConfId === current.id ||
          (list.filterType === 2 && current.keyName === '__crowd__')
        ) {
          list.keyValueType = current.keyType;
          list.symbolList =
            current.symbols !== ''
              ? current.symbols
              : [
                  { type: 1, displayName: '等于' },
                  { type: 2, displayName: '不等于' }
                ];
          list.filterConfId = current.id;
          list.useEnum = current.useEnum;
          // 受众规则是人群包
          const isCrowd =
            (list.filterConfId === 0 && current.useEnum === 1) ||
            (list.filterType === 2 && current.keyName === '__crowd__');
          const temp =
            current.keyType === 2
              ? [
                  { label: 'true', value: 'true' },
                  { label: 'false', value: 'false' }
                ]
              : current.keyValueEnums
              ? current.keyValueEnums
              : [];
          const arr = list.keyValue.map(value => {
            return { label: value, value };
          });
          list.keyValueEnums = clearDuplicate(
            current.keyType === 2 || isCrowd ? temp : [...temp, ...arr]
          );
          break;
        }
      }
    });
  });
};
// 组装受众规则操作类型
const assembleSymbolList = (symbolList, symbols) => {
  if (!Array.isArray(symbolList) || !symbolList.length || !symbols || !symbols.length) return [];
  let result = [];
  symbols.forEach(sy => {
    symbolList.forEach(list => {
      if (sy == list.type) {
        result.push(list);
      }
    });
  });
  //debugger;
  return result;
};
// 数组对象去重复
const clearDuplicate = arr => {
  // debugger;
  let map = new Map();
  for (let item of arr) {
    if (!map.has(item.label)) {
      map.set(item.label, item);
    }
  }
  //debugger;
  return [...map.values()];
};
// 设置互斥列表
export const setMutuList = (state, data) => {
  //debugger;
  state.mutuList = data;
};
// 设置互斥列表
export const setMutuList1 = (state, data) => {
  //debugger;
  state.mutuList = data;
  state.addData.newExclusiveId = state.mutuList[0].id;
};
const formatFilterList = data => {
  const result = [];
  Array.isArray(data) &&
    data.forEach(item => {
      result.push({
        label: item.displayName,
        value: item.keyName
      });
    });
  return result;
};

// 添加受众规则
export const addRule = (state, data) => {
  state.addData.filterRule.push({
    filterList: [
      {
        //keyName: '',
        keyType: '',
        keyValue: [],
        filterConfId: '',
        symbolList: []
        //keyValueType: '',
      }
    ]
  });
};

// 添加过滤规则
export const addFilterRule = (state, { index, item }) => {
  const data = Object.assign(
    {
      //keyName: '',
      keyType: '',
      keyValue: [],
      filterConfId: '',
      symbolList: []
      //keyValueType: '',
      //keyDisplayName: ''
    },
    item
  );
  state.addData.filterRule[index].filterList.push(data);
  return data;
};
// 删除过滤规则
export const delFilterRule = (state, { index, idx }) => {
  const filterArr = state.addData.filterRule[index].filterList;
  if (filterArr.length === 1) {
    // state.addData.filterRule.splice(index, 1);
    delRule(state, { index });
  } else {
    filterArr.splice(idx, 1);
  }
};

// 删除受众规则
export const delRule = (state, { index }) => {
  state.addData.filterRule.splice(index, 1);
};
// 设置白名单
export const setWhiteList = (state, data) => {
  state.whiteList = data ? data : [];
  // state.addData.versionList.forEach(item => {
  //   item.whiteOptions = _.cloneDeep(data);
  // });
  setWhiteListDisabled(state);
};
// 设置白名单
export const setIpList = (state, data) => {
  const list = data ? data : [];
  state.ipList = list.map(item => ({
    ...item,
    value: item.name,
    label: item.name
  }));
};

// 设置白名单
export const setWhiteListDisabled = state => {
  const whitelist = [];
  const versions = state.addData.versionList,
    trueVersions = versions.slice(1);
  trueVersions.forEach(item => {
    whitelist.push(...item.mywhitelist);
  });
  //debugger;
  state.whiteList.forEach(item => {
    item.selected = false;
    whitelist.forEach((itm, index) => {
      //debugger;
      if (itm === item.cuid) {
        //debugger;
        item.selected = true;
      }
    });
  });
  //debugger;
};
//setAppList
// 设置白名单
export const setAppList = (state, data) => {
  const origin = _.cloneDeep(data);
  let result = {
    displayName: '全部',
    appKey: 'all'
  };
  //debugger;
  data.unshift(result);
  state.appList1 = data;
  //debugger;
  state.appList = origin;
};
// 重置addData数据
export const resetAddData = (state, data) => {
  state.selectExclusive = {
    id: 0,
    flow: 0
  };
  state.fatherId = 0;
  state.fatherData = {};
  state.addData = {
    createUser: '',
    accountSystem: 1, // 账号体系，cuid：1，uid: 2
    indicators: [],
    selectedMetric: [],
    appKey: '', // 业务线
    displayName: '',
    isResetUser: 1, // 流量变更是否影响已进组用户0不影响1影响
    description: '',
    clientType: 1,
    duration: '',
    togetherEnd: false,
    newExclusiveId: '',
    exclusiveId: '',
    token: '',
    //mode: 0, //0编程实验1可视化实验2多连接实验4推送实验，默认0
    flow: 0,
    originFlow: 0, // 原始流量
    isWhitelistRule: 0,
    isFlowAvg: true,
    status: 0, // 新建默认传给后端代表实验处于草稿中
    isExpireNotice: false, // 开启实验到期提醒：0关闭1开启
    isLongNotice: true, // 开启长时运行提醒：0关闭1开启
    needReport: 0, // 是否需要实验报告：0不需要1需要
    emails: [], // 邮箱集合
    myEmails: [{ value: '' }], // 邮箱集合
    versionList: [
      {
        flow: 0,
        relationIds: [],
        fixedName: '',
        displayName: '',
        description: '',
        picUrl: '',
        versionId: 0,
        //id: '',
        featureList: [
          {
            featureId: 0,
            keyName: '',
            keyType: 1,
            keyValue: ''
          }
        ],
        whitelist: [
          //   {
          //     cuid: '',
          //     whitelistId: ''
          //   }
        ],
        mywhitelist: []
        //whiteOptions: []
      },
      {
        flow: 0,
        relationIds: [],
        fixedName: '对照版本',
        displayName: '对照版本',
        description: '',
        picUrl: '',
        versionId: 0,
        //id: '',
        featureList: [
          {
            featureId: 0,
            keyName: '',
            keyType: 1,
            keyValue: ''
          }
        ],
        whitelist: [
          //   {
          //     cuid: '',
          //     whitelistId: ''
          //   }
        ],
        mywhitelist: []
        //whiteOptions: []
        // ...pushData(),
        // ...getVisual()
      },
      {
        flow: 0,
        relationIds: [],
        fixedName: '实验版本',
        displayName: '实验版本1',
        description: '',
        picUrl: '',
        versionId: 0,
        //id: '',
        featureList: [
          {
            featureId: 0,
            keyName: '',
            keyType: 1,
            keyValue: ''
          }
        ],
        whitelist: [
          //   {
          //     cuid: '',
          //     whitelistId: ''
          //   }
        ],
        mywhitelist: []
        //whiteOptions: []
        // ...pushData(),
        // ...getVisual()
      }
    ],
    filterRule: [
      // {
      //     filterList: [{
      //         keyName: '',
      //         keyType: '',
      //         keyValue: [],
      //         filterConfId: 1
      //     }]
      // }
    ]
  };
};

// 设置实验详情数据
export const setExpDetail = (state, data) => {
  state.addData = formatAddData(state, data.res, data.params, data.isCopy);
  const originVersions = state.addData.originVersionList,
    originTrueVersions = originVersions.slice(1);
  state.originGroupFlow = originTrueVersions.map(item => item.flow);
};
// 设置已选互斥组数据
export const setSelectExclusive = (state, data) => {
  state.selectExclusive = data;
};
// 设置实验详情数据
export const setFatherExpDetail = (state, data) => {
  state.fatherData = formatAddData(state, data);
  state.addData.clientType = data.clientType;
  state.addData.appKey = data.appKey;
  state.addData.accountSystem = data.accountSystem;
};
export const setFatherId = (state, data) => {
  state.fatherId = data;
};
const formatWhiteList = data => {
  data.versionList.forEach((item, index) => {
    if (index === 0) {
      item.fixedName = '对照版本';
    } else {
      item.fixedName = '实验版本';
    }
    item.mywhitelist = [];
    // item.whitelist.forEach(list => {
    //   item.mywhitelist.push(list.whitelistId);
    // });
  });
  return data;
};
const formatAddData = (state, data1, params, isCopy) => {
  //debugger;
  // 拿到后端返回的某个实验的详细数据后，需要处理成自己想要的才能展示
  const data = _.cloneDeep(data1);
  data.selectedMetric = [];
  data.isExpireNotice = !!data.isExpireNotice; //
  data.isLongNotice = !!data.isLongNotice;
  data.exclusiveId = data.exclusiveId ? data.exclusiveId : '';
  data.togetherEnd = !!data.togetherEnd;
  data.isFlowAvg = !!data.isFlowAvg; //版本之间是否平均分配流量0否1是
  data.flow = data.flow ? data.flow / 10 : 0; // 实验总流量
  data.originFlow = data.flow; // 记录实验原始流量，因为实验发布之后流量只能调大不能调小
  data.originDuration = data.duration;
  if (data.status !== 3 && data.exclusiveId) {
    data.newExclusiveId = data.exclusiveId;
  }
  if (data.status !== 3 && !data.exclusiveId) {
    data.newExclusiveId = '';
  }
  if (data.status === 3 && data.exclusiveId) {
    data.newExclusiveId = data.exclusiveId;
  }
  if (data.status === 3 && !data.exclusiveId) {
    data.newExclusiveId = '';
  }
  data.versionList.forEach(item => {
    if (isCopy) {
      item.versionId = 0;
    }
    item.flow = item.flow / 10;
    item.relationIds = item.relationIds ? item.relationIds : [];
    //item.tabName = uuidv4();
  });
  if (data.filterRule.length === 1 && data.filterRule[0].filterList.length === 0) {
    data.filterRule = [];
  } else {
    data.filterRule.forEach(rule => {
      rule.filterList.forEach(list => {
        try {
          let kvx = JSON.parse(list.keyValue);
          if (Object.prototype.toString.call(kvx) === '[object Array]') {
            list.keyValue = Array.from(kvx).map(x => x + '');
          } else {
            list.keyValue = ['' + list.keyValue];
          }
        } catch (_) {
          list.keyValue = ['' + list.keyValue];
        }
      });
    });
  }
  // 如果不是从固化创建实验
  if (params && !params.correctId) {
    data.myEmails =
      Array.isArray(data.emails) &&
      data.emails.map(item => {
        var temp = {};
        temp.value = item.split('@')[0];
        return temp;
      });
  } else {
    const uname = localStorage.getItem('uname');
    //debugger;
    data.myEmails = [{ value: uname }];
    // data.myEmails[0].value = uname;
    //debugger;
  }

  const result = formatWhiteList(data);
  const parmarVersion = _.cloneDeep(result.versionList[0]);

  result.versionList.unshift(parmarVersion);
  result.originVersionList = _.cloneDeep(result.versionList);
  return result;
};
// 设置实验历史数据
export const setHistoryData = (state, data) => {
  //debugger;
  data.forEach(item => {
    item.open = false;
  });
  state.expHistoryData = data;
};
//setReportData
// 设置实验报告数据
export const setReportData = (state, data) => {
  state.expReportData = data;
};

// 重置受众规则，在新增实验的时候，如果先选择了受众规则，然后又回到第一步修改账号体系或者业务线，需要重置
export const resetFilterList = (state, data) => {
  state.addData.filterRule = [];
};

// 重置第四步骤白名单，在新增实验的时候，如果先选择了白名单，然后又回到第一步修改账号体系或者业务线，需要重置
export const resetMyWhiteList = (state, data) => {
  const versions = state.addData.versionList,
    trueVersions = versions.slice(1),
    trueVerLen = trueVersions.length;
  for (let j = 0; j < trueVerLen; j++) {
    trueVersions[j].mywhitelist = [];
  }
};

// 重置第四步骤互斥组，在新增实验的时候，如果先选择了互斥组，然后又回到第一步修改账号体系或者业务线或者实验类型，需要重置
export const resetNewExclusiveId = (state, data) => {
  state.addData.newExclusiveId = '';
  state.addData.flow = 0;
};
