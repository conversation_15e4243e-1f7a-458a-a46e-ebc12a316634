/*
 * @Author: hyw
 * @Date: 2021-01-22 10:43:44
 * @LastEditors: gechunyang
 * @LastEditTime: 2021-03-12 14:44:30
 */

import service from '@config/fetch/fetch.js';
// 获取过滤条件数据
export const getFilterList = ({ commit, state }, val) => {
  const params = {
    pn: 1,
    rn: 100000,
    appKey: val ? val : state.addData.appKey,
    accountSystem: state.addData.accountSystem
  };
  service
    .get('FILTER_LIST', { ...params })
    .then(res => {
      //debugger;
      commit('setFilterList', res);
    })
    .catch(err => {
      console.log(err);
    });
};
export const getFilterList1 = ({ commit, state }, val) => {
  const params = {
    pn: 1,
    rn: 100000,
    appKey: val ? val : state.addData.appKey,
    accountSystem: state.addData.accountSystem
  };
  service
    .get('FILTER_LIST', { ...params })
    .then(res => {
      //debugger;
      commit('setFilterList1', res);
    })
    .catch(err => {
      console.log(err);
    });
};
// 在实验第四步，新增互斥组，后需要获取最新的互斥组
export const getMutuListForExp = ({ commit, state }, val) => {
  const params = {
    pn: 0,
    rn: 100000,
    appKey: val ? val : state.addData.appKey,
    clientType: state.addData.clientType,
    isDisplay: 1,
    experimentStatus: state.addData.status == 3 ? 3 : '', // 当实验处于发布中的时候需要传递3
    accountSystem: state.addData.accountSystem
  };
  if (state.fatherId) {
    // 新增子实验
    params.parentExperimentId = state.fatherId;
  }
  if (state.addData.id) {
    // 编辑 or 查看 任意实验
    params.experimentId = state.addData.id;
  }
  service
    .get('AB_EXCLUSIVE', { ...params })
    .then(res => {
      //debugger;
      const { id, flow } = state.selectExclusive;
      if (
        id &&
        state.addData.status != 1 &&
        state.addData.status != 0 &&
        state.addData.status != 4
      ) {
        res.list.forEach(v => {
          if (v.id == id) {
            if (state.addData.status != 5) {
              v.freeFlow = v.freeFlow + flow;
            } else {
              v.freeFlow = v.freeFlow + state.addData.extendsGroupPubFlow;
            }
          }
        });
      }
      commit('setMutuList1', res.list);
    })
    .catch(err => {
      console.log(err);
    });
};
// 获取互斥数据
export const getMutuList = ({ commit, state }, val) => {
  const params = {
    pn: 0,
    rn: 100000,
    appKey: val ? val : state.addData.appKey,
    clientType: state.addData.clientType,
    isDisplay: 1,
    experimentStatus: state.addData.status == 3 && state.addData.exclusiveId == 0 ? 3 : '', // 当实验处于发布中的时候需要传递3
    accountSystem: state.addData.accountSystem
  };
  if (state.fatherId) {
    // 新增子实验
    params.parentExperimentId = state.fatherId;
  }
  if (state.addData.id) {
    // 编辑 or 查看 任意实验
    params.experimentId = state.addData.id;
  }
  //debugger;
  service
    .get('AB_EXCLUSIVE', { ...params })
    .then(res => {
      const { id, flow } = state.selectExclusive;
      if (
        id &&
        state.addData.status != 1 &&
        state.addData.status != 0 &&
        state.addData.status != 4
      ) {
        res.list.forEach(v => {
          if (v.id == id) {
            if (state.addData.status != 5) {
              v.freeFlow = v.freeFlow + flow;
            } else {
              v.freeFlow = v.freeFlow + state.addData.extendsGroupPubFlow;
            }
          }
        });
      }
      commit('setMutuList', res.list);
    })
    .catch(err => {
      console.log(err);
    });
  //debugger;
};
// 获取白名单
export const getWhiteList = ({ commit, state }, val) => {
  const params = {
    pn: 0,
    rn: 100000,
    appKey: val ? val : state.addData.appKey,
    accountSystem: state.addData.accountSystem
  };
  service
    .get('WHITE_LIST', { ...params })
    .then(res => {
      commit('setWhiteList', res.list);
    })
    .catch(err => {
      console.log(err);
    });
};

// 获取应用列表
export const getAppList = ({ commit, state }, val) => {
  const params = {
    pn: 0,
    rn: 100000
  };
  service
    .get('APPLIST', { ...params })
    .then(res => {
      commit('setAppList', res.list);
    })
    .catch(err => {
      console.log(err);
    });
};
// 获取实验详情
export const getExpDetail = ({ dispatch, commit, state }, val) => {
  const params = {
    experimentId: val.id,
    correctId: val.correctId
  };
  const isCopy = val.isCopy;
  //debugger;
  service
    .get('EDIT_TEST', { ...params }, { allback: 0, needLoading: true })
    .then(res => {
      if (isCopy) {
        const regex = /复制_(\d+)$/;
        const match = res.displayName.match(regex);

        if (match) {
          // 提取数字并加1
          const num = parseInt(match[1], 10) + 1;
          // 替换原数字部分
          res.displayName = res.displayName.substring(0, match.index) + `复制_${num}`;
        } else {
          // 如果没有匹配到，添加后缀 "_复制_1"
          res.displayName += '_复制_1';
        }
        res.status = 0;
        res.extendsExperimentList = [];
      }
      console.log('hahahahahahah', res);
      commit('setExpDetail', { res, params, isCopy });
      commit('setSelectExclusive', { id: res.exclusiveId, flow: res.flow });
      val.parentId && dispatch('getFatherExpDetail', { id: val.parentId });
      const appKey = res.appKey;
      dispatch('getFilterList1', appKey);
      dispatch('getMutuList', appKey);
      dispatch('getWhiteList', appKey);
      dispatch('getMetricList1', appKey);
      dispatch('getHistoryData', { id: res.id });
    })
    .catch(err => {
      console.log(err);
    });
};
// 获取父实验详情
export const getFatherExpDetail = ({ dispatch, commit, state }, val) => {
  const params = {
    experimentId: val.id
  };
  //debugger;
  service
    .get('EDIT_TEST', { ...params }, { allback: 0, needLoading: true })
    .then(res => {
      commit('setFatherExpDetail', res);
      // 为何判断：因为只有新增子实验的时候才需要走下面的逻辑，编辑的时候是不需要的
      if (val.isCreateChild) {
        const appKey = res.appKey;
        dispatch('getFilterList1', appKey);
        dispatch('getMutuList', appKey);
        dispatch('getWhiteList', appKey);
        dispatch('getMetricList1', appKey);
      }
    })
    .catch(err => {
      console.log(err);
    });
};
// 获取实验历史记录数据
export const getHistoryData = ({ commit, state }, val) => {
  // 从固化过来创建实验，没有实验id
  if (!val.id) return;
  const params = {
    experimentId: val.id
  };
  service
    .get('SHOW_LABHISTORY', { ...params }, { allback: 0, needLoading: true })
    .then(res => {
      //const flow = res.versionList[0].flow;
      commit('setHistoryData', res);
    })
    .catch(err => {
      console.log(err);
    });
};
// 实验报告
export const getReportData = ({ dispatch, commit, state }, val) => {
  const params = {
    id: val
  };
  service
    .get('CHECKREPORT', { ...params }, { allback: 0, needLoading: true })
    .then(res => {
      commit('setReportData', res);

      dispatch('getHistoryData', { id: val });
      // const flow = res.versionList[0].flow;
      // res.versionList.forEach((item) => {
      //   if (item.flow !== flow && flag.value) {
      //     this.flag = false;
      //   }
      // });
      //this.reportData = res;
    })
    .catch(err => {
      console.log(err);
    });
};

// 获取指标列表
export const getMetricList = ({ dispatch, commit, state }, val) => {
  const params = {
    isMust: -1, //全部指标
    status: 1, // 使用中
    search: '',
    appKey: val ? val : state.addData.appKey,
    pn: 1,
    rn: 100000
  };
  service
    .get('INDICATOR_LIST', { ...params }, { allback: 0, needLoading: false })
    .then(res => {
      //debugger;
      commit('setMetricData', res.list ? res.list : []);
      console.log(res);
    })
    .catch(err => {
      console.log(err);
    });
};
// 获取指标列表
export const getMetricList1 = ({ dispatch, commit, state }, val) => {
  const params = {
    isMust: -1, //全部指标
    //status: 1, // 使用中
    search: '',
    appKey: val ? val : state.addData.appKey,
    pn: 1,
    rn: 100000
  };
  service
    .get('INDICATOR_LIST', { ...params }, { allback: 0, needLoading: false })
    .then(res => {
      //debugger;
      commit('setMetricData1', res.list ? res.list : []);
      console.log(res);
    })
    .catch(err => {
      console.log(err);
    });
};

// 获取ip地址列表
export const getIpList = ({ dispatch, commit, state }, val) => {
  service
    .get('FILTERIPLIST', { allback: 0, needLoading: false })
    .then(res => {
      commit('setIpList', res ? res : []);
    })
    .catch(err => {
      console.log(err);
    });
};
