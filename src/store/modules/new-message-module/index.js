/*
 * @Author: g<PERSON><PERSON><PERSON>
 * @Date: 2021-01-22 10:43:44
 * @LastEditors: zhu<PERSON>e
 * @LastEditTime: 2021-08-24 11:50:15
 */

import Vue from 'vue';
import Vuex from 'vuex';
import * as mutations from './mutations';
import * as getters from './getters';
import * as actions from './actions';
import { pushData, getVisual } from './utils';

Vue.use(Vuex);

const state = {
  // 账号体系数据
  accSystemData: [
    {
      value: '',
      label: '全部'
    },
    {
      value: 1,
      label: 'cuid'
    },
    {
      value: 2,
      label: 'uid'
    }
  ],
  metricTableData: [], // 新建第二步table中展示的指标数据
  metricData: [], // 指标数据，主要在创建和编辑实验的时候需要
  // 登陆人信息
  userInfo: { uname: '' },
  // 登陆之后的cb
  afterLoginCallBacks: [],
  seletedWhiteList: [], //已经选中的白名单
  // 实验历史数据
  expHistoryData: [],
  expReportData: {},
  // 应用数据
  appList: [],
  // 应用数据
  appList1: [],
  locationList: [{
    value: 'home',
    label: '首页'
  },
  {
    value: 'other',
    label: '其他'
  }],
  // 实验状态
  expStatusList: [
    {
      value: '',
      label: '全部'
    },
    {
      value: 1,
      label: '草稿'
    },
    {
      value: 5,
      label: '待运行'
    },
    {
      value: 3,
      label: '运行阶段'
    },
    {
      value: 8,
      label: '发布阶段'
    },
    {
      value: 4,
      label: '结束'
    },
    // {
    //   value: 6,
    //   label: '已暂停'
    // },
    // {
    //   value: -1,
    //   label: '删除'
    // }
  ],
  statusMap: {
    1: '草稿',
    2: '调试中',
    3: '运行中',
    4: '结束',
    5: '待运行',
    '-1': '删除',
    6: '已暂停',
    7: '已冻结',
    8: '发布中',
    9: '已回滚'
  },
  whiteList: [],
  mutuList: [],
  filerList: [],
  paramLists: [
    {
      param: '',
      paramType: 1,
      paramList: [
        {
          value: ''
        },
        {
          value: ''
        }
      ]
    }
  ],
  paramTypes: [
    {
      label: 'string',
      value: 1
    },
    {
      label: 'boolean',
      value: 2
    },
    {
      label: 'float',
      value: 4
    },
    {
      label: 'json',
      value: 5
    }
  ],
  booleanList: [
    {
      name: 'true',
      label: 'true'
    },
    {
      name: 'false',
      label: 'false'
    }
  ],
  emailList: [],
  fatherId: 0,
  fatherData: {},
  // 创建实验的数据
  addData: {
    accountSystem: 1, // 账号体系，cuid：1，uid: 2
    indicators: [],
    selectedMetric: [],
    appKey: '', // 业务线
    displayName: '',
    isResetUser: 1, // 流量变更是否影响已进组用户0不影响1影响
    description: '',
    clientType: 1,
    duration: '',
    togetherEnd: false,
    newExclusiveId: '',
    exclusiveId: '',
    //mode: 0, //0编程实验1可视化实验2多连接实验4推送实验，默认0
    flow: 0,
    originFlow: 0, // 原始流量
    isWhitelistRule: 0,
    isFlowAvg: true,
    status: 0, // 新建默认传给后端代表实验处于草稿中
    isExpireNotice: false, // 开启实验到期提醒：0关闭1开启
    isLongNotice: true, // 开启长时运行提醒：0关闭1开启
    needReport: 0, // 是否需要实验报告：0不需要1需要
    emails: [], // 邮箱集合
    myEmails: [{ value: '' }], // 邮箱集合
    versionList: [
      {
        flow: 0,
        relationIds: [],
        fixedName: '',
        displayName: '',
        description: '',
        picUrl: '',
        versionId: 0,
        //id: '',
        featureList: [
          {
            featureId: 0,
            keyName: '',
            keyType: 1,
            keyValue: ''
          }
        ],
        whitelist: [
          //   {
          //     cuid: '',
          //     whitelistId: ''
          //   }
        ],
        mywhitelist: [],
        //whiteOptions: []
      },
      {
        flow: 0,
        relationIds: [],
        fixedName: '对照版本',
        displayName: '对照版本',
        description: '',
        picUrl: '',
        versionId: 0,
        //id: '',
        featureList: [
          {
            featureId: 0,
            keyName: '',
            keyType: 1,
            keyValue: ''
          }
        ],
        whitelist: [
          //   {
          //     cuid: '',
          //     whitelistId: ''
          //   }
        ],
        mywhitelist: [],
        //whiteOptions: [],
        // ...pushData(),
        // ...getVisual()
      },
      {
        flow: 0,
        relationIds: [],
        fixedName: '实验版本',
        displayName: '实验版本1',
        description: '',
        picUrl: '',
        versionId: 0,
        //id: '',
        featureList: [
          {
            featureId: 0,
            keyName: '',
            keyType: 1,
            keyValue: ''
          }
        ],
        whitelist: [
          //   {
          //     cuid: '',
          //     whitelistId: ''
          //   }
        ],
        mywhitelist: [],
        //whiteOptions: [],
        // ...pushData(),
        // ...getVisual()
      }
    ],
    originVersionList:[],
    originDuration:0,
    filterRule: [
      // {
      //     filterList: [{
      //         keyName: '',
      //         keyType: '',
      //         keyValue: [],
      //         filterConfId: 1
      //     }]
      // }
    ]
  },
  selectExclusive: {
    id: 0,
    flow: 0
  },
  ipList: []
};

const messages = {
  state,
  mutations,
  getters,
  actions
};

export default messages;
