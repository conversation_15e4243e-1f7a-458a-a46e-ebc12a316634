/*
 * @Author: gechunyang
 * @Date: 2021-01-22 10:43:44
 * @LastEditors: gechunyang
 * @LastEditTime: 2021-02-22 19:58:29
 */

export const fatherData = state => state.fatherData;
export const addData = state => state.addData;
export const originGroupFlow = state => state.originGroupFlow;
export const emailList = state => state.emailList;
export const paramTypes = state => state.paramTypes;
export const booleanList = state => state.booleanList;
export const paramLists = state => state.paramLists;
export const filerList = state => state.filerList;
export const mutuList = state => state.mutuList;
export const whiteList = state => state.whiteList;
export const expStatusList = state => state.expStatusList;
export const statusMap = state => state.statusMap;
export const appList = state => state.appList;
export const appList1 = state => state.appList1;
//export const reportData = state => state.reportData;
export const expHistoryData = state => state.expHistoryData;
export const expReportData = state => state.expReportData;
export const seletedWhiteList = state => state.seletedWhiteList;
export const userInfo = state => state.userInfo;
export const metricData = state => state.metricData;
export const metricTableData = state => state.metricTableData;
export const accSystemData = state => state.accSystemData;
export const ipList = state => state.ipList;
