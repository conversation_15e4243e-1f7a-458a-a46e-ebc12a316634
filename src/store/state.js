// /*
//  * @FileName: sate
//  * @Author: Leinov
//  * @Date: 2019-12-19 11:49:58
//  * @LastEditTime : 2020-01-06 11:11:11
//  * @Description: 群控状态总state
//  * @FilePath: \wxcservice\src\store\state.js
//  */
// /**
//  * {
//     tabs:[
//         {
//             name: string, // tab英文名称
//             label: number, // tab显示中文名称
//             disable: boolean, //是否可切换
//             formData: object //表单数据，添加时存量数据，编辑时初始化数据
//         }
//     ];
// }
//  */
// import * as _ from 'lodash';

// // 默认表单数据
// export const defaulFormtData = {
//     'group-list-setting': {
//         activityName: '',
//         coverPic: '',
//         tagName: '',
//         tags:[

//         ],
//         changeNum: 95,
//         startTime:{
//             type: 0,
//             value: ''
//         },
//         startUserNum: {
//             type: 0,
//             value: 0
//         },
//         removeRepeat: true
//     },
//     'group-props': {
//         name: '',
//         startNumber: 1,
//         notice:'',
//         robotName:'',
//         assistantName:'',
//         groupOp:[],
//     },
//     'greeting': {
//         openGreeting: true,
//         greetingRule: [1],
//         everySendTime: 2,
//         everyIntoNum:10,
//         greetingContent: [
//             {
//                 type: 1,
//                 text: '',
//                 key:'sfsfs'
//             }
//         ]
//     },
//     'share-task': {
//         openTask: true,
//         sendpicNum: 1,
//         finishRemindTimes: 2,
//         finishRemind:[
//             {
//                 type: 1,
//                 text: '',
//                 key: 'dex'
//             },
//         ],
//         unfinishRemindTime: 0,
//         repeatTimes:0,
//         unfinishRemind: [
//             {
//                 type: 1,
//                 text: '',
//                 key: 'res'
//             },
//         ]
//     },
//     'risk-control-setting': {
//         forbidAction: [
//             {
//                 type: 11,
//                 op: 2, // 踢群1 踢群拉黑2
//                 after: 0
//             },
//             {
//                 type: 5,
//                 op: 2,
//                 after: 0
//             },
//             {
//                 type: 0,
//                 op: 2,
//                 after: 0
//             },
//             {
//                 type: 18,
//                 op: 2,
//                 after: 0
//             },
//             {
//                 type: 4,
//                 op: 2,
//                 after: 0
//             },
//             {
//                 type: 100,
//                 op: 2,
//                 after: 0
//             },
//             {
//                 type: 1,
//                 op: 2,
//                 after: 1
//             }
//         ],
//         speakSensitive:{
//             list: ['所有人', '@所有人', '识别领取', '识', '点击头像', '抓紧时间', '识别二维码', '学习委员', '点链接'],
//             op: 2,
//         },
//         nickSensitive:{
//             list: ['群主', '课程老师', '小助手', '小管家', '助教', '助学', '学习委员', '班长', '老师', '课程顾问', '排课', '大塘小鱼', '高途', '跟谁学', '掌门', '海风', '领资料', '领课程', '洋葱'],
//             op: 2
//         },
//         removeMsg: {
//             content: '#user# \n [NO]此人是骗子已被老师移出此群，请大家不要相信此人, \n [闪电]特别说明 \n #任何人以"排课"和"发资料"等名义加您, 请不要相信，直接拉黑，谨防受骗！',
//             sendConentEveryTime: 0
//         },
//         blackMsg: {
//             content: '#user# \n [NO]此人是骗子已被老师移出此群，请大家不要相信此人, \n [闪电]特别说明 \n #任何人以"排课"和"发资料"等名义加您, 请不要相信，直接拉黑，谨防受骗！',
//             sendConentEveryTime: 1
//         }
//    }

// };

// const cloneFromData = _.cloneDeep(defaulFormtData);
// export default {
//     // 页面类型 创建活动 create / 编辑活动 edit
//     pageType: 'create',
//     // 添加群裂变任务当前显示tab名称
//     activeTabName: 'group-list-setting',
//     // 当前tabindex
//     activeTabIndex: 0,
//     // 活动id 1.添加时第一步传0，第二步之后为第一步保存返回的activityId，2.编辑页为列表进来带入的activityId
//     activityId: 0,
//     // 添加群裂变任务tab列表
//     tabs: [
//         {
//            step: 1,
//            name: 'group-list-setting',
//            label:'群裂变设置',
//            disable: false,
//            formData: cloneFromData['group-list-setting']
//         },
//         {
//            step: 2,
//            name: 'group-props',
//            label:'群属性',
//            disable: false,
//            formData: cloneFromData['group-props']
//         },
//         {
//            step: 3,
//            name: 'greeting',
//            label:'入群欢迎语',
//            disable: false,
//            formRules: {
//                 everySendTime:[
//                     {
//                         message: '请输入数字',
//                         type: 'number',
//                     },
//                     {
//                         message: '最小1分钟，最大10分钟',
//                         type: 'number',
//                         max:10,
//                         min:1
//                     }

//                 ],
//                 everyIntoNum:[
//                     {
//                         message: '请输入数字',
//                         type: 'number',
//                     },
//                     {
//                         message: '最小1人，最大100人',
//                         type: 'number',
//                         max: 100,
//                         min: 1
//                     }
//                 ]
//            },
//            formData: cloneFromData['greeting']
//         },
//         {
//            step: 4,
//            name: 'share-task',
//            label:'分享任务',
//            disable: false,
//            formData: cloneFromData['share-task']
//         },
//         {
//            step: 5,
//            name: 'risk-control-setting',
//            label:'风控设置',
//            disable: false,
//            formData: cloneFromData['risk-control-setting']
//         },
//     ]
// };
