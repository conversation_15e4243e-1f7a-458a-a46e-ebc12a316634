// /*
//  * @FileName: mutation
//  * @Author: Leinov
//  * @Date: 2019-12-19 13:49:21
//  * @LastEditTime : 2020-01-03 14:12:36
//  * @Description: 活动mutation
//  * @FilePath: \wxcservice\src\store\mutation.js
//  */
// import {defaulFormtData} from './state';
// import * as _ from 'lodash';
// // 下一步
// export const gotoNextTab = (state, payload) => {
//   const {activeTabIndex} = state;
//   state.activeTabIndex += 1;
//   state.activeTabName = state.tabs[state.activeTabIndex]['name'];
//   state.tabs[state.activeTabIndex]['disable'] = false;
// };

// // 上一步
// export const gotoPreTab = (state) => {
//   const {activeTabIndex} = state;
//   state.activeTabName = state.tabs[activeTabIndex - 1]['name'];
// };

// // 当前tab名称
// export const updateActiveName = (state, payload) => {
//   state.activeTabName = payload;
//   let index = state.tabs.findIndex((tab, index) => {
//       return tab.name === payload;
//   });
//   state.activeTabIndex = index;
//   state.step = index + 1;
// };

// // 当前tab index
// export const activeTabIndex = (state, payload) => {
//   state.activeTabIndex = payload;
// };

// // 新增标签
// export const addTag = (state, payload) => {
//   const {tag,name} = payload;
//   state.tabs.forEach((item, index)=>{
//       if (item.name === name) {
//           state.tabs[index].formData.tags.push(tag);
//       };
//   });
// };

// // 删除标签
// export const deleteTag = (state, payload) => {
//   const {name, index: tagIndex} = payload;
//   state.tabs.forEach((item, index)=>{
//       if (item.name === name) {
//           state.tabs[index].formData.tags.splice(tagIndex, 1);
//       };
//   });
// };

// // 修改内容
// export const updateContent = (state, payload) => {
//   const {data:{index:contentIndex, length, content}, prop} = payload;
//   if (content) {
//     state.tabs[state.activeTabIndex].formData[prop].splice(contentIndex, length, content);
//   } else {
//     state.tabs[state.activeTabIndex].formData[prop].splice(contentIndex, length);
//   }
// };

// // 新增内容
// export const addContent = (state, payload) => {
//   const {data, prop} = payload;
//   console.log(payload);
//   state.tabs[state.activeTabIndex].formData[prop].push(data);
// };

// // 初始化page信息
// export const pageInit = (state, payload) => {
//   const {pageType, activityId} = payload;
//   state.pageType = pageType;
//   state.activityId = activityId;
//   state.step = 1;
//   state.activeTabIndex = 0;
//   state.activeTabName = "group-list-setting";
//   if (pageType === 'edit') {
//     state.tabs.forEach((tab, index) => {
//       state.tabs[index].disable = false;
//     });
//     state.tabs[2].formData.greetingContent.forEach((item, index) => {
//       if (!item.key) {
//         state.tabs[2].formData.greetingContent[index].key = index + 'e' + Math.random();
//       }
//     });
//     state.tabs[3].formData.unfinishRemind.forEach((item, index) => {
//       if (!item.key) {
//         state.tabs[3].formData.unfinishRemind[index].key = index + Math.random();
//       }
//     });
//     state.tabs[3].formData.finishRemind.forEach((item, index) => {
//       if (!item.key) {
//         state.tabs[3].formData.finishRemind[index].key = index +'d' + Math.random();
//       }
//     });
//   } else {
//     state.tabs.forEach((tab, index) => {
//       if (index !== 0) {
//         state.tabs[index].disable = true;
//       }
//     });
//   }
// };

// // 清理表单
// export const cleanFormData = (state, clearnAll) => {
//   if (clearnAll) {
//     const cloneFromData = _.cloneDeep(defaulFormtData);
//     state.activeTabIndex =  0;
//     state.activeTabName = "group-list-setting";
//     state.activityId = 0;
//     state.pageType = "create";
//     state.step =  1;
//     state.tabs.forEach((tab, index) => {
//       state.tabs[index].formData = cloneFromData[tab.name];
//     });
//   } else {
//     const cloneFromData = _.cloneDeep(defaulFormtData);
//     const {activeTabIndex, activeTabName} = state;
//     state.tabs[activeTabIndex].formData = cloneFromData[activeTabName];
//   }
// };

// // 更新活动Id
// export const updateActivityId = (state, id) => {
//   state.activityId = id;
// };

// // 编辑页信息回填
// export const writeBackFormInfo = (state, payload) => {
//   const index = state.activeTabIndex;
//   // 兼容返回为空保存错误的情况
//   if (state.activeTabIndex === 2 && payload.greetingRule === null) {
//     payload.greetingRule = [];
//   }
//   if (state.activeTabIndex === 4) {
//     if (payload.speakSensitive && !payload.speakSensitive.list) {
//       payload.speakSensitive.list = [];
//     }
//     if (payload.nickSensitive && !payload.nickSensitive.list) {
//       payload.nickSensitive.list = [];
//     }
//   }
//   state.tabs[index].formData = payload;
// };
