/*
 * @Author:
 * @Date: 2020-08-10 16:17:43
 * @LastEditors: zhu<PERSON>e
 * @LastEditTime: 2021-08-11 14:19:33
 * @Description:  人群包
 */

const abprefix = '/earthworm/mis';

export default {
  // 人群包
  CROWDLIST: `${abprefix}/crowd/list`,
  CROWDINFO: `${abprefix}/crowd/info`,
  CROWDDEL: `${abprefix}/crowd/delete`,
  CROWDEXP: `${abprefix}/crowd/export`,
  CROWDINFOS: `${abprefix}/crowd/infos`,
  CROWDUPLOAS: `${abprefix}/crowd/uploadfile`,
  CROWDadd: `${abprefix}/crowd/add`,
  CROWDedit: `${abprefix}/crowd/edit`,
  CROWDUPDATE: `${abprefix}/crowd/update`,
  CROWDCHECK: `${abprefix}/crowd/check`,
  // 人群包关联的固化列表
  CROWDLISTFEAT: `${abprefix}/crowd/correct`,
  // 人群包关联实验
  GLTESTCROWD_LIST: `${abprefix}/crowd/experiment`,
  CROWD_HISTORY: `${abprefix}/crowd/history`,
  COSDOWNLOADURL:`${abprefix}/crowd/cosdownloadauth`,
  // 获取用户属性
  CROWD_ATTR:`${abprefix}/crowd/crowdprofile/attributes`,
  CROWD_RULE_CHECK:`${abprefix}/crowd/crowdprofile/check`,
  CROWD_RULE_ADD: `${abprefix}/crowd/crowdprofile/add`,
  CROWD_RULE_EDIT: `${abprefix}/crowd/crowdprofile/edit`,
  CROWD_RULE_VIEW:`${abprefix}/crowd/crowdprofile/view`,
  CROWD_RULE_HISTORY: `${abprefix}/crowd/crowdprofile/history`,
};
