/*
 * @Author:
 * @Date: 2020-08-10 16:17:43
 * @LastEditors: zhu<PERSON>e
 * @LastEditTime: 2021-08-11 14:19:33
 * @Description:
 */

const abprefix = '/earthworm/mis';
const abprefix1 = '/earthworm';

export default {
  // 左侧导航菜单
  // MENU_LIST: `/earthworm/auth/menulist`,
  // // 用户信息
  // USER_INFO: `/earthworm/auth/userinfo`,
  /** 用户信息 */
  //AUTH_USERINFO: `${abprefix}/manager/user`,
  // 实验列表
  AB_LIST: `${abprefix1}/experiment/list`,
  // 图片上传
  AB_IMAGELOADURL: `${abprefix}/upload/uploadimg`,
  // 互斥列表
  AB_EXCLUSIVE: `${abprefix}/exclusive/list`,
  // 设置互斥组展示（下线）
  AB_EXCLUSIVE_HIDE: `${abprefix}/exclusive/hide`,
  // 设置互斥组展示（上线）
  AB_EXCLUSIVE_SHOW: `${abprefix}/exclusive/show`,
  // 互斥组详情
  AB_EXCLUSIVE_VIEW: `${abprefix}/exclusive/view`,
  // 新建互斥
  AB_EXCLUSIVE_ADD: `${abprefix}/exclusive/add`,
  // 删除互斥
  AB_EXCLUSIVE_DELETE: `${abprefix}/exclusive/del`,
  // 更新互斥
  AB_EXCLUSIVE_UPDATE: `${abprefix}/exclusive/update`,
  // 过滤项列表
  FILTER_LIST: `${abprefix}/filterconf/list`,
  // 互斥实验配置
  EXCLUSIVE_LIST: `${abprefix}/exclusive/display`,
  // 白名单
  WHITE_LIST: `${abprefix}/whitelist/list`,
  // 删除白名单
  DELETEWHITE_LIST: `${abprefix}/whitelist/delete`,
  // 新建白名单
  ADDWHITE_LIST: `${abprefix}/whitelist/add`,
  // 编辑白名单
  EDITWHITE_LIST: `${abprefix}/whitelist/update`,
  // 白名单关联实验
  GLTESTWHITE_LIST: `${abprefix}/whitelist/experiment`,
  // 白名单分组列表
  WHITE_GROUP_LIST: `${abprefix}/whitelist/grouplist`,
  // 白名单分组详情
  WHITE_GROUP_VIEW: `${abprefix}/whitelist/groupview`,
  // 更新白名单分组
  WHITE_GROUP_UPDATE: `${abprefix}/whitelist/groupupdate`,
  // 添加白名单分组
  WHITE_GROUP_ADD: `${abprefix}/whitelist/groupadd`,
  // 删除白名单分组
  WHITE_GROUP_DELETE: `${abprefix}/whitelist/groupdelete`,
  // 白名单分组批量更新
  WHITE_GROUP_UPDATE_WHITELIST: `${abprefix}/whitelist/updategroup`,
  // 未分组白名单列表
  NO_GROUP_WHITELIST: `${abprefix}/whitelist/listnogroup`,
  // 删除实验
  DELETE_TEST: `${abprefix1}/experiment/del`,
  // 结束实验
  OVER_TEST: `${abprefix1}/experiment/stop`,
  // 暂停实验
  PAUSE_TEST: `${abprefix1}/experiment/pause`,
  // 继续实验
  PURSE_TEST: `${abprefix1}/experiment/pursue`,
  // 开始实验
  START_TEST: `${abprefix1}/experiment/start`,
  // 添加实验
  ADD_TEST: `${abprefix1}/experiment/add`,
  // 查看实验
  EDIT_TEST: `${abprefix1}/experiment/view`,
  // 白名单搜索
  WHITELIST_SEARCH: `${abprefix}/whitelist/search`,
  // 查看白名单view
  WHITELIST_VIEW: `${abprefix}/whitelist/view`,
  // 编辑实验
  VIEW_TEST: `${abprefix1}/experiment/edit`,
  // 实验流量建议工具
  FLOW_SUGGEST: `${abprefix1}/experiment/flowsuggest`,
  // 实验操作历史
  SHOW_LABHISTORY: `${abprefix1}/experiment/history`,
  // 预检查
  PRECHECK: `${abprefix1}/experiment/preCheck`,
  // 检查报告
  CHECKREPORT: `${abprefix}/experiment/report`,
  // 获取邮件人员列表
  EMAILLIST: `${abprefix1}/experiment/emaillist`,
  // 去固化
  TURNTOFEATURE: `${abprefix}/correct/view`,
  // 删除固化
  DELETEFEATURE: `${abprefix1}/correct/del`,
  // 引用列表
  APPLIST: `${abprefix}/application/list`,
  APPLISTWITHDATA: `${abprefix}/application/listwithdata`,
  APPREPORTLIST: `${abprefix}/application/reportlist`,
  // 退出登陆
  // LOGOUT: `/earthworm/auth/logout`,
  // 白名单关联的固化列表
  WHITELISTFEAT: `${abprefix}/whitelist/correct`,

  // 人群包
  // CROWDLIST: `${abprefix}/crowd/list`,
  // CROWDINFO: `${abprefix}/crowd/info`,
  // CROWDDEL: `${abprefix}/crowd/delete`,
  // CROWDEXP: `${abprefix}/crowd/export`,
  // CROWDINFOS: `${abprefix}/crowd/infos`,
  // CROWDUPLOAS: `${abprefix}/crowd/uploadfile`,
  // CROWDadd: `${abprefix}/crowd/add`,
  // CROWDedit: `${abprefix}/crowd/edit`,
  // //LOGOUT: `/earthworm/auth/logout`,
  // CROWDUPDATE: `${abprefix}/crowd/update`,
  // // 人群包关联的固化列表
  // CROWDLISTFEAT: `${abprefix}/crowd/correct`,
  // 人群包关联实验
  GLTESTCROWD_LIST: `${abprefix}/crowd/experiment`,

  GETENGINERET: `${abprefix}/test/getengineret`,
  GETENGINERES: `${abprefix}/test/getengineres`,
  GETEXPFILTER: `${abprefix}/test/getexpfilter`,

  // TASKLIST: `${abprefix}/taskmanage/tasklist`,
  // TASKLIST_DETAIL: `${abprefix}/taskmanage/taskinfolist`,
  // TASKRETRY: `${abprefix}/taskmanage/retry`,

  // 可视化实验
  CHANGELIST: `${abprefix}/experiment/changelist`,
  DINGTEST: `${abprefix1}/tools/dingtest`,

  // FILTERLIST: `${abprefix}/filterconf/filterlist`, // 过滤参数列表
  // AGRVIEEW: `${abprefix}/filterconf/view`, // 查看过滤项
  // FILTERADD: `${abprefix}/filterconf/add`, // 添加过滤项
  // FILTERUPDATE: `${abprefix}/filterconf/update`, // 查看过滤项
  // SYMBOLLIST: `${abprefix}/filterconf/symbollist`, // 操作符配置列表

  // APPADD: `${abprefix}/application/add`, // 添加应用
  // APPUPDATA: `${abprefix}/application/update`, // 更新应用
  // APPVIEW: `${abprefix}/application/view` // 查看应用
  SET_STATUS: `${abprefix1}/experiment/setstatus`,
  FILTERIPLIST: `${abprefix}/filterconf/iplocation`,

  // 所在流控实验
  flowexperiment: `${abprefix}/crowd/flowexperiment`,


  // 白名单所在流控实验
  whitelistflowexperiment: `${abprefix}/whitelist/flowexperiment`,
  // 控制业务线状态
  CONTROL_APP_STATUS: `${abprefix}/application/setstatus`,
  // 业务线分组列表
  APPGROUPLIST: `${abprefix}/appgroup/list`,
  GETEXPERMISSION: `${abprefix1}/experiment/permission/view`,
  UPDATEEXPERMISSION: `${abprefix1}/experiment/permission/update`,
  GETUNAMEINFO: `${abprefix1}/auth/employeeinfo`,
  GETDEPTINFO: `${abprefix1}/auth/deptinfo`,
  LABLELIST: `${abprefix}/label/list`,
  PUSHEXP: `${abprefix1}/experiment/push`,
  CHECKAPPSTATUS: `${abprefix1}/experiment/startcheck`,
  EDITPUSHEXP: `${abprefix1}/experiment/push/edit`,
  VIEWPUSHEXP: `${abprefix1}/experiment/push/view`,
  ROLLBACKPUSHEXP: `${abprefix1}/experiment/push/rollback`,
  VIEWPUSHREPORT: `${abprefix1}/experiment/push/report`,
  // GETMESSAGELIST: `${abprefix}/message/list`,
  GETMESSAGELIST: `message-center/message/list`,
  READMESSAGE: `message-center/message/read`,
  GETPROCESSLIST: `${abprefix}/application/process/list`,
  DINGGROUPVIEW: `${abprefix}/system/dinggroup/view`,
  FAVORITE: `${abprefix1}/experiment/favorite`,
  DINGGROUPLIST: '/message-center/dinggroup/list',
  EXWHITELIST: `${abprefix1}/experiment/whitelist/view`,
  UPDATEEXWHITELIST: `${abprefix1}/experiment/whitelist/update`,
  VIEWCRON: `${abprefix1}/experiment/cron/view`,
  DELETECRON: `${abprefix1}/experiment/cron/del`,
  UPDATECRON: `${abprefix1}/experiment/cron/update`,
};
