const abprefix = '/earthworm/mis';

// 应用列表
const FEATUREAPPLIST = `${abprefix}/application/list`;
// 固化列表
const FEATURELIST = `${abprefix}/correct/list`;
// 固化列表跳转固化编辑
const FEATUREMANAGE = `${abprefix}/correct/add`;
// 版本管理
const FEATUREVERSION = `${abprefix}/correct/versionlist`;
// 固化详情
const FEATUREDETAIL = `${abprefix}/correct/view`;
// 图片上传
const FEATUREUPLOADIMG = `${abprefix}/upload/uploadimg`;
// 添加固化
const FEATUREADD = `${abprefix}/correct/add`;
//版本管理-禁用原因
const FEATUREFORBIDDEN = `${abprefix}/correct/disable`;
// 版本管理-回滚
const FEATUREROLLBACK = `${abprefix}/correct/rollback`;
// 版本管理-发布
const FEATURERELEASE = `${abprefix}/correct/release`;

// 新的固化
const NEWFEATUREADD = `/earthworm/correct/add`;
// 新固化列表
const NEWFEATURELIST = `/earthworm/correct/list`;
// 固化版本列表
const NEWVERSIONLIST = `/earthworm/correct/versionList`;
// 固化版本发布
const NEWRELEASE = `/earthworm/correct/release`;
// 固化版本回滚
const NEWROLLBACK = `/earthworm/correct/rollback`;
// 固化版本禁用
const NEWDISABLE = `/earthworm/correct/disable`;
// 固化详情
const NEWFTETUREVIEW = `/earthworm/correct/view`;
// 固化汇总接口
const CTSUMMARY = `/earthworm/mis/report/ctsummary`;


export default {
	FEATUREAPPLIST,
	FEATURELIST,
	FEATUREMANAGE,
	FEATUREVERSION,
	FEATUREDETAIL,
	FEATUREUPLOADIMG,
	FEATUREADD,
	FEATUREFORBIDDEN,
	FEATUREROLLBACK,
	FEATURERELEASE,
	NEWFEATUREADD,
	NEWFEATURELIST,
	NEWVERSIONLIST,
	NEWRELEASE,
	NEWROLLBACK,
	NEWDISABLE,
	NEWFTETUREVIEW,
	CTSUMMARY
};
