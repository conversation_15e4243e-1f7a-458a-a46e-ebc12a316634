/*
 * @Author:
 * @Date: 2020-08-07 11:25:32
 * @LastEditors: wanglijuan01
 * @LastEditTime: 2020-08-10 17:25:46
 * @Description:
 */
import Common from './common';
import ABtest from './ab-test/index';
import Feature from './feature/index'; // 固化
import Metric from './metric/index'; // 指标
import FoundReport from './ab-test/report';
import Business from './business/index'; // 业务线管理
import Filter from './filter/index'; // 过滤规则
import Task from './task/index'; // 任务管理
import Crowd from './crowd/index'; // 人群包
import Permission from './permission/index'; // 人群包
import Question from './question';
import WorkTransfer from './work-transfer/index'; // 工作转交

export default {
	...Common,
	...ABtest,
	...Feature,
	...Metric,
	...FoundReport,
	...Business,
	...Filter,
	...Task,
	...Crowd,
	...Permission,
	...Question,
	...WorkTransfer
};
