import router from './router';
import Store from '@/store';
import NProgress from 'nprogress'; // progress bar
import '@static/css/nprogress.less';

NProgress.configure({
  showSpinner: false
}); // NProgress Configuration

router.beforeEach(async (to, from, next) => {
  NProgress.start();
  const path = to.path;
  if(path === "/permission"){
    next();
    return;
  }
  const userInfo = Store.getters.userInfo;
  if(userInfo && userInfo.uname){
    next();
  }else{
    Store.commit('setAfterLoginCallBacks', next);
  }
});
router.afterEach(() => {
  NProgress.done();
});