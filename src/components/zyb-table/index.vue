<template>
  <div class="zyb_table">
    <el-table :data="tableData" size="default" :show-overflow-tooltip="showOverflowTooltip" @selection-change="handleSelectionChange">
      <slot name="selection"></slot>
      <el-table-column v-for="item in tableColumns" :key="item.prop" :label="item.label" :prop="item.prop" :show-overflow-tooltip="item.showOverflowTooltip!==undefined?item.showOverflowTooltip: showOverflowTooltip" :width="item.width || ''" :min-width="item['min-width'] || ''" :fixed="item.fixed" align="center">
        <template slot="header" v-if="item.header">
          <slot :name="item.prop"></slot>
        </template>
        <template slot-scope="scope">
          <cell-content v-if="item.custom" :title="item.title" :label="scope.row[item.prop]" :line="item.line">
            <slot name="custom" v-bind:data="{ item, scope }"></slot>
          </cell-content>
          <cell-content v-else-if="item.render" :title="item.title" :label="item.render(scope)" :line="item.line" />
          <cell-content v-else :title="item.title" :label="scope.row[item.prop]" :line="item.line" />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import CellContent from './cell-content.vue';

export default {
  components: { CellContent },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    tableColumns: {
      type: Array,
      default: () => []
    },
    showOverflowTooltip: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleSelectionChange(selection) {
      this.$emit('handleSelectionChange', selection);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.el-table th,
/deep/.el-table td {
  padding: 16px 0;
  .cell_content{
    width: 100%;
    display: inline;
  }
  .cell.el-tooltip{
    // white-space: normal;
  }
}
</style>