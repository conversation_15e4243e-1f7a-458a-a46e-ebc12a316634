<template>
  <div class="cell_content" :title="_title" :style="{ '-webkit-line-clamp': line }">
    <slot>{{ _label }}</slot>
  </div>
</template>

<script>
export default {
  props: {
    title: false,
    label: {
      type: String | Number,
      default: '-'
    },
    line: {
      type: Number,
      default: 1
    }
  },
  computed: {
    _label() {
      return this.label || this.label === 0 ? this.label : '-';
    },
    _title() {
      return this.title ? this._label : '';
    }
  }
};
</script>

<style lang="less" scoped>
.cell_content {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>