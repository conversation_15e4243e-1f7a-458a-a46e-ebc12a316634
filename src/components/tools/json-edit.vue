<template>
  <div class="edit_wrapper">
    <div class="jsoneditor-container" :class="{'max-box':max,'min-box':!max}" :style="getHeight">
      <div ref="jsoneditor" class="jsoneditor-box"></div>
    </div>
  </div>
</template>

<script>
import JSONEditor from "jsoneditor/dist/jsoneditor.min.js";
import 'jsoneditor/dist/jsoneditor.min.css';


export default {
  name: 'json-edit',
  components: {},
  props: {
    options: {
      type: Object,
      default: () => {
        return {};
      }
    },
    value: [Object, Array, Number, String, Boolean],
    height: {
      type: String
    },
    plus: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      editor: null,
      style: {},
      max: false,
      internalChange: false,
    };
  },
  computed: {
    getHeight() {
      if (this.height && !this.max) {
        return {
          height: this.height
        };
      }
      return {};
    }
  },
  mounted() {
    this.initView();
  },
  watch: {
    value: {
      handler(value) {
        if (this.editor && value !== undefined && !this.internalChange) {
          this.editor.set(value);
        }
      },
      deep: true
    },
    max(value) {
      this.$nextTick(() => {
        this.initView();
      });
    },
    options: {
      handler(value) {
        if (this.options && this.options.mode && this.editor) {
          this.editor.setMode(this.options.mode);
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    onChange() {
      let error = null;
      let json = {};
      try {
        json = this.editor.get();
      } catch (err) {
        error = err;
      }
      if (error) {
        this.$emit("error", error);
      } else {
        if (this.editor) {
          this.internalChange = true;
          this.$emit("input", json);
          this.$nextTick(() => {
            this.internalChange = false;
          });
        }
      }
      this.options.onChange && this.options.onChange(...arguments);
    },
    initView() {
      if (!this.editor) {
        var container = this.$refs.jsoneditor;
        let cacheChange = this.options.onChange;
        delete this.options.onChange;
        const options = Object.assign(this.options, {
          onChange: this.onChange
        });
        this.editor = new JSONEditor(container, options);
        this.options.onChange = cacheChange;
      }
      this.editor.set(this.value !== undefined ? this.value : {});
    },
    destroyView() {
      if (this.editor) {
        this.editor.destroy();
        this.editor = null;
      }
    }
  }
};
</script>

<style lang="less">

.pico-modal-header {
    background-color: #42c57a !important;
    border: transparent;
  }

  .jsoneditor-button-first {
    background-color: #42c57a !important;
    border: transparent;
  }

.edit_wrapper {
  .jsoneditor-transform {
    display: none;
  }

  .jsoneditor-undo {
    display: none;
  }

  .jsoneditor-redo {
    display: none;
  }

  .jsoneditor {
    border: transparent !important;
  }

  .jsoneditor-menu {
    background-color: #42c57a;
    border: transparent;
  }

  .jsoneditor-sort {
    display: none;
  }

  .jsoneditor-box {
    height: 220px;
    width: 385px;
    // height: 100%;
  }

  .jsoneditor-poweredBy {
    display: none;
  }

  .ace_gutter {
    width: 0px !important;
    display: none;
  }
}

</style>