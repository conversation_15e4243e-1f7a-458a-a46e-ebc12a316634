/*
* @Author: liquan01
* @Date: 2020-04-02
* @description: 欢迎卡片
*/

<template>
  <h1 class="user-welcome">{{realName}}老师&nbsp;<span>{{timeStr}}好～</span></h1>
</template>

<script>

export default {
  data() {
    return {

    };
  },
  computed: {
    timeStr() {
      let time = new Date();
      let hour = time.getHours();
      if (hour >= 5 && hour <= 11) {
        return '上午';
      } else if (hour >= 12 && hour <= 17) {
        return '下午';
      }
      return '晚上';
    },
    realName() {
      if (!this.$store.state.userData) {
        return '';
      }
      return this.$store.state.userData.realName;
    },
  },

  mounted() {
  }
};
</script>

<style lang="less" scoped>
h1.user-welcome {
  font-size: 24px;
  font-weight: bold;
  color: #565656;
  margin-bottom: 16px;

  span {
    font-weight: bold;
    color: #54C784;
  }
}
</style>
