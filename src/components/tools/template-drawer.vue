/*
* @Author: tanghaichuan
* @Date: 2018-07-23 16:37:20
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2018-12-08 16:20:44
* @description: 抽屉组件
*/

<template>
  <div class="drawer" @keyup.esc="closeToggle">
    <!--遮罩层-->
    <div v-if="mask" v-show="toggle" @click="closeToggle" :style="{opacity:opacity}" class="drawer-mask"></div>
    <!--抽屉展示区域-->
    <transition :name="getAniName">
      <div :class="drawerCls" v-show="toggle" :style="getStyle">
        <div class="drawer-header">
          <span class="drawer_title">{{title}}</span>
          <i @click="closeToggle" class="el-icon-close"></i>
        </div>
        <slot v-if="toggle"></slot>
      </div>
    </transition>
  </div>
</template>
<script>
export default {
  name: 'drawer',
  props: {
    value: {
      type: Boolean,
      required: true
    },
    width: {
      type: [Number, String],
      default: 100
    },
    opacity: {
      type: Number,
      default: 0.1
    },
    mask: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    position: {
      validator(value) {
        return (
          ['', 'right', 'bottom', 'left', 'top', 'overall'].indexOf(value) !==
          -1
        );
      },
      default: 'right'
    },
    drawStyle: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      toggle: this.value
    };
  },
  computed: {
    // 例如：width可为200或者'200px'。top或者bottom属性下width强制为100%
    getWidth() {
      if (this.position !== 'top' && this.position !== 'bottom') {
        if (typeof this.width === 'string' && this.width.indexOf('px') > -1) {
          return this.width;
        } else {
          return `${this.width}px`;
        }
      } else {
        return `100%`;
      }
    },
    // 弹框样式属性
    getStyle() {
      if (JSON.stringify(this.drawStyle) === '{}') {
        return {width: this.getWidth};
      } else {
        return Object.assign(this.drawStyle, {width: this.getWidth});
      }
    },
    getAniName() {
      return `slide-${this.position}`;
    },
    drawerCls() {
      return [
        'drawer-content',
        {
          'drawer-pos-right': this.position === 'right',
          'drawer-pos-bottom': this.position === 'bottom',
          'drawer-pos-left': this.position === 'left',
          'drawer-pos-top': this.position === 'top',
          'drawer-pos-overall': this.position === 'overall'
        }
      ];
    }
  },
  methods: {
    handleClick() {
      this.toggle = !this.toggle;
    },
    closeToggle() {
      this.toggle = false;
    },
    // 监听esc事件回调函数
    handleListenFunc(e) {
      if (e.keyCode === 27) {
        this.closeToggle();
      }
    },
    // 监听esc按键事件
    listenEscKeyDown() {
      if (document.addEventListener) {
        document.addEventListener('keydown', this.handleListenFunc);
      } else {
        document.attachEvent('onkeydown', this.handleListenFunc);
      }
    },
    // 清除esc按键事件
    removeEscKeyDown() {
      if (document.removeEventListener) {
        document.removeEventListener('keydown', this.handleListenFunc);
      } else {
        document.detachEvent('onkeydown', this.handleListenFunc);
      }
    }
  },
  created() {
    this.listenEscKeyDown();
  },
  destroyed() {
    this.removeEscKeyDown();
  },
  watch: {
    value(val) {
      this.toggle = val;
    },
    toggle(val) {
      this.$emit('input', val);
    }
  }
};
</script>
<style lang="less" scoped>
  @import url('../../static/css/components/drawer.less');
</style>
