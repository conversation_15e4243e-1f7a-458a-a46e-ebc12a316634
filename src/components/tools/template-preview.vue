/*
* @Author: WangLeiLei
* @Date: 2019-03-16 10:39:37
* @Last Modified by: WangLeiLei
* @Last Modified time: 2019-03-19 16:29:14
* @Description 模板消息预览组件
*/
<template>
  <div class="template-news">
    <header>
      <p class="header-title" :style="{color: newsContent.first.color}" v-if="newsContent.first">{{newsContent.first.value}}</p>
    </header>
    <section>
      <ul v-for="(item, index) in newsContent.keyword_list" :key="index">
        <li class="news-keyword-list" :style="{color: newsContent.keyword_list[index].color}">
          <span>{{item.name}}：</span>
          <span style="white-space: pre-wrap;">{{item.value}}</span>
        </li>
      </ul>
      <ul  :style="{color: newsContent.remark.color}" style="white-space: pre-wrap;">{{newsContent.remark.value}}</ul>
    </section>
    <footer>
      <span>点击查看详情</span>
      <i class="el-icon-arrow-right"></i>
    </footer>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        newDialog: false,
        today: new Date()
      };
    },
    props: {
      newsContent: {
        type: Object
      }
    },
    methods: {
      changeVisible() {
        this.newDialog = !this.newDialog;
      }
    }
  };
</script>
<style lang="less" scoped>
  .template-news {
    font-size: 12px;
    border: 1px solid @zyb-grey-2;
    border-radius: 2px;
    padding: 0 16px;
    header {
      .header-title {
        font-size: 14px;
        line-height: 18px;
        padding: 16px 0 8px 0;
        white-space: pre-wrap;
      }
    }
    section {
      border-top: 1px solid @zyb-grey-4;
      border-bottom: 1px solid @zyb-grey-4;
      padding: 8px 0;
      ul {
        line-height: 18px;
        padding: 8px 0;
        li {
          line-height: 14px;
        }
      }
    }
    footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 36px;
      color: @zyb-font-grey-1;
      i {
        font-size: 16px;
        color: @zyb-blue-grey-2;
      }
    }
  }
</style>