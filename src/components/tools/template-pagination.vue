<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-11-19 16:07:33
 * @LastEditors: zhuyue
 * @LastEditTime: 2021-08-04 16:58:26
 * @Description: 分页
-->
<template>
 <div class="page_pagination_main page_card page_card_padding">
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="paginationInit.currentPage"
      :page-size="paginationInit.pageSize"
      :page-sizes="[10, 20, 50, 100]"
      @size-change="pageSizeChange"
      layout="total,sizes, prev, pager, next, jumper"
      :total="paginationInit.total">
    </el-pagination>
  </div>
</template>
<script>
export default {
  name: 'templatePagination',
  props: {
    // 搜索选项（默认总数为0）
    paginationInit: {
      type: Object,
      default() {
        return {
          total: 0,
          page: 1,
          pageSize: 20,
          pageSizes: [10, 20, 50]
        };
      }
    }
  },
  methods: {
    // 页面改变事件（当前页）
    handleCurrent<PERSON><PERSON><PERSON>(currentPage) {
      this.$emit('changePage', currentPage);
    },
    pageSizeChange(pageSize) {
      this.$emit('pageSizeChange', pageSize);
    }
  }
};
</script>

<style lang="less" scoped>

.page_pagination_main {
  float: right;
  margin-top: 20px;
}

</style>


