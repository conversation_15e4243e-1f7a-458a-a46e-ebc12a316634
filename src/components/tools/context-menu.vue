<template>
  <div class="context_menu">
    <div class="context_menu_control" :class="{'is_scroll':isScroll}">
      <span
        class="context_menu_tabs prev"
        v-show="isScroll"
        @click="onPrev">
        <i class="el-icon-arrow-left"></i>
      </span>
      <span
        class="context_menu_tabs next"
        v-show="isScroll"
        @click="onNext">
        <i class="el-icon-arrow-right"></i>
      </span>
      <div class="context_menu_nav" id="scrollabel_wrap" ref="control">
        <div ref="scroll" id="scrollabel" class="navlist is_scrollabel" :style="setSrollOffset">
          <div
            class="navlist_item"
            v-for="(item, index) in navList"
            :key="item.name"
            :class="{'is_closable': index > 0,'is_current':isCurrentNav(item.name)}"
            @click="$router.push({path:item.path,query:{title:item.name}})">
            <span>{{item.name}}</span>
            <i v-if="index > 0" class="el-icon-close ic_close" @click.self.stop="onDelNav(index, item.name)"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="context_menu_btn">
      <el-tooltip effect="dark" content="清空全部" placement="bottom">
        <div class="sch_opts ts_icon">
          <i class="icon_delete" @click="clearAllList"></i>
        </div>
      </el-tooltip>
    </div>
  </div>
</template>
<script>
import Utils from '@common/utils';
import { uniqBy } from 'lodash';

export default {
  name: 'contextMenu',
  props: {
    // 选中状态的导航
    // 支持sync
    currentNav: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // 滑动偏移量
      offsetX: 0,
      // 容器宽度
      controlWidth: 0,
      // 滑动区域宽度
      scrollWidth: 0,
      // 滑动容器节点
      $scrollEl: null,
      // 固定容器节点
      $controlEl: null,
      // 默认选择第一个tab
      currentVal: {
        name: '首页展示',
        path: '/home'
      },
      // 默认展示导航
      defaultList: [{
        name: '首页展示',
        path: '/home'
      }],
      // 动态导航列表
      list: []
    };
  },
  computed: {
    setSrollOffset() {
      return {
        transform: `translateX(${this.offsetX}px)`
      };
    },
    // 是否需要滑动
    isScroll() {
      return this.scrollWidth > this.controlWidth;
    },
    // 滑动偏移量
    getOffsetX() {
      return this.isScroll ? (parseInt(this.scrollWidth) - parseInt(this.controlWidth)) : 0;
    },
    // 合并默认导航和动态导航
    navList() {
      let arr = [...this.defaultList, ...this.list];
      return uniqBy(arr, 'name');
    }
  },
  methods: {
    // 判断是否为当前选择的菜单
    isCurrentNav(name) {
      return this.currentVal.name === name;
    },
    onPrev() {
      this.offsetX = 0;
    },
    onNext() {
      this.offsetX = -(this.getOffsetX + 50);
    },
    clearAllList() {
      // 清空全部是默认跳首页
      this.list = [];
      // 清空导航信息
      Utils.localStorage.removeItem('page');
      this.$router.push('/home');
    },
    // 删除一条导航
    onDelNav(index, name) {
      let isCurrent = this.isCurrentNav(name);
      let inx = index - this.defaultList.length;
      let nav = this.list.splice(inx + 1, 1)[0];
      this.delStorage(nav);
      // 如果删除的是当前导航，跳转路由
      if (isCurrent) {
        this.$nextTick(() => {
          let len = this.navList.length - 1;
          this.$router.push(this.navList[len].path);
        });
      }
      this.init();
    },
    // 当前导航列表中是否已存在该path
    hasNavItem(list, name = '') {
      return list ? list.filter(item => item.name === name).length > 0 : false;
    },
    // 删除本地存储的导航信息
    delStorage(nav) {
      let pageList = Utils.localStorage.getItem('page');
      pageList = pageList ? JSON.parse(pageList) : [];
      if (this.hasNavItem(pageList, nav.name)) {
        pageList = pageList.filter(item => item.name !== nav.name);
        Utils.localStorage.setItem('page', JSON.stringify(pageList));
      }
    },
    // 更新本地导航信息
    updateStorage(nav) {
      let pageList = Utils.localStorage.getItem('page');
      pageList = pageList ? JSON.parse(pageList) : [];
      if (!this.hasNavItem(pageList, nav.name)) {
        pageList.push(nav);
        // 持久化导航信息
        Utils.localStorage.setItem('page', JSON.stringify(pageList));
      }
    },
    linkToActiveNav(nav) {
      this.$router.push({path: nav.path,
        query: {
          title: nav.name
        }});
    },
    // 初始化dom节点
    // 初始化容器宽度
    init() {
      this.$nextTick(() => {
        this.$scrollEl = this.$refs.scroll;
        this.$controlEl = this.$refs.control;
        this.controlWidth = this.$controlEl.offsetWidth;
        this.scrollWidth = this.$scrollEl.offsetWidth;
      });
    }
  },
  mounted() {
    const pageList = Utils.localStorage.getItem('page');
    console.log(pageList);
    this.list = [].concat(JSON.parse(pageList));

    this.init();
    window.onresize = () => {
      return (() => {
        this.controlWidth = this.$controlEl.offsetWidth;
        this.scrollWidth = this.$scrollEl.offsetWidth;
      })();
    };
  },
  watch: {
    isScroll(val) {
      if (!val) {
        this.offsetX = 0;
      }
    },
    $route: {
      handler(val) {
        if (val.name && !val.meta.isHide) {
          if (!this.hasNavItem(this.navList, val.query.title)) {
            let nav = {name: val.query.title || val.name, path: val.path};
            this.list.push(nav);
            this.list = uniqBy(this.list, 'name');
            this.updateStorage(nav);
          }
          // 路由变更时更新nav选中状态
          this.currentVal = {name: val.query.title || val.name, path: val.path};
          // Utils.localStorage.setItem('currentVal', JSON.stringify(this.currentVal));
          this.$nextTick(() => {
            this.init();
          });
        }
      },
      immediate: true
    },
    currentVal(val) {
      this.$emit('update:currentNav', val);
    },
    currentNav(val) {
      this.currentVal = val;
    }
  }
};
</script>
<style lang="less" scoped>
  @import url('../../static/css/components/context-menu.less');
</style>
