/*
* @Author: tanghaichuan
* @Date: 2018-07-23 16:37:56
 * @Last Modified by: tanghaichuan
 * @Last Modified time: 2018-08-01 15:17:31
* @description: 自定义渲染表格组件
*/

<script>
import { isEmpty, isArray } from 'lodash';
export default {
  name: 'tableBodyRender',
  props: {
    render: Function,
    index: [String, Number],
    row: Object
  },
  render(h) {
    // 自定义render
    // 只能返回一个Vnode节点
    // h:createElement
    // index:表格当前行下标
    // row: 表格当前行数据
    // 例如 customRender(h, {index, row}){}
    let Vnodes = this.render(h, { index: this.index, row: this.row });
    if (!isEmpty(Vnodes)) {
      return isArray(Vnodes) ? Vnodes.pop() : Vnodes;
    }
    return '';
  }
};
</script>
