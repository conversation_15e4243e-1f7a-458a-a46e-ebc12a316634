/*
* @Author: tanghaichuan
* @Date: 2018-08-06 15:18:19
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2018-12-09 21:58:42
* @Description: 垂直动态表单组件
*   renderForm 传入为数组对象，对象属性为渲染的每个表单的配置
*     label 名称 必填
*     value 默认值 必填
*     type: 表单类型 必填
*     required 是否必填 选填
*     pattern 正则表达式 选填
*     isHide 当前表单项显示隐藏 选填
*     errorMsg 设置pattern时的错误提示 选填
*     valueFormat type为date相关类型时value的格式 选填
*     注：所有的异步操作或者dom操作应该在原渲染模型中进行
*/
<template>
  <div class="vertical_form">
    <h1 class="page_title" v-if="title">{{title}}</h1>
    <el-form
        :class="[inline ? 'transverse_form' : '', 'vertical_form_content']"
        :model="form"
        ref="form"
        :disabled="formDisabled"
        :label-width="labelWidth">
      <div
          class="vertical_form_wrapper"
          v-for="(item, key) in renderForm"
          :class="formWrapperCls(item, item.name, renderForm)"
          :key="item.name"
          @click="handleItemClick($event, item, key)">
        <el-form-item
          v-if="!item.isHide"
          :label="item.label || ''"
          :prop="item.name || key"
          :rules="customRules(item)">
          <!-- 展示数据 -->
          <label
              class="form_item_text"
              v-if="item.type==='label'"
              v-html="form[item.name]">
            {{item.value}}
          </label>
          <!-- input输入框 -->
          <el-input
              v-if="item.type === 'text'"
              v-model.trim="form[item.name]"
              :maxlength="item.maxlength"
              :class="item.className"
              :placeholder="item.placeholder ? item.placeholder : '请输入'"
              @blur="item.blur ? hanleBlur(item,form[item.name]):''"
              :disabled="item.disabled"
              :clearable="item.clearable">
            <template slot="append" v-if="item.mailSuffix">{{item.mailSuffix}}</template>
          </el-input>
          <!-- textarea输入框 -->
          <el-input
              v-if="item.type === 'textarea'"
              type="textarea"
              resize="none"
              :disabled="item.disabled"
              :maxlength="item.maxlength"
              :placeholder="item.placeholder ? item.placeholder : '请输入内容'"
              v-model.trim="form[item.name]">
          </el-input>
          <!-- select选择器 -->
          <el-select
              v-if="item.type === 'select'"
              v-model="form[item.name]"
              size="mini"
              clearable
              :disabled="item.disabled"
              :placeholder="item.placeholder ? item.placeholder : '请选择'"
              @change="handleSelectChange(...arguments, item.name)">
            <el-option
                v-for="ops in item.options"
                :label="ops.label"
                :value="ops.value"
                :key="ops.value">
            </el-option>
          </el-select>
          <!-- 远程select选择器 -->
          <el-select
              v-if="item.type === 'remote'"
              v-model="form[item.name]"
              clearable
              filterable
              remote
              :disabled="item.disabled"
              :placeholder="item.placeholder ? item.placeholder : '请输入关键字'"
              :remote-method="remoteMethod">
            <el-option
                v-for="ops in item.options"
                :label="ops.label"
                :value="ops.value"
                :key="ops.value">
            </el-option>
          </el-select>
          <!-- 日期时间选择器 -->
          <el-date-picker
              v-if="item.type === 'datetimerange'"
              v-model="form[item.name]"
              type="datetimerange"
              :disabled="item.disabled"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="item.pickerOptions || {}"
              :value-format="item.valueFormat||'timestamp'">
          </el-date-picker>
          <!-- 日期选择器 -->
          <el-date-picker
              class="vertical_form_date_input"
              v-if="item.type === 'date'"
              v-model="form[item.name]"
              type="date"
              :disabled="item.disabled"
              :picker-options="item.pickerOptions || {}"
              :value-format="item.valueFormat||'timestamp'"
              placeholder="选择日期">
          </el-date-picker>
          <!-- 日期范围选择器 -->
          <el-date-picker
              v-if="item.type === 'daterange'"
              v-model="form[item.name]"
              type="daterange"
              :disabled="item.disabled"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="item.pickerOptions || {}"
              :value-format="item.valueFormat||'timestamp'"
              :default-time="['00:00:00', '23:59:59']">
          </el-date-picker>
          <!-- 日期时间选择器 -->
          <el-date-picker
              v-if="item.type === 'datetime'"
              v-model="form[item.name]"
              type="datetime"
              :class="item.className"
              :disabled="item.disabled"
              :placeholder="item.placeholder ? item.placeholder : '开始日期'"
              :picker-options="item.pickerOptions || {}"
              :value-format="item.valueFormat||'timestamp'">
          </el-date-picker>
          <!-- 时间选择器 -->
          <el-time-picker
              v-if="item.type === 'timerange'"
              v-model="form[item.name]"
              :is-range="item.noRange ? false : true"
              :disabled="item.disabled"
              :format="item.format"
              :value-format="item.valueFormat||'timestamp'"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时间范围">
          </el-time-picker>
          <!-- 多选框 -->
          <el-checkbox
              :true-label="item.trueLabel"
              :false-label="item.falseLabel"
              :disabled="item.disabled"
              @change="handleCheckChange(...arguments, item.name)"
              v-if="item.type==='checkbox'" v-model="form[item.name]">
            {{item.text}}
            <span class="vertical_form_content_desc" v-if="item.desc">{{item.desc}}</span>
          </el-checkbox>
          <!-- 多选框组 -->
          <el-checkbox-group
              v-if="item.type==='checkboxGroup'"
              :disabled="item.disabled"
              @change="checkboxChange(...arguments, item.name)"
              v-model="form[item.name]">
            <el-checkbox
                v-for="(checkboxList, index) in item.options"
                :key="index"
                :disabled="checkboxList.disabled"
                :label="checkboxList.value">{{checkboxList.label}}
            </el-checkbox>
          </el-checkbox-group>
          <el-radio-group v-model="form[item.name]" v-if="item.type==='radioGroup'" @change="radioGroupChange(...arguments, item.name)">
            <el-radio
                v-for="(radioList, index) in item.options"
                :key="index"
                :label="radioList.value"
                :disabled="radioList.disabled">{{radioList.label}}
            </el-radio>
          </el-radio-group>
          <!-- 级联选框 -->
          <el-cascader
            v-if="item.type==='cascader'"
            v-model="form[item.name]"
            :disabled="item.disabled"
            :options="item.options"
            change-on-select
          ></el-cascader>
          <!-- 右侧浮动的input输入框 -->
          <template v-if="item.type === 'textRight'">
            <el-input
                class="select_input_right"
                v-model.trim="form[item.name]"
                placeholder="请输入"
                :disabled="item.disabled">
            </el-input>
            {{item.text}}
          </template>
          <slot :item="item"></slot>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>
<script>
  import { isArray, isEmpty, forEach, debounce, map, cloneDeep } from 'lodash';
  import { isUndef } from '@common/utils';

  export default {
    name: 'verticalForm',
    props: {
      value: {
        type: [Object, Array],
        default: () => {}
      },
      title: {
        type: String,
        default: ''
      },
      formRenderData: {
        type: [Object, Array],
        default: () => {}
      },
      formDisabled: {
        type: Boolean,
        default: false
      },
      labelWidth: {
        type: String,
        default: '112px'
      },
      inline: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        gradeLabel: '',
        form: {},
        remoteOption: [], // 远程查询的结果值
        renderForm: []
      };
    },
    methods: {
      hanleBlur(item, name) {
        this.$emit(`${item.name}Blur`, item, name);
      },
      handleItemClick(e, item, key) {
        this.$emit('onItemClick', e, item, key);
      },
      // 将map对象转换为带唯一name的数组
      mapRenderFormToArr(form) {
        return map(form, (data, key) => {
          let item = cloneDeep(data);
          return Object.assign(item, {name: key});
        });
      },
      // 后一个表单元素未设置label或者设置type为textRight
      // 将当前元素设置为行内块级元素
      formWrapperCls(item, name, forms) {
        const index = forms.findIndex(value => value.name === name);
        const afterIndex = parseInt(index) + 1;
        return {
          form_item_textarea: item.type === 'textarea',
          form_item_float: afterIndex > 0 && afterIndex < forms.length && (forms[afterIndex].type === 'textRight' || !forms[afterIndex].label || !forms[index].label)
        };
      },
      // 公用校验规则
      customRules(item) {
        let msg = item.label;
        if (msg && msg.indexOf('：') !== -1) {
          msg = msg.split('：')[0];
        } else if (msg && msg.indexOf(':') !== -1) {
          msg = msg.split(':')[0];
        }
        let baseRule = {
          required: item.required || false,
          message: `${item.type === 'select' ? '请选择' : '请输入'}${msg}`,
          trigger: `${item.type === 'text' ? 'blur' : 'change'}`
        };
        let patternRule = {
          pattern: item.pattern || true,
          message: item.errorMsg || '格式非法',
          trigger: ['change', 'blur']
        };
        return item.pattern ? [baseRule, patternRule] : [baseRule];
      },
      // 选择框改变时回调函数
      // 当前改变值
      // 对应表单项字段 如：'grade'、'subject'
      handleSelectChange(val, key) {
        this.$nextTick(() => {
          this.$emit('onChange', key, val, this.form);
        });
      },
      // checkbox值发生改变时回调函数
      handleCheckChange(val, e, key) {
        this.$nextTick(() => {
          this.$emit('onChange', key, val, this.form);
        });
      },
      // checkbox按钮组点击事件
      checkboxChange(val, key) {
        this.$nextTick(() => {
          this.$emit('checkboxGroupChange', key, val);
        });
      },
      // radioGroup 发生改变时触发的函数
      radioGroupChange(val, key) {
        this.$nextTick(() => {
          this.$emit('radioGroupChange', key, val);
        });
      },
      // 根据name获取数组的某一项
      getItemFromArray(list, name) {
        let item = {};
        forEach(list, (form, index) => {
          if ((form.name && form.name === name) || index === name) {
            item = form;
          }
        });
        return item;
      },
      // 初始化动态表单
      initFormValue() {
        let obj = {};
        if (!isEmpty(this.renderForm)) {
          forEach(this.renderForm, (item, key) => {
            obj[item.name] = item.value;
          });
        }
        this.form = Object.assign({}, this.form, obj);
      },
      // 动态添加表单
      append(form) {
        if (form) {
          this.renderForm.push(form);
          this.form[form.name] = form.value;
        } else {
          forEach(this.renderForm, item => {
            // 如果本地数据项不存在该项，则赋值
            if (isUndef(this.form[item.name])) {
              this.$set(this.form, item.name, item.value);
              this.$nextTick(() => {
                this.clearValidate(item.name);
              });
            }
          });
        }
      },
      // 动态删除表单
      remove(index) {
        if (index) {
          let item = this.renderForm[index];
          this.$delete(this.form, item.name);
        } else {
          forEach(this.form, (item, key) => {
            if (this.renderForm.findIndex(form => form.name === key) < 0) {
              this.$delete(this.form, key);
            }
          });
        }
      },
      // 重置表单对象值
      // name为表单对应的prop
      // 可为数组或者单个值，空值重置整个表单
      resetForm(name = 'resetFormAll') {
        if (name && isArray(name)) {
          name.forEach(item => {
            if (item && this.form[item]) {
              this.$set(this.form, item, this.getItemFromArray(this.formRenderData, name).value);
            }
          });
        } else if (name && this.form[name]) {
          this.$set(this.form, name, this.getItemFromArray(this.formRenderData, name).value);
        } else if (name === 'resetFormAll') {
          this.$refs.form.resetFields();
        }
      },
      // 验证表单
      validateForm(prop = '') {
        let isValid = false;
        if (prop && !Array.isArray(prop)) {
          this.$refs.form.validateField(prop, valid => {
            isValid = !valid;
          });
        } else {
          this.$refs.form.validate(valid => {
            isValid = valid;
          });
        }
        return isValid;
      },
      // 清楚校验结果
      clearValidate(props) {
        return this.$refs.form.clearValidate(props);
      },
      // 远程查询所得值
      remoteMethod: debounce(function (query) {
        this.$emit('queryRemote', query);
      }, 200)
    },
    created() {
      // 初始化form内容
      this.initFormValue();
    },
    watch: {
      value: {
        handler(val) {
          this.form = val;
        },
        deep: true
      },
      form: {
        handler(val) {
          this.$emit('input', val);
        },
        deep: true
      },
      formRenderData: {
        handler(val) {
          this.renderForm = Array.isArray(val) ? val : this.mapRenderFormToArr(val);
        },
        immediate: true,
        deep: true
      },
      'renderForm.length'(newVal, oldVal) {
        // 判断表单是否新增或者删除
        newVal > oldVal ? this.append() : this.remove();
      }
    }
  };
</script>
<style lang="less" scoped>
  .vertical_form {
    /deep/ .el-form {
      position: relative;
      /deep/ .el-input {
        width: 181px;
        input {
          width: 181px;
        }
      }
      /deep/ .select_input_right {
        input {
          width: 100%;
        }
      }
      /deep/ .el-date-editor--datetime.el-input--prefix .el-input__inner {
        padding-right: 20px;
      }
    }
    &_wrapper {
      margin-bottom: 20px;
      height: auto;
    }
    &_content {
      width: 710px;
      /deep/ .el-form-item {
        height: auto;
        margin-bottom: 0;
        &__content {
          height: auto !important;
          .vertical_form_date_input {
            padding: 0 !important;
          }
          /deep/ .el-input__prefix {
            left: 0;
          }
        }
        &__label {
          padding: 0 16px 0 0;
        }
      }
      &_desc {
        margin-left: 10px;
        font-size: 12px;
        color: #969799;
      }
    }
    .page_form {
      width: 550px;
    }
    .form_item_text {
      display: block;
      width: 200px;
    }
    .form_item_textarea {
      height: auto;
      /deep/ .el-form-item__content {
        height: auto;
      }
    }
    .form_item_float {
      display: inline-block;
      /deep/ .el-form-item__content {
        display: inline-block;
        margin-left: 0 !important;
      }
      & + .vertical_form_wrapper {
        display: inline-block;
        .el-form-item__content {
          margin-left: 10px !important;
        }
      }
      .select_input_right {
        width: 100px;
      }
    }
    /deep/ .el-checkbox {
      margin-left: 0;
      margin-right: 30px;
    }
    /deep/ .el-radio {
      margin-left: 0;
      margin-right: 30px;
      line-height: 30px;
    }
    .transverse_form {
      width: 100%;
      .vertical_form_wrapper {
        display: inline-block;
      }
    }
    /deep/ .el-cascader .el-icon-arrow-down {
      background: none;
    }
    /deep/ .el-form-item__error {
      padding-top: 0;
    }
  }
</style>
