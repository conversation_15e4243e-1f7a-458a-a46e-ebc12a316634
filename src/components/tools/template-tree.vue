/*
* @Author: tanghaichuan
* @Date: 2018-07-23 16:38:51
* @Last Modified by: tanghaichuan
* @Last Modified time: 2018-07-23 16:38:51
* @description: tree组件
*/

<template>
  <div class="tree-main">
    <el-tree
      show-checkbox
      ref="tree"
      highlight-current
      :data="data"
      :node-key="nodeKey"
      :default-expand-al="defaultExpand"
      :default-expanded-keys="defaultExpandedKeys"
      :default-checked-keys="defaultCheckedKeys"
      :empty-text="emptyText"
      :props="props"
      @node-click="handleNodeClick"
      @check-change="handleNodechange"
      @check="handleNodecheck"
      >
    </el-tree>
  </div>
</template>
<script>
import utils from '@common/utils';

export default {
  name: 'templateTree',
  data() {
    return {
      defaultExpand: this.expend === 'all',
      defaultExpandedKeys: [],
      defaultCheckedKeys: []
    };
  },
  props: {
    data: {
      type: [Object, Array],
      default: () => {},
      required: true
    },
    // 节点属性别名
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label'
        };
      }
    },
    // 设置节点展开属性 all 或者 key数组
    expend: {
      type: [String, Array],
      default: 'all'
    },
    // 设置节点勾选属性 key数组
    checked: {
      type: Array,
      default: () => []
    },
    // 数据为空时文本
    emptyText: {
      type: String,
      default: '空数据'
    },
    // 节点唯一标识
    nodeKey: {
      type: String,
      default: 'id'
    }
  },
  methods: {
    /**
     * 节点被点击时的回调
     * @param obj   data属性中对应节点对象
     * @param node  node节点对象
     * @param tree  tree
     */
    handleNodeClick(obj, node, tree) {
      this.$emit('node-click', obj, node, tree);
    },

    /**
     * 节点勾选状态发生改变时回调函数
     * @param obj   data属性中对应节点对象
     * @param node  node节点对象
     * @param tree  tree
     */
    handleNodechange(obj, node, tree) {
      this.$emit('check-change', obj, node, tree);
    },

    /**
     * 节点被勾选时回调函数
     * @param obj       data属性中对应节点对象
     * @param checkObj  树目前选中状态
     */
    handleNodecheck(obj, checkObj) {
      this.$emit('check', obj, checkObj);
    },

    /**
     * 获取勾选节点nodes
     * 通过this.$refs.tree或者$emit调用该方法
     *
     * @returns {array} 节点数组
     */
    getCheckedNodes() {
      let nodes = this.$refs.tree.getCheckedNodes();
      return nodes;
    },

    /**
     * 获取勾选节点keys
     * 通过this.$refs.tree或者$emit调用该方法
     *
     * @returns {array} 节点key数组
     */
    getCheckedKeys() {
      let nodes = this.$refs.tree.getCheckedKeys();
      return nodes;
    }
  },
  watch: {
    expend: {
      handler(val) {
        if (val && utils.isArray(val)) {
          this.defaultExpandedKeys = [...val];
        }
      },
      immediate: true
    },
    checked: {
      // 设置checked属性响应式更新tree组件
      handler(val) {
        if (val && utils.isArray(val)) {
          this.defaultCheckedKeys = [...val];
          if (this.$refs.tree) {
            this.$nextTick(() => {
              this.$refs.tree.setCheckedKeys([...val]);
            });
          }
        }
      },
      immediate: true
    }
  }
};
</script>
