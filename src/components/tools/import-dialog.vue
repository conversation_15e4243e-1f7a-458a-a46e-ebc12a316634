/*
* @Author: tanghaichuan
* @Date: 2018-07-24 13:19:38
 * @Last Modified by: tanghaichuan
 * @Last Modified time: 2018-08-14 16:25:08
* @description: 导入组件
*/

<template>
  <el-dialog
    class="dialog-style"
    :title="title"
    :visible.sync="dialogVisible">
    <div class="upload-area">
      <el-upload
        class="upload-demo"
        ref="upload"
        :action="verify"
        :data="params"
        :limit=1
        :before-upload="beforeUpload"
        :on-change="uploadChange"
        :on-success="uploadSuccess"
        :auto-upload="false">
        <el-button
          class="choose-btn"
          slot="trigger"
          size="mini"
          type="primary">
          <i class="el-icon-upload2 el-icon--left"></i>上传文件
        </el-button>
        <span
          slot="tip"
          class="el-upload__tip">
          还没有模板？点击
          <a
            :href="model"
            class="download-btn"
            target="_self">下载导入模板</a>
        </span>
      </el-upload>
    </div>
    <div
      class="dialog-footer"
      slot="footer">
      <el-button
        @click="dialogClose"
        size="mini">
        取消
      </el-button>
      <el-button
        @click="verifyFile"
        size="mini"
        type="success">
        校验
      </el-button>
      <el-button
        :disabled="disabled"
        @click="importFile"
        size="mini"
        type="success">
        导入
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import ReportApi from '@api/report';
import Message from '@common/message';
import Axios from 'axios';
import {isEmpty, forEach} from 'lodash';

const ORIGIN = window.location.origin;

export default {
  data() {
    return {
      dialogVisible: this.visible,
      disabled: true,
      file: '',
      model: `${ORIGIN}/${this.modelUrl}`, // 下载模板接口
      verify: `${ORIGIN}/${this.verifyUrl}` // 校验及导入接口
    };
  },

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 标题
    title: {
      type: String,
      default: '导入'
    },
    // 下载模板接口
    modelUrl: {
      type: String,
      default: ''
    },
    // 校验及导入接口
    verifyUrl: {
      type: String,
      default: ''
    },
    // 导入时需要上传的参数
    params: {
      type: Object,
      default: () => {}
    }
  },

  methods: {
    // 上传拦截钩子函数
    beforeUpload(file) {
      const regx = new RegExp('(.xls|.xlsx)$');
      let { name = '' } = file;
      if (regx.test(name)) {
        return true;
      }
      Message.warning('上传文件只能是 xls、xlsx格式!');
      return false;
    },
    dialogClose() {
      this.dialogVisible = false;
      this.$emit('hidden-import');
      this.$refs.upload.clearFiles();
    },
    // 上传的文件改变时存储新文件
    uploadChange(file) {
      this.file = file.raw;
    },
    // 上传
    uploadSuccess(res, file) {
      if (res.errNo === 0) {
        if (res.data.url) {
          Message.error('校验失败！详细信息请查看下载文件！');
          let link = document.createElement('a');
          link.href = `${window.location.origin}/${res.data.url}`;
          link.download = '错误信息';
          link.click();
          this.disabled = true;
        } else {
          Message.success('校验通过！可点击导入上传信息！');
          this.disabled = false;
        }
      } else {
        Message.error(res.errStr);
        return false;
      }
    },
    // 校验文件
    verifyFile() {
      this.$refs.upload.submit();
    },
    // 导入
    importFile() {
      let data = new FormData();
      data.append('file', this.file);
      // 插入传入的参数
      if (!isEmpty(this.params)) {
        forEach(this.params, (item, index) => {
          data.append(index, item);
        });
      }

      Axios.request({
        url: `${ORIGIN}/${ReportApi.courserelationimport}`,
        method: 'POST',
        headers: {
          'content-type': 'multipart/form-data'
        },
        data,
        withCredentials: true
      }).then(res => {
        let { data } = res;
        if (data.errNo === 0) {
          Message.success('导入成功');
          this.dialogVisible = false;
          this.$emit('hidden-import');
          this.$refs.upload.clearFiles();
          setTimeout(() => {
            window.location.reload();
          }, 500);
        } else {
          Message.error(data.errStr);
        }
      }).catch((error) => {
        Message.error(error);
      });
    }
  },

  watch: {
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
    visible(val) {
      this.dialogVisible = val;
    }
  }
};
</script>

<style lang="less" scoped>
.upload-area {
  width: calc(~'100% - 32px');
  height: 48px;
  background-color: #f7f8fb;
  padding: 10px 0 10px 16px;
  box-sizing: border-box;
  border-radius: 2px;
  margin: 0 0 0 16px;
}
.choose-btn {
  background-color: #42c57a;
  border-color: #42c57a;
  height: 28px;
  width: 96px;
  &:visited {
    background-color: #42c57a;
    border-color: #42c57a;
  }
  &:hover {
    background-color: #42c57a;
    border-color: #42c57a;
  }
  &:active {
    background-color: #42c57a;
    border-color: #42c57a;
  }
  &:focus {
    background-color: #42c57a;
    border-color: #42c57a;
  }
}
.choose-btn {
  margin-right: 16px;
}
.download-btn {
  color: #409eff;
}
.upload-demo /deep/ .el-upload {
  height: auto;
  width: auto;
  line-height: 0;
  border: 0;
  .el-icon--left {
    color: #fff;
    font-size: 12px;
  }
}
.dialog-footer {
  margin: 66px 16px 0px 0;
  padding-bottom: 16px;
}
</style>
