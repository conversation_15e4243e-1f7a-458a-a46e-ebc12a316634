<template>
  <div>
    <!--class="page_table_main page_table page_card"-->
    <el-table :ref="tableRef" :max-height="maxHeight" :data="tableList" v-if="tableColumn.length" :height="height" :row-class-name="rowClassName" @sort-change="handleSortChange" @selection-change="handleSelectionChange" size="small">
      <!-- empty -->
      <span slot="empty" v-if="emptyText" class="empty">
        {{ emptyText }}
      </span>
      <!-- checkbox列 -->
      <el-table-column v-if="selection" type="selection" fixed="left" width="55" :selectable="selectable">
      </el-table-column>
      <!-- 列数 -->
      <el-table-column v-if="hasIndex" type="index" label="序号" width="60">
      </el-table-column>
      <!--正常列-->
      <el-table-column v-for="item in tableColumn" v-if="!item.render" :show-overflow-tooltip="item.showOverflowTooltip !== undefined
      ? item.showOverflowTooltip
      : true
      " :sortable="item.sortable === undefined ? false : item.sortable" :fixed="item.fixed" :align="item.align || 'center'" :key="item.name" :prop="item.name" :label="item.label" :render-header="item.renderHeader" :width="item.width">
        <template slot-scope="{row}">
          <template v-if="item.type === 'customData'">
            {{ item.handleCustomData(row[item.name]) }}
          </template>
          <template v-else-if="item.type === 'html'">
            <div v-html="item.handleCustomData(row[item.name])"></div>
          </template>
          <template v-else-if="item.type === 'customEvent'">
            <el-button type="text-primary" @click="item.handleCustomEvent(row[item.name], row)">{{ row[item.name] }}</el-button>
          </template>
          <template v-else-if="item.type === 'cusDataEvent'">
            <el-button type="text-primary" @click="item.handleCustomEvent(row[item.name], row)">
              {{ item.handleCustomData(row[item.name]) }}</el-button>
          </template>
          <div v-else>{{ row[item.name] }}</div>
        </template>
      </el-table-column>
      <!--自定义渲染-->
      <el-table-column v-else :prop="item.name" :label="item.label" :render-header="item.renderHeader" :fixed="item.fixed" :align="item.align || 'center'" :width="item.width">
        <template slot-scope="scope">
          <table-body-render :index="scope.$index" :row="scope.row" :render="item.render"></table-body-render>
        </template>
      </el-table-column>
      <!--操作列-->
      <el-table-column v-if="hasButton" class="table_operation" prop="operation" label="操作" fixed="right" :width="operationBtnWidth">
        <template slot-scope="scope">
          <span v-for="(bItem, bIndex) in getButtonArr(scope.$index, scope.row)" :key="bItem.label">
            <el-button :class="bItem.className" :role="bItem.role" :disabled="bItem.disabled ? bItem.disabled : handleDisbledBtn(bItem.disabledHandler, scope.$index, scope.row, bItem.role)" size="medium" type="text" v-if="handleFunc(
      bItem.showHandler,
      scope.$index,
      scope.row,
      bItem.role
    )
      " @click="operationEvent(scope.$index, scope.row, bItem.role)" :icon="bItem.icon">{{ bItem.label }}
            </el-button>
            <i class="split" v-if="bIndex !== getButtonArr(scope.$index, scope.row).length - 1"></i>
          </span>
          <el-dropdown class="btn_dropdown" trigger="click" v-show="getDropdownArr(scope.$index, scope.row).length">
            <!-- <i class="split"></i> -->
            <el-button type="text" class="btn_more">
              <i class="split"></i>
              更多
              <i class="el-icon-arrow-down  el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in getDropdownArr(scope.$index, scope.row)" :key="item.role" :role="item.role" @click.native="
      operationEvent(scope.$index, scope.row, item.role)
      ">
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import TableBodyRender from './table-body-render';
import { isEmpty } from 'lodash';

export default {
  name: 'templateTable',
  components: {
    TableBodyRender
  },
  props: {
    tableRef: {
      type: String
    },
    tableColumn: {
      type: Array
    },
    tableList: {
      type: Array
    },
    tableBtns: {
      type: Array
    },
    columnWidth: {
      type: Number
    },
    operationBtnWidth: {
      type: Number,
      default: 180
    },
    selection: {
      type: Boolean
    },
    selectable: {
      type: Function
    },
    hasIndex: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    rowClassName: Function,
    height: Number,
    emptyText: String
  },
  // 计算下是否有操作按钮列
  computed: {
    hasButton() {
      return this.tableBtns && this.tableBtns.length;
    }
  },
  data() {
    return {
      maxHeight: 1800
    };
  },
  methods: {
    handleDisbledBtn(funcT, index, row, role) {
      if (funcT) {
        return funcT(index, row, role);
      }
      return false;
    },
    handleFunc(funcT, index, row, role) {
      if (funcT) {
        return funcT(index, row, role);
      }
      return true;
    },
    /**
     * 行数据中按钮计算个数，超过3个时候，显示两个按钮，其余在下拉中展示
     * @param arr 按钮数组
     */
    getDropdownArr(index, row) {
      let arr = [];
      if (this.hasButton) {
        let btns = this.filterFalseBtns(index, row);
        let length = btns.length;
        // arr = length <= 3 ? [] : btns.slice(2, length);
        arr = length <= 4 ? [] : btns.slice(2, length);
      }
      return arr;
    },
    getButtonArr(index, row) {
      let arr = [];
      if (this.hasButton) {
        let btns = this.filterFalseBtns(index, row);
        let length = btns.length;
        // arr = length <= 3 ? btns : btns.slice(0, 2);
        arr = length <= 4 ? btns : btns.slice(0, 2);
      }
      return arr;
    },
    /**
     * 当数据中有操作按钮时，按钮的单击事件
     * @param arr 按钮数组
     */
    filterFalseBtns(index, row) {
      let arr = [];
      if (!isEmpty(this.tableBtns)) {
        arr = this.tableBtns.filter(item => {
          // 过滤掉showHandler值为false的按钮
          let tag = typeof item.showHandler;
          switch (tag) {
            case 'function':
              return item.showHandler(index, row, row.role);
            case 'boolean':
              return item.showHandler;
            case 'undefined':
              return true;
            default:
              return true;
          }
        });
      }
      return arr;
    },
    /**
     * 当数据中有操作按钮时，按钮的单击事件
     * @param index 操作行index
     * @param row   操作行数据
     * @param role  按钮角色
     */
    operationEvent(index, row, role) {
      this.$emit('operationEvent', row, role, index);
    },
    /**
     * table的排序改变触发事件
     * @param {Object} column 选中的行信息以及排序信息
     */
    handleSortChange(column) {
      this.$emit('getSortedRow', column);
    },
    /**
     * table的左侧checkbox改变触发事件
     * @param {Array} rowList 选中的行所成的数组
     */
    handleSelectionChange(rowList) {
      this.$emit('getCheckedRow', rowList);
    }
  },
  mounted() {
    // 大屏增大表格固定高度
    // this.$nextTick(() => {
    //   let windowWidth = window.screen.width;
    //   this.maxHeight = windowWidth > 1400 ? 600 : 375;
    // });
  }
};
</script>
<style lang="less" scoped>
.split {
  display: inline-block;
  width: 1px;
  height: 10px;
  margin: 0 10px;
  vertical-align: middle;
  background-color: @zyb-grey-2;
}

.empty {
  color: lighten(red, 16);
  font-size: 16px;
}

/deep/ .el-button.is-disabled {
  background-color: transparent !important;
}

.btn_more {
  color: #444 !important;
}

/*.page_table_main {*/
/*.el-button {*/
/*min-width: 0;*/
/*margin: 0;*/
/*padding: 0 5px;*/
/*}*/
/*.btn_dropdown {*/
/*float: right;*/
/*line-height: 28px;*/
/*}*/
/*.btn_more {*/
/*span {*/
/*color: #444444 !important;*/
/*}*/
/*.el-icon-arrow-down {*/
/*background: none;*/
/*}*/
/*}*/
/*}*/
/*.table_operation {*/
/**/
/*}*/
</style>
