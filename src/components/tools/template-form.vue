/*
* @Author: tanghaichuan
* @Date: 2018-07-23 16:37:35
 * @Last Modified by: tanghaichuan
 * @Last Modified time: 2018-10-12 14:52:32
* @description: 关联表单组件
*/

<template>
  <div class="dynamic-form">
    <el-dialog title="选择班级模型" :width="modalWidth" :modal="showModal" class="form-dialog" :visible.sync="dialogVisible" label-width="60px" append-to-body>
      <el-form :model="formData" inline ref="formData" class="model-select" label-width="80px">
        <!--动态表单项-->
        <el-form-item v-for="(item, index) in formData" :key="index" :label="item.label" :prop="`${index}.value`" :rules="{
            required: item.required, message: `${item.label}不能为空`, trigger: 'change'
          }">
          <el-select clearable v-model="item.value" placeholder="请选择" @change="change(...arguments,item.name)" :multiple="!item.single" :collapse-tags="!item.single">
            <!--二级分组-->
            <el-option-group v-for="group in collecRelate(item.related, item.children)" :key="group.val" :label="group.txt" v-if="group.children">
              <el-option v-for="(item, index) in group.children" :key="index" :label="item.txt" :value="item.val">
              </el-option>
            </el-option-group>
            <!--一级分组-->
            <el-option v-else :key="group.val" :label="group.txt" :value="group.val">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <!--操作-->
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible=false">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import Util from '@common/utils';
import { uniqBy, isArray, isEmpty } from 'lodash';

export default {
  name: 'dynamicForm',
  props: {
    // 定义动态表单模型
    value: {
      type: [Object, Array],
      default: () => {},
      required: true
    },
    // 控制模态框
    visible: {
      type: Boolean,
      default: false
    },
    showModal: {
      type: Boolean,
      default: true
    },
    modalWidth: {
      type: String
    }
  },
  data() {
    return {
      dialogVisible: false,
      formData: {},
      gradeSubject: [],
      // 存放表单扁平化数据
      tempFormData: {}
    };
  },
  computed: {
    // 获取年级数据
    getGradeSelect() {
      return this.formData.grade.value;
    }
  },
  methods: {
    /**
     * 表单项联动，依据年级筛选学科单元项
     *
     * @param relate {string} 指定联动属性
     * @param target {object|array} 学科
     * @returns {object|array}
     */
    collecRelate(relate, target) {
      let iterator = this.formData[relate];
      let value = '';
      let len = 0;
      if (iterator) {
        value = iterator.value;
        if (isArray(value)) {
          len = value.length;
        } else {
          len = value.toString().length;
        }
      }

      if (relate && iterator && len > 0) {
        // 选择年级后，展示对应学科
        let arr = [];
        if (isArray(value)) {
          iterator.value.forEach(item => {
            if (!Util.isArray(target[item]) && target[item]) {
              arr.push(...Util.objectToArr(target[item].children));
            }
            arr = uniqBy(arr, 'val');
          });
        } else {
          if (!Util.isArray(target[value]) && target[value]) {
            arr.push(...Util.objectToArr(target[value].children));
          }
          arr = uniqBy(arr, 'val');
        }

        return arr;
      } else if (relate && iterator && len < 0) {
        // 未选择任何年级时，展示所有学科
        let arr = [];
        arr = this.horizontalDataMap(target);
        return arr;
      }
      return target;
    },

    /**
     * 数据扁平化处理
     * @returns {array}
     */
    horizontalDataMap(target) {
      let arr = [];
      Object.keys(target).forEach(item => {
        let transTar = target[item].children ? target[item].children : target;
        arr.push(...Util.objectToArr(transTar));
      });
      arr = uniqBy(arr, 'val');
      return arr;
    },

    // 获取表单数组中的value值
    collecFormData(val) {
      let obj = {};
      Object.keys(val).forEach(item => {
        // 返回value对应的val和txt值
        obj[item] = this.tempFormData[item].filter(inx => {
          if (isArray(val[item].value)) {
            return val[item].value.indexOf(inx.val) > -1;
          } else {
            return val[item].value.toString() === inx.val.toString();
          }
        });
      });
      return obj;
    },

    // 选择框值改变时
    change(val, name) {
      if (name === 'grade' || name === 'gradeSubjectMap') {
        this.$emit('gradeAndSubChange', this.collecFormData(this.formData), name);
      }
      this.$emit('change', this.collecFormData(this.formData), name);
      this.$emit('input', this.formData);
    },
    // 点击确定按钮
    handleConfirm() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.$emit('confirm', this.collecFormData(this.formData));
          this.dialogVisible = false;
        }
      });
    },
    reset() {
      if (!isEmpty(this.$refs)) {
        this.$refs.formData.resetFields();
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
    },
    value: {
      handler(val) {
        if (val) {
          this.formData = val;
          // 将表单数据扁平化处理，存放在tempFormData中
          Object.keys(this.formData).forEach(item => {
            let target = this.formData[item].children;
            this.tempFormData[item] = this.horizontalDataMap(target);
          });
        }
      },
      deep: true
    },
    // 年级变动时，初始化学科值
    'formData.grade.value.length'(val) {
      this.formData.gradeSubjectMap.value = [];
    },
    // 体系变动时，初始化班型值
    'formData.courseSystemList.value.length'(val) {
      this.formData.classTypeList.value = [];
    }
  }
};
</script>
<style lang="less">
.form-dialog {
  // .el-dialog {
  //   width: 50%;
  // }
  .el-dialog__body {
    padding: 32px 35px;
  }
  .el-form-item__label {
    font-weight: normal !important;
  }
}
.model-select {
  .el-form-item {
    margin-right: 8px;
  }
}
</style>
