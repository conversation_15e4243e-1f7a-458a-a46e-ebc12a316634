/*
* @Author: liquan01
* @Date: 2020-04-02
* @description: 无数据提示组件
*/

<template>
  <div class="no-data-card-container">
    <UserWelcome></UserWelcome>
    <p class="no-data-tiny-text">很高兴您选择我们的微信服务，如果您在使用中遇到问题，可以进群咨询：<span @click="showQrcode">查看群二维码》</span></p>
    <p class="no-data-middle-area">您还未在系统接入任何账号，如需接入请按下方提示操作</p>
    <div class="no-data-bottom-area">
      <div class="left-area">
        <h2>授权公众号注意事项</h2>
        <ul>
          <li>1、必须是公众号绑定的管理员个人微信扫码授权，使用者无法授权。</li>
          <li>2、每个公众号最多可以授权给5家第三方平台，互不影响。</li>
          <li>3、授权后，对公众号原有的菜单、关键词回复、关注回复……等设置均无任何影响。</li>
          <li>4、未认证账号仅可使用自动回复功能，如需使用其他功能请使用认证的账号。</li>
        </ul>
      </div>
      <div class="right-area">
        <div class="wx-item" v-if="app_type == 1">
          <img src="@/static/images/gzh_icon.png" alt="">
          <el-button type="primary" size="medium" @click="addProduct">接入公众号</el-button>
        </div>
        <div class="wx-item" v-if="app_type == 2">
          <img src="@/static/images/xcx_icon.png" alt="">
          <el-button type="primary" size="medium">接入小程序</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import UserWelcome from './user-welcome';
import { HELPER_QRCODE_IMG } from "@/constans";

const ORIGIN = window.location.origin;

export default {
  data() {
    return {
    };
  },

  components: {
    UserWelcome
  },

  computed: {
  },

  props: {
    app_type: {
      type: Number,
      default: 1
    }
  },

  methods: {
    /**
     * 新增授权公众号
     */
    addProduct() {
      this.$addGroupDialog();
    },
    showQrcode() {
      this.$alert('<img src="' + HELPER_QRCODE_IMG + '"/>', '查看群二维码', {
        dangerouslyUseHTMLString: true,
        customClass: 'no-data-qrcode-dialog'
      });
    }
  },

  mounted() {

  }
};
</script>

<style lang="less" scoped>
.no-data-card-container {
  width: 800px;
  text-align: left;
  padding: 45px 85px;
  margin: 0 auto;
  background-color: #F7F8FB;

  p.no-data-tiny-text {
    font-size: 14px;
    color: #808080;

    span {
      color: #07C160;
      cursor: pointer;
    }
  }

  p.no-data-middle-area {
    height: 145px;
    line-height: 145px;
    text-align: center;
    color: #555555;
    font-size: 16px;
    font-weight: bold;
  }

  .no-data-bottom-area {
    &::after {
      content: '';
      height: 0;
      line-height: 0;
      display: block;
      visibility: hidden;
      clear: both;
    }
    .left-area {
      width: 432px;
      padding-right: 76px;
      border-right: 1px solid #e6e6e6;
      float: left;

      h2 {
        font-size: 16px;
        color: #333333;
        font-weight: bold;
        margin-bottom: 16px;
      }
      li {
        font-size: 14px;
        color: #333333;
        line-height: 25px;
        text-align: left;
      }
    }

    .right-area {
      padding-left: 72px;
      float: left;
      margin-right: -200px;
      
      .wx-item {
        width: 213px;
        text-align: center;
        margin-bottom: 15px;

        img {
          height: 76px;
          display: inline-block;
          margin-bottom: 35px;
        }

        button {
          width: 100%;
        }
      }
    }
  }
}
</style>
<style lang="less">
.no-data-qrcode-dialog {
  .el-message-box__message {
    margin: 0 auto;
  }
  img {
    width: 240px;
  }
}
</style>
