<!--
 * @Description: 原生模板消息预览组件
 * @Author: Ask
 * @LastEditors: Ask
 * @Date: 2019-10-15 16:09:03
 * @LastEditTime: 2019-10-21 14:05:58
 -->

<template>
  <div class="template-news">
    <header>
      <p class="header-title" v-text="'{{first.DATA}}'"></p>
    </header>
    <section>
      <ul v-for="(item, index) in newsContent.keyword_list" :key="index">
        <li class="news-keyword-list">
          <span>{{item.name}}：</span>
          <span v-text="`{{keyword${index+1}.DATA}}`"></span>
        </li>
      </ul>
      <ul v-text="'{{remark.DATA}}'"></ul>
    </section>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        newDialog: false,
        today: new Date()
      };
    },
    props: {
      newsContent: {
        type: Object
      }
    },
    methods: {
      changeVisible() {
        this.newDialog = !this.newDialog;
      }
    }
  };
</script>
<style lang="less" scoped>
  .template-news {
    font-size: 12px;
    border: 1px solid @zyb-grey-2;
    border-radius: 2px;
    padding: 0 16px;
    header {
      .header-title {
        font-size: 14px;
        line-height: 18px;
        padding: 16px 0 8px 0;
      }
    }
    section {
      border-top: 1px solid @zyb-grey-4;
      border-bottom: 1px solid @zyb-grey-4;
      padding: 8px 0;
      ul {
        line-height: 18px;
        padding: 8px 0;
        li {
          line-height: 14px;
        }
      }
    }
    footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 36px;
      color: @zyb-font-grey-1;
      i {
        font-size: 16px;
        color: @zyb-blue-grey-2;
      }
    }
  }
</style>