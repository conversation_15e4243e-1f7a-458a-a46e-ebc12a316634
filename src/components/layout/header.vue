<template>
  <div class="zyb-top-bar">
    <div class="logo icon" @click="goHome">{{ this.collapse ? '' : 'AB实验' }}</div>
    <div v-if="!hideMenu" class="fold-icon icon" @click="foldClick"></div>
    <div class="zyb-top-content">
      <section>
        <Head-Menu></Head-Menu>
        <el-dropdown @command="handleCommand">
          <span class="el-dropdown-link zyb-btn-green">
            环境列表<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1">国内平台</el-dropdown-item>
            <el-dropdown-item :command="2">美东平台</el-dropdown-item>
            <el-dropdown-item :command="3">测试平台</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </section>
      <section class="menu-container">
        <Message />
        <div class="zyb-user">
          <div class="avatar"></div>
          <span class="uname">{{ username }}</span>
          <em class="avatar-icon el-icon-caret-bottom"></em>
          <form method="get" id="myForm" action="/wxadmin/admin/logout" class="cover">
            <a href="javascript:void(0);" class="zyb-btn-green" @click="goToPersonalCenter">个人中心</a>
            <a href="javascript:void(0);" class="zyb-btn-green" @click="handlePermission">申请权限</a>
            <a href="javascript:void(0);" class="zyb-btn-green" @click="logout">退出</a>
          </form>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { localStorage } from '@/common/utils';
import HeadMenu from './head-menu.vue';
import Message from './message.vue';
export default {
  name: 'zyb-header',
  props: {
    collapse: {
      type: Boolean,
      default: false
    },
    hideMenu: {
      type: Boolean,
      default: true
    },
    uname: {
      type: String
    }
  },
  components: {
    HeadMenu,
    Message
  },
  data() {
    return {
      isShip: location.href.includes('suanshubang.cc'),
      systemName: 'AB实验',
      username: this.uname
    };
  },
  mounted() { },
  watch: {
    uname(newVal) {
      this.username = newVal;
    }
  },
  methods: {
    // 帮助中心
    handleHelp() {
      // 'https://zyb-ab-test-1253445850.cos.ap-beijing.myqcloud.com/AB%E5%AE%9E%E9%AA%8C%E5%B9%B3%E5%8F%B0%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3-v1.pdf'
      // 'https://zyb-abtest-group-1253445850.cos.ap-beijing.myqcloud.com/file/ABTEST%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E%E6%96%87%E6%A1%A3.pdf'
      window.open(
        'https://docs.zuoyebang.cc/space/1651?ddtab=true'
      );
    },
    // 申请权限
    handlePermission() {
      this.$router.push({ path: '/permission' });
    },
    handleCommand(command) {
      const list = [
        '',
        'https://abtest.zuoyebang.cc/static/fe-abtest-new/#/exp-manage/list',
        'https://global-abtest.zuoyebang.cc/static/fe-abtest-new/#/exp-manage/list',
        'https://abtest-base-cc.suanshubang.cc/static/fe-abtest-new/#/exp-manage/list'
      ];
      console.log('command', command);
      window.open(list[command]);
    },
    /**
     * 导航栏小化和恢复操作
     */
    foldClick() {
      const { collapse } = this;
      // if (collapse) {
      //   this.systemName = 'AB实验';
      // } else {
      //   this.systemName = '';
      // }
      this.$emit('update:collapse', !collapse);
    },
    /**
     * 退出登录
     * 清空session
     */
    logout() {
      window.location.href = '/earthworm/auth/logout';
      //this.$router.push({path: '/login'});
      //  this.$service.get('LOGOUT', '').then(res => {
      //   localStorage.clear();
      //   //location.href = res.loginUrl;
      // });
    },
    goHome() {
      localStorage.clear();
      this.$router.push({ path: '/' });
    },
    goToPersonalCenter() {
      this.$router.push({ path: '/personal-center' });
    }
  }
};
</script>

<style lang="less" scoped>
.icon {
  background-repeat: no-repeat;
  background-position: center;
}

.zyb-top-bar {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  height: 50px;
  background-color: #fff;
  border-bottom: 1px solid @zyb-grey-4;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  z-index: 9;

  /deep/.el-tabs__item {
    height: 50px;
  }
}

.zyb-top-menu {
  display: flex;

  li {
    margin: 0 20px;
    height: 50px;
    box-sizing: border-box;
    line-height: 48px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 500;
    color: #555;
    position: relative;
    cursor: pointer;
  }

  .actived {
    color: #42c57a;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 2px;
      background: #42c57a;
    }
  }
}

.logo {
  height: 50px;
  width: 160px;
  background-color: @zyb-green-1;
  //background-image: url(../../static/images/zyb_logo-big.svg);
  //background-size: 105px 38px;
  transition: width 0.3s ease-in-out;
  color: #fff;
  font-size: 26px;
  font-weight: 700;
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.fold-icon {
  height: 28px;
  width: 28px;
  background-image: url(../../static/images/ic_topbar_fold_normal.svg);
  background-size: 28px 28px;
  cursor: pointer;
  margin-left: 16px;

  &:hover {
    background-image: url(../../static/images/ic_topbar_fold_hover.svg);
  }
}

.zyb-top-content {
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 16px;

  &>section {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .select-app {
    line-height: 50px;
  }

  .zyb-user {
    position: relative;
    display: flex;
    align-items: center;
    margin-right: 16px;
    min-width: 100px;
    height: 100%;

    &:hover {
      .avatar-icon {
        color: @zyb-blue-grey-1;
        transform: rotateZ(180deg);
      }

      .cover {
        font-size: 14px;
        display: block;
        color: @zyb-green-1;
        box-shadow: 0 2px 12px 0 hsla(225, 5%, 54%, 0.2);
      }
    }

    .avatar {
      height: 24px;
      width: 24px;
      vertical-align: middle;
      margin: 0 6px;
      border-radius: 50%;
    }

    .uname {
      font-size: 14px;
      color: @zyb-font-grey-1;
    }

    .avatar-icon {
      margin-left: 10px;
      font-size: 12px;
      color: @zyb-blue-grey-2;
      transform-origin: center;
      transition: color 0.3s, transform 0.3s;
    }

    .cover {
      display: none;
      position: absolute;
      background-color: #ffffff;
      right: 0;
      top: 48px;
      width: 100px;
      font-size: 12px;
      line-height: 30px;
      text-align: center;
      padding: 6px 0;

      a {
        display: block;
        line-height: 32px;
      }
    }
  }
}

.top-hot-area {
  font-size: 0;

  a.top-hot-area-title {
    display: inline-block;
    position: relative;
    font-size: 14px;
    height: 100%;
    padding: 0 12px;
    line-height: 50px;
    vertical-align: middle;
    text-decoration: none;
    outline: none;
    color: #333;
    overflow: hidden;
    margin-right: 20px;

    &:after {
      -webkit-transition: 0.2s;
      transition: 0.2s;
      content: '';
      position: absolute;
      left: 0;
      bottom: -4px;
      display: block;
      width: 100%;
      height: 4px;
      background: #61ca74;
    }

    &:hover {
      &:after {
        bottom: 0;
      }
    }
  }
}

.help-con {
  display: flex;
  align-items: center;
  cursor: pointer;

  span {
    color: #42c57a;
    font-size: 14px;
    margin-left: 12px;
  }

  &:hover span {
    color: #42c57a;
  }
}
</style>
