<!--
 * @Author: 
 * @Date: 2020-08-07 11:25:32
 * @LastEditors: huy<PERSON><PERSON>
 * @LastEditTime: 2021-02-01 21:30:23
 * @Description: 
-->
<template>
  <ul class="zyb-top-menu">
    <li
      v-for="(item, idx) in menu"
      :key="idx"
      :class="{actived: currentTab === item.platformUrlName}"
      @click="tabChange(item)"
    >
      {{ item.platformName }}
    </li>
  </ul>
</template>
<script>
import {HELPER_QRCODE_IMG} from '@/constans';
export default {
  data() {
    return {
      menu: [],
      currentTab: 'number'
    };
  },
  created() {
    //this.getTopMenu();
  },
  methods: {
    tabChange(item) {
      localStorage.setItem('currentTab', item.platformUrlName); // 兼容以前公众号、小程序服务、小程序工具系统
      window.open(item.platformUrl);
    },
    // 获取顶部导航信息
    // getTopMenu() {
    //   this.$service.get('getUserInfomation').then(res => {
    //     // todo
    //     // 已停用
    //     if (res.status === 2) {
    //       this.$alert(
    //         '<p class="head-text">您的账户已停止使用，请用钉钉扫描二维码，进入沟通群内反馈</p>' +
    //           '<div class="qrcode-container">' +
    //           '<img src="' +
    //           HELPER_QRCODE_IMG +
    //           '"/>' +
    //           '<p class="qrcode-title">微信技术支持群</p>' +
    //           '<p class="qrcode-text">钉钉扫描左侧二维码进群</p>' +
    //           '</div>',
    //         '',
    //         {
    //           dangerouslyUseHTMLString: true,
    //           customClass: 'user-stop-dialog'
    //         }
    //       );
    //     }
    //     if (res.platformList && res.platformList.length == 0)
    //       this.$router.push({path: '/no-authority'});
    //     this.menu = res.platformList;
    //     // 把权限系统的url,对应成前端路由
    //     this.menu.forEach((item, index) => {
    //       // 接入的权限系统，如果是权限系统的超管会返回此项
    //       const urlLength = item.platformUrl.split('/');
    //       const name = urlLength[urlLength.length - 1];
    //       item.platformUrlName = name;
    //     });
    //   });
    // }
  }
};
</script>
<style lang="less" scoped>
.zyb-top-menu {
  display: flex;
  li {
    margin: 0 9px;
    height: 50px;
    box-sizing: border-box;
    line-height: 48px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 500;
    color: #555;
    position: relative;
    cursor: pointer;
    min-width: 70px;
    text-align: center;
  }
  .actived {
    color: #42c57a;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 2px;
      background: #42c57a;
    }
  }
  :hover {
    color: #42c57a;
  }
}
</style>
