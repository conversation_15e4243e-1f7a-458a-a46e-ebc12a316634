<!--
 * @Author:
 * @Date: 2020-08-07 11:25:32
 * @LastEditors: wanglijuan01
 * @LastEditTime: 2020-08-26 20:26:40
 * @Description:
-->
<template>
  <div :class="['zyb-layout', { fold: collapse }]">
    <zyb-header v-if="!hideWrap" :collapse.sync="collapse" :hideMenu="hideMenu" :uname="uname" />
    <zyb-menu v-if="!hideMenu && !hideWrap" :collapse.sync="collapse" />
    <div :class="['layout-grid', { 'hide-menu': hideMenu }, { 'hide-wrap': hideWrap }]">
      <slot></slot>
      <div v-if="waterMark.text.userName" style="height: auto" v-watermask="waterMark"></div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import zybHeader from './header';
import zybMenu from './menu';
export default {
  name: 'zyb-layout',
  props: {
    showLogo: {
      type: Boolean,
      default: true
    },
    hideMenu: {
      type: Boolean,
      default: false
    },
    hideWrap: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      uname: '',
      collapse: false,
      waterMark: {
        text: {
          userName: '',
          userNumber: 'ZYB',
          currentDate: new Date().toLocaleString()
        },
        opacity: 0.08 // 水印的透明度 默认0.15,选填
      }
    };
  },
  computed: {
    ...mapGetters(['userInfo'], 'message'),
  },
  watch: {
    $route: {
      handler(newVal) {
        if (['/exp-manage/visual-editor'].includes(newVal.path)) {
          this.collapse = true;
        }
      },
      immediate: true
    },
    userInfo: {
      handler(data) {
        let text = this.waterMark.text;
        text.userName = data.uname;
        this.uname = data.uname;
        this.$set(this.waterMark, 'text', text);
      },
      immediate: true
    }
  },
  components: {
    zybHeader,
    zybMenu
  },
  beforeMount() { },
  methods: {
  }
};
</script>

<style lang="less" scoped>
.zyb-layout {
  .layout-grid {
    padding: 50px 0 0 160px;
    box-sizing: border-box;
    height: 100%;
    border-radius: 2px;
    box-sizing: border-box;
    transition: padding-left 0.3s ease-in-out;
    position: relative;

    &.hide-menu {
      padding-left: 0;
    }

    &.hide-wrap {
      padding-left: 0;
    }
  }

  &.fold {
    .layout-grid {
      padding-left: 64px;
      transition: padding-left 0.3s ease-in-out;
      position: relative;
    }

    .zyb-top-bar {
      /deep/.logo {
        width: 64px;
        background-image: url(../../static/images/zyb_logo_small.svg);
        background-size: 36px 36px;
        transition: width 0.3s ease-in-out;
      }

      /deep/.fold-icon {
        background-image: url(../../static/images/ic_topbar_unfold_normal.svg);

        &:hover {
          background-image: url(../../static/images/ic_topbar_unfold_hover.svg);
        }
      }
    }
  }
}</style>
