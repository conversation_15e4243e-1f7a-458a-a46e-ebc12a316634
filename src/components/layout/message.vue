<template>
  <div>
    <el-drawer
      title="通知"
      :size="550"
      custom-class="message-history-drawer"
      :before-close="handleClose"
      :append-to-body="true"
      :withHeader="false"
      :visible.sync="showDrawer"
      direction="rtl"
      ref="drawer"
      :destroy-on-close="true"
    >
      <section class="page-container">
        <section class="header">
          <span style="font-weight: bolder">消息通知</span>
          <section class="right">
            <el-tooltip class="item" effect="dark" content="全部标为已读" placement="left">
              <el-button
                type="text"
                icon="el-icon-document-checked"
                style="font-size: 18px;"
                @click="readMessage()"
              ></el-button>
            </el-tooltip>
            <el-button type="text" @click="openMesaageConfigStstem">消息接受配置</el-button>
          </section>
        </section>
        <section class="history-page">
          <section class="header">
            <el-radio-group v-model="type" @change="getMessageList()">
              <el-radio-button :label="0">全部消息</el-radio-button>
              <el-radio-button :label="1">流程中心</el-radio-button>
              <el-radio-button :label="2">运维消息</el-radio-button>
              <el-radio-button :label="3">风险通知</el-radio-button>
            </el-radio-group>
            <el-checkbox v-model="notRead" @change="getMessageList()">只看未读</el-checkbox>
          </section>
          <section class="list">
            <section class="item" v-for="(item, index) in list" :key="index">
              <section class="header">
                <span class="html-content" v-html="item.message"></span>
                <el-button type="text" v-if="item.status === 1" @click="readMessage(item.id)">
                  标为已读
                </el-button>
              </section>
              <p>{{ handleFormatDate(item.createTime) }}</p>
            </section>
          </section>
        </section>
        <section class="footer">
          <el-button type="primary" @click="showDrawer = false">关闭</el-button>
        </section>
      </section>
    </el-drawer>

    <el-tooltip class="item" effect="dark" content="消息通知" placement="bottom">
      <el-badge
        :value="total"
        class="item"
        :hidden="total === 0"
        :max="99"
        style="margin-top: 1px;"
      >
        <i class="el-icon-bell" style="font-size: 21px;" @click="openDrawer"></i>
      </el-badge>
    </el-tooltip>
  </div>
</template>

<script>
import { formatDate } from '@/common/utils';
import { formatFormData } from '@/utils/util';

export default {
  name: 'MessageHistory',
  components: {},
  data() {
    return {
      type: 0,
      notRead: false,
      showDrawer: false,
      total: 0,
      // list: [{
      //   content: '[待审批]zhangsan提交的”×××“待你审批，请尽快处理',
      //   time: 1612345678,
      // }, {
      //   content: '[待审批]zhangsan提交的”×××“待你审批，请尽快处理',
      //   time: 1612345678,
      // }, {
      //   content: '[待审批]zhangsan提交的”×××“待你审批，请尽快处理',
      //   time: 1612345678,
      // }, {
      //   content: '[待审批]zhangsan提交的”×××“待你审批，请尽快处理',
      //   time: 1612345678,
      // }]
      list: []
    };
  },
  created() {
    this.getUnReadNum();
  },
  mounted() {},
  methods: {
    openMesaageConfigStstem() {
      const origin = window.location.origin;
      const path = '/static/fe-ugc-message/';
      const url = `${origin}${path}`;
      window.open(url);
    },
    async getUnReadNum() {
      const { total = 0 } = await this.$service.get(
        'GETMESSAGELIST',
        {
          pn: 1,
          rn: 1,
          status: 1,
          type: 0,
          system: 'abtest'
        },
        { allback: 0, needLoading: false }
      );
      this.total = total;
    },
    async getMessageList() {
      const { list = [] } = await this.$service.get(
        'GETMESSAGELIST',
        {
          pn: 1,
          rn: 1000,
          status: this.notRead ? 1 : 0,
          type: this.type,
          system: 'abtest'
        },
        { allback: 0, needLoading: true }
      );
      this.list = list;
    },
    async readMessage(id = 0) {
      const data = formatFormData({
        id
      });
      await this.$service.post('READMESSAGE', data, { allback: 0, needLoading: true });
      this.getMessageList();
    },
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'MM-dd HH:mm:ss');
      }
      return '-';
    },
    handleClose(done) {
      this.showDrawer = false;
    },
    openDrawer() {
      this.getMessageList();
      this.showDrawer = true;
    }
  },
  beforeDestroy() {}
};
</script>
<style lang="less"></style>
<style lang="less" scoped>
.page-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 22px;
    .right {
      display: flex;
      align-items: center;
    }
  }

  & > .header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e1e3e9;
    font-size: 16px;
    font-weight: bold;
    color: black;
  }

  .footer {
    height: 32px;
    padding-top: 12px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    border-top: 1px solid #e1e3e9;
  }
}

.history-page {
  flex: 1;
  overflow-y: auto;
  padding-right: 16px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .list {
    margin-top: 12px;

    .item {
      margin: 0;
      padding: 8px;
      border-radius: 4px;
      font-size: 15px;
      //font-weight: bold;

      &:hover {
        background-color: #e1e3e9;

        .header {
          .el-button {
            display: inherit;
          }
        }
      }

      .header {
        height: auto;
        margin-bottom: 8px;
        .el-button {
          display: none;
        }
        /* 深度选择器解决方案 */
        .html-content :deep(b) {
          color: coral;
        }
      }

      & > p {
        color: #999;
        font-size: 12px;
        font-weight: bold;
        margin-top: 2px;
      }
    }
  }
}
</style>
