<template>
  <el-menu
    class="zyb-menu"
    :router="true"
    :default-active="currentMenu"
    :collapse="collapse"
    @select="handleSelect"
  >
    <template v-for="(item, index) in menuList">
      <el-submenu
        v-if="item.childrenPage && item.childrenPage.length > 0"
        popper-class="zyb-pop-submenu"
        :index="item.authId + ''"
        :key="index"
      >
        <template slot="title">
          <i
            :class="[menuIcon[item.authId] || 'znzt-product-features', 'sub_icon', 'iconfont']"
          ></i>
          <span class="sub_title">{{ item.menuName }}</span>
        </template>
        <template>
          <el-menu-item
            v-for="(subItem, idx) in filterHide(item.childrenPage)"
            :key="`${item.authId}-${subItem.authId}-${idx}`"
            :route="{ path: subItem.frontRouterUrl }"
            :index="subItem.frontRouterUrl"
          >
            {{ subItem.menuName }}
          </el-menu-item>
        </template>
      </el-submenu>
      <el-menu-item
        v-else
        :index="item.frontRouterUrl"
        :key="index"
        :route="{ path: item.frontRouterUrl }"
      >
        <i :class="[menuIcon[item.authId] || 'znzt-product-features', 'sub_icon', 'iconfont']"></i>
        <span slot="title" class="sub_title">{{ item.menuName }}</span>
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script>
import Utils from '@/common/utils';
import { MENU_LIST_ICON } from '@/components/layout/menu-icon.js';
export default {
  props: {
    collapse: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      path: '',
      menuList: [],
      menuIcon: MENU_LIST_ICON
    };
  },
  computed: {
    isHide() {
      const { isHide = false } = this.$route.meta;
      return isHide;
    },
    currentMenu() {
      return this.path;
    }
  },
  mounted() {
    // 接入权限系统的话，会有系统id
    this.getmenuinfo();
  },
  watch: {
    $route(newVal, oldVal) {
      let { path = '' } = newVal;
      // 跳转目标路由为isHide为true时，不变更菜单选中状态
      let routeFromPath = Utils.sessionStorage.getItem('routeFromPath') || '';
      routeFromPath = this.isHide ? routeFromPath : path;
      this.path = routeFromPath;
      Utils.sessionStorage.setItem('routeFromPath', routeFromPath);
    }
  },
  methods: {
    handleSelect(index, indexPath) {
      // if (index === '/customer-service/workbench') {
      //   window.open('https://www.zybang.com/static/wxcservice/index.html#/login');
      //   // window.open(location.origin + '/static/wxcservice/index.html#/login');
      // }
      // if (index === '/customer-service/quick-reply') {
      //   window.open(location.origin + '/wxwork/view/index/group-control/quick-reply');
      // }
    },
    /**
     * 获取左侧菜单列表
     */
    getmenuinfo() {
      let hostName = location.hostname;
      let system_id = '';
      if (/docker|test/gi.test(hostName) || /^localhost$/gi.test(hostName)) {
        system_id = 2136;
      } else {
        system_id = 105; // 需要确认线上的system_id
      }
      this.$service.get('MENU_LIST', { system_id }).then((data) => {
        // const newMenu = {
        //   "authId": 63971,
        //   "menuName": "客服系统",
        //   "description": null,
        //   "parentId": 0,
        //   "frontRouterUrl": "",
        //   "rank": 2,
        //   "platformId": "2047",
        //   "status": 1,
        //   "childrenPage": [
        //     {
        //       "authId": 63982,
        //       "menuName": "客服账号",
        //       "description": null,
        //       "parentId": 63973,
        //       "backRouterUrl": "/wxqk/analysis/list",
        //       "frontRouterUrl": "/customer-service/account",
        //       "rank": 0,
        //       "platformId": "2047",
        //       "status": 1,
        //       "elementButtonList": [],
        //     },
        //      {
        //         "authId": 6402,
        //         "menuName": "客服分组",
        //         "description": null,
        //         "parentId": 6397,
        //         "backRouterUrl": "/wxqk/analysis/effect",
        //         "frontRouterUrl": "/customer-service/groups",
        //         "rank": 1,
        //         "platformId": "2047",
        //         "status": 1,
        //         "elementButtonList": []
        //       },
        //        {
        //         "authId": 6403,
        //         "menuName": "客服工作台",
        //         "description": null,
        //         "parentId": 63973,
        //         "backRouterUrl": "/wxqk/analysis/effect",
        //         "frontRouterUrl": "/customer-service/workbench",
        //         "rank": 1,
        //         "platformId": "2047",
        //         "status": 1,
        //         "elementButtonList": []
        //       }
        //     ]
        //   };
        this.menuList = data;
        // this.menuList.push(newMenu);
        this.dealMenu(this.menuList);
      });
    },
    // deal menu数据,使得当的左侧菜单下的所有路由相互切换时，选中的左侧菜单项不变，永远都是撑开和选中状态
    dealMenu(data) {
      if (this.$route.meta.isHide) {
        const parent = this.$route.meta.parent;
        data.forEach((item) => {
          if (item.childrenPage) {
            item.childrenPage.forEach((key) => {
              if (key.menuName === parent) {
                this.path = key.frontRouterUrl;
              }
            });
          } else {
            if (item.menuName === parent) {
              this.path = item.frontRouterUrl;
            }
          }
        });
      } else {
        //   // todo 优化路由结构
        this.path = this.$route.path;
      }
    },
    /**
     * 过滤隐藏页
     */
    filterHide(list) {
      let isGlobal = location.href.includes('global-abtest');
      if (isGlobal) {
        return list.filter((item) => (!item.meta || !item.meta.isHide) && !item.menuName.includes("指标"));
      } else {
        return list.filter((item) => !item.meta || !item.meta.isHide);
      }
    },
    /**
     * 在导航菜单数据中书否存在所传的路由
     * @param {string} pathName 需要检测的导航链接
     * @param {Array} list 导航菜单数据
     * @return {boolean} 是否存在
     */
    isExistMenu(pathName = '', list = []) {
      if (!Array.isArray(list)) {
        return false;
      }
      return list.some((item) => {
        const { children = [], route = '' } = item;
        return route === pathName ? true : this.isExistMenu(pathName, children);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.zyb-menu {
  position: fixed;
  left: 0;
  top: 50px;
  bottom: 0;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 1;
  background: #fff;
  border-right: solid 1px #f0f1f5;
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  &:not(.el-menu--collapse) {
    width: 160px;
  }
}

.el-menu--collapse {
  /deep/.el-submenu__title {
    text-align: center;
  }
}

.el-menu--collapse {
  .sub_icon {
    margin-right: 0 !important;
  }
}

/deep/ .el-submenu__title:hover {
  color: @zyb-green-1;
  i {
    color: @zyb-green-1;
  }
}
/deep/ .el-menu .el-menu-item {
  height: 40px;
  font-size: 14px;
  color: #333;
  list-style: none;
  cursor: pointer;
  position: relative;
  transition: border-color 0.3s, background-color 0.3s, color 0.3s;
  box-sizing: border-box;
  display: flex;
  line-height: 1.5;
  align-items: center;
  font-weight: 500;
}
/deep/ .el-menu-item.is-active:before {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #42c57a;
}
/deep/ .el-menu-item.is-active {
  color: #42c57a;
  background-color: #f7f8fb;
}
/deep/ .el-submenu [class^='znzt-'],
/deep/ .el-submenu [class^='znzt-'] {
  vertical-align: middle;
  margin-right: 8px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}
/deep/ .el-submenu .el-menu-item {
  padding: 0 10px 0 40px !important;
  min-width: 160px;
}
/deep/ .sub_title {
  font-weight: 500;
}
</style>
