<template>
  <div class="page">
    <div class="page-header" v-if="breadcrumbs.length">
      <!--顶部左侧面包屑，暂时先放一级路由-->
      <section>
        <el-button v-if="breadcrumbs.length > 1" @click="goBack" type="text" icon="el-icon-arrow-left" :style="{ color: 'black', fontSize: '16px', fontWeight: 'bold' }">{{title?title:(breadcrumbs[1].meta ? breadcrumbs[1].meta.title : breadcrumbs[1].name)
          }}</el-button>
        <el-button v-if="breadcrumbs.length === 1" type="text" :style="{ color: 'black', fontSize: '16px', fontWeight: 'bold' }">{{ breadcrumbs[0].meta ? breadcrumbs[0].meta.title : breadcrumbs[0].name }}</el-button>
      </section>
      <section>
        <slot name='header-action'></slot>
      </section>

      <!-- <el-breadcrumb separator="/">
        <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index">
          {{ (item.meta && item.meta.title) || (item.name && item.name) }}
        </el-breadcrumb-item>
      </el-breadcrumb> -->
    </div>
    <div class="page-cont">
      <div class="page-section">
        <slot name="pageSection"></slot>
      </div>
      <!-- 备用 -->
      <div class="other">
        <slot name="other"></slot>
      </div>
      <!-- tab切换 -->
      <div class="page-tab">
        <slot name="tabs"></slot>
      </div>
      <div v-if="actionminwidth + minWidth - clientWith >= 200" style="margin-bottom: 10px" class="page-action">
        <Action-Btn :actionList="actionList"></Action-Btn>
      </div>
      <!-- page-search -->
      <div :class="searchData.length == 0 ? 'page-search' : 'page-search1'">
        <Search v-if="searchData.length" :style="{ width: minWidth, 'max-width': '100%' }" :searchList="searchData" :searchBtns="searchBtns" :hideBtn="hideBtn" />
        <!--操作区域-->
        <div v-if="actionminwidth + minWidth - clientWith < 200" class="page-action">
          <Action-Btn :actionList="actionList"></Action-Btn>
        </div>
      </div>
      <div class="page-container">
        <!-- 内容区域 -->
        <slot></slot>
        <div class="page-container-pagination" v-if="hasPagination">
          <slot name="pagination"></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Search from '@components/common/search-box/search-box.vue';
import ActionBtn from '@components/common/actionbtn/index.vue';
import { localStorage } from '@/common/utils';
import router from '@router';
export default {
  components: {
    Search,
    ActionBtn
  },
  props: {
    title: '',
    hasPagination: {
      type: Boolean,
      default: true
    },
    breadcrumbList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    searchData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    actionList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    searchBtns: {
      type: Array,
      default: () => []
    },
    hideBtn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      clientWith: document.body.clientWidth || document.body.offsetWidth,
      breadcrumbs: [],
      prevCrumb: ''
    };
  },
  created() {
    let routes = [];
    router.options.routes.forEach((item, idx) => {
      if (item.children && item.children.length > 0) {
        routes = routes.concat(item.children);
      }
    });
    this.breadcrumbList.forEach((key, index) => {
      routes.length &&
        routes.forEach((item) => {
          if (
            (key && key.name && key.name === item.name) || // 兼容字符串和对象
            key === item.name || // 兼容name为中文
            (item.meta && key === item.meta.title)
          ) {
            this.breadcrumbs.push({ ...item, ...key });
          }
        });
    });
    this.$bus.$on('syncAppInfo', this.setBreadCrumb);
    this.prevCrumb = JSON.parse(localStorage.getItem('appInfo'));
  },
  methods: {
    setBreadCrumb(syncAppInfo) {
      this.prevCrumb = syncAppInfo;
    },
    goBack() {
      this.$router.go(-1);
    }
  },
  computed: {
    actionminwidth() {
      const { actionList } = this;
      return actionList.length * 140;
    },
    minWidth() {
      const { searchData, searchBtns } = this;
      let width = 0;
      if (searchData.length) {
        for (let i = 0; i < searchData.length; i++) {
          switch (searchData[i].type) {
            case 'timeSelect':
              if (searchData[i].label) {
                width += 320;
              } else {
                width += 240;
              }
              break;
            case 'datetimeSelect':
              if (searchData[i].label) {
                width += 250;
              } else {
                width += 170;
              }
              break;
            case 'moreDatetimeSelect':
              if (searchData[i].label) {
                width += 400;
              } else {
                width += 300;
              }
              break;
            case 'input':
            case 'select':
            case 'checkboxGroup':
            case 'select-multiple':
              if (searchData[i].label) {
                width += 230;
              } else {
                width += 150;
              }
              break;
            default:
              width += 150;
              break;
          }
        }
        if (searchBtns.length) {
          for (let j = 0; j < searchBtns.length; j++) {
            width += 60;
          }
        } else {
          width += 120;
        }
      }
      return width + 'px';
    }
  },
  beforeDestroy() {
    this.$bus.$off('syncAppInfo', this.setBreadCrumb);
  }
};
</script>
<style lang="less" scoped>
.page {
  height: 100%;
  margin: 16px 24px;
  display: flex;
  flex-direction: column;
  background: white;
  padding: 6px 24px 16px 24px;
}

.page-tab {
  // margin: 20px 0;
}

.page-cont {
  // margin: 0 24px 24px;
  background: white;
}

.page-header {
  border-bottom: 1px solid #e1e3e9;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  height: 24px;
  flex-shrink: 0;
  background: white;
  font-size: 14px;
  height: 48px;
  box-sizing: border-box;
  // box-shadow: 0 3px 10px 0 #c7c7d44d;
}

.page-action {
  text-align: right;
  padding: 0px 10px 0px 0px;
}

.page-search {
  padding: 0px 20px 0px 20px;
  text-align: right;
}

.page-search1 {
  padding: 0px 20px 0px 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.page-container {
  flex-grow: 1;
  border-radius: 2px;
  padding-top: 16px;
  background: #ffffff;

  .page-container-pagination {
    margin-bottom: 16px;
    text-align: right;
    padding: 16px;
  }
}

/deep/ .el-breadcrumb__inner {
  font-size: 14px;
}
</style>
