/*
 * @Author: 
 * @Date: 2020-08-07 11:25:32
 * @LastEditors: wanglijuan01
 * @LastEditTime: 2020-08-13 14:14:16
 * @Description: 
 */

export default {
  data() {
    return {
      cacheData: {},
      pagination: {
        total: 0,
        pn: 1,
        rn: 20,
      },
    };
  },
  mounted() {
    this.$bus.$on('search', this.search);
    this.$bus.$on('reset', this.reset);
    this.$bus.$on('initSearch', this.initSearch);
  },
  methods: {
    /**
     * 如果搜索组件初始化时有值的话，就把初始化组件时的数据传给翻页按钮
     * 这样有一个问题，就是在搜索框数据发生变化的时候 initSearch和search都会执行
     * 现在在执行initSearch之后就off掉当前注册事件，后续在看怎么处理
     * @param {object} data 初始化组件时的数据
     * <AUTHOR>
     */
    initSearch(data) {
      // 过滤空字符串
      for (let it in data) {
        data[it] === '' && delete data[it];
      }
      this.cacheData = data;
      this.pagination.pn = 1;
      console.log('initSearch', this.cacheData);
      this.$bus.$off('initSearch', this.initSearch);
    },

    search(data) {
      // 过滤空字符串
      for (let it in data) {
        data[it] === '' && delete data[it];
      }
      this.cacheData = data;
      this.pagination.pn = 1;
      console.log('search', this.cacheData);
      this.queryTableData(this.cacheData);
    },
    reset() {
      this.cacheData = {};
      this.pagination.pn = 1;
      this.queryTableData();
    },
    /**
     * 请求列表时候携带的翻页参数
     */
    paginationParams() {
      return {
        page_size: this.pagination.rn || 20,
        // page_no: this.pagination.pn
        page_no: this.pagination.pn || 1,
        pageSize: this.pagination.rn || 20,
        pageNo: this.pagination.pn || 1,
        pn: this.pagination.pn || 1,
        rn: this.pagination.rn || 20,
      };
    },
    /**
     * 翻页事件
     * @param pn  当前页码
     */
    changePage(pn) {
      this.pagination.pn = pn;
      this.cacheData = {...this.cacheData, page_no: pn};
      this.queryTableData(this.cacheData);
    },
    /**
     * 给翻页组件赋值
     * @param meta 翻页信息
     */
    assignPagination(meta) {
      this.pagination.total = meta.total;
      this.pagination.pn = meta.pageNo || meta.page_no || meta.page;
      this.pagination.rn = meta.pageSize || meta.page_size || meta.size;
    },
    /**
     * 每页条数变化
     */
    pageSizeChange(val) {
      this.pagination.rn = val;
      this.queryTableData();
    },
  },
  beforeDestroy() {
    this.$bus.$off('search', this.search);
    this.$bus.$off('reset', this.reset);
    this.$bus.$off('initSearch', this.initSearch);
  },
};
