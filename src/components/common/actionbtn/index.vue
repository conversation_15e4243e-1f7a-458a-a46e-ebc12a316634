<!--
 * @Author: wang<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-03-01 12:03:54
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-08-16 14:30:18
 * @Description: 操作按钮
 -->
<script>
export default {
  props: {
    actionList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
    };
  },
  created() {},
  methods: {
    emitFather(cd) {
      cd();
    }
  },
  render() {
    return (<div>
      {this.actionList.map(item => {
        return <el-button type={item.btnType} size={item.size} onClick={() => this.emitFather(item.calback)}>{item.content}</el-button>;
      })}
    </div>);
  }
};
</script>
<style lang="less">
</style>