<template>
  <a class="zyb_text" :disabled="disabled">
    <span @click="handleClick">
      <slot></slot>
    </span>
  </a>
</template>

<script>
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClick() {
      this.$emit('click');
    }
  }
};
</script>

<style lang="less" scoped>
.zyb_text {
  margin: 6px;
  span {
    display: inline-block;
  }
}

.zyb_text[disabled='disabled'] {
  cursor: not-allowed;
  span {
    color: #bbbcbf;
    pointer-events: none;
  }
}
</style>