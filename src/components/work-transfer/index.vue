<template>
  <el-dialog
    title="转交工作"
    :visible.sync="visible"
    width="560px"
    :before-close="handleClose"
    class="work-transfer-dialog"
  >
    <div class="tip-message">该功能只转交资源的所属关系，不会转交账号的权限。</div>

    <el-form :model="form" :rules="rules" ref="transferForm" label-width="140px">
      <el-form-item label="业务线：" prop="appKey" required>
        <el-select v-model="form.appKey" placeholder="请选择" style="width: 100%" clearable>
          <el-option
            v-for="item in appList"
            :key="item.appKey"
            :label="item.displayName"
            :value="item.appKey">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="转交资源类别：" prop="types" required clearable>
        <el-select v-model="form.types" multiple placeholder="请选择" style="width: 100%">
          <el-option
            v-for="item in resourceTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="转交前负责人：" required>
        <el-input v-model="currentUser" disabled></el-input>
      </el-form-item>

      <el-form-item label="转交后负责人：" prop="newOwner" required>
        <el-select
          v-model="form.newOwner"
          filterable
          remote
          :remote-method="remoteMethod"
          :loading="loading"
          placeholder="请选择"
          style="width: 100%">
          <el-option
            v-for="item in userOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="转交说明：" prop="description">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入转交说明"
          v-model="form.description">
        </el-input>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { formatFormData } from '@/utils/util';

export default {
  name: 'WorkTransfer',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        appKey: '',
        types: [],
        newOwner: '',
        description: ''
      },
      rules: {
        appKey: [
          { required: true, message: '请选择业务线', trigger: 'change' }
        ],
        types: [
          { required: true, message: '请选择转交资源类别', trigger: 'change' }
        ],
        newOwner: [
          { required: true, message: '请选择转交后负责人', trigger: 'change' }
        ]
      },
      resourceTypeOptions: [
        { value: 1, label: '实验' },
        { value: 2, label: '互斥组' },
        { value: 3, label: '流量控制' },
        { value: 4, label: '白名单' },
        { value: 5, label: '白名单分组' },
        { value: 6, label: '指标' },
        { value:7, label: '过滤参数' },
        { value: 8, label: '用户分群' }
      ],
      userOptions: [],
      loading: false
    };
  },
  computed: {
    ...mapGetters(['userInfo', 'appList']),
    currentUser() {
      return this.userInfo.uname || '';
    }
  },
  created() {
  },
  methods: {
    remoteMethod(query) {
      if (query) {
        this.loading = true;
        // 搜索用户
        this.$service.get('GETUNAMEINFO', { uname: query }).then(res => {
          this.loading = false;
          if (res && Array.isArray(res)) {
            this.userOptions = res.map(item => ({
              value: item.uname,
              label: `${item.realName}(${item.uname})`
            }));
          }
        }).catch(err => {
          this.loading = false;
          console.error('搜索用户失败', err);
        });
      } else {
        this.userOptions = [];
      }
    },
    submitForm() {
      this.$refs.transferForm.validate((valid) => {
        if (valid) {
          // 提交表单
          const params = formatFormData({
            appKey: this.form.appKey,
            types: this.form.types,
            oldOwner: this.currentUser,
            newOwner: this.form.newOwner,
            description: this.form.description
          });

          // 调用API进行工作转交
          this.$service.post('TRANSFER_WORK', params).then(res => {
            this.$message.success('工作转交成功');
            this.handleClose();
          }).catch(err => {
            console.error('工作转交失败', err);
            this.$message.error('工作转交失败，请重试');
          });
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$refs.transferForm.resetFields();
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="less" scoped>
.work-transfer-dialog {
  .tip-message {
    padding: 8px 16px;
    background-color: #f0f9eb;
    color: #67c23a;
    border-radius: 4px;
    margin-bottom: 20px;
    font-size: 14px;
  }
}
</style>
