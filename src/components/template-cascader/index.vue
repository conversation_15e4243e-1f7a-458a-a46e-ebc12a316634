<template>
  <t-cascader trigger="click" v-model="version.mywhitelist" :multiple="true" clearable value-type="full"
    :show-all-levels="true" :options="getWhiteListOptions()" style="width: 100%" :disabled="isCheck" :load="load">
    <template #valueDisplay="{ value, selectedOptions, onClose }">
      <template v-if="value && value.length">
        <t-tag v-for="(option, index) in selectedOptions" :key="option.value" closable @close="() => onClose(index)">
          <t-tooltip :content="option.value">
            <span class="cascader-item-name">{{ option.value }}</span>
          </t-tooltip>
        </t-tag>
      </template>
    </template>
  </t-cascader>
  <!-- <ElSelectV2 v-model="version.mywhitelist" :options="getWhiteListOptions()" :multiple="true" :disabled="disabled"></ElSelectV2> -->
</template>
<script>
import ElSelectV2 from 'el-select-v2';
import { mapGetters } from 'vuex';
export default {
  components: {
    ElSelectV2
  },
  props: ['version', 'allSelectedIds', 'disabled'],
  data() {
    return {
      list: [],
      inputProps: {}
    };
  },
  computed: {
    ...mapGetters(['whiteList']),
    isCheck() {
      return this.$route.query.type === 'check';
    }
  },
  methods: {
    load(node) {
      console.log('node0;====》》》》》', node);
      const allIds = this.allSelectedIds;
      const whiteList = this.version.mywhitelist;
      const target = this.whiteList.find(item => {
        return node.value === 'group-' + item.whitelistGroupDisplayName;
      });
      const children = target ? target.whitelist : [];
      const nodes = children.map(item => {
        return {
          value: item.id,
          label: item.displayName,
          children: false,
          disabled: !whiteList.includes(item.id) && allIds.includes(item.id)
        };
      });

      return new Promise((resolve) => {
        resolve(nodes);
      });
    },
    getWhiteListOptions() {
      const whiteList = this.version.mywhitelist;
      const allIds = this.allSelectedIds;
      const res = this.whiteList.map(item => {
        const children = item.whitelist || [];
        return {
          value: 'group-' + item.whitelistGroupDisplayName,
          label: item.whitelistGroupDisplayName,
          options: children.map(item => {
            return {
              value: item.id,
              label: item.displayName,
              disabled: !whiteList.includes(item.id) && allIds.includes(item.id)
            };
          })
        };
      });
      return res;
    }
  }
};
</script>

<!-- <template>
  <section>
    <t-cascader v-model="value1" value-type="full" clearable :options="options" :load="load" :multiple="true" />
  </section>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          label: '选项1',
          value: '1',
          children: true,
        },
        {
          label: '选项2',
          value: '2',
          children: true,
        },
      ],
      value1: []
    };
  },
  methods: {
    load(node) {
      return new Promise((resolve) => {
        setTimeout(() => {
          let nodes = [];
          if (node.level < 2) {
            nodes = [
              {
                label: `${node.label}.1`,
                value: `${node.value}-1.${node.level}`,
                children: node.level < 1,
              },
              {
                label: `${node.label}.2`,
                value: `${node.value}-2.${node.level}`,
                children: node.level < 1,
              },
            ];
          }
          resolve(nodes);
        }, 100);
      });
    },
    handleChange(value) {
      console.log('value', value);
    }
  },
};
</script> -->
