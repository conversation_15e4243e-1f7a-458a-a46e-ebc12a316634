<template>
  <el-select
    v-model="userList"
    filterable
    remote
    :remote-method="remoteMethod"
    :loading="loading"
    multiple
    :disabled="disabled"
    placeholder="请选择负责人"
  >
    <el-option
      v-for="option in options"
      :label="option.realName"
      :value="option.uname"
      :key="option.uname"
    ></el-option>
  </el-select>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  props: ['value', 'usedefault', 'disabled'],
  data() {
    return {
      options: [],
      loading: false
    };
  },
  watch: {
    userInfo: {
      handler(data) {
        if (this.usedefault && !this.value) {
          this.userList = [data.uname];
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapGetters(['userInfo'], 'message'),
    userList: {
      get() {
        if (!this.value) {
          return [];
        }
        return this.value.split(',');
      },
      set(val) {
        const value = val.join(',');
        this.$emit('input', value);
      }
    }
  },
  methods: {
    remoteMethod(query) {
      if (query) {
        this.loading = true;
        this.$service.get('GETUNAMEINFO', { uname: query }).then(res => {
          this.options = res || [];
        });
        setTimeout(() => {
          this.loading = false;
        }, 200);
      } else {
        this.options = [];
      }
    }
  }
};
</script>
