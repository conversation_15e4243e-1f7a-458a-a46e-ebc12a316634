/*
 * @Author: wang<PERSON><PERSON><PERSON>01
 * @Date: 2020-02-29 10:46:08
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-12-31 13:26:24
 * @Description: 入口文件
 */
import Vue from 'vue'
import uploader from 'vue-simple-uploader'
import App from './App.vue'
import router from './router'
import store from './store/index'
// import ZybPcUi from 'zyb-pc-ui'
import ElementUI from 'element-ui'
// 全局引入
//import 'element-ui/lib/theme-chalk/index.css';
import '../theme/index.css'
// 由于之前使用的是ZybPcUi，只能保持这种风格了
// import 'zyb-pc-ui/lib/theme-chalk/index.css'
import '@/icons'
import '@/static/css/index.less'
import '@/static/css/reset.less'
import '@/static/icon/iconfont.css'
import '@/static/icon1/iconfont.css'
import '@/static/icon2/iconfont.css'
import '@/static/icon3/iconfont.css'
import tools from '@/utils/inject'
import * as consoleLog from '@common/consoleLog'
import util from '@common/utils'
import fetch from '@config/fetch/fetch.js'
import '@src/permission'
import '@static/css/animation/index.less'
import '@static/css/icon-front/iconfont.js'
import '@static/css/icon-front/iconfont.less'
import * as echarts from 'echarts'
import 'highlight.js/styles/arduino-light.css'
import 'swiper/css/swiper.css'
import 'tdesign-vue/es/style/index.css'
import VueAwesomeSwiper from 'vue-awesome-swiper'
import VueClipboard from 'vue-clipboard2'
import VueHighlightJS from 'vue-highlightjs'
import infiniteScroll from 'vue-infinite-scroll'
import watermask from 'vue-watermask'
Vue.use(VueHighlightJS)

// import { TCascader } from 'tdesign-vue';
import { Cascader as TCascader, Tag as Ttag, Tooltip as Ttooltip } from 'tdesign-vue'

// 引入组件库的少量全局样式变量
// import 'tdesign-vue/es/style/index.css';
Vue.use(TCascader);
Vue.use(Ttag)
Vue.use(Ttooltip)
//sentry错误收集
// import * as Sentry from '@sentry/browser';
// import * as Integrations from '@sentry/integrations';

Vue.use(VueAwesomeSwiper)

Vue.use(uploader)

//Vue.use(ZybPcUi);
Vue.use(watermask)
Vue.use(infiniteScroll)
Vue.use(tools)
//VueClipboard.config.autoSetContainer = true // add this line
Vue.use(VueClipboard)
Vue.prototype.$service = fetch
Vue.prototype.$log = consoleLog.log
Vue.prototype.$echarts = echarts
Vue.config.productionTip = false
Vue.prototype.$eventbus = new Vue()
// Vue.config.productionTip = false
// 使用element-ui 原因是ZybPcUi的部分组件封装不完善，导致部分功能没有
Vue.use(ElementUI, { size: 'small' })
Vue.filter('formatTime', function(date, formatType = 'yyyy-MM-dd HH:mm') {
  return util.formatDate(date * 1000, formatType)
})

// 用来监控的，后续需要的话再加
// !(function sentryInit() {
//   let {hostname} = location;
//   hostname === 'wxtools.zuoyebang.cc' &&
//     Sentry.init({
//       dsn: 'https://<EMAIL>/9',
//       integrations: [new Integrations.Vue({Vue, attachProps: true})]
//     });
// })();

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')

// 路由切换时需要处理的逻辑
router.beforeEach((to, from, next) => {
  if (to.path) {
    window.scrollTo(0, 0) // 保证进入各个页面都是从（0,0）开始显示
  }
  next()
})
