/* eslint-disable space-before-function-paren */
/*
 * @Description: This is a description
 * @Author: Ask
 * @LastEditors: Ask
 * @Date: 2019-10-09 19:32:42
 * @LastEditTime: 2019-10-09 19:40:32
 */
/**
 * Copyright (c) 2014-2017 Zuoyebang, All rights reseved.
 * @file  防抖
 * <AUTHOR>
 * @version 1.0 | 2018/12/11 11:02 | chenliuqing //初始版本
 */

/**
 * immediate 是否立即执行
 * @returns fn
 */
export default function debounce(func, wait, immediate) {
  let time;
  const debounced = function() {
    const context = this;
    if (time) clearTimeout(time);

    if (immediate) {
      const callNow = !time;
      if (callNow) func.apply(context, arguments);
      time = setTimeout(() => {
        time = null;
      }, wait);
    } else {
      time = setTimeout(() => {
        func.apply(context, arguments);
      }, wait);
    }
  };

  /* debounced.cancel = function() {
    clearTimeout(time);
    time = null
  }; */

  return debounced;
}
