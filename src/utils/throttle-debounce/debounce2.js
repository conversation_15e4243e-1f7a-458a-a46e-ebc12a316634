/*
 * @Description: This is a description
 * @Author: Ask
 * @LastEditors: Ask
 * @Date: 2019-10-09 19:32:42
 * @LastEditTime: 2019-10-09 19:32:42
 */
/* eslint-disable no-extend-native */
/**
 * Copyright (c) 2014-2017 Zuoyebang, All rights reseved.
 * @file  防抖
 * <AUTHOR>
 * @version 1.0 | 2018/12/12 11:02 | chenliuqing //初始版本
 */

/** 调用
 *  assignTaskFn:function(){
 *         this.assignTask.$debounce(1000,this)
 *      },
 */
Function.prototype.$debounce = function (wait, currThis, ...values) {
  if (this.timer) {
    clearTimeout(this.timer);
  }
  var params = values || [];
  /* 当前this指Function实例 => 即当前要防抖的方法 */
  if (!this.timer) this.apply(currThis, params);
  this.timer = setTimeout(() => {
    this.timer = null;
  }, wait || 500);
};
