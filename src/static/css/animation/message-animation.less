.message_shake_animation.el-message--error {
  animation: tada 0.8s ease;
}

.message_shake_animation.el-message--success {
  animation: pulse 0.8s ease;
}

.message_shake_animation.el-message--warning {
  animation: swing 0.8s ease;
}

@keyframes tada {
  0% { transform: translateX(-50%) scale(1); }
  10%,
  20% { transform: translateX(-50%) scale(0.9) rotate(-3deg); }
  30%,
  50%,
  70%,
  90% { transform: translateX(-50%) scale(1.1) rotate(3deg); }
  40%,
  60%,
  80% { transform: translateX(-50%) scale(1.1) rotate(-3deg); }
  100% { transform: translateX(-50%) scale(1) rotate(0); }
}

@keyframes pulse {
  0% { transform: translateX(-50%) scale(1); }
  50% { transform: translateX(-50%) scale(1.1); }
  100% { transform: translateX(-50%) scale(1); }
}

@keyframes swing {
  0% { transform: translateX(-50%); }
  20% { transform: translateX(-50%) rotate(10deg); }
  40% { transform: translateX(-50%) rotate(-8deg); }
  60% { transform: translateX(-50%) rotate(3deg); }
  80% { transform: translateX(-50%) rotate(-3deg); }
  100% { transform: translateX(-50%) rotate(0deg); }
}
