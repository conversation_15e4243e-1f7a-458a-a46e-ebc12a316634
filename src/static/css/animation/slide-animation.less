/* 幻灯片动画 */
@keyframes rotateSlideOut {
	25% { opacity: .5; transform: translateZ(-500px); }
	75% { opacity: .5; transform: translateZ(-500px) translateX(-200%); }
	100% { opacity: .5; transform: translateZ(-500px) translateX(-200%); }
}
@keyframes rotateSlideIn {
	0%, 25% { opacity: .5; transform: translateZ(-500px) translateX(200%); }
	75% { opacity: .5; transform: translateZ(-500px); }
	100% { opacity: 1; transform: translateZ(0) translateX(0); }
}

/* 换边动画 */
@keyframes rotateSidesIn {
	from { opacity: 0; transform: translateZ(-500px) rotateY(-90deg); }
}
@keyframes rotateSidesOut {
	to { opacity: 0; transform: translateZ(-500px) rotateY(90deg); }
}

@slide-time: 1s;
@slide-function: ease;
@slide-mode: both;

@side-time: .5s;
@side-in-func: ease-out;
@side-out-func: ease-in;

/* 边到边动画 */
.slide {
	&-enter-active {
		animation: rotateSlideIn @slide-time @slide-function @slide-mode;
	}
	&-leave-active {
		animation: rotateSlideOut @slide-time @slide-function @slide-mode;
	}
}

.side {
	&-enter-active {
		transform-origin: 150% 50%;
		animation: rotateSidesIn @side-time @side-in-func @slide-mode;
	}
	&-leave-active {
		transform-origin: -50% 50%;
		animation: rotateSidesOut @side-time @side-out-func @slide-mode;
	}
}