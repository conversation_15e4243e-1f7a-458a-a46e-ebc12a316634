@import './zyb-color.less';

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video,
input,
button {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font-weight: normal;
  vertical-align: baseline;
  outline: none;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
  font-family: 'Avenir', Helvetica, Arial, 'Microsoft Yahei', sans-serif;
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: @zyb-font-grey-1;
  font-size: 12px;
}
html,
body,
#app {
  height: 100%;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* custom */
html,
body {
  background-color: @zyb-grey-5;
}

a {
  color: @zyb-green-1;
  cursor: pointer;
  text-decoration: none;
  -webkit-backface-visibility: hidden;

  &:hover {
    color: @zyb-green-hover;
  }
}

a li {
  list-style: none;
}

li {
  list-style-type: none;
}

em,
i {
  font-style: normal;
}

input {
  border: none;
}

#app {
  .text-danger {
    color: @zyb-red-1;
  }

  .btn-split {
    display: inline-block;
    width: 20px;
  }
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track-piece {
  background-color: @zyb-grey-6;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(131, 134, 143, 0.4);
  border-radius: 8px;

  &:vertical {
    width: 8px;
  }

  &:horizontal {
    height: 8px;
  }

  &:hover {
    background-color: rgba(131, 134, 143, 0.8);
  }
}
