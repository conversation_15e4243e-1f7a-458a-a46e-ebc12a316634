@import './element-ui.less';
// .el-message .el-icon-warning:before {
//   content: '\e62e';
// }
// .el-message .el-icon-success:before {
//   content: '\e62d';
// }
// .el-message .el-icon-info:before {
//   content: '\e61a';
// }
// .el-message .el-icon-error:before {
//   content: '\e62c';
// }

// .el-tooltip__popper {
//   max-width: 300px;
// }

// .ex-table-message {
//   .el-message-box__message {
//     -webkit-line-clamp: initial;
//   }
// }
// .el-message-box__container {
//   position: relative;
// }
// .el-message-box__status {
//   position: absolute;
//   top: 50%;
//   transform: translateY(-50%);
//   font-size: 20px !important;
// }
// .el-message-box__status.el-icon-warning {
//   color: #e6a23c;
// }
// .el-message-box__status + .el-message-box__message {
//   padding-left: 36px;
//   padding-right: 12px;
// }

.form-title {
  position: relative;
  padding-left: 12px;
  font-weight: 500;
  font-size: 14px;
  line-height: 14px;
  display: flex;
  align-items: center;
  margin: 16px 0;
  &::after {
    content: ' ';
    display: inline-block;
    width: 4px;
    height: 18px;
    position: absolute;
    left: 0;
    margin-right: 8px;
    background-color: #42c57a;
  }
}


.page-header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e1e3e9;
  padding-bottom: 6px;

  section {
    display: flex;
    align-items: center;
  }

  .el-button {
    margin-left: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2px 6px;
    padding-right: 12px;
  }

  .right {
    /deep/ i {
      // display: inline-block;
      width: 16px;
      height: 16px;
      margin-left: 4px;
      margin-right: 6px;
      display: inline-block;
    }

    .icon-yunhang {
      // width: 18px;
      // height: 18px;
    }
  }

  .page-title {
    font-size: 16px;
    font-weight: 500;
    line-height: 32px;
    margin: 0 8px;
  }

  i {
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.icon-primary-button {
  i {
    color: #42c57a;
  }
}

.el-select-group__title{
  font-weight: bold;
  color: #333;
  border-bottom: 1px solid #e1e3e9;
}

.el-select__tags{
  .el-select__tags-text{
    max-width: 100px;
    overflow: hidden;
    display: inline-block;
  }
  .el-icon-close{
    top: -6px!important;
  }

}

.suoliang-tips{
  color: #42c57a;
}

.t-select-input {
  .t-input.t-is-disabled{
    background-color: #f5f7fa;
    .t-tag{
      svg{
        display: none;
      }
    }
  }
}

.el-popover.overhidden-auto-popover{
  max-width: 850px!important;
  max-height: 650px!important;
  overflow: auto;
}