.el-table {
  font-size: 14px;
}

.el-dropdown-menu--small .el-dropdown-menu__item {
  font-size: 14px;
}

.el-pagination {
  color: red;
  font-weight: normal;
  button {
    min-width: 28px;
    border: 1px solid #dbdbdb;
    &.btn-prev {
      padding-right: 5px;
      &:hover {
        color: #42c57a;
      }
      &.disabled {
        color: #b4bccc;
      }
    }
    &.btn-next {
      margin-left: 10px;
      padding-left: 5px;
      &:hover {
        color: #42c57a;
      }
      &.disabled {
        color: #b4bccc;
      }
    }
  }
  &__total {
    margin-right: 10px;
    font-weight: 400;
    color: #5a5e66;
  }
  &-box {
    display: flex;
    flex-direction: row-reverse;
    padding: 20px 0;
  }
}

.el-pager li {
  border: 1px solid #dbdbdb;;
  border-radius: 1px;
  margin-left: 10px;
  min-width: 28px;
  font-size: 10px;
  color: #333333;
  &:hover {
    color: #42c57a;
  }
  &.active {
    color: #ffffff !important;
    background-color: #42c57a;
    &:hover {
      color: white;
      cursor: pointer;
    }
    & + li {
      border: 1px solid #dbdbdb !important;
    }
  }
}
