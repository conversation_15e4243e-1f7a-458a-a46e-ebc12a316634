/* stylelint-disable */
//主色
@zyb-green-1: #42c57a;
@zyb-green-2: #52cc87;
@zyb-green-3: #38bc6d;
@zyb-green-hover: #68d195;

//字体色
@zyb-font-grey-1: #323233;
@zyb-font-grey-2: #525354;
@zyb-font-grey-3: #757678;
@zyb-font-grey-4: #969799;
@zyb-font-grey-5: #b9babd;

//辅助色
@zyb-red-1: #fa574b;
@zyb-red-2: #f9685d;
@zyb-red-3: #de4f43;
@zyb-orange-1: #f78502;
@zyb-orange-2: #f9b001;
@zyb-green-4: #42c57a;
@zyb-lake-blue: #00cccd;
@zyb-purple: #8276de;
@zyb-blue: #5392ff;
@zyb-blue-grey-1: #83868f;
@zyb-blue-grey-2: #afb0b3;
@zyb-light-yellow: #fff7d1;
@zyb-light-green: #e6faef;
@zyb-light-blue: #e6f3fa;

//中性色
@zyb-grey-1: #d7d8db;
@zyb-grey-2: #e1e2e6;
@zyb-grey-3: #ebecf0;
@zyb-grey-4: #f0f1f5;
@zyb-grey-5: #f3f4f7;
@zyb-grey-6: #f7f8fb;

//提示条
@zyb-remind-blue: #d2edfa;
@zyb-remind-green: #d2fadf;
@zyb-remind-yellow: #faedd2;
@zyb-remind-red: #ffeeeb;
@zyb-remind-black: #494b4d;
