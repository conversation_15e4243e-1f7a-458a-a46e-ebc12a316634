<?xml version="1.0" encoding="UTF-8"?>
<svg width="80px" height="80px" viewBox="0 0 80 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.2 (67145) - http://www.bohemiancoding.com/sketch -->
    <title>暂无数据</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <path d="M23.9612497,26 L56.0387503,26 C56.6463177,26 57.2209436,26.2761794 57.6004879,26.7506099 L64.5617376,35.452172 C64.8454385,35.8067981 65,36.2474191 65,36.7015621 L65,52 C65,53.1045695 64.1045695,54 63,54 L17,54 C15.8954305,54 15,53.1045695 15,52 L15,36.7015621 C15,36.2474191 15.1545615,35.8067981 15.4382624,35.452172 L22.3995121,26.7506099 C22.7790564,26.2761794 23.3536823,26 23.9612497,26 Z" id="path-1"></path>
        <filter x="-4.0%" y="-7.1%" width="108.0%" height="114.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="2" dy="2" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M24,26 L56,26 C56.5522847,26 57,26.4477153 57,27 L57,55 L23,55 L23,27 C23,26.4477153 23.4477153,26 24,26 Z" id="path-3"></path>
        <filter x="-5.9%" y="-6.9%" width="111.8%" height="113.8%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="2" dy="2" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <path d="M65,52.9903803 C65,54.1002626 64.1109357,55 63.0090859,55 L16.9909141,55 C15.8913626,55 15,54.1090746 15,53.0025781 L15,37.9974219 C15,36.8942762 15.8926228,36 16.9950893,36 L31.0049107,36 C32.1067681,36 33.4016683,36.8033365 33.895496,37.7909921 L34.604504,39.2090079 C35.0990728,40.1981455 36.3958578,41 37.4973917,41 L42.5026083,41 C43.6057373,41 44.9016683,40.1966635 45.395496,39.2090079 L46.104504,37.7909921 C46.5990728,36.8018545 47.8926228,36 48.9950893,36 L63.0049107,36 C64.1067681,36 65,36.8978581 65,38.0080394 L65,51.8549422 L65,52.9903803 Z" id="path-5"></path>
        <filter x="-4.0%" y="-10.5%" width="108.0%" height="121.1%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="2" dy="2" in="SourceAlpha" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="组件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="暂无数据">
            <g>
                <circle id="Oval" fill="#F5F8FC" cx="40" cy="40" r="40"></circle>
                <rect id="Rectangle" fill="#E6EBF5" x="15" y="55" width="50" height="4" rx="2"></rect>
                <path d="M61.3801996,52.155111 L62.9041585,52.155111 L62.9041585,53.6750664 C62.9041585,52.8297657 62.2218588,52.155111 61.3801996,52.155111 Z" id="Rectangle-189-Copy-3" fill="#D6DADD" transform="translate(62.142179, 52.915089) scale(1, -1) translate(-62.142179, -52.915089) "></path>
                <path d="M61.3801996,36.4489043 L62.9041585,36.4489043 L62.9041585,37.9688598 C62.9041585,37.1235591 62.2218588,36.4489043 61.3801996,36.4489043 Z" id="Rectangle-189-Copy-3" fill="#D6DADD"></path>
                <g id="Path-4">
                    <use fill="#EFF2F7" fill-rule="evenodd" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                    <use stroke="#CFD7E6" stroke-width="2" xlink:href="#path-1"></use>
                </g>
                <g id="Rectangle">
                    <use fill="#EFF2F7" fill-rule="evenodd" xlink:href="#path-3"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    <use stroke="#CFD7E6" stroke-width="2" xlink:href="#path-3"></use>
                </g>
                <g id="Stroke-2">
                    <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-5"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                    <use stroke="#CFD7E6" stroke-width="2" xlink:href="#path-5"></use>
                </g>
            </g>
        </g>
    </g>
</svg>