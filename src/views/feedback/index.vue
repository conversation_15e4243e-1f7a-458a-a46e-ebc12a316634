<template>
  <Page class="container">
    <section class="header">
      <section class="page-title">问题反馈列表</section>
      <el-button class="btn_add" type="primary" v-if="selections.length" @click="batchDeal">批量处理</el-button>
    </section>
    <div class="search">
      <el-form size="small" ref="searchForm" inline>
        <el-form-item label="">
          <el-input size="small" class="keyword_input" placeholder="问题描述/提交人" v-model="query.keyword" @change="handleSearch" clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="问题类型：">
          <el-select v-model="query.type" @change="handleSearch">
            <el-option label="全部" :value="0"></el-option>
            <el-option label="bug反馈" :value="1"></el-option>
            <el-option label="体验问题" :value="2"></el-option>
            <el-option label="功能建议" :value="3"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="状态：">
          <el-select v-model="query.status" @change="handleSearch">
            <el-option label="全部" :value="0"></el-option>
            <el-option label="未处理" :value="1"></el-option>
            <el-option label="已处理" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提交时间：">
          <el-date-picker v-model="query.time" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleSearch" value-format="timestamp">
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-content">
      <template-table :selection="true" :selectable="selectable" @getCheckedRow="getCheckedRow" :hasIndex="false" class="detail-table" ref="multipleTableParent" :table-column="tableColumns" :table-list="tableList" :tableBtns="tableBtns"
        @operationEvent="operationEvent">
      </template-table>
      <template-pagination :pagination-init="paginationInit" @changePage="changePage" @pageSizeChange="pageSizeChange">
      </template-pagination>
    </div>
    <el-dialog title="处理" :visible.sync="dealDialog.show" width="450px" :destroy-on-close="true">
      <Deal :ids="dealDialog.ids" @close="closeDealDailog"></Deal>
    </el-dialog>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import { formatDate } from '@/common/utils';
import TemplateTable from '@/components/tools/template-table.vue';
import TemplatePagination from '@/components/tools/template-pagination.vue';
import { omit } from 'lodash';
import Deal from './deal.vue';
export default {
  name: 'Feedback',
  components: {
    Page,
    TemplateTable,
    TemplatePagination,
    Deal
  },
  data() {
    return {
      breadcrumbList: ['Feedback'],
      paginationInit: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      tableBtns: [
        {
          label: '处理',
          role: 'deal',
          showHandler(index, row) {
            return row.resultType < 2;
          }
        },
        {
          label: '查看结果',
          role: 'view',
          showHandler(index, row) {
            return row.resultType >= 2;
          }
        }
      ],
      query: {
        keyword: '',
        type: 0,
        status: 0,
        time: []
      },
      total: 0,
      tableList: [{
        createTime: 1730187707,
        id: 1322411253
      }],
      tableColumns: [
        {
          label: 'ID',
          name: 'id'
        },
        {
          label: '问题描述',
          name: 'description',
          'min-width': '185px'
        },
        {
          label: '问题类型',
          name: 'type',
          render: (h, { row }) => {
            const { type } = row;
            const map = { '0': '全部', '1': 'bug反馈', '2': '体验问题', 3: '功能建议' };
            return <p>{map[type]}</p>;
          }
        },
        {
          label: '状态',
          name: 'resultType',
          render: (h, { row }) => {
            const { resultType } = row;
            const text = resultType > 1 ? '已处理' : '未处理';
            return <p>{text}</p>;
          }
        },
        {
          label: '提出人',
          name: 'createUser',
          'min-width': '105px',
        },
        {
          label: '提交时间',
          name: 'createTime',
          render: (h, { row }) => {
            const { createTime } = row;
            return <p>{formatDate(createTime * 1000, `yyyy-MM-dd HH:mm:ss`)}</p>;
          }
        }
      ],
      dealDialog: {
        ids: [],
        show: false
      },
      selections: []
    };
  },
  computed: {},
  methods: {
    selectable(row) {
      return row.resultType < 2;
    },
    getCheckedRow(rowList) {
      this.selections = rowList.map(item => item.id);
    },
    pageSizeChange(e) {
      this.paginationInit.pageSize = e;
      this.getList();
    },
    changePage(page) {
      this.paginationInit.currentPage = page;
      this.getList();
    },
    operationEvent(row, btnRole) {
      switch (btnRole) {
        case 'view':
          this.$router.push({
            path: '/system/feedback/view',
            query: { id: row.id }
          });
          break;
        case 'deal':
          this.$router.push({
            path: '/system/feedback/view',
            query: { id: row.id, edit: 1 }
          });
          break;
      }
    },
    batchDeal() {
      this.dealDialog.show = true;
      this.dealDialog.ids = this.selections;
    },
    // 查询列表数据
    handleSearch() {
      this.paginationInit.currentPage = 1;
      this.getList();
    },
    getList() {
      let params = {
        pn: this.paginationInit.currentPage,
        rn: this.paginationInit.pageSize
      };
      const [createTimeStart, createTimeEnd] = this.query.time || [];
      const query = omit(this.query, ['time']);
      this.$service
        .get('QUESTION_LIST', { ...query, ...params, createTimeStart: createTimeStart ? createTimeStart / 1000 : undefined, createTimeEnd: createTimeEnd ? createTimeEnd / 1000 + 86399 : undefined }, { needLoading: true })
        .then((res) => {
          this.tableList = res.list || [];
          this.paginationInit.total = res.total;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 重置
    handleReset() {
      this.paginationInit.currentPage = 1;
      this.query = {
        id: '',
        keyword: ''
      };
      this.getList();
    },
    closeDealDailog(refresh) {
      this.dealDialog.show = false;
      if (refresh) {
        this.handleSearch();
      }
    }
  },
  created() {
    this.handleSearch();
  },
};
</script>

<style lang="less" scoped>
.container {
  margin-top: 24px;

  /deep/ .page-container {
    padding-top: 16px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;
    margin-bottom: 12px;

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
    }
  }
}

.search {
  position: relative;

  .el-select {
    width: 175px;
  }
}

.keyword_input {
  width: 270px;
  margin-left: -1px;

  /deep/ .el-input__suffix {
    right: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    cursor: pointer;
  }
}

/deep/ .el-table {
  font-size: 14px;
}

.table-pagination {
  margin-top: 20px;
  text-align: right;
}

.table_description {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.related_flights_text {
  color: rgb(70, 96, 239);
  cursor: pointer;
}

.table_tag {
  display: inline-block;
  border-radius: 4px;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  color: #2f2f3f;
  background: #f8f8fc;
  border: 1px solid #e7e9f5;
}

.table_tag_required {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
}

.action_edit {
  margin-right: 10px;
}

/deep/ .op-switch.is-disabled .el-switch__core,
/deep/ .op-switch.is-disabled .el-switch__label {
  background-color: rgb(155, 156, 158);
  border-color: rgb(155, 156, 158);
  cursor: pointer;
}

/deep/ .el-switch.is-disabled.is-checked .el-switch__core,
/deep/ .op-switch.is-disabled.is-checked .el-switch__label {
  border-color: #42c57a;
  background-color: #42c57a;
}

/deep/ .el-button--text-primary>span {
  font-size: 14px;
}
</style>