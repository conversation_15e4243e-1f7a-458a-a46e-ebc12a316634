<template>
  <Page class="container" :breadcrumbList="[]" :hasPagination="false">
    <section class="header">
      <section>
        <i class="el-icon-arrow-left" @click="handleGoBack(true)"></i>
        <span class="page-title">问题处理结果</span>
      </section>
      <section v-if="edit">
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="close">取消</el-button>
      </section>
    </section>
    <el-row :gutter="32" style="padding: 0 12px">
      <el-col :span="12">
        <p class="form-title">问题内容</p>
        <el-form :disabled="true" style="padding-left: 16px" :model="detail">
          <el-form-item label="问题类型">
            <el-radio-group v-model="detail.type">
              <el-radio :label="1">bug反馈</el-radio>
              <el-radio :label="2">体验问题</el-radio>
              <el-radio :label="3">功能建议</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="问题描述">
            <el-input v-model="detail.description" placeholder="请详细描述建议或反馈的内容，如：我建议***产品增加***功能，我发现***产品存在***问题等" type="textarea" :rows="4" />
          </el-form-item>
          <el-form-item v-if="detail.cosAddress">
            <el-image style="width: 100px; height: 100px" :src="detail.cosAddress" :preview-src-list="[detail.cosAddress]">
            </el-image>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="12">
        <p class="form-title">处理结果</p>
        <el-form label-position="right" :disabled="!edit" style="padding-left: 16px" :model="detail">
          <el-form-item label="处理结果" prop="resultType">
            <el-radio-group v-model="detail.resultType">
              <el-radio :label="2">已处理</el-radio>
              <el-radio :label="3">不处理</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="处理说明" prop="result" class="flex-form-item">
            <el-input v-model="detail.result" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }"></el-input>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
  </Page>
</template>
<script>
import Page from '@/components/common/page/index.vue';
import { formatFormData } from '@/utils/util';

export default {
  name: 'FeedbackView',
  components: {
    Page
  },
  data() {
    return {
      edit: !!this.$route.query.edit || false,
      detail: {
        "type": 1,  // 1 bug反馈   2 体验问题  3 功能建议
        "description": "",
        "cosAddress": "", // 图片地址
        "resultType": 2,//   2 已处理   3 不处理
        "result": "",  // 处理说明
      }
    };
  },
  computed: {},
  methods: { 
    handleGoBack() {
      this.$router.push({
        path: '/system/feedback'
      });
    },
    getDetail() {
      const { id } = this.$route.query;
      this.$service.get('QUESTION_VIEW', { id }).then((res) => {
        this.detail = {
          ...res
        };
        if (!this.detail.resultType) {
          this.detail.resultType = 2;
        }
      });
    },
    close() {
      this.$router.push({
        path: '/system/feedback',
      });
    },
    save() {
      if (!this.detail.result) {
        this.$message.error(`请完善处理说明！`);
        return;
      }
      const { id } = this.$route.query;
      const data = formatFormData({
        ids: [+id],
        ...this.detail
      });
      this.$service
        .post('QUESTION_UPDATE', data, { needLoading: true })
        .then(() => {
          this.close(true);
        });
    }
  },
  created() {
    this.getDetail();
  },
};
</script>
<style lang="less" scoped>
.container {
  overflow: auto;
  // height: calc(100vh - 100px);
  // padding: 24px;
  padding: 12px 16px;
  box-sizing: border-box;
  display: flex;

  /deep/ .page-container {
    padding-top: 0px;
    padding-bottom: 6px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    section {
      display: flex;
      align-items: center;
    }

    .right {
      /deep/ i {
        // display: inline-block;
        width: 16px;
        height: 16px;
        margin-left: 4px;
        margin-right: 6px;
        display: inline-block;
      }

      .el-button {
        margin-left: 12px;
      }

      .icon-yunhang {
        // width: 18px;
        // height: 18px;
      }
    }

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin: 0 8px;
    }

    i {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .el-tag {

      // @zyb-red-1: #fa574b;
      // @zyb-red-2: #f9685d;
      // @zyb-red-3: #de4f43;
      // @zyb-orange-1: #f78502;
      // @zyb-orange-2: #f9b001;
      // @zyb-green-4: #42c57a;
      // @zyb-lake-blue: #00cccd;
      // @zyb-purple: #8276de;
      // @zyb-blue: #5392ff;
      &.el-tag--default {
        color: #5392ff;
        background-color: #d9ecff;
        border-color: #d9ecff;
      }
    }
  }
}
</style>