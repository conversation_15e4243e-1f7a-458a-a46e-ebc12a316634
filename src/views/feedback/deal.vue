<template>
  <section class="component-container">
    <el-form :model="formData" label-position="right">
      <el-form-item label="处理结果" prop="resultType" required>
        <el-radio-group v-model="formData.resultType">
          <el-radio :label="2">已处理</el-radio>
          <el-radio :label="3">不处理</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="处理说明" prop="result" class="flex-form-item" required>
        <el-input v-model="formData.result" type="textarea" :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
      </el-form-item>
    </el-form>
    <section class="footer">
      <el-button @click="close(false)">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </section>
  </section>
</template>
<script>
import { formatFormData } from '@/utils/util';

export default {
  name: 'Feedback',
  props: {
    ids: []
  },
  components: {
  },
  data() {
    return {
      formData: {
        resultType: 2,
        result: ''
      }
    };
  },
  computed: {},
  methods: {
    confirm() {
      if (!this.formData.result) {
        this.$message.error(`请完善处理说明！`);
        return;
      }
      const data = formatFormData({
        ids: this.ids,
        ...this.formData
      });
      this.$service
        .post('QUESTION_UPDATE', data, { needLoading: true })
        .then(() => {
          this.close(true);
        });
    },
    close(refresh) {
      this.$emit('close', refresh);
    }
  },
  created() {
  },
};
</script>
<style lang="less" scoped>
.component-container {
  padding: 0 24px;

  .footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
  }
}

.flex-form-item {
  display: flex;

  /deep/ .el-form-item__label {
    flex-shrink: 0;
  }

  /deep/ .el-form-item__content {
    flex: 1;
  }
}
</style>