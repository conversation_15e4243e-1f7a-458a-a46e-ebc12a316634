<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-17 17:12:19
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-08-04 16:36:04
 * @Description: 实验列表
-->

<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
    <div class="select-container">
      <div>
        <el-form ref="searchForm" inline>
          <el-form-item>
            <el-input
              style="width: 220px"
              class="key-world-input"
              placeholder="互斥组名称/创建人"
              v-model="formData.keyword"
              clearable
              @change="handleSearch"
            ></el-input>
          </el-form-item>
          <el-form-item label="业务线：">
            <el-select
              v-model="formData.appKey"
              style="width: 150px"
              placeholder="请选择业务线"
              @change="handleSearch"
            >
              <el-option
                v-for="item in appList1"
                :key="item.appKey"
                :label="item.displayName"
                :value="item.appKey"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账号体系：">
            <el-select
              style="width: 150px"
              v-model="formData.accountSystem"
              placeholder="请选择"
              @change="handleSearch"
            >
              <el-option
                v-for="item in accSystemData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="实验类型：">
            <el-select
              v-model="formData.clientType"
              style="width: 150px"
              @change="handleSearch"
            >
              <el-option
                v-for="item in clientTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <el-button class="btn_add" type="primary" @click="createMutu">新建互斥组</el-button>
    </div>
    <mutu-table
      :tableList="tableList"
      :appKey="formData.appKey"
      @handelEditMutu="handelEditMutu"
      @handleSearch="handleSearch"
    ></mutu-table>

    <div slot="pagination">
      <el-pagination
        v-show="total"
        layout="total,sizes, prev, pager, next, jumper"
        :page-size="pagination.rn"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="pageSizeChange"
        :current-page="pagination.pn"
        :total="total"
        @current-change="pageNoChange"
      ></el-pagination>
    </div>
    <create-dialog
      v-if="isShowDialog"
      :dialogVisible.sync="isShowDialog"
      :editId="editId"
      @handleSearch="handleSearch"
      @resetEditId="resetEditId"
    ></create-dialog>
  </Page>
</template>

<script>
import { formatDate } from '@/common/utils';
import Page from '@/components/common/page/index.vue';
import { mapGetters } from 'vuex';
import CreateDialog from './components/create-dialog.vue';
import MutuTable from './components/mutu-table.vue';
export default {
  name: 'MutuList', //互斥组列表
  components: {
    Page,
    MutuTable,
    CreateDialog
  },
  // mixins: [PageCommon],
  data() {
    return {
      editId: 0,
      isShowDialog: false,
      breadcrumbList: ['Muexc'],
      //statusList,
      // appKeyList,
      formData: {
        appKey: 'all',
        keyword: '',
        accountSystem: '',
        clientType: 0
      },
      pagination: {
        pn: 1,
        rn: 10
      },
      total: 0,
      tableList: [],
      showTableModal: false,
      currentTaskId: 0,
      activityIdList: [],
      statusList: [
        {
          value: 1,
          label: '使用中'
        },
        {
          value: 0,
          label: '未上线'
        }
      ],
      clientTypes: [{
        value: 0,
        label: '全部'
      },
      {
        value: 1,
        label: '客户端'
      },
      {
        value: 2,
        label: '服务端'
      }],
    };
  },
  computed: {
    ...mapGetters(['appList1', 'accSystemData'], 'message')
  },
  created() {
    this.$store.dispatch('getAppList');
    this.handleSearch();
  },
  methods: {
    // 当用户在新建的时候，同步到列表
    resetEditId(val) {
      this.editId = 0;
    },
    // 处理数据格式
    formatData(data) {
      return data.reduce((pre, cur) => {
        pre.push({
          label: cur.activityName,
          value: cur.activityId
        });
        return pre;
      }, []);
    },
    // 重置
    handleReset() {
      this.pagination.pn = 1;
      this.formData = {
        appKey: 'all',
        createUser: '',
        status: ''
      };
      this.getList();
    },
    //
    pageSizeChange(rn) {
      this.pagination.rn = rn;
      this.getList();
    },
    //
    pageNoChange(pn) {
      this.pagination.pn = pn;
      this.getList();
    },
    // 查询列表数据
    handleSearch() {
      this.pagination.pn = 1;
      this.getList();
    },
    // 列表数据
    getList() {
      this.$service
        .get('AB_EXCLUSIVE', { ...this.formData, ...this.pagination }, { needLoading: true })
        .then((data) => {
          this.tableList = data.list;
          //this.formatResult(data);
          this.total = data.total;
          this.pagination.rn = data.rn;
          this.pagination.pn = data.pn;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    formatResult(data) {
      this.tableList = data.list.map((item) => {
        item.startTime = item.startTime ? formatDate(item.startTime * 1000, 'yyyy-MM-dd') : '-';
        item.endTime = item.endTime ? formatDate(item.endTime * 1000, 'yyyy-MM-dd') : '至今';
        if (item.status == 1 || item.status == 2) {
          item.actionTime = '~';
        } else {
          item.actionTime = `${item.startTime} ~ ${item.endTime}`;
        }
        return item;
      });
    },
    // 创建邀请任务
    createMutu() {
      this.isShowDialog = true;
    },
    // 编辑互斥组
    handelEditMutu(id) {
      this.editId = id;
      // debugger
      this.isShowDialog = true;
    }
  }
};
</script>

<style lang="less" scoped>
.head-title {
  width: 100%;
  margin-top: 20px;
  p {
    padding: 0 20px;
    box-sizing: border-box;
    line-height: 26px;
    color: #fa574b;
  }
}
.select-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  padding: 20px 0 5px;
  // /deep/ .el-form .el-form-item .el-form-item__label {
  //   padding: 0 !important;
  // }
}
/deep/ .page-tab {
  margin: 0;
}
.key-world-input {
  width: 280px;
}
/deep/ .el-button--medium > span {
  font-size: 14px;
  vertical-align: middle;
}

.btn_add {
  position: absolute;
  top: 20px;
  right: 0;
}

.self-collapse-btns {
  height: 33px;
}
</style>