<template>
  <el-dialog :title="title" :visible="dialogVisible" :before-close="dialogClose" width="450px">
    <!-- <h3 class="tip">
      在互斥组的实验，相互之间是互斥的，这意味着应用的用户只能命中该互斥组中的一个实验。
    </h3> -->
    <el-form ref="mutuformBase" :model="addData" label-width="110px" :rules="rules" style="max-width: 300px;">
      <el-form-item label="业务线：" required prop="appKey">
        <el-select :disabled="disableEdit" v-model="addData.appKey" placeholder="请选择业务线" @change="handleChangAppKey">
          <el-option v-for="item in appList" :key="item.id" :label="item.displayName" :value="item.appKey"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="互斥组名称：" required prop="displayName">
        <el-input class="f-width-c" type="text" v-model="addData.displayName" placeholder="请输入互斥组名称"></el-input>
      </el-form-item>
      <el-form-item label="描述：" prop="description">
        <el-input type="textarea" v-model="addData.description" class="f-width-c" rows="3" resize="vertical"
          show-word-limit placeholder="请输入描述信息"></el-input>
      </el-form-item>
      <el-form-item label="账号体系：" required>
        <!-- 账号体系，cuid：0，uid: 1-->
        <el-radio-group v-model="addData.accountSystem" :disabled="disableEdit">
          <el-radio :label="1">cuid</el-radio>
          <el-radio :label="2">uid</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="实验类型：" required prop="clientType">
        <el-radio-group v-model="addData.clientType" :disabled="disableEdit">
          <el-radio :label="1">客户端</el-radio>
          <el-radio :label="2">服务端</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="负责人：" prop="createUser">
        <search-user v-model="addData.createUser" :usedefault="true"></search-user>
      </el-form-item>
    </el-form>
    <!-- 确定取消按钮组 -->
    <section slot="footer" class="dialog-footer">
      <el-button @click="dialogClose">取 消</el-button>
      <el-button type="primary" @click="validateCreate">确定</el-button>
    </section>
  </el-dialog>
</template>
<script>
import { mapGetters } from 'vuex';
import SearchUser from '@/components/search-user/index.vue';
export default {
  name: 'create-dialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    editId: {
      type: Number
    }
  },
  components: {
    SearchUser
  },
  computed: {
    disableEdit() {
      return this.editId ? true : false;
    },
    ...mapGetters(['appList'], 'message')
  },
  data() {
    return {
      addData: {
        clientType: 1,
        appKey: '',
        displayName: '',
        description: '',
        accountSystem: 1, //cuid：1，uid: 2
        createUser: ''
      },
      title: '新建互斥组',
      rules: {
        appKey: [{ required: true, message: '请选择业务线', trigger: 'blur' }],
        displayName: [
          { required: true, message: '互斥组名称不可为空', trigger: 'blur' },
          { required: true, message: '互斥组名称不可为空', trigger: 'change' }
        ],
        createUser: [{ required: true, message: '请选择负责人', trigger: 'blur' }],
      },
    };
  },
  created() {
    if (this.editId) {
      // 获取详情
      this.getMutuDetai();
      this.title = '编辑互斥组';
    }
  },
  mounted() { },
  methods: {
    handleChangAppKey(val) {
      //   this.$emit("changeAppKey",val)
    },
    validateCreate() {
      this.$refs['mutuformBase'].validate((valid) => {
        if (valid) {
          this.handleCreateMutu();
        } else {
          console.log('提交失败');
          return false;
        }
      });
    },
    handleCreateMutu() {
      const formData = this.formatFormData(this.addData);
      let api = this.editId ? 'AB_EXCLUSIVE_UPDATE' : 'AB_EXCLUSIVE_ADD';
      let tip = this.editId ? '编辑成功' : '新建成功';
      this.$service
        .post(api, formData, { needLoading: true })
        .then((data) => {
          if (this.editId) {
            this.$emit('resetEditId');
          }
          this.dialogClose();
          // 在互斥组管理模块，创建成功之后，需要刷新列表
          this.$emit('handleSearch');

          this.$message.success(tip);
        })
        .catch((err) => {
          console.log(err);
        });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
      // this.defaultFormat();
    },
    //获取互斥责详情
    getMutuDetai() {
      this.$service
        .get('AB_EXCLUSIVE_VIEW', { id: this.editId }, { needLoading: true })
        .then((data) => {
          //debugger;
          this.addData = data;
        })
        .catch((err) => {
          console.log(err);
        });
    }
  },
  beforeDestroy() {
    this.$emit('resetEditId');
  },
  watch: {
    appList: {
      handler(val) {
        if (val.length && !this.addData.appKey) {
          this.addData.appKey = val[0].appKey;
        }
      },
      immediate: true,
      deep: true
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 760px;
  min-width: 30%;
}

.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}

.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;

  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }

  .detail-value {
    width: 200px;

    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }

  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;

    span {
      padding: 0 11px;
    }
  }

  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }

  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}

/deep/ .divider-con {
  margin: 20px 0;
}

.tip {
  padding: 10px 10px 20px;
}

/deep/ .el-radio__label {
  font-size: 14px;
}
</style>
