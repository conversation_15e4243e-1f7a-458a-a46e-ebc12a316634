<!--
 * @Description:
 * @Author: huy<PERSON>ei
 * @LastEditors  : Please set LastEditors
 * @Date: 2021-06-17 17:12:19
 * @LastEditTime : Please set LastEditTime
 -->
<template>
  <div>
    <zyb-table :table-data="tableList" :table-columns="tableColumns">
      <template v-slot:custom="{ data: { item, scope } }">
        <template v-if="item.prop === 'experimentCount'">
          <a @click="checkMutu(scope.row)">{{ scope.row.experimentCount || 0 }}</a>
        </template>
        <template v-else-if="item.prop === 'displayName'">
          <el-popover placement="top" trigger="hover">
            <span>{{ scope.row[item.prop] }}</span>
            <span slot="reference">{{ scope.row.displayName }}</span>
          </el-popover>
        </template>
        <template v-else-if="item.prop === 'description'">
          <el-popover placement="top" trigger="hover" v-if="scope.row.description">
            <span>{{ scope.row[item.prop] }}</span>
            <span slot="reference">{{ scope.row.description }}</span>
          </el-popover>
          <span v-else>-</span>
        </template>
        <template v-else-if="item.prop === 'action'">
          <zyb-text @click="editMutu(scope.row.id)">编辑</zyb-text>
          <zyb-text v-if="+scope.row.useFlow === 0" @click="deleteMutu(scope.row)">删除</zyb-text>
          <!-- <zyb-text v-if="scope.row.isDisplay === 0" @click="handleBeforeLine(scope.row, 0)">
            上线
          </zyb-text>
          <zyb-text
            v-if="scope.row.isDisplay === 1"
            class="warn-tip"
            :disabled="scope.row.experimentCount > 0"
            @click="handleBeforeLine(scope.row, 1)"
          >
            下线
          </zyb-text> -->
        </template>
      </template>
    </zyb-table>

    <detail-dialog
      v-if="isShowDialog"
      :dialogVisible.sync="isShowDialog"
      :checkedId="checkedId"
      :name="mutuName"
    ></detail-dialog>
  </div>
</template>
<script>
import { formatDate } from '@/common/utils';
import ZybTable from '@/components/zyb-table/index.vue';
import ZybText from '@/components/zyb-text/index.vue';
import DetailDialog from './detail-dialog.vue';

export default {
  name: 'mutu-table',
  components: { DetailDialog, ZybTable, ZybText },
  data() {
    return {
      checkedId: 0,
      isShowDialog: false,
      mutuName: '',
      currentTaskId: 0,
      showRobotDetail: false,
      tableColumns: [
        {
          label: 'ID',
          prop: 'id'
        },
        {
          label: '业务线',
          prop: 'appDisplayName',
          title: true,
          line: 2
        },
        {
          label: '账号体系',
          prop: 'accountSystem',
          render: (scope) => {
            return scope.row.accountSystem == 1 ? 'cuid' : 'uid';
          }
        },
        {
          label: '名称',
          prop: 'displayName',
          title: false,
          line: 1,
          custom: true
        },
        {
          label: '实验类型',
          prop: 'clientType',
          width: 150,
          render: (scope) => {
            return scope.row.clientType == 1 ? '客户端' : '服务端';
          }
        },
        {
          label: '实验数量',
          prop: 'experimentCount',
          width: 150,
          custom: true
        },
        {
          label: '可用流量',
          prop: 'freeFlow',
          render: (scope) => {
            return scope.row.freeFlow > 0 ? scope.row.freeFlow / 10 + '%' : '0%';
          }
        },
        {
          label: '创建人',
          prop: 'createUser',
          custom: true
        },
        {
          label: '创建时间',
          prop: 'createTime',
          render: (scope) => {
            return formatDate(scope.row.createTime * 1000);
          }
        },
        {
          label: '操作',
          prop: 'action',
          custom: true,
          width: 230
        }
      ]
    };
  },
  props: {
    tableList: {
      type: Array
    },
    appKey: {
      type: String
    }
  },
  mounted() {
    console.log(this.appKey);
  },
  filters: {
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'yyyy-MM-dd');
      }
      return '-';
    }
    // handleStatus(status) {
    //   const statusData = statusList;
    //   return statusData[status].label;
    // },
  },
  methods: {
    // 编辑任务
    editMutu(id) {
      this.$emit('handelEditMutu', id);
    },
    deleteMutu(row){
      const { displayName, id } = row;
      this.$confirm(`确定删除互斥组${displayName}？删除后将无法恢复！`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      .then(()=>{
        console.log('id', id);
        this.$service.get('AB_EXCLUSIVE_DELETE', { id }, { allback: 0, needLoading: true }).then((res) => {
          this.$emit('handleSearch');
          this.$message.success(`删除成功`);
        });
      });
    },
    // 查看任务
    checkMutu(row) {
      const { id } = row;
      this.checkedId = id;
      this.isShowDialog = true;
      this.mutuName = row.displayName;
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    // 验证下线或下线
    handleBeforeLine(data, type) {
      const api = type === 1 ? 'AB_EXCLUSIVE_HIDE' : 'AB_EXCLUSIVE_SHOW'; // 1代表去下线，0代表去上线
      const tip = type === 1 ? '确认下线互斥组' : '确认上线互斥组';
      this.$confirm(`${tip}：${data.displayName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleLine(data.id, type);
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    // 下线和上线的具体操作
    handleLine(id, type) {
      const tip = type === 1 ? '下线成功' : '上线成功';
      const api = type === 1 ? 'AB_EXCLUSIVE_HIDE' : 'AB_EXCLUSIVE_SHOW';
      const data = this.formatFormData({ id });
      //debugger;
      this.$service.post(api, data, { allback: 0, needLoading: true }).then((res) => {
        this.$emit('handleSearch');
        this.$message.success(`${tip}`);
      });
    },
    // 验证上线
    handleBefOnline(data) {
      const that = this;
      this.$confirm(`确认上线互斥组：${data.displayName}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleOffline(data.id);
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    // 上线
    handleOffline(id) {
      const data = this.formatFormData({ id });
      //debugger;
      this.$service
        .post('AB_EXCLUSIVE_SHOW', data, { allback: 0, needLoading: true })
        .then((res) => {
          this.$emit('handleSearch');
          this.$message.success('上线成功');
        });
    }
  }
};
</script>
<style lang="less" scoped>
/deep/ .warn-tip:focus {
  color: #fa574b;
}
/deep/ .el-button.is-disabled {
  background-color: transparent;
  color: #bbbcbf;
}
/deep/ .el-table {
  font-size: 14px;
}
/deep/ .el-button--text-primary > span {
  font-size: 14px;
}
/deep/ .el-button + .el-button {
  margin-left: 12px;
}

.warn-tip {
  color: #fa574b;
  &:hover {
    color: #fa574b;
  }
}
</style>
