<template>
  <el-dialog :title="title" :visible="dialogVisible" :before-close="dialogClose" width="80%">
    <div>
      <el-progress
        :percentage="detailData.useFlow ? detailData.useFlow / 10 : 0"
        :show-text="false"
        :stroke-width="20"
        color="#42c57a"
      ></el-progress>
      <section class="percent-detail">
        <span>已使用流量: {{ detailData.useFlow ? detailData.useFlow / 10 : 0 }}%</span>
        <span>剩余可用流量: {{ detailData.useFlow ? (1000 - detailData.useFlow) / 10 : 100 }}%</span>
      </section>
    </div>
    <section class="percent-detail">
      <span>实验数量: {{ detailData.experimentList ? detailData.experimentList.length : 0 }}</span>
      <span>流量层类型: {{ detailData.clientType === 1 ? '客户端' : '服务端' }}</span>
    </section>
    <zyb-table :table-data="detailData.experimentList" :table-columns="tableColumns">
      <template v-slot:custom="{ data: { item, scope } }">
        <template v-if="item.prop === 'status'">
          <el-button
            type="text"
            class="status-btn"
            :style="{ backgroundColor: statusConf[scope.row.status][1] }"
          >
            {{ statusConf[scope.row.status][0] }}
          </el-button>
        </template>
        <template v-if="item.prop === 'displayName'">
          <a @click="checkExp(scope.row)">{{ scope.row.displayName }}</a>
        </template>
      </template>
    </zyb-table>

    <!-- 确定取消按钮组 -->
    <!-- <section slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogClose">取 消</el-button>
      <el-button size="small" type="primary" @click="dialogClose">确定</el-button>
    </section> -->
  </el-dialog>
</template>
<script>
import { formatDate } from '@/common/utils';
import ZybTable from '@/components/zyb-table/index.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'detail-dialog',
  components: { ZybTable },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    checkedId: {
      type: Number
    },
    name: {
      type: String
    }
  },
  computed: {
    ...mapGetters(['appList'], 'message'),
    title(){
      return `「${this.name}」互斥组实验列表`;
    }
  },
  data() {
    return {
      detailData: {},
      addData: {
        clientType: 1,
        appKey: '',
        displayName: '',
        description: ''
      },
      statusConf: {
        0: ['默认', '#f50'],
        1: ['草稿', '#2db7f5'],
        2: ['调试中', '#87d068'],
        3: ['发布中', '#108ee9'],
        4: ['结束', 'gray'],
        5: ['继承待发布', 'yellow'],
        6: ['暂停', 'gray'],
        7: ['冻结', 'gray']
      },
      tableColumns: [
        {
          label: 'ID',
          prop: 'id'
        },
        {
          label: '实验名称',
          prop: 'displayName',
          title: true,
          line: 2,
          custom: true
        },
        {
          label: '实验描述',
          prop: 'description',
          title: true,
          line: 2
        },
        {
          label: '创建者',
          prop: 'createUser',
          width: 150
        },
        {
          label: '实验状态',
          prop: 'status',
          custom: true
        },
        {
          label: '实验流量',
          prop: 'flow',
          width: 150,
          render: (scope) => {
            return scope.row.flow > 0 ? scope.row.flow / 10 + '%' : '0%';
          }
        },
        {
          label: '实验开启时间',
          prop: 'startTime',
          title: true
          // render: (scope) => {
          //   return this.handleFormatDate(scope.row.startTime);
          // }
        },
        {
          label: '实验结束时间',
          prop: 'stopTime',
          title: true
          // render: (scope) => {
          //   return scope.row.endTime || '-';
          // }
        }
      ]
    };
  },
  created() {
    if (this.checkedId) {
      // 获取详情
      this.getMutuDetai();
    }
  },
  mounted() {},
  methods: {
    checkExp(row) {
      const hash = '/exp-manage/list/edit';
      const params = {
        id: row.id,
        type: 'check',
        ex: row.type
      };
      if (row.parentId / 1) {
        params.parentId = row.parentId;
      }
      const { href } = this.$router.resolve({ path: hash, query: params });
      window.open("/static"+href, "_blank");
    },
    handleFormatDate(val) {
      if (val != 0) {
        return formatDate(val * 1000, 'yyyy-MM-dd');
      }
      return '-';
    },
    handleChangAppKey(val) {
      //   this.$emit("changeAppKey",val)
    },
    validateCreate() {
      this.$refs['mutuformBase'].validate((valid) => {
        if (valid) {
          this.handleCreateMutu();
        } else {
          console.log('提交失败');
          return false;
        }
      });
    },
    handleCreateMutu() {
      const formData = this.formatFormData(this.addData);
      let api = this.editId ? 'AB_EXCLUSIVE_UPDATE' : 'AB_EXCLUSIVE_ADD';
      let tip = this.editId ? '编辑成功' : '新建成功';
      this.$service
        .post(api, formData, { needLoading: true })
        .then((data) => {
          if (this.editId) {
            this.$emit('resetEditId');
          }
          this.dialogClose();
          this.$emit('handleSearch');
          this.$message.success(tip);
        })
        .catch((err) => {
          console.log(err);
        });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
      // this.defaultFormat();
    },
    //获取互斥责详情
    getMutuDetai() {
      this.$service
        .get('AB_EXCLUSIVE_VIEW', { id: this.checkedId }, { needLoading: true })
        .then((data) => {
          //debugger;
          this.detailData = data;
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 65%;
  min-width: 30%;
}
.percent-detail{
  margin: 10px 0;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}
.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }
  .detail-value {
    width: 200px;
    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }
  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;
    span {
      padding: 0 11px;
    }
  }
  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }
  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}
/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}
/deep/ .divider-con {
  margin: 20px 0;
}
.tip {
  display: flex;
  padding: 10px 10px 20px;
  h3 {
    font-weight: 700;
  }
}
.p-con {
  width: 88%;
  margin: 0 auto;
}
/deep/ .status-btn {
  width: 50px;
  color: #fff;
}
/deep/ .status-btn:hover {
  color: #fff;
}
/deep/ .el-progress-bar {
  .el-progress-bar__outer {
    border-radius: inherit;
  }
  .el-progress-bar__inner {
    border-radius: inherit;
  }
} 
</style>
