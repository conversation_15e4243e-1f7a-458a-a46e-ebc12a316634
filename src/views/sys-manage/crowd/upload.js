/* 1.获取临时秘钥data 2.初始化 3.判断上传文件的类型 4.判断文件大小 是否需要分片上传*/

import COS from 'cos-js-sdk-v5';
import axios from 'axios';
import { Message } from 'element-ui';
// 上传人群包之前鉴权
//GETAUTHOR: `${abprefix}/crowd/sts`
// 上传
const actions = {
    local: '/testAddress/earthworm/mis/crowd/sts',
    test: '/earthworm/mis/crowd/sts',
    server: '/earthworm/mis/crowd/sts'
  };
function computedEnv() {
    let hostName = location.hostname;
    let url = '';
    if (/^localhost$/gi.test(hostName)) {
        url = actions.local;
    } else if (/docker|test/gi.test(hostName)) {
        url = actions.test;
    } else {
        url = actions.server;
    }
    return url;
};
export function putObject(file, callback) {
    // console.log(file)
    let file_name = file.name;
    //let url = 'https://vido-api-2020.4kt.net/admin/v2.tengxun/up_sign'; // 秘钥相关信息
    let url = computedEnv();
    axios.post(url).then(response => {
        // 后台接口返回 密钥相关信息
        //debugger;
        const data = response.data.data;
        var credentials = data && data.credentials;
        //debugger;

        // 获取当前时间戳 可以与文件名拼接 为cos.putObject里的参数Key
        // let uploadFileName = Date.parse(new Date())

        if (!data || !credentials) return console.error('credentials invalid');
        // 初始化
        var cos = new COS({
            getAuthorization: function (options, callback) {
                callback({
                    TmpSecretId: credentials.tmpSecretId,
                    TmpSecretKey: credentials.tmpSecretKey,
                    XCosSecurityToken: credentials.sessionToken,
                    StartTime: data.startTime,
                    ExpiredTime: data.expiredTime,
                    expiration: data.expiration,
                    requestId: data.requestId
                });
            }
        });
        // 获取上传文件类型
        let type = file.type.substring(0, 5);
        let size = file.size;
        //debugger;
        // console.log(size / (1024 * 2024))
        if (size / (1024 * 2024) < 50) {
            // console.log('普通上传')
            cos.putObject(
                {
                    Bucket: 'zyb-abtest-group-1253445850', // 存储桶名称
                    Region: 'ap-beijing', // 地区
                    Key: file_name, // 文件名称
                    StorageClass: 'STANDARD',
                    Body: file, // 上传文件对象
                    onHashProgress: function (progressData) {
                        // console.log('校验中', JSON.stringify(progressData))
                    },
                    onProgress: function (progressData) {
                        //v.$refs.elProgress.percentage = progressData.percent*100;
                        //console.log(v.$refs.elProgress.percentage);
                        console.log('上传中', JSON.stringify(progressData));
                        
                    }
                },
                function (err, data) {
                    // console.log('1')
                    // console.log(err)
                    // console.log(data)
                    console.log('999', err, data, data.Location);
                    if (err) {
                        //debugger;
                        Message({ message: '文件上传失败,请重新上传', type: 'error' });
                    } else {
                        //debugger;
                        let fileUrl = 'http://' + data.Location;
                        callback(fileUrl, file_name);
                    }
                }
            );
        } else {
            console.log('上传分块');
            cos.sliceUploadFile(
                {
                    Bucket: 'zyb-abtest-group-1253445850' /* 必须 */,
                    Region: 'ap-beijing' /* 存储桶所在地域，必须字段 */,
                    Key: file_name /* 必须 */,
                    Body: file,
                    onTaskReady: function (taskId) {
                        /* 非必须 */
                        // console.log(taskId)
                    },
                    onHashProgress: function (progressData) {
                        /* 非必须 */
                        // console.log(JSON.stringify(progressData))
                    },
                    onProgress: function (progressData) {
                        /* 非必须 */
                        console.log(JSON.stringify(progressData));
                    }
                },
                function (err, data) {
                    console.log(err);
                    //debugger;
                    console.log(data);
                    if (err) {
                        //debugger;
                        Message({ message: '文件上传失败,请重新上传', type: 'error' });
                    } else {
                        let fileUrl = 'http://' + data.Location;
                        callback(fileUrl, file_name);
                    }
                }
            );
        }
        // if (type === 'video') {
        //   // console.log(size / (1024 * 2024))
        //   if (size / (1024 * 2024) < 50) {
        //     // console.log('普通上传')
        //     cos.putObject(
        //       {
        //         Bucket: 'zyb-abtest-group-1253445850', // 存储桶名称
        //         Region: 'ap-beijing', // 地区
        //         Key: file_name, // 文件名称
        //         StorageClass: 'STANDARD',
        //         Body: file, // 上传文件对象
        //         onHashProgress: function (progressData) {
        //           // console.log('校验中', JSON.stringify(progressData))
        //         },
        //         onProgress: function (progressData) {
        //           // console.log('上传中', JSON.stringify(progressData))
        //         }
        //       },
        //       function (err, data) {
        //         // console.log('1')
        //         // console.log(err)
        //         // console.log(data)
        //         // console.log('999', err, data, data.Location)
        //         if (err) {
        //           Message({ message: '文件上传失败,请重新上传', type: 'error' });
        //         } else {
        //           let fileUrl = 'http://' + data.Location;
        //           callback(fileUrl, file_name);
        //         }
        //       }
        //     );
        //   } else {
        //     // console.log('上传分块')
        //     cos.sliceUploadFile(
        //       {
        //         Bucket: 'zyb-abtest-group-1253445850' /* 必须 */,
        //         Region: 'ap-beijing' /* 存储桶所在地域，必须字段 */,
        //         Key: file_name /* 必须 */,
        //         Body: file,
        //         onTaskReady: function (taskId) {
        //           /* 非必须 */
        //           // console.log(taskId)
        //         },
        //         onHashProgress: function (progressData) {
        //           /* 非必须 */
        //           // console.log(JSON.stringify(progressData))
        //         },
        //         onProgress: function (progressData) {
        //           /* 非必须 */
        //           // console.log(JSON.stringify(progressData))
        //         }
        //       },
        //       function (err, data) {
        //         // console.log(err)
        //         // console.log(data)
        //         if (err) {
        //           Message({ message: '文件上传失败,请重新上传', type: 'error' });
        //         } else {
        //           let fileUrl = 'http://' + data.Location;
        //           callback(fileUrl, file_name);
        //         }
        //       }
        //     );
        //   }
        // } else if (type === 'image') {
        //   cos.putObject(
        //     {
        //       Bucket: 'zyb-abtest-group-1253445850', // 存储桶名称
        //       Region: 'ap-beijing', // 地区
        //       Key: file_name, // 图片名称
        //       StorageClass: 'STANDARD',
        //       Body: file, // 上传文件对象
        //       onHashProgress: function (progressData) {
        //         // console.log('校验中', JSON.stringify(progressData))
        //       },
        //       onProgress: function (progressData) {
        //         // console.log('上传中', JSON.stringify(progressData))
        //         // callback(progressData)
        //       }
        //     },
        //     function (err, data) {
        //       // console.log(err)
        //       // console.log(data)
        //       // console.log('999', err, data, data.Location)
        //       if (err) {
        //         Message({ message: '文件上传失败,请重新上传', type: 'error' });
        //       } else {
        //         let fileUrl = 'http://' + data.Location;
        //         callback(fileUrl, file_name);
        //       }
        //     }
        //   );
        // }
    });
}

export function deleteObject(file, callback) {
    let file_name = file.name;
    let url = 'https://vido-api-2020.4kt.net/admin/v2.tengxun/up_sign'; // 秘钥相关信息
    axios.post(url).then(response => {
        // 后台接口返回 密钥相关信息
        const data = response.data;
        var credentials = data && data.credentials;
        let uploadFileName = Date.parse(new Date());
        var cos = new COS({
            getAuthorization: function (options, callback) {
                callback({
                    TmpSecretId: credentials.tmpSecretId,
                    TmpSecretKey: credentials.tmpSecretKey,
                    XCosSecurityToken: credentials.sessionToken,
                    StartTime: data.startTime,
                    ExpiredTime: data.expiredTime,
                    expiration: data.expiration,
                    requestId: data.requestId
                });
            }
        });
        console.log(file);
        let type = file.type.substring(0, 5);
        if (type === 'video') {
            // console.log('是视频类型')
            cos.deleteObject(
                {
                    Bucket: 'zyb-abtest-group-1253445850' /* 必须 */,
                    Region: 'ap-beijing' /* 存储桶所在地域，必须字段 */,
                    Key: file.name /* 必须 */
                },
                function (err, data) {
                    // console.log(err)
                    // console.log(data)
                    if (data) {
                        console.log('文件删除成功');
                        callback(1);
                    }
                }
            );
        } else if (type === 'image') {
            // console.log('是图片类型')
            cos.deleteObject(
                {
                    Bucket: 'zyb-abtest-group-1253445850' /* 必须 */,
                    Region: 'ap-beijing' /* 存储桶所在地域，必须字段 */,
                    Key: file.name /* 必须 */
                },
                function (err, data) {
                    console.log('图片删除成功');
                    // console.log(err)
                    // console.log(data)
                    if (data) {
                        callback(1);
                    }
                }
            );
        }
    });
}

export default {
    putObject,
    deleteObject
};
