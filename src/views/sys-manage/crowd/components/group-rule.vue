<template>
  <el-form class="com-container" :model="rule" ref="RuleForm">
    <el-select :value="1" style="width: 130px;flex-shrink: 0;" :disabled="disabled">
      <el-option label="用户属性满足" :value="1">
      </el-option>
    </el-select>
    <el-select v-model="rule.ruleKey" @change="ruleKeyChange" style="width: 135px;flex-shrink: 0;" :disabled="disabled">
      <el-option v-for="item in attrs" :key="item.key" :label="item.cname" :value="item.key">
        <el-popover :open-delay="300" placement="right" trigger="hover">
          <div>
            <div style="min-width: 200px">
              <h3 style="font-size: 14px; font-weight: 600;margin-bottom: 8px">参数名: {{ item.cname }}</h3>
              <p style="font-size: 14px">数据类型: {{ getDataType(item.type) }}</p>
            </div>
          </div>
          <p slot="reference">{{ item.cname }}</p>
        </el-popover>
      </el-option>
    </el-select>
    <el-select v-model="rule.compareType" @change="compareTypeChange" style="width: 120px;flex-shrink: 0;" :disabled="disabled">
      <el-option v-for="item in operationList" :key="item" :label="operationTypeMap[item]" :value="item">
      </el-option>
    </el-select>
    <template v-if="!notShowInput">
      <el-form-item :rules="[{ required: true, message: '过滤条件不能为空', trigger: 'change' }]" prop="values">
        <template v-if="isDateType">
          <el-date-picker v-show="isRangeType" v-model="rule.values" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :disabled="disabled" clearable value-format="timestamp" style="min-width: 300px;">
          </el-date-picker>
          <el-date-picker v-show="!isRangeType" v-model="rule.values[0]" placeholder="选择日期" :disabled="disabled" clearable value-format="timestamp" style="min-width: 300px;">
          </el-date-picker>
        </template>
        <template v-if="isNumberType">
          <template v-if="isRangeType">
            <el-input-number size="small" v-model="rule.values[0]" :disabled="disabled" clearable style="width: 140px;"></el-input-number>
            <span style="font-size: 28px;font-weight: 100;color: #434343;margin: 0 6px">-</span>
            <el-input-number size="small" v-model="rule.values[1]" :disabled="disabled" clearable style="width: 139px;"></el-input-number>
          </template>
          <template v-else-if="isEqualOrNotEqual">
            <el-select v-model="rule.values" allow-create filterable :disabled="disabled" clearable multiple style="min-width: 300px;">
              <el-option v-for="key in attrDetail.enum" :key="key" :label="key" :value="key">
              </el-option>
            </el-select>
          </template>
          <el-input-number v-else size="small" v-model="rule.values[0]" :disabled="disabled" clearable></el-input-number>
        </template>
        <template v-if="isStringType">
          <el-select v-if="isEqualOrNotEqual" v-model="rule.values" allow-create filterable :disabled="disabled" clearable multiple style="min-width: 300px;">
            <el-option v-for="key in attrDetail.enum" :key="key" :label="key" :value="key">
            </el-option>
          </el-select>
          <el-input v-else size="small" v-model="rule.values[0]" :disabled="disabled" clearable style="min-width: 300px;"></el-input>
        </template>
      </el-form-item>
    </template>
    <el-button type="text" icon="el-icon-delete" @click="remove" v-if="!disabled && length > 1"></el-button>
  </el-form>
</template>
<script>
import { isEqual } from 'lodash';
const operationTypeMap = {
  1: '等于',
  2: '不等于',
  3: '小于',
  4: '小于等于',
  5: '大于',
  6: '大于等于',
  7: '小于(版本)',
  8: '小于等于(版本)',
  9: '大于(版本)',
  10: '大于等于(版本)',
  11: "小于（字典序）",
  12: "小于等于（字典序）",
  13: "大于（字典序）",
  14: "大于等于（字典序）",
  15: "包含",
  16: "不包含",
  17: "为空",
  18: "不为空",
  19: "正则匹配",
  20: "正则不匹配",
  21: "区间",
  22: "为真",
  23: "为假"
};
export default {
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    attrs: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    length: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      rule: {
        ruleKey: '',
        ruleType: 0,
        compareType: '',
        values: []
      },
      operationTypeMap
    };
  },
  computed: {
    attrDetail() {
      const target = this.getAttrDetail(this.rule.ruleKey);
      return target || {};
    },
    operationList() {
      return this.attrDetail.compareTypeEnum || [];
    },
    notShowInput() {
      const types = [17, 18, 22, 23];
      return types.includes(this.rule.compareType);
    },
    isRangeType() {
      return +this.rule.compareType === 21;
    },
    isEqualOrNotEqual() {
      const types = [1, 2];
      return types.includes(this.rule.compareType);
    },
    isDateType() {
      return +this.attrDetail.type === 5;
    },
    isNumberType() {
      return +this.attrDetail.type === 4;
    },
    isStringType() {
      return +this.attrDetail.type === 1;
    }
  },
  watch: {
    rule: {
      handler(value) {
        // console.log('rule change', value);
        const { cname, type } = this.attrDetail;
        this.$emit('update', {
          ruleCname: cname,
          ruleType: type,
          ...value
        });
      },
      deep: true
    },
    value: {
      handler(value) {
        if (isEqual(value, this.rule)) {
          return;
        }
        // console.log('value changed');
        this.rule = {
          ...value
        };
      },
      deep: true,
      immediate: true
    },
  },
  methods: {
    getAttrDetail(key) {
      const target = this.attrs.find(item => item.key === key);
      return target || {};
    },
    remove() {
      this.$emit('remove');
    },
    ruleKeyChange() {
      this.rule.compareType = this.operationList.length ? this.operationList[0] : '';
      this.rule.values = [];
      const { cname, type } = this.attrDetail;
      this.rule.ruleCname = cname;
      this.rule.ruleType = type;
    },
    compareTypeChange() {
      this.rule.values = [];
    },
    getDataType(type) {
      const dataTypeMap = {
        1: "String",
        2: "Boolean",
        4: "Float",
        5: "Date"
      };
      return dataTypeMap[type];
    }
  },
  mounted() {
  },
};
</script>
<style scoped lang="less">
.com-container {
  // margin-bottom: 18px;
  display: flex;
  gap: 12px;

  &>.el-button {
    height: 32px;
  }

  .el-form-item {
    margin-bottom: 0;
  }

  /deep/ .el-form-item__content {
    display: flex;
  }

  .el-select {
    /deep/ .el-tag {
      white-space: normal;
      word-break: break-all;
      height: auto;
      margin: 3px 5px;
      box-sizing: border-box;
      position: relative;
      top: 0px;
      overflow: hidden;
    }
  }
}
</style>
