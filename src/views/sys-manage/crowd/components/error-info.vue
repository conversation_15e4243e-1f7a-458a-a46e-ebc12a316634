<template>
  <section>
    <p class="info">本次共上传{{ value.allLine}}条数据，其中{{value.errLine.length}}条异常，剩余{{ value.totalLine }}上传成功。</p>
    <section class="error-info">
      <p>异常名单抽样展示</p>
      <el-link type="success" @click="open_doc()"><i class="el-icon-question el-icon--right"></i>查看标准格式</el-link>
    </section>
    <el-table :data="value.errLine">
      <el-table-column
        :show-overflow-tooltip="true"
        prop="ssid"
        label="CUID/UID"
        min-width="40%">
      </el-table-column>
      <el-table-column
        prop="reason"
        label="异常原因"
      >
      </el-table-column>
      </el-table>
  </section>
</template>
<script>
export default{
  name: '',
  props: ['value'],
  methods: {
    open_doc(){
      const url = 'https://docs.zuoyebang.cc/doc/1821436633540587521?ddtab=true';
      window.open(url);
    }
  }
};

</script>
<style lang="less" scoped>
.error-info{
  margin: 12px 0;
  display: flex;
  justify-content: space-between;
}
.info{
  margin-bottom: 40px;
}
</style>
