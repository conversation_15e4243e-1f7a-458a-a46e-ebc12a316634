<template>
  <el-dialog :visible="dialogVisible" :before-close="handleClose" title="请选择创建分群的方式">
    <el-row :gutter="20" style="padding: 40px">
      <el-col :span="24" @click.native="crowdEdit(3)"  v-if="!isGlobalAB">
        <el-card class="el-card-image" shadow="hover">
          <div class="card-body-container">
            <!-- <span class="iconfont3 icon-siweidaotu"></span> -->
            <svg-icon icon-class="quanxuan"></svg-icon>
            <section>
              <p class="cuustom-card-title">规则圈选</p>
              <p class="sub-title">可根据行为、属性和原有分群进行组合筛选。灵活创建人群</p>
            </section>
          </div>
        </el-card>
      </el-col>
      <el-col :span="24" @click.native="crowdEdit(1)">
        <el-card class="el-card-image" shadow="hover">
          <div class="card-body-container">
            <span class="iconfont3 icon-renqunbaohuaxiang"></span>
            <section>
              <p class="cuustom-card-title">通过斑马人群包创建</p>
              <p class="sub-title">通过在用户洞察平台圈选用户，同步至AB使用</p>
            </section>
          </div>
        </el-card>
      </el-col>
      <el-col :span="24" @click.native="crowdEdit(2)">
        <el-card class="el-card-image" shadow="hover">
          <div class="card-body-container">
            <span class="iconfont3 icon-CSV"></span>
            <section>
              <p class="cuustom-card-title">通过文件创建</p>
              <p class="sub-title">上传包含用户ID的文件，可以直接创建相关的人群</p>
            </section>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </el-dialog>
</template>

<script>
export default {
  //   props: {
  //     dialogVisible: {
  //       type: Boolean,
  //       default: false,
  //     },
  //   },
  data() {
    return {
      dialogVisible: false,
      isGlobalAB: location.href.includes('global-abtest')
    };
  },
  methods: {
    crowdEdit(type) {
      this.$router.push({
        path: '/sys-manage/crowd/crowd-edit',
        query: {
          from: 'add',
          type
        }
      });
    },
    handleClose() {
      this.dialogVisible = false;
    },
    showModal() {
      this.dialogVisible = true;
    }
  },
  created() { }
};
</script>
<style lang="less" scoped>
.el-card-image {
  cursor: pointer;
  margin-bottom: 12px;

  .card-body-container {
    display: flex;

    &>span {
      margin-right: 8px;
      font-size: 48px;
      color: #42C57A;
    }

    &>section {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 4px;
    }

    .cuustom-card-title {
      color: #2f2f3f;
      font-weight: 700;
    }

    .sub-title {
      color: #9f9fab;
    }
  }

  svg {
    position: relative;
    /* font-size: 32px; */
    width: 48px;
    height: 48px;
    margin-right: 8px;
    top: 0;
  }
}
</style>
