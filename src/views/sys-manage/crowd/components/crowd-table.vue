<template>
  <div class="crowd-table">
    <zyb-table :table-data="tableList.list" :table-columns="tableColumns">
      <template v-slot:custom="{ data: { item, scope } }">
        <template v-if="item.prop === 'cname'">
          <zyb-text @click="handleView(scope.row)">{{ scope.row.cname }}</zyb-text>
        </template>
        <template v-else-if="item.prop === 'experimentCount'">
          <zyb-text @click="showAssociateExps(scope.row)">{{ scope.row.experimentCount }}</zyb-text>
        </template>
        <template v-else-if="item.prop === 'taskStatus'">
          <zyb-text @click="showStatusDetail(scope.row)">{{ getStatusText(scope.row.taskStatus) }}</zyb-text>
        </template>
        <template v-else-if="item.prop === 'action'">
          <zyb-text @click="handleEdit(scope.$index, scope.row)">编辑</zyb-text>
          <el-dropdown placement="bottom">
            <zyb-text class="action_more">
              更多
              <i class="el-icon-arrow-down"></i>
            </zyb-text>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :disabled="scope.row.type !== 1" @click.native="handleUpdate(scope.$index, scope.row)">
                更新
              </el-dropdown-item>
              <el-dropdown-item class="delete_btn" @click.native="handleDelete(scope.$index, scope.row)">
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </template>
    </zyb-table>

    <div class="pagination">
      <el-pagination v-show="tableList.total" layout="total,sizes, prev, pager, next, jumper" :page-size="pagination.rn" :page-sizes="[10, 20, 50, 100]" @size-change="pageSizeChange" :current-page="pagination.pn" :total="tableList.total"
        @current-change="pageNoChange"></el-pagination>
    </div>
    <associate-dialog v-if="isShowDialog" type="crowd" :dialogVisible.sync="isShowDialog" :checkedId="checkedId" :title="title"></associate-dialog>
    <el-dialog title="异常信息" :visible.sync="showErrorDialog" direction="rtl" ref="drawer" class="history-drawer">
      <error-info :value="currentRow"></error-info>
    </el-dialog>
  </div>
</template>

<script>
import { formatDate } from '@/common/utils';
import ZybTable from '@/components/zyb-table/index.vue';
import ZybText from '@/components/zyb-text/index.vue';
import AssociateDialog from '@/views/white-list/components/associate-dialog.vue';
import ErrorInfo from './error-info.vue';
export default {
  components: { AssociateDialog, ZybTable, ZybText, ErrorInfo },
  props: {
    formData: {
      type: Object
    }
  },
  data() {
    return {
      title: '当前人群包所在实验和固化',
      pagination: {
        pn: 1,
        rn: 10
      },
      total: 0,
      tableList: {},
      checkedId: 0,
      isShowDialog: false,
      currentRow: {},
      showErrorDialog: false,
      tableColumns: [
        {
          label: 'ID',
          prop: 'id'
        },
        {
          label: '分群名称',
          prop: 'cname',
          'min-width': '105px',
          title: false,
          line: 1,
          custom: true,
        },
        {
          label: '业务线',
          prop: 'appName',
          title: false,
          line: 1,
          custom: true
        },
        {
          label: '账号体系',
          prop: 'accountSystem',
          'min-width': '105px',
          render: (scope) => {
            return scope.row.accountSystem === 1 ? 'cuid' : 'uid';
          }
        },
        {
          label: '预估数量',
          prop: 'totalNum'
        },
        {
          label: '状态',
          prop: 'taskStatus',
          custom: true
        },
        {
          label: '更新方式',
          prop: 'updateType',
          'min-width': '105px',
          render: (scope) => {
            return scope.row.updateType === 2 ? '例行更新' : '手动更新';
          }
        },
        {
          label: '创建方式',
          prop: 'type',
          'min-width': '105px',
          render: (scope) => {
            const type = scope.row.type;
            if (type === 3) {
              return '规则圈选';
            }
            return scope.row.type === 1 ? '斑马人群包' : '文件';
          }
        },
        {
          label: '关联实验',
          prop: 'experimentCount',
          custom: true
        },
        {
          label: '创建人',
          prop: 'creator',
          'min-width': '105px',
          title: true
        },
        {
          label: '创建时间',
          prop: 'createTime',
          'min-width': '160px',
          line: 1,
          render: (scope) => {
            return this.handleFormatDate(scope.row.createTime);
          }
        },
        {
          label: '更新人',
          prop: 'modifier',
          'min-width': '105px',
          title: true
        },
        {
          label: '更新时间',
          prop: 'updateTime',
          'min-width': '160px',
          line: 1,
          render: (scope) => {
            return this.handleFormatDate(scope.row.updateTime);
          }
        },
        {
          label: '操作',
          prop: 'action',
          custom: true,
          'min-width': '140px',
        }
      ]
    };
  },
  methods: {
    getStatusText(status) {
      const map = {
        1: '未完成',
        2: '已完成',
        3: '全部异常',
        4: '部分异常'
      };
      return map[status] || '--';
    },
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000);
      }
      return '-';
    },
    handleEdit(index, row) {
      this.$router.push({
        path: '/sys-manage/crowd/crowd-edit',
        query: {
          from: 'edit',
          type: row.type,
          id: row.id,
          accountSystem: row.accountSystem
        }
      });
    },
    handleView(row) {
      this.$router.push({
        path: '/sys-manage/crowd/crowd-edit',
        query: {
          from: 'view',
          type: row.type,
          id: row.id,
          accountSystem: row.accountSystem
        }
      });
    },
    getList(params) {
      this.$service
        .get('CROWDLIST', { ...params, ...this.pagination }, { needLoading: true })
        .then((data) => {
          this.tableList = data || {};
        })
        .catch((err) => { });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    handleDelete(index, row) {
      const { cname } = row;
      // if(!canDelete){
      //   this.$message.error('存在运行阶段实验引用，无法删除。');
      //   return;
      // }
      this.$confirm(`确定删除分群${cname}?删除后将无法恢复。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.delCrowd(row);
        })
        .catch(() => { });
    },
    handleUpdate(index, row) {
      this.$service
        .post('CROWDUPDATE', this.formatFormData({ id: row.id }), {
          needLoading: true
        })
        .then((data) => {
          this.$message({
            type: 'success',
            message: `已更新`
          });
          this.getList(this.formData);
        })
        .catch((err) => { });
    },
    delCrowd(row) {
      this.$service
        .post('CROWDDEL', this.formatFormData({ id: row.id }), {
          needLoading: true
        })
        .then((data) => {
          this.$message({
            type: 'success',
            message: `已删除`
          });
          this.getList(this.formData);
        })
        .catch((err) => { });
    },
    pageSizeChange(rn) {
      this.pagination.rn = rn;
      this.getList(this.formData);
    },
    pageNoChange(pn) {
      this.pagination.pn = pn;
      this.getList(this.formData);
    },
    // 获取关联实验
    showAssociateExps(data) {
      this.checkedId = data.id;
      this.isShowDialog = true;
    },
    showStatusDetail(row) {
      const { errLine = [] } = row;
      if (!errLine.length) {
        return;
      }
      this.currentRow = {
        ...row
      };
      this.showErrorDialog = true;
    }
  },
  mounted() { }
};
</script>
<style lang="less" scoped>
.crowd-table {
  .pagination {
    text-align: right;
    margin-top: 20px;
  }
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}

.delete_btn {
  color: #fa574b;
}
</style>
