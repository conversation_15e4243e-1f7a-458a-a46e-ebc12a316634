<template>
  <Page class="crowd-table" :hasPagination="false">
    <section class="real-page">
      <section class="header">
        <section>
          <i class="el-icon-arrow-left" @click="handleGoBack(true)"></i>
          <section class="page-title"> {{ $route.query.from == 'add' ? '新建' : $route.query.from == 'view' ? '查看' : '编辑'
            }}分群</section>
        </section>
        <section>
          <template v-if="$route.query.from !== 'view'">
            <el-button type="primary" @click="submitForm('ruleForm')" :disabled="isProgess">保存</el-button>
            <el-button @click="handleGoBack()">取消</el-button>
          </template>
          <template v-else>
            <el-tooltip class="item" effect="dark" content="操作历史" placement="top" v-if="$route.query.type != 1">
              <i class="icon2-lishi2 iconfont2 history-btn" @click="openDrawer" style="margin-right: 12px"></i>
            </el-tooltip>
            <el-button type="primary" @click="handleEdit()">编辑</el-button>
          </template>
        </section>
      </section>
      <section class="content">
        <el-form :model="ruleForm" label-position="right" :rules="rules" ref="ruleForm" label-width="100px"
          class="form-container">
          <p class="form-title">基础信息</p>
          <el-form-item label="业务线：" prop="appKey" style="width: 900px;">
            <el-select :disabled="$route.query.from !== 'add'" v-model="ruleForm.appKey" placeholder="请选择"
              @change="handleChangAppKey">
              <el-option v-for="item in appOptionsData" :key="item.appKey" :label="item.displayName"
                :value="item.appKey">
              </el-option>
            </el-select>
            <el-alert v-if="$route.query.type == 3" style="display: inline; margin-left: 8px;"
              title="仅显示有基础数据的业务线。不显示的业务线，如需规则圈选人群包，请联系AB团队" type="warning" show-icon></el-alert>
          </el-form-item>

          <el-form-item label="账号体系">
            <el-radio-group v-model="ruleForm.accountSystem" :disabled="$route.query.from !== 'add'"
              @change="changeAccountSystem">
              <el-radio :label="1">cuid</el-radio>
              <el-radio :label="2">uid</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="分群名称" prop="name">
            <el-input v-model.trim="ruleForm.name" placeholder="请输入分群名称，支持50个以内字符" @change="handleNameChange"
              :disabled="$route.query.from === 'view'"></el-input>
          </el-form-item>
          <el-form-item label="更新方式">
            <el-radio-group :disabled="!($route.query.from == 'add' && $route.query.type == 3)"
              v-model="ruleForm.updateType">
              <el-radio :label="1">手动更新</el-radio>
              <el-radio :label="2">每日例行</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="负责人：" prop="createUser">
            <search-user v-model="ruleForm.createUser" :usedefault="true" :disabled="$route.query.from === 'view'"></search-user>
          </el-form-item>
          <el-form-item label="分群描述" prop="desc">
            <el-input v-model="ruleForm.desc" placeholder="请输入描述信息" type="textarea" :rows="2"
              :disabled="$route.query.from === 'view'"></el-input>
          </el-form-item>
          <p class="form-title">分群规则</p>
          <template v-if="$route.query.type == 1">
            <el-form-item label="斑马人群包" prop="populationName">
              <el-select v-model="ruleForm.populationId" :disabled="$route.query.from !== 'add'"
                :style="{ width: '300px' }" @change="handlePopulationChange">
                <el-option v-for="(i, k) in crowdList" :key="k" :label="i.populationName"
                  :value="i.populationId"></el-option>
              </el-select>
            </el-form-item>
          </template>
          <template v-if="$route.query.type == 3">
            <rule-crowd ref="RuleCrowd" v-model="ruleForm.rules" :accountSystemSignal="accountSystemSignal"
              :accountSystem="ruleForm.accountSystem" :disabled="$route.query.from == 'view'"></rule-crowd>
          </template>
          <template v-if="$route.query.type == 2 && $route.query.from !== 'view'">
            <el-form-item label="">
              <el-upload :disabled="isProgess || $route.query.from === 'view'" class="upload-demo" drag
                accept=".txt, .csv" action="#" :http-request="getTmpSecreKeys"
                :before-upload="(file) => beforeFileUpload(file)"
                @drop.native="(e) => beforeFileUpload(e.dataTransfer.files[0])" :on-success="handleUploadSuccess"
                :on-remove="handleRemove" :on-exceed="handleExceed" :limit="1" :file-list="fileList">
                <i class="el-icon-upload" style="margin-top: 13px"></i>
                <div class="el-upload__text">
                  点击或将文件拖拽到这里上
                  <p>仅支持上传单个txt文本，文件内容格式回车符分割</p>
                </div>
              </el-upload>
            </el-form-item>
          </template>
          <template v-if="$route.query.type == 2 && $route.query.from === 'view'">
            <el-form-item label="分群文件" v-if="this.fileList.length !== 0">
              <li class="el-upload-list__item is-success">
                <a class="el-upload-list__item-name">
                  <i class="el-icon-document"></i>
                  {{ this.fileList[0].name }}
                </a>
                <label class="el-upload-list__item-status-label">
                  <i class="el-icon-upload-success el-icon-circle-check"></i>
                </label>
                <i class="el-icon-download" @click="downloadFile"></i>
              </li>
            </el-form-item>
          </template>
          <p class="form-title">预估人群</p>
          <el-form-item label="用户数" prop="totalNum">
            <span v-if="totalNum !== null && totalNum !== undefined" style="font-weight: bold; margin-right: 16px;">{{ totalNum }}</span>
            <el-button v-if="$route.query.from !== 'view'" type="primary" @click="computeNum" :disabled="$route.query.from === 'view'">计算</el-button>
          </el-form-item>
          <el-form-item v-if="isProgess">
            <el-progress :percentage="elPercent"></el-progress>
          </el-form-item>
        </el-form>
      </section>
    </section>
    <el-drawer title="操作历史" :visible.sync="showDrawer" direction="rtl" ref="drawer" class="history-drawer">
      <History v-if="showDrawer"></History>
    </el-drawer>
    <el-dialog title="分群规则变更记录" :visible.sync="showRulesTable" direction="rtl" ref="drawer" class="history-drawer">
      <el-table :data="ruleTableList" style="width: 100%">
        <el-table-column prop="versionId" label="版本">
          <template slot-scope="scope">
            <p style="color: #42c57a; cursor: pointer;" @click="showVersionDetail(scope.row.versionId)">版本{{
              scope.row.versionId }}</p>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="更新时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime ? dateTimeFormat(scope.row.createTime) : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createUser" label="更新人">
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-drawer :title="'版本' + versionId" :visible.sync="showRuleHistoryDrawer" direction="rtl" size="40%" ref="drawer"
      class="history-drawer">
      <History3 v-if="showRuleHistoryDrawer" :versionId="versionId" :accountSystem="ruleForm.accountSystem"></History3>
    </el-drawer>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import { getUrlByLocation } from '@/utils/util';
import axios from 'axios';
import COS from 'cos-js-sdk-v5';
import { v4 as uuidv4 } from 'uuid';
import History from './history';
import RuleCrowd from './rule-crowd.vue';
import dayjs from 'dayjs';
import History3 from './history3.vue';
import SearchUser from '@/components/search-user/index.vue';

const actions = {
  local: '/testAddress/earthworm/mis/crowd/sts',
  test: '/earthworm/mis/crowd/sts',
  server: '/earthworm/mis/crowd/sts'
};

export default {
  components: { Page, History, RuleCrowd, History3, SearchUser },
  data() {
    return {
      appOptionsData: [],
      ruleForm: {
        updateType: 1,
        name: '',
        desc: '',
        appKey: '', // 业务线
        accountSystem: this.$route.query.accountSystem ? this.$route.query.accountSystem : 1, //账号体系，cuid：1，uid: 2
        populationName: '',
        rules: {},
        createUser: ''
      },
      rules: {
        appKey: [{ required: true, message: '请选择业务线', trigger: 'blur' }],
        name: [{ required: true, message: '分群名称不可为空', trigger: 'blur' }],
        populationName: [{ required: true, message: '请选择斑马人群包', trigger: 'blur' }],
        createUser: [{ required: true, message: '请选择负责人', trigger: 'blur' }]
      },
      crowdList: [],
      cosAddress: '',
      uploadFile: '',
      fileList: [],
      totalNum: undefined,
      elPercent: 0,
      isProgess: false,
      oCos: '',
      nameChanged: false,
      showDrawer: false,
      showRulesTable: false,
      ruleTableList: [],
      showRuleHistoryDrawer: false,
      versionId: 0,
      accountSystemSignal: 0
    };
  },
  computed: {
    uploadConfig() {
      return {
        action: this.computedEnv()
      };
    }
  },
  methods: {
    showVersionDetail(versionId) {
      this.showRuleHistoryDrawer = true;
      this.versionId = versionId;
    },
    dateTimeFormat(value) {
      const res = dayjs.unix(value).format('YYYY-MM-DD HH:mm:ss');
      return res;
    },
    handleEdit() {
      const hash = '/sys-manage/crowd/crowd-edit';
      const query = this.$route.query || {};
      const params = {
        ...query,
        from: 'edit',
      };
      const url = getUrlByLocation(hash, params);
      // window.open(url);
      location.href = url;
    },
    handleClose(done) {
      this.showDrawer = false;
    },
    openDrawer() {
      const { id, type } = this.$route.query;
      if (+type === 3) {
        this.$service.get('CROWD_RULE_HISTORY', {
          id
        })
          .then(res => {
            this.showRulesTable = true;
            if (Array.isArray(res)) {
              this.ruleTableList = res;
              return;
            }
            this.ruleTableList = res.list || [];
          });
        return;
      }
      this.showDrawer = true;
    },
    async handleGoBack(flag = false) {
      if (this.$route.query.from !== 'view') {
        const result = await this.$confirm(`本次编辑内容尚未保存，返回将放弃页面已填写内容，确定返回吗？？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        if (!result) {
          return;
        }
      }
      this.$router.push({
        path: '/sys-manage/crowd'
      });
    },
    changeAccountSystem() {
      const { type } = this.$route.query;
      this.accountSystemSignal += 1;
      if (type != 3) {
        this.getCrowdInfos();
      }
      this.totalNum = undefined;
      this.ruleForm.name = '';
    },
    handlePopulationChange(val) {
      const target = this.crowdList.find((item) => item.populationId == val);
      if (target) {
        if (!this.ruleForm.name) {
          this.ruleForm.name = target.populationName;
        }
        this.ruleForm.populationName = target.populationName;
        this.ruleForm.updateType = target.populationValue ? target.populationValue.peride : this.ruleForm.updateType;
        this.totalNum = target.totalNum;
      }
    },
    handleNameChange() {
      this.nameChanged = true;
    },
    computeNum() {
      const { populationId, accountSystem, appKey } = this.ruleForm;
      const type = +this.$route.query.type;
      const params = {
        appKey,
        accountSystem,
        type
      };
      let url = 'CROWDCHECK';
      if (type === 1) {
        if (!populationId) {
          this.$message.warning('请选择斑马人群包！');
          return;
        }
        params.populationId = populationId;
      } else if (type === 2) {
        if (!this.cosAddress) {
          this.$message.warning('请上传文件！');
          return;
        }
        params.cosAddress = this.cosAddress;
      } else {
        params.rules = this.getType3Rules() || {};
        url = 'CROWD_RULE_CHECK';
      }
      this.$service
        .post(url, this.formatFormData(params), { needLoading: true })
        .then(res => {
          this.totalNum = res.totalNum;
        });
    },
    computedEnv() {
      let hostName = location.hostname;
      let url = '';
      if (/^localhost$/gi.test(hostName)) {
        url = actions.local;
      } else if (/docker|test/gi.test(hostName)) {
        url = actions.test;
      } else {
        url = actions.server;
      }
      return url;
    },
    beforeFileUpload(file) {
      this.elPercent = 0;
      let fName = file.name;
      if (file.size / (1024 * 2024) > 300) {
        this.$message.warning('请上传小于300MB的txt文件');
        return false;
      }
      if (fName.lastIndexOf('.txt') === -1 && fName.lastIndexOf('.csv') === -1) {
        this.$message.warning('请上传txt/csv文件');
        return false;
      }
      this.uploadFile = file;
    },
    getTmpSecreKeys() {
      this.isProgess = true;
      this.putObject(this, this.uploadFile, (fileUrl, file_name) => {
        console.log(fileUrl);
        this.cosAddress = fileUrl;
        this.fileName = file_name;
        if (!this.ruleForm.name) {
          this.ruleForm.name = file_name;
        }
        this.$message.success(`上传成功`);
        this.isProgess = false;
      });
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件`);
    },
    putObject(that, file, callback) {
      let file_name = file.name;
      //let url = 'https://vido-api-2020.4kt.net/admin/v2.tengxun/up_sign'; // 秘钥相关信息
      let url = this.computedEnv();
      axios.post(url).then((response) => {
        const data = response.data.data;
        var credentials = data && data.credentials;

        if (!data || !credentials) return console.error('credentials invalid');
        var cos = new COS({
          getAuthorization: function (options, callback) {
            callback({
              TmpSecretId: credentials.tmpSecretId,
              TmpSecretKey: credentials.tmpSecretKey,
              XCosSecurityToken: credentials.sessionToken,
              StartTime: data.startTime,
              ExpiredTime: data.expiredTime,
              expiration: data.expiration,
              requestId: data.requestId
            });
          }
        });
        // 获取上传文件类型
        let type = file.type.substring(0, 5);
        let size = file.size;
        let uKey = uuidv4() + '.txt';
        if (size / (1024 * 2024) < 50) {
          cos.putObject(
            {
              Bucket: 'zyb-abtest-group-1253445850', // 存储桶名称
              Region: 'ap-beijing', // 地区
              Key: '/txt/' + uKey, // 文件名称
              StorageClass: 'STANDARD',
              Body: file, // 上传文件对象
              onHashProgress: function (progressData) {
              },
              onProgress: function (progressData) {
                console.log('简单上传');
                that.elPercent = Number.parseInt(progressData.percent * 100);
                console.log(v.$refs.elProgress.percentage);
                console.log('上传中', JSON.stringify(progressData));
              }
            },
            function (err, data) {
              console.log('999', err, data, data.Location);
              if (err) {
                this.$message({ message: '文件上传失败,请重新上传', type: 'error' });
              } else {
                let fileUrl = 'http://' + data.Location;
                callback(fileUrl, file_name);
              }
            }
          );
        } else {
          console.log('上传分块');
          cos.sliceUploadFile(
            {
              Bucket: 'zyb-abtest-group-1253445850' /* 必须 */,
              Region: 'ap-beijing' /* 存储桶所在地域，必须字段 */,
              Key: '/txt/' + uKey /* 必须 */,
              Body: file,
              onProgress: function (progressData) {
                console.log('分块上传', that.elPercent);
                console.log(JSON.stringify(progressData));
                that.elPercent = Number.parseInt(progressData.percent * 100);
              }
            },
            function (err, data) {
              console.log(err);
              console.log(data);
              if (err) {
                this.$message({ message: '文件上传失败,请重新上传', type: 'error' });
              } else {
                let fileUrl = 'http://' + data.Location;
                callback(fileUrl, file_name);
              }
            }
          );
        }
      });
    },
    handleUploadSuccess(res, file) {
      this.cosAddress = res.data.url;
      this.fileName = file.name;
      this.totalNum = res.data.totalNum;
      if (this.totalNum == 0) {
        this.$message.warning(`禁止上传空文件`);
        this.fileList = [];
        this.cosAddress = '';
        this.totalNum = undefined;
      }
      if (res.errNo !== 0) {
        this.$message.warning(`${res.errStr}`);
        this.fileList = [];
        this.cosAddress = '';
      }
    },
    handleRemove(file, fileList) {
      this.cosAddress = '';
      this.totalNum = undefined;
    },
    getType3Rules() {
      const rules = this.ruleForm.rules.outerRules.map(item => {
        const { innerRules = [] } = item;
        return {
          ...item,
          innerRules: innerRules.map(subItem => {
            return {
              ...subItem,
              values: subItem.values ? subItem.values.map(value => {
                const res = subItem.ruleType === 5 ? value / 1000 : value;
                return res + '';
              }) : null
            };
          })
        };
      });
      return {
        logicType: this.ruleForm.rules.logicType,
        outerRules: rules
      };
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const { type, id, from } = this.$route.query;
          let params = {};
          if (+type === 3) {
            const { outerRules = [] } = this.ruleForm.rules;
            if (!outerRules.length) {
              this.$message({
                type: 'warning',
                message: `请添加有效规则`
              });
              return;
            }
            const GroupRule = this.$refs.RuleCrowd ? this.$refs.RuleCrowd.$refs.GroupRule : [];
            const valid = await Promise.all(GroupRule.map(async (ins) => {
              const form = ins.$refs.RuleForm;
              if (form) {
                return await form.validate();
              }
            })).catch(() => false);
            console.log('valid', valid);
            if (!valid) {
              this.$message.warning('过滤条件不能为空');
              return;
            }

            if(!this.totalNum){
              this.$message.warning('请先计算预估用户数');
              return;
            }
            if(this.totalNum > 1000 * 10000){
              await this.$confirm(
                '当前创建分群用户数已超过1000万，点击确定将启动分群用户超限审批流程，确定创建当前分群吗？', 
                '分群超限提示',
                {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }
              );
            }
            params = {
              id,
              name: this.ruleForm.name,
              type: type * 1,
              desc: this.ruleForm.desc,
              appKey: this.ruleForm.appKey,
              accountSystem: this.ruleForm.accountSystem,
              updateType: this.ruleForm.updateType,
              rules: this.getType3Rules()
            };
            this.updateCrowdRule(params);
            return;
          }
          if (type == 1) {
            params = {
              name: this.ruleForm.name,
              populationId: this.ruleForm.populationId,
              type: type * 1,
              desc: this.ruleForm.desc,
              appKey: this.ruleForm.appKey,
              accountSystem: this.ruleForm.accountSystem,
              updateType: this.ruleForm.updateType
            };
          }
          if (type == 2) {
            params = {
              name: this.ruleForm.name,
              cosAddress: this.cosAddress,
              type: type * 1,
              desc: this.ruleForm.desc,
              fileName: this.fileName,
              appKey: this.ruleForm.appKey,
              accountSystem: this.ruleForm.accountSystem,
              updateType: this.ruleForm.updateType
            };
            if (!this.cosAddress) {
              this.$message({
                type: 'warning',
                message: `请上传文本文件`
              });
              return;
            }
          }
          from == 'edit' && (params.id = id);
          // if (typeof (this.totalNum) === "undefined") {
          //   this.$message({
          //     type: 'warning',
          //     message: `请先计算用户数`
          //   });
          //   return;
          // }
          params.totalNum = typeof (this.totalNum) === "undefined" ? 0 : this.totalNum;
          this.updateCrowd(params);
        }
      });
    },
    getCrowdInfos() {
      if (this.$route.query.type !== '1') {
        return;
      }
      const { accountSystem } = this.ruleForm;
      this.$service
        .get('CROWDINFOS', { accountSystem }, { needLoading: true })
        .then((data) => {
          this.crowdList = data.list || [];
        })
        .catch((err) => { });
    },
    getCrowdInfo() {
      const { id, type } = this.$route.query;
      const url = type == 3 ? 'CROWD_RULE_VIEW' : 'CROWDINFO';
      id &&
        this.$service
          .get(url, { id }, { needLoading: true })
          .then((data) => {
            const { cname, description, extData, cosAddress, totalNum, accountSystem, appKey, updateType, populationId, rules = [], createUser= ''  } = data;
            const ext = type != 3 && JSON.parse(extData);
            const outerRules = type == 3 ? rules.outerRules.map(item => {
              const { innerRules = [] } = item;
              return {
                ...item,
                innerRules: innerRules.map(subItem => {
                  return {
                    ...subItem,
                    values: subItem.values ? subItem.values.map(value => {
                      const res = subItem.ruleType === 5 ? parseInt(value) * 1000 : subItem.ruleType === 4 ? parseFloat(value) : value;
                      return res + '';
                    }) : null
                  };
                })
              };
            }) : {};
            this.ruleForm = {
              name: cname,
              desc: description,
              accountSystem,
              appKey: appKey,
              createUser,
              updateType: updateType === 2 ? updateType : 1,
              populationId: populationId,
              rules: type == 3 ? {
                logicType: rules.logicType,
                outerRules: outerRules
              } : rules
            };
            this.fileList = [
              {
                name: ext.fileName || 'file_0',
                url: cosAddress
              }
            ];
            this.cosAddress = cosAddress;
            this.fileName = ext.fileName;
            this.totalNum = totalNum;
          })
          .catch((err) => { });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    updateCrowd(params) {
      this.$service
        .post(`CROWD${this.$route.query.from}`, this.formatFormData(params), {
          needLoading: true
        })
        .then((data) => {
          this.$message({
            type: 'success',
            message: `创建成功`
          });
          this.$router.push({
            path: '/sys-manage/crowd',
            query: {}
          });
        })
        .catch((err) => { });
    },
    updateCrowdRule(params) {
      const { id } = this.$route.query;
      const url = id ? 'CROWD_RULE_EDIT' : 'CROWD_RULE_ADD';
      this.$service
        .post(url, this.formatFormData(params), {
          needLoading: true
        })
        .then(async (data) => {
          // 根据data中状态码判断
          if (data.status == 1000) {
            const htmlString = `
          <section class="suoliang-tips">
            <p>当前创建分群用户数已超过1000万，点击确定将启动分群用户超限审批流程，确定创建当前分群吗？</p>
          </section>
          `;
            const { value: value1 } = await this.$confirm(
              htmlString,
              '分群超限提示',
              {
                dangerouslyUseHTMLString: true,
              }
            );
          }
          this.$message({
            type: 'success',
            message: `创建成功`
          });
          this.$router.push({
            path: '/sys-manage/crowd',
            query: {}
          });
        })
        .catch((err) => { });
    },
    // 业务线
    getapplist() {
      const { type } = this.$route.query;
      this.$service
        .get(type == 3 ? 'APPLISTWITHDATA' : 'APPLIST', { pn: 0, rn: 100000 }, { needLoading: true })
        .then((res) => {
          this.appOptionsData = res.list;
          // this.appOptionsData.push({ "appKey": "other", "displayName": "其他" });
          if (!this.ruleForm.appKey) {
            this.ruleForm.appKey = this.appOptionsData[0].appKey;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleChangAppKey(val) {
      //   this.$emit("changeAppKey",val)
    },
    downloadFile() {
      this.$service
        .post('COSDOWNLOADURL', this.formatFormData({ url: this.fileList[0].url }), {
          needLoading: true
        }).then((url) => {
          const tempLink = document.createElement('a');
          tempLink.href = url;
          tempLink.click();
        });
    }
  },
  mounted() {
    this.getapplist();
    this.getCrowdInfos();
    this.getCrowdInfo();
  },
};
</script>

<style lang="less" scoped>
.crowd-table {
  display: flex;

  .real-page {
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 120px);
    overflow: hidden;

    .content {
      flex: 1;
      overflow: auto;
    }
  }

  /deep/ span.el-breadcrumb__inner.is-link {
    color: #2f2f3f;
    margin-bottom: 25px;
    font-size: 18px;
    font-weight: 500;
  }

  /deep/ span.el-breadcrumb__inner {
    font-size: 18px;
  }

  .upload-demo {
    /deep/ .el-upload-dragger {
      height: 180px;
    }
  }

  .el-breadcrumb__item {
    margin: 25px auto;
  }

  .el-upload__text {
    p {
      color: #8b8ba6;
      font-size: 12px;
      margin: 20px;
    }
  }

  .form-container {
    max-width: 700px;

    /deep/ .el-alert__title {
      margin-right: 8px;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    align-items: center;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    &>section {
      display: flex;
      align-items: center;
    }

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin-left: 12px;
    }

    i {
      cursor: pointer;
    }
  }
}

.history-drawer {
  /deep/ .el-drawer__header {
    span {
      font-weight: 500;
      display: inline-block;
      font-size: 16px;
    }
  }

  /deep/ .el-drawer__body {
    margin-right: 32px;
  }
}

.el-upload-list__item:first-child {
  margin-top: 4px;
}

.el-upload-list__item .el-icon-download {
  display: none;
  position: absolute;
  top: 5px;
  right: 5px;
  cursor: pointer;
  opacity: .75;
  color: #606266;
}

.el-upload-list__item .el-icon-download:hover {
  opacity: 1;
}

.el-upload-list__item:hover .el-icon-download {
  display: inline-block;
}
</style>
