<template>
  <section>
    <el-timeline class="history-content" v-if="list.length">
      <el-timeline-item v-for="item, index in list" :key="index" type="primary">
        <section class="space-b top-item">
          <span>{{ dateTimeFormat(item.createTime) }}</span>
          <span class="user-name" effect="dark">{{ item.createUser }}</span>
        </section>
        <section class="bottom-item">
          <p>本次上传cuid{{ item.operationDetail.allLine }}个</p>
          <p>其中异常值{{ item.operationDetail.errLine }}个</p>
          <p>正常值{{ item.operationDetail.totalLine }}个</p>
        </section>
      </el-timeline-item>
    </el-timeline>
    <p v-else class="empty-text">暂无操作历史</p>
  </section>
</template>
<script>
import dayjs from 'dayjs';
export default {
  name: 'ExpHistory',
  components: {},
  props: {
    expName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.$service.get('CROWD_HISTORY', {
        id: this.$route.query.id
      })
        .then(res => {
          this.list = res.list || [];
        });
    },
    dateTimeFormat(value) {
      const res = dayjs.unix(value).format('YYYY-MM-DD HH:mm:ss');
      return res;
    }
  }
};
</script>
<style lang="less" scoped>
.history-content {
  padding: 0 24px;

  .top-item {
    padding-top: 3px;
    display: flex;
    justify-content: space-between;

    span {
      font-weight: 500;
    }
  }

  .bottom-item {
    padding-top: 8px;

    p {
      line-height: 24px;
    }
  }
}

.empty-text {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  margin-top: 300px;
  opacity: 0.75;
}
</style>