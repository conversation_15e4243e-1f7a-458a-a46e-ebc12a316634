<template>
  <div class="container">
    <section class="group-container">
      <section v-show="rules.outerRules.length > 1" class="condition">
        <span @click="outerLogicChange">{{ getLogicText(rules.logicType) }}</span>
      </section>
      <section class="card-container">
        <el-card v-for="rule, index in rules.outerRules" :key="index" v-if="rule.innerRules.length">
          <!-- <div slot="header" class="header">
            <span style="padding: 8px 0">组条件{{ index + 1 }}</span>
            <el-button type="text" icon="el-icon-delete" v-if="!disabled" @click="removeGroup(index)"></el-button>
          </div> -->
          <section class="rule-container group-container">
            <section v-show="rule.innerRules.length > 1" class="condition">
              <span @click="innerLogicChange(index)">{{ getLogicText(rule.logicType) }}</span>
            </section>
            <section class="card-container">
              <group-rule ref="GroupRule" v-for="item, itemIndex in rule.innerRules" :key="itemIndex" :disabled="disabled" :length="rule.innerRules.length" v-model="rule.innerRules[itemIndex]"
                @update="(value) => { updateGroupItem(index, itemIndex, value) }" :attrs="attrs" @remove="() => { removeGroupItem(index, itemIndex) }"></group-rule>
            </section>
          </section>
          <el-button type="text" @click="addRule(index)" icon="el-icon-plus" v-if="!disabled">用户属性</el-button>
          <el-button type="text" icon="el-icon-delete" v-if="!disabled" @click="removeGroup(index)">删除</el-button>
        </el-card>
      </section>
    </section>
    <el-button class="icon-primary-button" type="default" @click="addGroup" icon="el-icon-circle-plus" v-if="!disabled">组条件</el-button>
  </div>
</template>
<script>
import GroupRule from './group-rule.vue';
import { isEqual } from 'lodash';
const logicChangeMap = {
  1: 2,
  2: 1
};
const logicMap = {
  1: '且',
  2: '或'
};
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    accountSystem: {
      type: Number | String,
      default: 1
    },
    accountSystemSignal: {
      type: Number,
      default: 0
    },
    value: {
      type: Object,
      default: () => { }
    }
  },
  components: { GroupRule },
  data() {
    return {
      rules: {
        logicType: 2,
        outerRules: []
      },
      attrs: [],
    };
  },
  watch: {
    rules: {
      handler(value) {
        // console.log('rules change', value);
        this.$emit('input', value);
      },
      deep: true
    },
    value: {
      handler(value) {
        if (isEqual(value, this.rules)) {
          return;
        }
        this.rules = value;
      },
      deep: true
    },
    accountSystemSignal() {
      this.rules = {
        logicType: 2,
        outerRules: []
      };
      this.getAttrs();
    }
  },
  computed: {
  },
  created() {
    this.getAttrs();
  },
  methods: {
    getAttrs() {
      this.$service.get('CROWD_ATTR', {
        accountSystem: this.accountSystem
      })
        .then(res => {
          this.attrs = res || [];
        });
    },
    addGroup() {
      this.rules.outerRules.push({
        logicType: 1,
        innerRules: []
      });
      this.addRule(this.rules.outerRules.length - 1);
    },
    removeGroup(index) {
      this.rules.outerRules.splice(index, 1);
    },
    addRule(index) {
      const innerRules = this.rules.outerRules[index].innerRules;
      const attr = this.attrs[0] || {};
      innerRules.push({
        ruleKey: attr.key,
        compareType: attr.compareTypeEnum ? attr.compareTypeEnum[0] : '',
        values: []
      });
    },
    removeGroupItem(index, itemIndex) {
      const target = this.rules.outerRules[index].innerRules;
      target.splice(itemIndex, 1);
      // console.log('target.innerRules', target.innerRules);
    },
    updateGroupItem(index, itemIndex, value) {
      const target = this.rules.outerRules[index].innerRules;
      target[itemIndex] = {
        ...value
      };
      // console.log('target.innerRules', target);
    },
    outerLogicChange() {
      if (!this.disabled) {
        const logicType = this.rules.logicType;
        this.rules.logicType = logicChangeMap[logicType];
      }
    },
    innerLogicChange(index) {
      if (!this.disabled) {
        const logicType = this.rules.outerRules[index].logicType;
        this.rules.outerRules[index].logicType = logicChangeMap[logicType];
      }
    },
    getLogicText(logicType) {
      return logicMap[logicType];
    }
  },
  mounted() {
  },
};
</script>
<style scoped lang="less">
.container {
  padding: 12px 0 12px 32px;
  width: 100%;

  .group-container {
    display: flex;

    .condition {
      position: relative;
      border: 2px solid #42c57a;
      border-right: none;
      margin: 8px 12px;
      width: 12px;
      flex-shrink: 0;

      span {
        display: inline-block;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translate(-50%, -50%);
        font-size: 14px;
        background-color: #42c57a;
        padding: 5px 4px;
        border-radius: 3px;
        color: white;
        cursor: pointer;
      }
    }

    .card-container {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 8px;
      flex: 1;

      .is-always-shadow {
        box-shadow: none;
      }

      .el-card {
        border: 0px;

        /deep/ .el-card__header {
          padding: 4px 16px;
        }

        /deep/ .el-card__body {
          padding: 4px;
          margin-left: 0px;
        }

        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          span {
            display: inline-block;
            font-weight: 500;
          }
        }
      }
    }
  }

  .rule-container {
    margin-left: 0px;
  }

  .icon-primary-button {
    margin-top: 12px;
  }
}
</style>
