<template>
  <section>
    <rule-crowd v-model="rules" :disabled="true" :accountSystem="accountSystem"></rule-crowd>
  </section>
</template>
<script>
import dayjs from 'dayjs';
import RuleCrowd from './rule-crowd.vue';

export default {
  name: 'ExpRuleHistory',
  components: {
    RuleCrowd
  },
  props: {
    versionId: {
      type: Number,
      default: 0
    },
    accountSystem: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      rules: {}
    };
  },
  created() {
    this.getVersionDetail();
  },
  methods: {
    getVersionDetail() {
      this.$service.get('CROWD_RULE_VIEW', {
        id: this.$route.query.id,
        versionId: this.versionId
      })
        .then(res => {
          this.rules = res.rules || {};
        });
    },
    dateTimeFormat(value) {
      const res = dayjs.unix(value).format('YYYY-MM-DD HH:mm:ss');
      return res;
    }
  }
};
</script>
<style lang="less" scoped>
.history-content {
  padding: 0 24px;

  .top-item {
    padding-top: 3px;
    display: flex;
    justify-content: space-between;

    span {
      font-weight: 500;
    }
  }

  .bottom-item {
    padding-top: 8px;

    p {
      line-height: 24px;
    }
  }
}

.empty-text {
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  margin-top: 300px;
  opacity: 0.75;
}
</style>
