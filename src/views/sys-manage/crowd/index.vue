<template>
  <Page :breadcrumbList="breadcrumbList">
    <div class="select-container">
      <div>
        <el-form size="small" ref="searchForm" inline>
          <el-form-item>
            <el-input placeholder="分群名称/创建人" :style="{ width: '250px' }" @change="handleSearch" v-model="formData.keyword">
              <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </el-form-item>
          <el-form-item label="业务线：">
            <el-select v-model="formData.appKey" style="width: 150px" placeholder="请选择appKey" @change="handleSearch">
              <el-option v-for="item in appList1" :key="item.appKey" :label="item.displayName" :value="item.appKey"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账号体系：">
            <el-select style="width: 150px" v-model="formData.accountSystem" clearable placeholder="请选择" @change="handleSearch">
              <el-option v-for="item in accSystemData" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select v-model="formData.taskStatus" placeholder="请选择" @change="handleSearch">
              <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建方式：">
            <el-select v-model="formData.createType" placeholder="请选择" @change="handleSearch">
              <el-option v-for="item in createOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item :style="{ float: 'right' }">
            <el-button class="w80" type="primary" icon="el-icon-plus" @click="createForm">
              新建分群
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <crowd-table ref="crowdTable" :tableList="tableList" :formData="formData" @handelEditWhiteList="handelEditWhiteList" @handleSearch="handleSearch" />
    <create-crowd ref="createModal" :dialogVisible.sync="visible" @cancel="handleCancel" />
  </Page>
</template>
<script>
import Page from '@/components/common/page/index.vue';
import CreateCrowd from './components/create-crowd';
import CrowdTable from './components/crowd-table';

import { mapGetters } from 'vuex';
const statusOptions = [{
  label: '全部',
  value: 0
}, {
  label: '未完成',
  value: 1
}, {
  label: '部分异常',
  value: 4
}, {
  label: '全部异常',
  value: 3
}, {
  label: '已完成',
  value: 2
}];

const createOptions = [{
  label: '全部',
  value: 0
}, {
  label: '斑马人群包',
  value: 1
}, {
  label: '文件上传',
  value: 2
}, {
  label: '规则圈选',
  value: 3
}];

export default {
  name: 'CrowdList', //白名单列表
  components: {
    Page,
    CrowdTable,
    CreateCrowd
  },
  // mixins: [PageCommon],
  data() {
    return {
      statusOptions,
      createOptions,
      breadcrumbList: ['CrowdList'],
      editId: 0,
      isShowDialog: false,
      formData: {
        keyword: '',
        accountSystem: '',
        appKey: 'all',
        createType: 0,
        taskStatus: 0
      },
      tableList: {},
      noborder: false,
      visible: false
    };
  },
  mounted() {
    this.$store.dispatch('getAppList');
    this.handleSearch();
  },
  computed: {
    ...mapGetters(['accSystemData', 'appList1'], 'message')
  },
  methods: {
    handleCancel() {
      this.visible = false;
    },
    createForm() {
      this.$refs.createModal.showModal();
    },
    resetEditId(val) {
      this.editId = 0;
    },
    handleSearch() {
      this.$refs.crowdTable.pagination.pn = 1;
      this.$refs.crowdTable.getList({ ...this.formData });
    },
    getList() {
      this.$service
        .get('CROWDLIST', { ...this.formData, ...this.pagination }, { needLoading: true })
        .then((data) => {
          this.tableList = data || {};
        })
        .catch((err) => { });
    },
    createWhiteList() {
      this.isShowDialog = true;
    },
    handelEditWhiteList(id) {
      this.editId = id;
      this.isShowDialog = true;
    }
  }
};
</script>
<style lang="less" scoped>
.no-border {
  background-color: #f3f4f7;
  padding: 10px;
}
</style>
