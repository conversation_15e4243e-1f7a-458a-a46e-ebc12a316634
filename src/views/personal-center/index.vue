<template>
  <div class="personal-center">
    <section class="header">
      <div class="back-link" @click="goBack">
        <i class="el-icon-arrow-left"></i>
        <span>个人中心</span>
      </div>
      <el-button type="primary" size="small" class="transfer-btn" @click="showWorkTransfer">转交工作</el-button>
    </section>
    <div class="personal-center-content">
      <!-- 基本信息区域 -->
      <div class="basic-info-section">
        <h3 class="form-title">基本信息</h3>
        <div class="info-item">
          <span class="label">登录账号：</span>
          <span class="value">{{ userInfo.employeeId}}</span>
        </div>
        <div class="info-item">
          <span class="label">邮箱：</span>
          <span class="value">{{ userInfo.email }}</span>
        </div>
      </div>

      <!-- 功能配置区域 -->
      <div class="function-section">
        <h3 class="form-title">功能配置</h3>
        <el-tabs v-model="activeTab">
          <el-tab-pane label="数据权限" name="dataPermission">
            <el-table :data="appList" style="width: 100%">
              <el-table-column prop="displayName" label="业务线名称" width="180"></el-table-column>
              <el-table-column prop="appKey" label="key" width="180"></el-table-column>
              <el-table-column prop="owner" label="业务线负责人"></el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <WorkTransfer :visible.sync="workTransferVisible" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import WorkTransfer from '@/components/work-transfer/index.vue';

export default {
  name: 'PersonalCenter',
  components: {
    WorkTransfer
  },
  data() {
    return {
      activeTab: 'dataPermission',
      workTransferVisible: false
    };
  },
  computed: {
    ...mapGetters(['userInfo', 'appList']),
  },
  created() {
    this.$store.dispatch('getAppList');
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    showWorkTransfer() {
      this.workTransferVisible = true;
    }
  }
};
</script>

<style lang="less" scoped>
.personal-center {
  background-color: white;
  min-height: calc(100vh - 112px);
  margin: 16px;
  padding: 16px;
  .header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #f1f2f3;
    padding-bottom: 12px;
  }
  .back-link {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 16px;
    i {
      margin-right: 5px;
    }
  }

  .personal-center-content {
    display: flex;
    gap: 20px;
    flex: 1;
    min-height: 400px;

    .basic-info-section {
      width: 220px;
      background-color: #fff;
      padding: 12px 20px;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
      }

      .info-item {
        margin-bottom: 15px;
        margin-top: 15px;
        .label {
          color: #333;
          font-weight: bold;
        }

        .value {
          color: #303133;
          font-weight: 500;
        }
      }
    }

    .function-section {
      flex: 1;
      background-color: #fff;
      padding:8px 20px;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .section-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
      }
    }
  }
}
</style>
