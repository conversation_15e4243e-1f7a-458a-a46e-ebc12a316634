<template>
  <div class="conversation-container">
    <div class="conversation-list">
      <div v-for="(message, index) in messages" :key="index" :class="['message', message.type === 3 ? 'message-right' : 'message-left']">
        <div class="avatar">
          <el-avatar v-if="message.type === 3" icon="el-icon-user-solid" :size="40"></el-avatar>
          <div v-else class="iconfont3 icon-ABshiyan"></div>
        </div>
        <div class="message-content">
          <section class="message-container">
            <section v-if="message.type === 2" class="message-text hot">
              <p class="title">【{{ hot.title }}】</p>
              <p class="hot-item" v-for="item in hot.list" :key="item" @click="ask(item)">{{ item }}</p>
            </section>
            <div class="message-text" v-else>
              <vue-markdown>{{ message.content }}</vue-markdown>
            </div>
            <div class="message-time">{{ message.time }}</div>
          </section>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VueMarkdown from 'vue-markdown';
export default {
  components: {
    VueMarkdown
  },
  props: {
    messages: [],
    hot: {
      list: [],
      title: '热门问题'
    }
  },
  data() {
    return {
    };
  },
  methods: {
    ask(text) {
      this.$emit('ask', text);
    }
  }
};
</script>

<style lang="less">
.conversation-container {

  .conversation-list {
    margin-bottom: 20px;

    .message {
      display: flex;
      margin-bottom: 8px;

      .avatar {
        width: 48px;
        height: 48px;
      }

      .message-container {
        min-width: 50px;
        max-width: 400px;
      }

      .message-content {
        flex: 1;
        display: flex;

        .message-text {
          padding: 10px 16px;
          border-radius: 18px;
          font-size: 14px;
          line-height: 1.4;
        }

        .message-time {
          font-size: 12px;
          color: #999;
          margin-top: 5px;
        }
      }

      .hot {
        min-width: 220px;

        .title {
          border-bottom: 1px solid #e1e3e9;
          padding-bottom: 8px;
          font-weight: 500;
        }

        .hot-item {
          margin-top: 8px;
          padding: 0px 12px;
          color: #42c57a;
          font-weight: 400;
          cursor: pointer;
        }
      }

      &.message-left {
        justify-content: flex-start;

        .message-content {
          border-top-left-radius: 0;

          .message-text {
            background-color: #ecfdf5;
            color: black;
          }
        }
      }

      &.message-right {
        flex-direction: row-reverse;

        .avatar {
          margin-right: 0;
          margin-left: 10px;
        }

        .message-content {
          align-items: flex-end;
          justify-content: flex-end;

          .message-text {
            background-color: #42C57A;
            color: #fff;
          }

          .message-time {
            text-align: right;
          }
        }
      }
    }
  }

  .icon-ABshiyan {
    color: #42C57A;
    font-size: 40px;
  }
}
</style>