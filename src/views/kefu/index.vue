<template>
  <section class="drag-ball" :style="ballStyle" @mousedown="handleMouseDown">
    <el-popover width="150px" trigger="click" placement="left-start" popper-class="kefu-popover">
      <section class="button-container">
        <el-button plain @click="feedback">
          <span class="iconfont3 icon-wode_wentifankui_48" style="font-size: 18px;"></span>
          问题反馈
        </el-button>
        <!-- <el-button plain @click="assistantDialog = true">
          <span class="iconfont3 icon-lianxikefu" style="font-size: 18px;"></span>
          联系我们</el-button> -->
        <el-button plain @click="joinDialog = true">
          <span class="iconfont3 icon-saomaruqun" style="font-size: 18px;"></span>
          加入组织
        </el-button>
        <el-button plain @click="openHelpDoc">
          <span class="iconfont3 icon-bangzhushouce" style="font-size: 18px;"></span>
          帮助手册
        </el-button>
      </section>
      <el-button circle slot="reference">
        <i class="el-icon-service"></i>
      </el-button>
    </el-popover>

    <FeedBack v-if="feedbackDialog" :dialogVisible.sync="feedbackDialog"></FeedBack>
    <Assistant v-if="assistantDialog" :dialogVisible.sync="assistantDialog"></Assistant>
    <Join v-if="joinDialog" :dialogVisible.sync="joinDialog"></Join>
  </section>
</template>

<script>
import FeedBack from './feedback.vue';
import Assistant from './assistant.vue';
import Join from './join.vue';
export default {
  components: { FeedBack, Join, Assistant },
  data() {
    return {
      isDragging: false,
      ballPosition: { x: 32, y: 32 }, // 拖拽div初始位置
      startPosition: { x: 0, y: 0 }, // 鼠标按下时的位置
      feedbackDialog: false,
      joinDialog: false,
      assistantDialog: false
    };
  },
  computed: {
    ballStyle() {
      return {
        position: 'fixed',
        right: `${this.ballPosition.x}px`,
        bottom: `${this.ballPosition.y}px`
      };
    }
  },
  methods: {
    feedback() {
      this.feedbackDialog = true;
    },
    handleMouseDown(event) {
      this.isDragging = true;
      this.startPosition = { x: event.clientX, y: event.clientY };
      document.addEventListener('mousemove', this.handleMouseMove);
      document.addEventListener('mouseup', this.handleMouseUp);
    },
    handleMouseMove(event) {
      if (this.isDragging) {
        const diffX = event.clientX - this.startPosition.x;
        const diffY = event.clientY - this.startPosition.y;
        this.ballPosition = {
          x: this.ballPosition.x - diffX,
          y: this.ballPosition.y - diffY
        };
        this.startPosition = { x: event.clientX, y: event.clientY };
        this.constrainToScreen();
      }
    },
    handleMouseUp() {
      this.isDragging = false;
      document.removeEventListener('mousemove', this.handleMouseMove);
      document.removeEventListener('mouseup', this.handleMouseUp);
    },
    constrainToScreen() {
      // 限制拖拽时在屏幕范围内
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const ballRadius = 50; // 假设悬浮球半径为50px

      if (this.ballPosition.x < ballRadius) {
        this.ballPosition.x = ballRadius;
      } else if (this.ballPosition.x > windowWidth - ballRadius) {
        this.ballPosition.x = windowWidth - ballRadius;
      }
      if (this.ballPosition.y < ballRadius) {
        this.ballPosition.y = ballRadius;
      } else if (this.ballPosition.y > windowHeight - ballRadius - 100) {
        this.ballPosition.y = windowHeight - ballRadius - 100;
      }
    },
    openHelpDoc() {
      const url = 'https://docs.zuoyebang.cc/space/1651?ddtab=true';
      window.open(url);
    }
  }
};
</script>

<style lang="less" scoped>
.drag-ball {
  cursor: move;
  background: #ffffff;
  box-shadow: 0px 4px 30px 0px rgba(191, 212, 239, 1);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;

  i {
    font-size: 28px;
  }

  .el-button {
    width: 50px;
    height: 50px;
  }
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
  // width: 120px;

  .el-button {
    margin: 0;
    border: none;
    font-size: 16px;
  }
}
</style>
<style>
.kefu-popover {
  padding: 8px 0 !important;
}
</style>
