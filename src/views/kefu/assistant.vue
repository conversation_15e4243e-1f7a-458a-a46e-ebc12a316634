<template>
  <el-dialog class="assistant-dialog" :visible="dialogVisible" :before-close="closeDialog" :append-to-body="true" width="550px">
    <template slot="title">
      <div class="assistant-header">
        <div class="logo">
          <div class="iconfont3 icon-ABshiyan"></div>
          <span class="logo-text">AB小助手</span>
        </div>
      </div>
    </template>
    <div class="assistant-container">
      <section class="content">
        <div class="greeting">
          <h2>Hi,</h2>
          <p>我是你的AB小助手</p>
        </div>
        <div class="example-questions">
          <p v-for="item in demoQuestions" :key="item" @click="ask(item)">{{ item }}</p>
        </div>
        <chat :messages="list" class="chat-container" :hot="hot" @ask="ask"></chat>
      </section>
      <section class="footer">
        <div class="hot-topics">
          <el-button type="default" @click="ask(null, 2)">热门问题</el-button>
        </div>
        <div class="question-input input-area">
          <el-input v-model="question" placeholder="请告诉你想要解决的问题" @keyup.enter.native="sendMessage">
            <el-button slot="append" icon="el-icon-s-promotion" @click="sendMessage"></el-button>
          </el-input>
        </div>
      </section>
    </div>
    <!-- </div> -->
  </el-dialog>
</template>

<script>
import Chat from './chat.vue';
import dayjs from 'dayjs';
import { formatFormData } from '@/utils/util';

export default {
  components: { Chat },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  created() {
    this.getHotQuestions();
  },
  data() {
    return {
      demoQuestions: ['未命中实验原因？', '感觉数据不对，如何排查数据？'],
      list: [],
      hot: {
        title: '热门问题',
        list: []
      },
      question: ''
    };
  },
  methods: {
    getHotQuestions() {
      this.$service.get('HOT_QUESTION', { allback: 0, needLoading: true }).then((res) => {
        this.hot = {
          title: res.title,
          list: res.list
        };
      });
    },
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    ask(text, type = 3) {
      const data = {
        content: text,
        type,
        time: dayjs().format('HH:mm:ss')
      };
      this.list.push(data);
      if (type === 2) {
        return;
      }
      const postData = formatFormData({
        content: text
      });
      this.$service.post('ASK_QUESTION', postData, { needLoading: true }).then(res => {
        const data = {
          content: res,
          type: 1,
          time: dayjs().format('HH:mm:ss')
        };
        this.list.push(data);
      });
    },
    sendMessage() {
      if (!this.question) {
        this.$message.error(`请输入你想要解决的问题!`);
        return;
      }
      this.ask(this.question);
      this.question = '';
    }
  }
};
</script>

<style lang="less">
.assistant-dialog {
  .el-dialog {
    margin-top: 8vh !important;
    height: 84vh;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    padding: 0;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__header {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    padding: 16px;

    &>button {
      position: relative;
      top: initial;
      right: 0;
    }
  }

  .assistant-container {
    padding: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    overflow: hidden;

    .content {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding-bottom: 16px;
      overflow: auto;

      .greeting {
        text-align: center;
        margin-bottom: 24px;

        h2 {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 8px;
        }

        p {
          color: #4b5563;
        }
      }

      .example-questions {
        border-radius: 8px;
        margin-bottom: 16px;

        p {
          color: #1f2937;
          font-weight: 500;
          margin-bottom: 8px;
          background-color: #ecfdf5;
          padding: 12px;
          cursor: pointer;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .chat-container {}
    }

    .footer {
      padding-top: 12px;
      // border-top: 1px solid #fafbff;
    }

    .question-input {
      margin-top: 8px;

      /deep/ .el-input__inner {
        border-radius: 9999px;
      }

      .send-button {
        color: #9ca3af;
      }
    }

    .hot-topics {
      .el-button {
        font-size: 14px;
        color: #6b7280;
      }
    }
  }

  .assistant-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .logo {
      display: flex;
      align-items: center;

      .iconfont3 {
        font-size: 28px;
        color: #42C57A;
      }

      .logo-text {
        margin-left: 8px;
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  .input-area {
    .el-input-group__append {
      background-color: #42C57A;
      border-color: #42C57A;
      color: #fff;

      &:hover {
        background-color: darken(#42C57A, 10%);
      }
    }
  }
}
</style>