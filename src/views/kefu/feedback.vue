<template>
  <el-dialog title="我们珍视您的每一条反馈" :visible="dialogVisible" :before-close="closeDialog" :append-to-body="true">
    <template>
      <el-form :model="form" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
        <el-form-item label="问题类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio :label="1">bug反馈</el-radio>
            <el-radio :label="2">体验问题</el-radio>
            <el-radio :label="3">功能建议</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="问题描述" prop="description">
          <el-input v-model="form.description" placeholder="请详细描述建议或反馈的内容，如：我建议***产品增加***功能，我发现***产品存在***问题等" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="图片" prop="cos">
          <el-upload class="avatar-uploader" action="/earthworm/mis/upload/uploadimg" list-type="picture-card" :on-success="handleAvatarSuccess" :limit="1">
            <div slot="trigger">
              <i class="el-icon-plus"></i>
            </div>
            <div v-if="!form.cosAddress"><br/><br/>图片格式仅支持jpg、png，大小不超过10M</div>
          </el-upload>
        </el-form-item>
      </el-form>
    </template>

    <template slot="footer">
      <el-button @click="closeDialog">关闭</el-button>
      <el-button @click="submit" type="primary">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import ImageUpload from '@/components/common/upload/image-upload.vue';
import { formatFormData } from '@/utils/util';

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  components: { ImageUpload },
  data() {
    return {
      form: {
        type: 1,
        description: "",
        cosAddress: ""
      },
      rules: {
        type: [{ required: true, message: '请选择业务线', trigger: 'blur' }],
        description: [
          { required: true, message: '问题描述不能为空', trigger: 'change' },
        ]
      }
    };
  },
  methods: {
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          return;
        }
        const formData = formatFormData({
          ...this.form
        });
        this.$service.post('SUBMIT_QUESTION', formData, { allback: 0, needLoading: true }).then((res) => {
          this.$message.success('问题反馈成功！');
          this.closeDialog();
        });
      });
    },
    handleAvatarSuccess(res) {
      const { errNo = 0, data = {}, errStr = '上传图片失败！' } = res;
      if (errNo === 0) {
        this.form.cosAddress = data.url;
        return;
      }
      this.$message.error(errStr);
    }
  }
};
</script>

<style lang="less" scoped>
.ex_list {
  display: flex;
}

.ex_box {
  margin-left: 24px;
  cursor: pointer;

  &:first-child {
    margin-left: 0;
  }
}

.ex_card {
  width: 220px;
  height: 100%;
  box-sizing: border-box;

  /deep/ .el-card__body {
    padding-top: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

.ex_img {
  width: 46px;
  height: 46px;
}

.ex_title {
  color: #606266;
  font-size: 14px;
  text-align: center;
  margin-top: 14px;
  font-weight: 700;
}

.ex_desc {
  line-height: 1.65;
  margin-top: 14px;
  color: #9f9fab;
  font-size: 12px;
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  float: left;
  margin-right: 10px;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>