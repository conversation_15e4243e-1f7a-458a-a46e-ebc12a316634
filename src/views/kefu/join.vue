<template>
  <el-dialog
    title=""
    :visible="dialogVisible"
    :before-close="closeDialog"
    :append-to-body="true"
    custom-class="qr-code-dialog"
  >
    <section slot="title" class="header">
      <span>AB平台介绍&使用培训</span>
      <el-tag size="small">内部</el-tag>
    </section>
    <section class="container">
      <p class="text-doc">
        该群属于“作业帮教育科技(北京)有限公司”内部群，仅组织内部成员可以加入，如果组织外部人员收到此分享，需要先申请加入该组织。
      </p>
      <img :src="imgUrl" alt="" />
      <el-button type="primary" style="width: 280px; margin-bottom: 12px;" @click="joinGroup">
        一键加入
      </el-button>
    </section>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      imgUrl: ''
    };
  },
  created() {
    this.getQrCode();
  },
  methods: {
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    getQrCode() {
      this.$service.get('DINGGROUPVIEW', {}).then(res => {
        this.imgUrl = res.dingGroupImg;
      });
    },
    joinGroup() {
      window.open(
        'https://qr.dingtalk.com/action/joingroup?code=v1,k1,2AUvY4h1vHePyG9kEAteIpI+p1Q2fMv7F0GMOxLUPVU=&_dt_no_comment=1&origin=11',
        '_blank'
      );
    }
  }
};
</script>

<style lang="less" scoped>
.header {
  span {
    display: inline-block;
    font-size: 16px;
    font-weight: 500;
    margin-right: 8px;
  }

  .el-tag {
    font-size: 12px;
  }
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .text-doc {
    background: #f1f1f1;
    padding: 6px 12px;
    line-height: 22px;
    border-radius: 5px;
  }

  img {
    width: 280px;
    margin-bottom: 12px;
  }
}
</style>
<style lang="less">
.qr-code-dialog {
  .el-dialog__body {
    padding-top: 8px;
  }
}
</style>
