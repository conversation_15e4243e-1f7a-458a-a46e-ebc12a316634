<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
    <div class="search">
      <el-form size="small" ref="searchForm" inline>
        <el-form-item label="业务线：">
          <el-select v-model="query.appKey" placeholder="业务线" filterable>
            <el-option
              v-for="item in businessOptions"
              :key="item.appKey"
              :value="item.appKey"
              :label="item.displayName"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="指标类型：">
          <el-select v-model="query.isMust">
            <el-option label="全部指标" :value="-1"></el-option>
            <el-option label="必看指标" :value="1"></el-option>
            <el-option label="非必看指标" :value="0"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="指标状态：">
          <el-select v-model="query.status">
            <el-option label="使用中" :value="1"></el-option>
            <el-option label="已下线" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="指标模式：">
          <el-select v-model="query.source">
            <el-option label="全部指标" :value="-1"></el-option>
            <el-option label="客户端埋点" :value="1"></el-option>
            <el-option label="自定义事件" :value="2"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="关键字：">
          <el-input
            class="keyword_input"
            placeholder="请输入指标名称、描述、创建人搜索"
            v-model="query.search"
          >
            <i class="el-icon-search" slot="suffix"></i>
          </el-input>
        </el-form-item>
      </el-form>

      <el-button size="small" class="btn_add" type="primary" @click="handleAddMetric">
        新建指标
      </el-button>
    </div>

    <div class="table-content">
      <zyb-table :table-data="tableData" :table-columns="tableColumns">
        <template v-slot:custom="{ data: { item, scope } }">
          <template v-if="item.prop === 'experimentCnt'">
            <a @click="handleRelatedFlights(scope.row)">{{ scope.row[item.prop] }}</a>
          </template>
          <template v-else-if="item.prop === 'name'">
            <el-popover placement="top" trigger="hover">
              <span>{{ scope.row[item.prop] }}</span>
              <span slot="reference">{{ scope.row.name }}</span>
            </el-popover>
          </template>
          <template v-else-if="item.prop === 'description'">
            <el-popover placement="top" trigger="hover" v-if="scope.row.description">
              <span>{{ scope.row[item.prop] }}</span>
              <span slot="reference">{{ scope.row.description }}</span>
            </el-popover>
            <span v-else>-</span>
          </template>
          <template v-else-if="item.prop === 'isMust'">
            <el-switch
              class="op-switch"
              v-model="scope.row[item.prop]"
              :width="40"
              :active-value="1"
              :inactive-value="0"
              @click.native.prevent="(val) => handleComfirm(scope.row, val)"
              disabled
            ></el-switch>
          </template>

          <template v-else-if="['updateTime', 'createTime'].includes(item.prop)">
            <div v-if="scope.row[item.prop]">
              <!-- <span>{{scope.row[item.prop]}}</span> -->
              <!-- <span>{{ scope.row[item.prop].split(' ')[0] }}&nbsp;</span>
              <span style="white-space: nowrap">{{ scope.row[item.prop].split(' ')[1] }}</span> -->
              <el-popover placement="top" trigger="hover">
                <span>{{ scope.row[item.prop] }}</span>
                <span slot="reference">{{ scope.row[item.prop] }}</span>
              </el-popover>
            </div>
            <div v-else>-</div>
          </template>

          <template v-else-if="item.prop === 'action'">
            <zyb-text @click="handleEdit(scope.row)">修改</zyb-text>
            <el-dropdown placement="bottom" @command="clickMore">
              <zyb-text class="action_more">
                更多
                <i class="el-icon-arrow-down"></i>
              </zyb-text>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="scope.row.status == 1" :command="{ key: 1, ...scope.row }">
                  下线
                </el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status == 2" :command="{ key: 1, ...scope.row }">
                  上线
                </el-dropdown-item>
                <el-dropdown-item :command="{ key: 2, ...scope.row }">操作历史</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </template>
      </zyb-table>

      <el-pagination
        class="table_pagination"
        :current-page.sync="query.pn"
        :page-sizes="[10, 20, 50, 100]"
        :page-size.sync="query.rn"
        layout="total,sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>

    <flight-dialog
      v-if="flightVisible"
      :metricId="metricId.flight"
      :dialog-visible.sync="flightVisible"
    />

    <history-dialog
      v-if="historyVisible"
      :metricId="metricId.history"
      :dialog-visible.sync="historyVisible"
    />

    <modify-dialog
      v-if="modifyVisible"
      :metricId="metricId.modify"
      :dialog-visible.sync="modifyVisible"
      :businessOptions="filterBusinessOptions"
      @fetchMmetricList="fetchMmetricList"
    />
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import { formatDate } from '@/common/utils';
import { debounce, cloneDeep } from 'lodash';
import FlightDialog from './flight-dialog';
import HistoryDialog from './history-dialog';
import ModifyDialog from './modify-dialog/index.vue';
import ZybTable from '@/components/zyb-table/index.vue';
import ZybText from '@/components/zyb-text/index.vue';

export default {
  name: 'MetricList',
  components: {
    Page,
    FlightDialog,
    HistoryDialog,
    ModifyDialog,
    ZybTable,
    ZybText
  },
  data() {
    return {
      detailData: {},
      breadcrumbList: ['MetricList'],
      query: {
        isMust: -1,
        status: 1,
        appKey: 'all',
        search: '',
        pn: 1,
        rn: 10
        //source: -1
      },
      total: 0,
      tableData: [],
      tableColumns: [
        {
          label: 'ID',
          prop: 'id'
        },
        {
          label: '指标名称',
          prop: 'name',
          'min-width': '105px',
          title: false,
          line: 1,
          custom: true
        },
        {
          label: '指标模式',
          prop: 'source',
          'min-width': '105px',
          title: false,
          line: 1,
          render: (scope) => {
            return scope.row.source === 1 ? '客户端埋点' : '自定义事件';
          }
        },
        {
          label: '指标描述',
          prop: 'description',
          title: false,
          line: 1,
          custom: true
        },
        {
          label: '指标状态',
          render: (scope) => {
            return scope.row.status === 1 ? '使用中' : '已下线';
          }
        },
        {
          label: '业务线',
          prop: 'appName',
          'min-width': '105px',
          title: true
        },
        {
          label: '创建人',
          prop: 'createUser',
          title: true
        },
        {
          label: '创建时间',
          custom: true,
          prop: 'createTime',
          'min-width': 120,
          line: 1
        },
        {
          label: '更新时间',
          custom: true,
          prop: 'updateTime',
          'min-width': 120,
          line: 1
        },
        {
          label: '关联实验',
          prop: 'experimentCnt',
          custom: true
        },
        {
          label: '设为必看指标',
          prop: 'isMust',
          width: '140px',
          custom: true
        },
        {
          label: '操作',
          prop: 'action',
          custom: true,
          'min-width': 130
        }
      ],
      businessOptions: [],
      metricId: {
        flight: 0,
        history: 0,
        modify: 0
      },
      flightVisible: false,
      historyVisible: false,
      modifyVisible: false
    };
  },
  computed: {
    filterBusinessOptions() {
      const newVal = cloneDeep(this.businessOptions);
      newVal.shift();
      return newVal;
    }
  },
  watch: {
    query: {
      handler() {
        this.fetchMmetricList();
      },
      deep: true
    }
  },
  methods: {
    // 确认开关必看指标操作
    handleComfirm(row, type) {
      const tip = row.isMust === 1 ? '非必看指标' : '必看指标';
      this.$confirm(`确定将${row.name}设置为：${tip}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleMustDetail(row);
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    // 具体操作
    handleMustDetail(row) {
      const query = {
        id: row.id,
        must: row.isMust === 1 ? 0 : 1
      };
      this.$service.get('SETMUST', { ...query }, { allback: 0, needLoading: false }).then((res) => {
        this.fetchMmetricList();
        this.$message.success('设置成功');
      });
    },
    fetchAppList() {
      this.$service
        .get('APPLIST', {
          pn: 1, // 传递1获取包括全部的数据
          rn: 100000
        })
        .then((res) => {
          this.businessOptions = res.list || [];
          this.query.appKey = this.businessOptions.length ? this.businessOptions[0].appKey : '';
          this.fetchMmetricList();
        });
    },
    fetchMmetricList: debounce(function () {
      this.$service
        .get('INDICATOR_LIST', { ...this.query }, { allback: 0, needLoading: true })
        .then((res) => {
          this.tableData = res.list || [];
          this.total = res.total;
        });
    }, 200),
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000);
      }
    },
    handleAddMetric() {
      this.metricId.modify = 0;
      this.modifyVisible = true;
    },
    handleRelatedFlights(row) {
      this.metricId.flight = row.id;
      this.flightVisible = true;
    },
    handleEdit(row) {
      this.metricId.modify = row.id;
      this.modifyVisible = true;
    },
    clickMore(item) {
      if (item.key === 1) {
        const tips = item.status === 1 ? '下线' : '上线';
        this.$confirm(`确认${tips}指标：${item.name}`, '提示', {
          type: 'warning'
        }).then(() => {
          this.$service
            .get('INDICATOR_STATUS', { id: item.id, status: item.status === 1 ? 2 : 1 })
            .then((res) => {
              this.$message.success(`${tips}成功`);
              this.fetchMmetricList();
            });
        });
      } else if (item.key === 2) {
        this.metricId.history = item.id;
        this.historyVisible = true;
      }
    }
  },
  created() {
    this.fetchAppList();
  }
};
</script>

<style lang="less" scoped>
.search {
  position: relative;

  .el-select {
    width: 165px;
  }
}

.keyword_input {
  width: 260px;
  margin-left: -1px;

  // /deep/ .el-input__inner {
  //   border-radius: 0;
  // }

  /deep/ .el-input__suffix {
    right: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    cursor: pointer;
  }
}

.btn_add {
  position: absolute;
  top: 0;
  right: 0;
}

.table_pagination {
  margin-top: 20px;
  text-align: right;
}

.related_flights_text {
  color: rgb(70, 96, 239);
  cursor: pointer;
}

.table_tag {
  display: inline-block;
  border-radius: 4px;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  color: #2f2f3f;
  background: #f8f8fc;
  border: 1px solid #e7e9f5;
}

.table_tag_required {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
}

.action_edit {
  margin-right: 12px;
}

.action_more {
  color: #444;
}

.action_more i {
  font-size: 12px;
}

/deep/ .op-switch.is-disabled .el-switch__core,
/deep/ .op-switch.is-disabled .el-switch__label {
  background-color: rgb(155, 156, 158);
  border-color: rgb(155, 156, 158);
  cursor: pointer;
}
/deep/ .el-switch.is-disabled.is-checked .el-switch__core,
/deep/ .op-switch.is-disabled.is-checked .el-switch__label {
  border-color: #42c57a;
  background-color: #42c57a;
}
/deep/ .el-button--text-primary > span {
  font-size: 14px;
}
</style>