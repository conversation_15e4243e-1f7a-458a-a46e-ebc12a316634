<template>
  <el-dialog title="操作历史" :visible="dialogVisible" :before-close="closeDialog">
    <template>
      <div class="table-content">
        <zyb-table :table-data="tableData" :table-columns="tableColumns"></zyb-table>

        <el-pagination
          class="table-pagination"
          :current-page.sync="query.pn"
          :page-sizes="[10, 20, 50, 100]"
          :page-size.sync="query.rn"
          layout="total,sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { formatDate } from '@/common/utils';
import ZybTable from '@/components/zyb-table/index.vue';

export default {
  components: {
    ZybTable
  },
  props: {
    metricId: Number,
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      query: {
        pn: 1,
        rn: 10
      },
      total: 0,
      tableData: [],
      tableColumns: [
        {
          label: '操作时间',
          prop: 'createTime'
        },
        {
          label: '操作人',
          prop: 'createUser'
        },
        {
          label: '操作内容',
          prop: 'editType'
        }
      ]
    };
  },
  created() {
    this.fetchHistoryList();
  },
  watch: {
    query: {
      handler() {
        this.fetchHistoryList();
      },
      deep: true
    }
  },
  methods: {
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'yyyy-MM-dd HH:mm:ss');
      }
      return '-';
    },
    fetchHistoryList() {
      this.$service
        .get('INDICATOR_HISTORY', { ...this.query, id: this.metricId }, { needLoading: true })
        .then((res) => {
          this.tableData = res.list || [];
          this.total = res.total;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.table-pagination {
  margin: 20px 0;
  text-align: right;
}
</style>