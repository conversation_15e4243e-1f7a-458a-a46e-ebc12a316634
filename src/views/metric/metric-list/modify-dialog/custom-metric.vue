<template>
  <el-form-item label="设置指标">
    <div v-for="(itemRef, index) in events" :key="index" class="event-wrap">
      <div class="event_box">
        <div class="metric_index">
          <span>{{ String.fromCharCode(index + 65) }}</span>
        </div>
        <el-input
          slot="reference"
          v-model="itemRef.pointName"
          placeholder="请输入事件名称"
          class="event_select"
          v-if="!isEdit"
        ></el-input>
        <el-popover
          placement="top-start"
          width="200"
          trigger="hover"
          v-if="isEdit"
          :content="itemRef.pointName"
        >
          <el-input
            slot="reference"
            v-model="itemRef.pointName"
            placeholder=""
            class="event_select"
            v-if="isEdit"
            :disabled="true"
          ></el-input>
        </el-popover>

        <el-select
          class="way_select"
          v-model="itemRef.eventType"
          placeholder="请选择计算方式"
          popper-class="way_select_pop"
          filterable
          :disabled="disabled"
        >
          <el-option
            v-for="item in wayOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          >
            <el-tooltip :open-delay="300" placement="left" effect="light">
              <div slot="content">
                {{ item.label }}
                <br />
                {{ item.des }}
              </div>
              <span>{{ item.label }}</span>
            </el-tooltip>
          </el-option>
        </el-select>

        <i
          v-if="!isSingle && !disabled"
          class="event_delete_icon el-icon-copy-document"
          :disabled="events.length >= 10"
          @click="handleCopyEvent(index)"
        ></i>
        <i
          v-if="!disabled"
          class="event_delete_icon el-icon-delete"
          @click="handleDeleteEvent(index)"
        ></i>
      </div>
    </div>
    <!-- 取数逻辑 -->
    <div class="access-logic">
      <span>取数逻辑：</span>
      <el-popover placement="top" trigger="hover" width="700">
        <div class="intro-wrap">
          <h4 style="padding-bottom: 10px;">文档说明</h4>
          <el-radio-group v-model="radio1" style="padding-bottom: 5px;">
            <el-radio-button label="1">单一指标demo</el-radio-button>
            <el-radio-button label="2">组合指标demo</el-radio-button>
          </el-radio-group>
          <!-- <h4 style="padding: 3px 0">单一指标demo:</h4> -->
          <!-- <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入内容"
            v-model="textarea"
          ></el-input> -->
          <div class="demo-out" v-if="radio1 == 1" style="height: 380px; overflow:auto;">
            <pre>
select
  cuid,
  uid,
  A -- A 需为数值类型，如count(*) as A / 1 as A,并与指标事件前缀对齐
from
  (
    select
      cuid,
      '' as uid,
      A  -- A 需为数值类型，如count(*) as A / 1 as A,并与指标事件前缀对齐
    from
      tableName
    where
      dt in ('{@date}')
  )
           </pre
            >
          </div>
          <!-- <h4 style="padding: 3px 0">组合指标demo:</h4> -->
          <div class="demo-out" v-if="radio1 == 2" style="height: 380px; overflow:auto;">
            <pre>
select
  cuid,
  uid,
  A,-- A 需为数值类型，如count(*) as A / 1 as A,并与指标事件前缀对齐
  B -- B 需为数值类型，如count(*) as B / 1 as B,并与指标事件前缀对齐
  -- ... 此为组合指标后的多事件字母
from
  (
    select
      a.cuid as cuid,
      a.uid as uid,
      a.A as A,-- A 需为数值类型，如count(*) as A / 1 as A
      b.B as B -- B 需为数值类型，如count(*) as B / 1 as B
    from
      (
        select
          cuid,
          '' as uid,
          A -- A 需为数值类型，如count(*) as A / 1 as A
        from
          tableName
        where
          dt in ('{@date}')
      ) a
      full join (
        select
          cuid,
          '' as uid,
          B -- B 需为数值类型，如count(*) as B / 1 as B
        from
          tableName2
        where
          dt in ('{@date}')
      ) b on a.cuid = b.cuid
  )
           </pre
            >
          </div>
          <h4 style="padding: 3px 0">注意：</h4>
          <h4>1：cuid，uid，A为必须校验字段且A必须为数值类型；</h4>
          <h4>2：dt in ('{@date}')为必须限制条件。</h4>
        </div>

        <i slot="reference" class="el-icon-question"></i>
      </el-popover>
      <div class="s-wrap">
        <!-- <p class="s-list">select</p>
        <span class="s-con">
          <span class="s-item">dt,</span>
          <span class="s-item">cuid,</span>
          <span class="s-item">uid,</span>
          <span v-if="isSingle" class="s-item">A</span>
          <template v-else>
            <span v-for="(itemRef, index) in events" :key="index" class="s-item">
              <template v-if="index !== events.length - 1">
                {{ String.fromCharCode(index + 65) }},
              </template>
              <template v-else>{{ String.fromCharCode(index + 65) }}</template>
            </span>
          </template>
        </span> -->

        <!-- <p class="s-list">from</p> -->
        <div class="codemirror">
          <!-- <div class="tools-con">
          </div> -->
          <codemirror
            ref="myCm"
            v-model="sql"
            :options="cmOptions"
            @ready="onCmReady"
            @focus="onCmFocus"
            @input="onCmCodeChange"
          ></codemirror>
        </div>
        <!-- <p class="s-list">table_view</p> -->

        <div class="s-op">
          <div :style="{visibility: isShowBtn ? 'visible' : 'hidden'}">
            <el-button icon="el-icon-plus" size="mini" @click="addTableName()">添加表名</el-button>
          </div>
          <div>
             <el-button
              @click="formatSql"
              :disabled="isEdit && canEditSql !== 1"
              size="mini"
              class="tools-con-btn"
              type="primary"
            >
              <!-- <i class="iconfont znzt-format_icon tools-con-i"></i> -->
              格式化
            </el-button>
            <el-button
              @click="checkSql"
              :disabled="isEdit && canEditSql !== 1"
              size="mini"
              :loading="loading"
              class="s-op-btn"
              type="primary"
            >
              <!-- <i v-if="this.loading === false" class="iconfont znzt-jiankangzhenduan s-op-i"></i> -->
              语法检查
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 表名 -->
    <div class="table_name" v-if="isShowBtn">
      <div class="table_name_wrapper" style="margin-bottom: 5px;"  v-for="(item, idx) in sqlTableName" :key="idx">
        <el-input class="table_name_input" v-model="sqlTableName[idx]" placeholder="请输入内容"></el-input>
        <i class="el-icon-close" @click="handleDeleteParams(idx)"></i>
      </div>
    </div>
    <div
      v-if="!isSingle && !disabled && events.length <= 10"
      class="add-events-btn"
      @click="handleAddEvent"
    >
      + 新增事件
    </div>

    <div ref="desBoxCont" class="des-box-content">{{ desData.point_name }}</div>
  </el-form-item>
</template>

<script>
import EventFilter from './event-filter';
import { deepClone } from '@/utils/util.js';
import { debounce } from 'lodash';
import { codemirror } from 'vue-codemirror';
// import base style
import 'codemirror/lib/codemirror.css';
import 'codemirror/mode/sql/sql.js';
// theme css
import 'codemirror/theme/solarized.css';
// require active-line.js
import 'codemirror/addon/selection/active-line.js';
// closebrackets
import 'codemirror/addon/edit/closebrackets.js';
// keyMap
import 'codemirror/mode/clike/clike.js';
import 'codemirror/addon/edit/matchbrackets.js';
import 'codemirror/addon/comment/comment.js';
import 'codemirror/addon/dialog/dialog.js';
import 'codemirror/addon/dialog/dialog.css';
import 'codemirror/addon/search/searchcursor.js';
import 'codemirror/addon/search/search.js';
import 'codemirror/keymap/emacs.js';
import { format } from 'sql-formatter';
export default {
  components: {
    EventFilter,
    codemirror
  },
  props: {
    metricType: {
      type: Number,
      default: 1
    },
    event: {
      type: Array,
      default: () => []
    },
    tableData: {
      type: Array,
      default: () => []
    },
    reset: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    canEditSql: {
      type: Number,
      default: 1
    },
    sqlData: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isShowBtn: false,
      sqlTableName: [],
      radio1: 1,
      loading: false,
      events: [{}],
      eventsOptions: [],
      wayOptions: [],
      calcOptions: [
        { label: '等于', value: 'in' },
        { label: '不等于', value: 'notin' },
        { label: '包含', value: 'like' },
        { label: '不包含', value: 'notlike' },
        { label: '为空', value: 'empty' },
        { label: '不为空', value: 'notempty' },
        { label: '正则匹配', value: 'reg' }
      ],
      paramOptsList: {},
      loadings: {},
      desData: {},
      sql: '',
      loading: false,
      cmOptions: {
        tabSize: 4,
        styleActiveLine: true,
        lineNumbers: true,
        line: true,
        mode: 'text/x-mysql',
        theme: 'ambiance',
        indentWithTabs: true,
        smartIndent: true,
        matchBrackets: true,
        readOnly: this.isEdit && this.canEditSql !== 1 ? true : false
      },
      textarea:
        "select cuid,uid,A from(select cuid,'' as uid from default.platform_dwd_ps_sid_d where  dt in ('{@date}') )"
    };
  },
  computed: {
    isSingle() {
      return this.metricType === 1;
    },
    codemirror() {
      return this.$refs.myCm.codemirror;
    }
  },
  watch: {
    isSingle: {
      immediate: true,
      handler() {
        this.events = [{}];
      }
    },
    reset: {
      immediate: true,
      handler() {
        this.events = [{}];
        this.sql = '';
      }
    },
    tableData: {
      handler(newVal, oldVal) {
        console.log(newVal, oldVal);
        if(newVal && newVal.length >= 1) {
          this.isShowBtn = true;
          this.sqlTableName = newVal;
        }
      },
      immediate: true,
      deep: true
    }
    // event: {
    //   immediate: true, // 很重要！！！
    //   handle(newVal, oldVal) {
    //     console.log(newVal, oldVal);
    //     debugger;
    //     if (newVal && newVal.length) {
    //       this.events = newVal;
    //     }
    //   },
    //   deep: true
    // }
  },
  methods: {
    addTableName() {
      this.$emit('sqlTableEvent', this.sqlTableName);
      this.sqlTableName.push('');
    },
    // 删除数据库表
    handleDeleteParams(idx) {
      this.$emit('sqlTableEvent', this.sqlTableName);
      this.sqlTableName.splice(idx, 1);
    },
    handleAddEvent() {
      this.events.push({ filterConds: [] });
    },
    handleCopyEvent(index) {
      if (this.events.length >= 10) return;
      this.events.push(deepClone(this.events[index]));
    },
    handleDeleteEvent(index) {
      if (this.events.length <= 1) return;
      this.events.splice(index, 1);
    },
    validateEvents() {
      let valid = true;
      const isEmpty = this.validateSqlIsEmpty(this.sql);
      if (isEmpty) {
        this.$message.error('sql内容不能为空');
        return;
      }
      for (let i = 0; i < this.events.length; i++) {
        const event = this.events[i];
        if (!event.pointName || !event.eventType) {
          valid = false;
          break;
        }
        if (!valid) break;
      }

      if (valid) {
        return this.events;
      } else {
        this.$message.error('设置指标填写不完整');
        return false;
      }
    },
    // 获取配置信息
    getConfigInfo() {
      this.$service.get('INDICATOR_CONF').then((res) => {
        this.wayOptions = res.eventTypeList || [];
        this.calcOptions = res.calcTypeList || [];
      });
    },
    /* 代码格式化*/
    formatSql() {
      /* 获取文本编辑器内容*/
      let sqlContent = '';
      sqlContent = this.codemirror.getValue();
      //debugger
      console.log(format);
      const result = format(sqlContent);
      //debugger
      /* 将sql内容进行格式后放入编辑器中*/
      this.codemirror.setValue(result);
    },
    onCmReady(cm) {
      this.codemirror.setSize('-webkit-fill-available', 'auto');
    },
    onCmFocus(cm) {
      // console.log('the editor is focus!', cm)
    },
    onCmCodeChange(newCode) {
      console.log('this is new code', newCode);
      this.sql = newCode;
      this.$emit('handleSqlChange', this.sql);
    },
    // 校验sql语法
    checkSql() {
      const isEmpty = this.validateSqlIsEmpty(this.sql);
      if (isEmpty) {
        this.$message.error('sql内容不能为空');
        return;
      }
      if (!this.events.length || !this.events[0].pointName) {
        this.$message.error('事件不能为空');
        return;
      }

      this.loading = true;
      const resultFormData = this.formatFormData({ sql: this.sql, event: this.events });
      this.$service
        .post('CHECKSYNTAX', resultFormData, { needLoading: false })
        .then((res) => {
          this.loading = false;
          //debugger
          // if (res.status == 1) {
          //   this.$message.success('验证通过');
          //   return;
          // }
          if (res.status == 1) {
            this.isShowBtn = true;
            this.$message.success('验证通过');
            this.sqlTableName = res.tables  || [];
            this.$emit('sqlTableEvent', this.sqlTableName);
            return;
          }
          this.$message.error(res.msg, 6000);
        })
        .catch((err) => {
          this.loading = false;
        });
    },
    validateSqlIsEmpty(str) {
      let string = str;
      //去掉所有的换行符
      string = string.replace(/\r\n/g, '');
      string = string.replace(/\n/g, '');

      //去掉所有的空格（中文空格、英文空格都会被替换）
      //string = string.replace(/\s/g, '');

      //输出转换后的字符串
      console.log(string, str);
      return string === '' ? true : false;
    },
    // 转换成formData格式提交给后台
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    }
  },
  created() {
    //debugger;
    if (this.isEdit) {
      //debugger
      this.events = this.event;
      this.sql = this.sqlData;
    }
    this.getConfigInfo();
  },
  beforeDestroy() {}
};
</script>

<style lang="less" scoped>
/deep/.table_name_input .el-input__inner {
  width: 150px !important;
}

.table_name_wrapper {
  width: 170px;
  display: flex;
  align-items: center;
}

.metric_index {
  width: 32px;
  height: 32px;
  line-height: 32px;
  margin-right: 8px;
  padding: 0 4px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  box-sizing: border-box;
  background-color: #f8f8fc;
  display: inline-block;
  span {
    font-weight: 400;
  }
}

.event_select {
  // width: 260px;
  width: 300px;
  margin-right: 8px;
}

.way_select {
  width: 160px;
}

.event-wrap {
  margin-bottom: 20px;
}

.event_delete_icon {
  color: #83868f;
  font-size: 16px;
  cursor: pointer;
  vertical-align: middle;
  margin-left: 10px;
}

.add_filters_btn {
  display: inline-block;
  margin: 12px 0 18px;
  color: #728bfc;
  font-size: 12px;
  cursor: pointer;
  line-height: 1;
}

.add_filters_btn_not_single {
  margin-left: 36px;
}

.add-events-btn {
  display: inline-block;
  border: 1px dashed #c8c8d3;
  font-size: 12px;
  cursor: pointer;
  border-radius: 4px;
  padding: 4px 32px;
  margin-top: 12px;
  line-height: 1;

  &:hover {
    opacity: 0.7;
  }
}

.filters_box {
  position: relative;
  padding-left: 68px;
}

.connect_line {
  position: absolute;
  top: 16px;
  left: 50px;
  height: calc(100% - 32px);
  width: 18px;
  border-top: 1px solid #e1e2e6;
  border-left: 1px solid #e1e2e6;
  border-bottom: 1px solid #e1e2e6;
}

.and {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  // border: 1px solid #5392ff;
  border-radius: 2px;
  font-size: 12px;
  color: black;
  background: white;

  &:active {
    outline: none;
  }
  &:focus {
    outline: none;
  }
}

.filters_box_single {
  padding-left: 32px;

  .connect_line {
    left: 14px;
  }
}

.des-box-content {
  width: 260px;
  border-radius: 2px;
  border: 1px solid #e6ebf5;
  box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.15);
  height: 266px;
  background: white;
  padding: 10px;
  display: none;
}

.event_select_pop {
  padding: 20px;
  max-width: 300px;
}

.event_select_pop .el-select-dropdown__item {
  //padding: 0;

  div {
    padding-left: 16px;
    padding-right: 48px;
  }
}
//.s-wrap {
// box-sizing: border-box;
// padding: 10px;
// border: 1px solid #dcdfe6;
// border-radius: 4px;
//}
.s-con {
  margin-left: 30px;
}
.s-list {
  color: #728bfc;
}
.s-item {
  margin-right: 5px;
}
.s-op {
  display: flex;
  // justify-content: flex-end;
  justify-content: space-between;
  margin-top: 20px;
  margin-bottom: 10px;
}
.codemirror {
  height: auto;
  min-height: 180px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}
.tools-con {
  height: 30px;
  border-bottom: 1px solid #dcdfe6;
}
.tools-con-btn {
  border: none;
  //padding: 0 2px;
}
.tools-con {
  .el-button--mini {
    padding: 7px 8px;
    font-size: 13px;
    border-radius: 3px;
  }
  .el-button--mini:first-child {
    margin-left: 20px;
  }
}
.tools-con-i {
  font-size: 13px;
  color: #42c57a;
}
/deep/ .CodeMirror .CodeMirror-scroll {
  min-height: 180px;
  width: 100%;
  max-height: 400px;
}
.demo-out {
  display: block;
  resize: vertical;
  padding: 5px 15px;
  line-height: 1.5;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  font-size: inherit;
  color: #606266;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.intro-wrap /deep/ .el-radio-button--small .el-radio-button__inner {
  font-size: 14px;
}
</style>