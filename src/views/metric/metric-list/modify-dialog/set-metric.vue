<template>
  <el-form-item label="设置指标">
    <div v-for="(itemRef, index) in events" :key="index" class="event-wrap">
      <div class="event_box">
        <div v-if="!isSingle" class="metric_index">
          <span>{{ String.fromCharCode(index + 65) }}</span>
        </div>

        <el-select v-if="!isEdit" filterable remote reserve-keyword v-model="itemRef.point_id"
          placeholder="请输入事件名称" popper-class="event_select_pop" class="event_select"
          :loading="loading" :disabled="disabled"
          :remote-method="(query) => getEvents(query, itemRef, index)"
          @change="handleEventChange(itemRef, index)"
        >
          <el-option style="max-width: 300px !important"
            v-for="(item, index) in itemRef.eventsOptions"
            :key="index" :value="item.point_id"
            :label="`${item.point_cname}（${item.point_name}）`"
          >
            <el-popover :open-delay="300" placement="right" trigger="hover" width="200">
              <div>
                <h3 style="font-size: 14px; font-weight: 600">{{ item.point_cname }}</h3>
                <p style="padding: 20px 0">{{ item.point_name }}</p>
              </div>
              <p slot="reference" class="self-dropdown-item"
                 style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                {{ `${item.point_cname}（${item.point_name}）` }}
              </p>
            </el-popover>
          </el-option>
          <div v-if="itemRef.exceeded" class="event_select_search_hint">
            <span>只显示前100条结果，请完善搜索关键字</span>
          </div>
        </el-select>

        <el-popover
          v-if="isEdit"
          placement="top-start"
          width="200"
          trigger="hover"
          :content="itemRef.pointCname"
        >
          <el-input
            slot="reference"
            v-model="itemRef.pointCname"
            placeholder=""
            class="event_select"
            v-if="isEdit"
            :disabled="true"
          ></el-input>
        </el-popover>

        <el-select
          class="way_select"
          v-model="itemRef.eventType"
          placeholder="请选择计算方式"
          popper-class="way_select_pop"
          filterable
          :disabled="disabled"
        >
          <el-option
            v-for="item in wayOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          >
            <el-tooltip :open-delay="300" placement="left" effect="light">
              <div slot="content">
                {{ item.label }}
                <br />
                {{ item.des }}
              </div>
              <span>{{ item.label }}</span>
            </el-tooltip>
          </el-option>
        </el-select>

        <i
          v-if="!isSingle && !disabled"
          class="event_delete_icon el-icon-copy-document"
          :disabled="events.length >= 10"
          @click="handleCopyEvent(index)"
        ></i>
        <i
          v-if="!disabled"
          class="event_delete_icon el-icon-delete"
          @click="handleDeleteEvent(index)"
        ></i>
      </div>

      <!-- 过滤条件 -->
      <div :class="['filters_box', { filters_box_single: isSingle }]">
        <template v-if="itemRef.filterConds">
          <event-filter
            v-for="(item, i) in itemRef.filterConds"
            :key="i"
            :filter="item"
            :options="paramOptsList[itemRef.point_id] || []"
            :calc-options="calcOptions"
            :disabled="disabled"
            :isEdit="isEdit"
            @delete-filter="deleteFilter(index, i)"
          ></event-filter>
          <ul
            v-if="itemRef.filterConds.length && itemRef.filterConds.length > 1"
            class="connect_line"
          >
            <li v-for="i in itemRef.filterConds.length - 1" :key="i"></li>
            <div class="and">且</div>
          </ul>
        </template>
      </div>

      <div
        v-if="!disabled"
        :class="['add_filters_btn', { add_filters_btn_not_single: !isSingle }]"
        @click="handleAddFilter(itemRef, index)"
      >
        + 过滤条件
      </div>
    </div>

    <div
      v-if="!isSingle && !disabled && events.length <= 10"
      class="add_events_btn"
      @click="handleAddEvent"
    >
      + 新增事件
    </div>

    <div ref="desBoxCont" class="des_box_content">{{ desData.point_name }}</div>
  </el-form-item>
</template>

<script>
import EventFilter from './event-filter';
import { deepClone } from '@/utils/util.js';
import { debounce } from 'lodash';

export default {
  components: {
    EventFilter
  },
  props: {
    metricType: {
      type: Number,
      default: 1
    },
    event: {
      type: Array,
      default: () => []
    },
    reset: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      events: [{ filterConds: [], eventsOptions: [] }],
      eventsOptions: [],
      wayOptions: [],
      calcOptions: [
        { label: '等于', value: 'in' },
        { label: '不等于', value: 'notin' },
        { label: '包含', value: 'like' },
        { label: '不包含', value: 'notlike' },
        { label: '为空', value: 'empty' },
        { label: '不为空', value: 'notempty' },
        { label: '正则匹配', value: 'reg' }
      ],
      paramOptsList: {},
      loadings: {},
      desData: {}
    };
  },
  computed: {
    isSingle() {
      return this.metricType === 1;
    }
  },
  watch: {
    isSingle() {
      this.events = [{ filterConds: [] }];
    },
    reset() {
      this.events = [{ filterConds: [] }];
    },
    event(newVal) {
      if (newVal && newVal.length) {
        this.events = newVal;
      }
    }
  },
  methods: {
    // 当用户选择事件后
    handleEventChange(item, index) {
      //debugger;
      if (item.eventsOptions.length) {
        for (let i = 0, len = item.eventsOptions.length; i < len; i++) {
          const current = item.eventsOptions[i];
          if (item.point_id == current.point_id) {
            item.point_name = current.point_name;
            item.point_cname = current.point_cname;
            item.pointId = current.point_id;
            item.pointName = current.point_name;
            item.pointCname = current.point_cname;
            item.eventTypeField = ''; // 目前不用
            break;
          }
        }
      }

      this.getFilterData(item, index);
      this.pointNameChange(item, index);
      //debugger;
    },
    pointNameChange(item, index) {
      this.events[index].filterConds = [];
      //this.handleAddFilter(item,index);
    },
    handleAddEvent() {
      this.events.push({ filterConds: [] });
    },
    handleCopyEvent(index) {
      if (this.events.length >= 10) return;
      this.events.push(deepClone(this.events[index]));
    },
    handleDeleteEvent(index) {
      if (this.events.length <= 1) return;
      this.events.splice(index, 1);
    },
    // 当选择了事件之后获取事件对应的过滤条件
    getFilterData(item, index) {
      if (!this.paramOptsList[item.point_id] && !this.loadings[item.point_id]) {
        this.loadings[item.point_id] = true;
        this.$service.get('INDICATOR_EVENTATTR', { id: item.point_id }).then((res) => {
          this.paramOptsList[item.point_id] = res.list || [];
          this.loadings[item.point_id] = false;
        });
      }
    },
    handleAddFilter(item, index) {
      //debugger;
      // if (!this.paramOptsList[item.point_id] && !this.loadings[item.point_id]) {
      //   this.loadings[item.point_id] = true;
      //   this.$service.get('INDICATOR_EVENTATTR', {id: item.point_id}).then((res) => {
      //     this.paramOptsList[item.point_id] = res.list || [];
      //     this.loadings[item.point_id] = false;
      //   });
      // }

      this.events[index].filterConds.push({
        paramName: '',
        calcType: '',
        filterValue: []
      });
    },
    deleteFilter(index, i) {
      this.events[index].filterConds.splice(i, 1);
    },
    validateEvents() {
      let valid = true;
      for (let i = 0; i < this.events.length; i++) {
        const event = this.events[i];
        if (!event.point_name || !event.eventType) {
          valid = false;
          break;
        }

        if (event.filterConds && event.filterConds.length) {
          for (let j = 0; j < event.filterConds.length; j++) {
            const filter = event.filterConds[j];
            const keys = Object.keys(filter);
            keys.forEach((v) => {
              if (v === 'filterValue') {
                if (!['empty', 'notempty'].includes(filter['calcType']) && !filter[v].length) {
                  valid = false;
                }
              } else if (!filter[v]) {
                valid = false;
              }
            });
            if (!valid) break;
          }
        }

        if (!valid) break;
      }

      if (valid) {
        return this.events.map((v) => {
          const opt = v.eventsOptions.find((f) => f.point_id === v.point_id);
          //debugger;
          const obj = {
            pointId: opt.point_id,
            pointName: opt.point_name,
            pointCname: opt.point_cname
          };
          if (v.filterConds && v.filterConds.length) {
            obj.filterConds = v.filterConds;
          }
          obj.eventType = v.eventType;
          return obj;
        });
      } else {
        this.$message.error('设置指标填写不完整');
        return false;
      }
    },
    handleOptionHover(data, className) {
      this.desData = data;
      const pop = document.getElementsByClassName(className)[0];
      const top = pop.style.top;
      const left = pop.style.left;
      const width = pop.offsetWidth;
      const desBox = document.getElementsByClassName('desBox')[0];
      desBox.style.top = parseInt(top) + 4 + 'px';
      desBox.style.left = parseInt(left) + width - 5 + 'px';
      desBox.appendChild(this.$refs.desBoxCont);
      this.$refs.desBoxCont.style.display = 'block';
    },
    handleOptionBlur() {
      const desBox = document.getElementsByClassName('desBox')[0];
      desBox.style.top = -10000 + 'px';
      desBox.style.left = -10000 + 'px';
    },
    // 获取指标事件
    getEvents: debounce(function (query, itemRef, index) {
      if (query) {
        this.loading = true;
        this.$service
          .get('INDICATOR_EVENT', { keyword: query }, { needLoading: false })
          .then((res) => {
            itemRef.eventsOptions = res.list;
            // 是否超出最大100条限制
            itemRef.exceeded = res.total > res.num;
            this.loading = false;
          })
          .catch((err) => {
            this.loading = false;
          });
      }
    }, 500),
    // 获取配置信息
    getConfigInfo() {
      this.$service.get('INDICATOR_CONF').then((res) => {
        this.wayOptions = res.eventTypeList || [];
        this.calcOptions = res.calcTypeList || [];
      });
    }
  },
  created() {
    // this.$service.get('INDICATOR_EVENT', {keyword: '基础'}).then((res) => {
    //   this.eventsOptions = res.list || [];
    // });
    this.isEdit && this.getEvents();
    this.getEvents();
    this.getConfigInfo();

    const desBox = document.createElement('div');
    desBox.setAttribute('class', 'desBox');
    desBox.style.position = 'fixed';
    desBox.style.zIndex = 99999;
    document.body.appendChild(desBox);
  },
  beforeDestroy() {
    const desBox = document.getElementsByClassName('desBox')[0];
    document.body.removeChild(desBox);
  }
};
</script>

<style lang="less" scoped>
.metric_index {
  width: 32px;
  height: 32px;
  line-height: 32px;
  margin-right: 8px;
  padding: 0 4px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  box-sizing: border-box;
  background-color: #f8f8fc;
  display: inline-block;
  span {
    font-weight: 400;
  }
}

.event_select {
  // width: 260px;
  width: 300px;
  margin-right: 8px;
}

.event_select_search_hint {
  text-align: center;
  padding: 3px 0;
  color:#83868f;
  font-size: 13px;
}

.way_select {
  width: 160px;
}

.event-wrap {
  margin-bottom: 20px;
}

.event_delete_icon {
  color: #83868f;
  font-size: 16px;
  cursor: pointer;
  vertical-align: middle;
  margin-left: 10px;
}

.add_filters_btn {
  display: inline-block;
  margin: 12px 0 18px;
  color: #728bfc;
  font-size: 12px;
  cursor: pointer;
  line-height: 1;
}

.add_filters_btn_not_single {
  margin-left: 36px;
}

.add_events_btn {
  display: inline-block;
  border: 1px dashed #c8c8d3;
  font-size: 12px;
  cursor: pointer;
  border-radius: 4px;
  padding: 4px 32px;
  // margin-top: 12px;
  line-height: 1;

  &:hover {
    opacity: 0.7;
  }
}

.filters_box {
  position: relative;
  padding-left: 68px;
}

.connect_line {
  position: absolute;
  top: 16px;
  left: 50px;
  height: calc(100% - 32px);
  width: 18px;
  border-top: 1px solid #e1e2e6;
  border-left: 1px solid #e1e2e6;
  border-bottom: 1px solid #e1e2e6;
}

.and {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  // border: 1px solid #5392ff;
  border-radius: 2px;
  font-size: 12px;
  color: black;
  background: white;

  &:active {
    outline: none;
  }
  &:focus {
    outline: none;
  }
}

.filters_box_single {
  padding-left: 32px;

  .connect_line {
    left: 14px;
  }
}

.des_box_content {
  width: 260px;
  border-radius: 2px;
  border: 1px solid #e6ebf5;
  box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.15);
  height: 266px;
  background: white;
  padding: 10px;
  display: none;
}


</style>
