<template>
  <el-dialog top="30px" class="edit_metric_style" lock-scroll :title="title" width="55%" :visible="dialogVisible"
    :before-close="closeDialog">
    <template>
      <el-form ref="metricForm" label-width="80px" label-suffix="" :model="formData" :rules="rules">
        <el-form-item label="业务线" prop="appKey">
          <el-select v-model="formData.appKey" filterable :disabled="isEdit">
            <el-option v-for="item in businessOptions" :key="item.appKey" :value="item.appKey"
              :label="item.displayName"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="指标名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称，支持50个以内字符" :maxlength="50" :rows="1"
            show-word-limit></el-input>
        </el-form-item>

        <el-form-item label="负责人：" prop="createUser">
          <search-user v-model="formData.createUser" :usedefault="true"></search-user>
        </el-form-item>

        <el-form-item label="指标描述" prop="description">
          <el-input v-model="formData.description" placeholder="请输入描述信息，支持1000个以内字符" type="textarea" :maxlength="1000"
            :rows="4" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="指标模式" prop="source">
          <el-radio-group v-model="formData.source" :disabled="isEdit">
            <el-radio :label="1">客户端埋点</el-radio>
            <!-- <el-radio :label="2">自定义事件</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item label="指标类型" prop="description">
          <el-radio-group v-model="formData.type" :disabled="isEdit">
            <el-radio :label="1">单一指标</el-radio>
            <el-radio :label="2">
              组合指标
              <el-popover placement="top" width="100" trigger="hover">
                <span>针对多个事件，可选择多指标组合</span>
                <i slot="reference" class="el-icon-warning-outline"></i>
              </el-popover>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 客户端埋点 设置指标-->
        <template v-if="formData.source === 1">
          <set-metric ref="setMetric" :metricType="formData.type" :event="formData.event || []" :reset="reset"
            :disabled="isEdit" :isEdit="isEdit"></set-metric>
        </template>
        <!-- 自定义事件 设置指标-->
        <!-- <template v-else>
          <el-form-item label="数据周期" prop="dataTime">
            <el-radio-group v-model="formData.dataTime">
              <el-radio :label="1">
                天级
              </el-radio>
              <el-radio :label="3">
                累计
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <custom-metric
            ref="setMetric"
            :tableData="tableData"
            :metricType="formData.type"
            :event="formData.event || []"
            :reset="reset"
            :sqlData="formData.sql"
            :disabled="isEdit"
            :canEditSql="formData.canEditSql"
            :isEdit="isEdit"
            @handleSqlChange="handleSqlChange"
            @sqlTableEvent="handleSqlTableEvent"
          ></custom-metric>
        </template> -->

        <el-form-item label="数值格式">
          <el-select v-model="formData.dataFormat" style="width: 100px">
            <el-option label="数字" :value="1"></el-option>
            <el-option label="百分比" :value="2"></el-option>
          </el-select>
          <span class="form_label">小数位数</span>

          <el-input-number v-if="formData.dataFormat == 1" v-model="formData.digit" :max="5" :min="0"
            controls-position="right" @change="(val) => !val && val !== 0 && (formData.digit = -1)"></el-input-number>
          <span v-if="formData.dataFormat == 1" class="form_label">示例：{{ examples }}</span>
          <el-input-number v-if="formData.dataFormat == 2" v-model="formData.digit" :max="5" :min="0"
            controls-position="right"></el-input-number>
          <span v-if="formData.dataFormat == 2" class="form_label">示例：{{ examples }}</span>
        </el-form-item>

        <el-form-item v-if="formData.type === 2" label="指标关系" prop="relation">
          <el-input v-model="formData.relation" placeholder="请输入组合指标计算公式，示例：A/B+C" style="width: calc(100% - 35px)"
            :disabled="isEdit"></el-input>
          <el-popover placement="top" width="200" trigger="hover">
            <span>
              允许事件编号大写字母、输入括号()、加号+、减号-、乘号*、除号/;计算公式可任意组合，仅支持一层括号的计算
            </span>
            <i slot="reference" class="el-icon-warning-outline"></i>
          </el-popover>
        </el-form-item>

        <el-form-item label="必看指标">
          <el-switch v-model="formData.isMust" active-text="是" inactive-text="否"></el-switch>
        </el-form-item>
      </el-form>
    </template>

    <!-- 底部 -->
    <template slot="footer">
      <el-button @click="closeDialog">取消</el-button>
      <el-button v-if="!isEdit" type="primary" @click="metricSave('next')">添加下一个</el-button>
      <el-button type="primary" @click="metricSave('save')">保存</el-button>
    </template>
  </el-dialog>
</template>

<script>
import SetMetric from './set-metric';
import CustomMetric from './custom-metric';
import { deepClone } from '@/utils/util.js';
import SearchUser from '@/components/search-user/index.vue';

export default {
  components: {
    SetMetric,
    CustomMetric,
    SearchUser
  },
  props: {
    metricId: Number,
    dialogVisible: {
      type: Boolean,
      default: false
    },
    businessOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableData: [],
      isGrammarCheck: false,
      percent: '10.2456',
      formData: {
        appKey: '',
        name: '',
        description: '',
        type: 1,
        dataFormat: 1,
        digit: 4,
        relation: '',
        isMust: false,
        id: 0, // 新增的时候id为0
        source: 1, // 默认为客户端埋点
        sql: '', // 适应自定义事件
        dataTime: 1, // 数据周期 1：天级，3: 累计,
        canEditSql: 1, // 是否可编辑sql 1 可以编辑； 0 不可以编辑
        createUser: ''
      },
      rules: {
        name: [{ required: true, message: '请输入指标名称' }],
        appKey: [{ required: true, message: '请选择业务线' }],
        createUser: [{ required: true, message: '请选择负责人', trigger: 'blur' }]
      },
      reset: false,
      tableName: [],
      noCheck: false
    };
  },
  computed: {
    isEdit() {
      return !!this.metricId;
    },
    title() {
      return this.metricId ? '修改指标' : '新增指标';
    },
    examples() {
      if (this.formData.dataFormat == 1) {
        const str = '1,024.56789';
        const index = this.formData.digit === 0 ? 5 : 6 + this.formData.digit;
        return str.substring(0, index);
      } else {
        const str = '10.24567';
        const index = this.formData.digit === 0 ? 2 : 3 + this.formData.digit;
        return str.substring(0, index) + '%';
      }
    }
  },
  created() {
    if (this.metricId) {
      this.fetchMetricDetail();
    }
  },
  methods: {
    handleSqlTableEvent(ev) {
      this.tableName = ev;
      this.isGrammarCheck = true;
      this.noCheck = false;
    },
    fetchMetricDetail() {
      this.$service
        .get(
          'INDICATOR_INFO',
          { id: this.metricId },
          {
            allback: 0,
            needLoading: true
          }
        )
        .then((res) => {
          const data = deepClone(res);
          const result = this.formatResData(data);
          this.formData = result;
          if (result.tables.length >= 1) {
            this.noCheck = true;
            this.tableData = result.tables;
            this.tableName = result.tables;
          }
          // console.log(result, this.formData);
        });
    },
    formatResData(data) {
      data.isMust = data.isMust === 1 ? true : false;
      return data;
    },
    resetFormData() {
      // const oldFormData = deepClone(this.formData);
      const oldSource = this.formData.source;
      this.formData = {
        appKey: this.businessOptions.length ? this.businessOptions[0].appKey : '',
        name: '',
        description: '',
        type: 1,
        dataFormat: 1,
        digit: 4,
        relation: '',
        isMust: false,
        id: 0, // 新增的时候id为0
        source: oldSource, // 保持上一个指标模式
        sql: '', // 适应自定义事件
        dataTime: 1, // 数据周期 1：天级，3: 累计,
        canEditSql: 1 // 是否可编辑sql 1 可以编辑； 0 不可以编辑
      };
      this.reset = !this.reset;
      this.$nextTick(this.$refs['metricForm'].clearValidate);
    },
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    // 转换成formData格式提交给后台
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    metricSave(type) {
      if (!this.noCheck) {
        if (this.formData.source === 2 && !this.isGrammarCheck) {
          this.$message.error('请先语法检查然后再操作');
          return;
        }
      }

      let result1;
      this.$refs['metricForm'].validate((valid) => {
        if (valid) {
          result1 = this.formData;
        }
      });
      if (!result1) return;
      let params = {};
      this.formData.isMust = this.formData.isMust ? 1 : 0;
      if (!this.isEdit) {
        const result2 = this.$refs['setMetric'].validateEvents();
        if (!result2) return;

        params = {
          ...this.formData,
          event: result2,
          tables: this.tableName
        };
      } else {
        params = {
          ...this.formData,
          tables: this.tableName
        };
      }

      //debugger
      this.metricId && (params.id = this.metricId / 1);
      const message = this.metricId ? '更新成功' : '新建成功';
      const resultFormData = this.formatFormData(params);
      //debugger;
      this.$service.post('INDICATOR_EDIT', resultFormData, { needLoading: true }).then((res) => {
        this.$message.success(message);
        if (type === 'save') {
          this.$emit('fetchMmetricList');
          this.closeDialog();
          // 新建成功之后刷新列表
        } else {
          this.$emit('fetchMmetricList');
          this.resetFormData();
        }
      }).catch((err) => {
        console.log("catch error:");
        console.log(err);
      });
    },
    handleSqlChange(val) {
      this.formData.sql = val;
      console.log(val);
    }
  }
};
</script>

<style lang="less" scoped>
.info_icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid #999999;
  text-align: center;
  color: #999999;
  line-height: 12px;
  margin: 0 8px;
  box-sizing: content-box;
}

.form_label {
  color: #777;
  font-size: 12px;
  padding: 0 8px;
}

/deep/ .el-tooltip__popper {
  width: 300px;
}

/deep/ .el-radio__label {
  font-size: 14px;
}

.edit_metric_style {
  /deep/ .el-dialog__body {
    height: 530px;
    overflow: auto;
  }
}
</style>