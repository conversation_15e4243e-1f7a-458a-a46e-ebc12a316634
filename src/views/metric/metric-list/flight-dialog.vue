<template>
  <el-dialog title="当前指标所在实验" :visible="dialogVisible" :before-close="closeDialog">
    <template>
      <div class="search">
        <div>
          <span class="search_label">状态过滤</span>
          <el-radio-group v-model="query.status">
            <el-radio-button label="0">全部</el-radio-button>
            <el-radio-button label="2">调试中</el-radio-button>
            <el-radio-button label="3">运行中</el-radio-button>
            <el-radio-button label="4">已结束</el-radio-button>
          </el-radio-group>
        </div>
        <div>
          <el-input
            class="keyword_input"
            placeholder="请输入指标名称、描述、创建人搜索"
            v-model="query.search"
          ></el-input>
        </div>
      </div>

      <div class="table-content">
        <zyb-table :table-data="tableData" :table-columns="tableColumns"></zyb-table>

        <el-pagination
          class="table-pagination"
          :current-page.sync="query.pn"
          :page-sizes="[10, 20, 50, 100]"
          :page-size.sync="query.rn"
          layout="total,sizes, prev, pager, next, jumper"
          :total="total"
        />

        <el-alert
          title="当前页面只显示您拥有「查看权限」的实验相关信息。"
          type="warning"
          show-icon
        />
      </div>
    </template>

    <template slot="footer">
      <el-button type="primary" @click="closeDialog">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { formatDate } from '@/common/utils';
import ZybTable from '@/components/zyb-table/index.vue';

export default {
  components: {
    ZybTable
  },
  props: {
    metricId: Number,
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      query: {
        status: 0,
        search: '',
        pn: 1,
        rn: 10
      },
      total: 0,
      tableData: [],
      tableColumns: [
        {
          label: '实验名称',
          prop: 'displayName',
          title: true,
          line: 2
        },
        {
          label: '互斥组',
          prop: 'exclusiveName',
          title: true
        },
        {
          label: '实验状态',
          prop: 'status',
          render: (scope) => {
            return this.statusName[scope.row.status];
          }
        },
        {
          label: '创建人',
          prop: 'createUser'
        },
        {
          label: '实验流量',
          prop: 'flow',
          render: (scope) => {
            return scope.row.flow / 10 + '%';
          }
        },
        {
          label: '实验创建时间',
          prop: 'createTime',
          'min-width': '140px'
        }
      ],
      statusName: {
        0: '全部',
        2: '调试中',
        3: '运行中',
        4: '已结束'
      }
    };
  },
  watch: {
    query: {
      handler() {
        this.fetchFlightList();
      },
      deep: true
    }
  },
  created() {
    this.fetchFlightList();
  },
  methods: {
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'yyyy/MM/dd HH:mm');
      }
      return '-';
    },
    fetchFlightList() {
      this.$service
        .get('INDICATOR_EXPERIMENT', { ...this.query, id: this.metricId }, { needLoading: true })
        .then((res) => {
          this.tableData = res.list || [];
          this.total = res.total;
        });
    }
  }
};
</script>

<style lang="less" scoped>
.search {
  position: relative;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search_label {
  font-size: 14px;
  margin-right: 12px;
}

.keyword_input {
  width: 238px;
}

.table-pagination {
  margin: 20px 0;
  text-align: right;
}
</style>