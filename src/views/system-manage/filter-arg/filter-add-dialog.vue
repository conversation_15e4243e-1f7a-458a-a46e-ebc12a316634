<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-05 10:52:36
 * @LastEditors: zhuyue
 * @LastEditTime: 2021-08-12 17:01:24
 * @Description: 新建/编辑参数
-->
<template>
  <el-dialog
    width="45%"
    :title="title"
    :visible="dialogVisible"
    :before-close="dialogClose"
    class="arg_style_dialog"
    top="80px"
  >
   <el-alert
    v-if="type == 'add'"
    style="margin-bottom: 10px;"
    title="请添加您在本应用内的受众过滤维度参数，可以被服务端实验和服务端固化使用"
    type="info"
    show-icon>
  </el-alert>
    <!-- <h3 class="tip">
      请添加您在本应用内的受众过滤维度参数，可以被服务端实验和服务端固化使用
    </h3> -->
    <el-form 
      ref="argFormRef"
      :model="formData"
      label-width="100px"
      :rules="rules"
      >
      <el-form-item label="参数名称：" prop="displayName">
        <el-input
          :disabled="isViewDisabled"
          class="f-width-c"
          type="text"
          v-model="formData.displayName"
          maxlength="50"
          show-word-limit
          placeholder="请输入名称，支持50个以内字符"
        ></el-input>
      </el-form-item>
      <el-form-item label="描述：" prop="description">
        <el-input
          :disabled="isViewDisabled"
          type="textarea"
          v-model="formData.description"
          class="f-width-c"
          rows="3"
          resize="vertical"
          maxlength="1000"
          show-word-limit
          placeholder="请输入描述信息，支持1000个以内字符"
        ></el-input>
      </el-form-item>

      <el-form-item label="业务线：" prop="appKey">
        <el-select
          :disabled="isDisabled"
          v-model="formData.appKey"
          placeholder="请选择"
          @change="handleChangAppKey"
        >
          <el-option
            v-for="item in appOptionsData"
            :key="item.appKey"
            :label="item.displayName"
            :value="item.appKey"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="Key：" prop="keyName">
        <el-input
          :disabled="isDisabled"
          class="f-width-c"
          type="text"
          v-model="formData.keyName"
          maxlength="20"
          placeholder="代码中引用，使用20个字符以内的字母，数字，下划线"
        ></el-input>
      </el-form-item>

      <el-form-item label="类型：" prop="keyType">
        <el-select
          :disabled="isDisabled"
          v-model="formData.keyType"
          placeholder="请选择"
          @change="handleChangKeyType"
        >
          <el-option
            v-for="item in typeOptionsData"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="备选项：" prop="keyValueEnums">
        <el-select
          multiple
          filterable
          allow-create
          :disabled="isViewDisabled"
          v-model="formData.keyValueEnums"
          placeholder="请添加备选项"
          @change="handleChangAppKey"
        >
          <el-option
            v-for="item in keyValueEnumsOpytion"
            :key="item.id"
            :label="item.displayName"
            :value="item.appKey"
          ></el-option>
        </el-select>
      </el-form-item>

      <section style="margin-left: 20px;">
        <div style="display: flex;">
          <el-button type="text" class="adv-set" @click="openAdancedSet">
          高级设置
          <i :class="[isAdanced ? 'el-icon-caret-bottom' : 'el-icon-caret-top']"></i>
        </el-button>
        <p class="warn_style" v-if="!symbolValue.length">请选择操作符</p>
        </div>
        <section style="margin-top: 20px;" v-if="isAdanced">
          <el-checkbox :disabled="isViewDisabled" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选操作符</el-checkbox>
          <div style="margin: 15px 0;"></div>
          <el-checkbox-group v-model="symbolValue" @change="handleCheckedsSymbolChange">
            <el-checkbox style="margin-top:10px;" class="checked_style" :disabled="isViewDisabled" v-for="symbol in symbollist" :label="symbol" :key="symbol">{{symbol}}</el-checkbox>
          </el-checkbox-group>
        </section>
      </section>
          
    </el-form>
    <!-- 确定取消按钮组 -->
    <section slot="footer" class="dialog-footer">
      <el-button @click="dialogClose">取 消</el-button>
      <el-button type="primary" @click="handleSure">确定</el-button>
    </section>
  </el-dialog>
</template>
<script>
export default {
  name: 'create-dialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    editId: {
      type: Number | String
    },
    argId: {
      type: Number | String
    },
    type: {
      type: String
    }
  },
  computed: {
    isDisabled() {
      return this.argId != '';
    },
    isViewDisabled() {
      return this.type  === 'view';
    },
    add() {
      return '';
    }
  },
  data() {
    return {
      keyValueEnumsOpytion: [],
      symbolOptions: [],
      appOptionsData: [],
      typeOptionsData: [
        {
          label: 'String',
          value: 1
        },
        {
          label: 'Boolean',
          value: 2
        },
        {
          label: 'Float',
          value: 4
        },
      ],
      formData: {
        displayName: '',
        description: '',
        appKey:'', // 业务线
        keyName: '', // key 
        keyType: 1,
        keyValueEnums: ''
      },
      checkAll: false,
      symbolValue: [],  // checkbox选择的值
      symbollist: [], // checkbox的option
      isIndeterminate: true,
      isAdanced: false,
      addData: {
        clientType: 1,
        appKey: '',
        displayName: '',
        description: ''
      },
      title: '注册服务端实验过滤参数',
      rules: {
        displayName: [
          { 
            required: true,
            message: '请输入参数名称',
            trigger: 'change'
          }
        ],
        keyName: [
          { 
            required: true, 
            pattern: /^\w+$/,
            message: '代码中引用，使用20个字符以内的字母，数字，下划线',
            trigger: 'change'
          }
        ],
        keyType: [
          { 
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ],
        appKey: [
          { 
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ]
      }
    };
  },
  created() {
    if(this.argId) {
      this.title = '编辑服务端实验过滤参数';
    } else {
      this.title = '注册服务端实验过滤参数';
    }
    if(this.type === 'view') {
      this.title = '服务端实验过滤参数详情';
    }
  },
  mounted() {
    // 业务线option
    this.getapplist();
    // 所有的操作符
    this.getsymbollist();
  },
  methods: {
    /**
     * @description: 全选实现
     * @param {*} val
     * @return {*}
     */    
    handleCheckAllChange(val) {
      this.symbolValue = val ? this.symbollist : [];
      this.isIndeterminate = false;
    },
    handleCheckedsSymbolChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.symbollist.length;;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.symbollist.length;
    },
    /**
     * @description: 切换类型
     * @param {*} e
     * @return {*}
     */    
    handleChangKeyType(e) {
      console.log(',,,,', this.symbolOptions[e]);
      // 复选框展示数组
      let arrCheck = [];
      for(var i in this.symbolOptions[e]) {
        arrCheck.push(this.symbolOptions[e][i]);
      }
      this.symbollist = arrCheck;
      // 值回填
      if(e === 1) {
        // 字符串
        this.symbolValue = [arrCheck[0], arrCheck[1]];
      } else if(e === 2) {
        // 布尔值
        this.symbolValue = [arrCheck[0], arrCheck[1]];
      } else if(e === 4) {
        // 数字
        this.symbolValue = arrCheck;
      }
    },
    /**
     * @description: 得到所有操作符
     * @param {*}
     * @return {*}
     */    
    getsymbollist() {
      this.$service
        .get('SYMBOLLIST', {pn: 0, rn: 100000}, {needLoading: true})
        .then((res) => {
          this.symbolOptions = res;

          // 字符串类型默认值回填
          let arrCheck = [];
          for(var i in this.symbolOptions['1']) {
            arrCheck.push(this.symbolOptions['1'][i]);
          }
          this.symbollist = arrCheck;
          this.symbolValue = [arrCheck[0], arrCheck[1]];

          // 调取详情接口
          if(this.argId) {
            this.getArgDetail();
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 业务线
    getapplist() {
      this.$service
        .get('APPLIST', {pn: 0, rn: 100000}, {needLoading: true})
        .then((res) => {
          this.appOptionsData = res.list;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    /**
     * @description: 获取详情
     * @param {*}
     * @return {*}
     */    
    getArgDetail() {
      this.$service
        .get('AGRVIEEW', {id: this.argId}, {needLoading: true})
        .then((res) => {
          this.formData = res;
          // this.symbolValue = res.symbols
          let hasSymbols = res.symbols;


          // 编辑的话重写高级设置复选框option
          let arrCheck = [];
          for(var i in this.symbolOptions[this.formData.keyType]) {
            arrCheck.push(this.symbolOptions[this.formData.keyType][i]);
          }
          this.symbollist = arrCheck;
          
          
          // 数字->文字
          let currentOption = this.symbolOptions[this.formData.keyType];
          let arr = [];
          for(var i = 0; i < hasSymbols.length; i++) {
            arr.push(currentOption[hasSymbols[i]]);
          }

          this.symbolValue = arr;

          // ["2", "4", "6"] ==> 文字
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 高级设置切换
    openAdancedSet() {
      this.isAdanced = !this.isAdanced;
    },
    handleChangAppKey(val) {
      //   this.$emit("changeAppKey",val)
    },
    /**
     * @description: 提交保存
     * @param {*}
     * @return {*}
     */    
    handleSure() {
      if(this.type === 'view') {
        this.$emit('update:dialogVisible', false);
        return;
      }

      if(!this.symbolValue.length) {
        return;
      }
      // 当前选中类型的option值
      let currentOption = this.symbolOptions[this.formData.keyType];
      
      // symbolValue 当前选中值(文字)
      // 文字->数字
      let keyArr = [];
      for(var j = 0; j < this.symbolValue.length; j++) {
        for(var i in currentOption) {
          if(this.symbolValue[j] == currentOption[i]) {
            keyArr.push(i);
          }
          // arrCheck.push(this.symbolOptions[e][i])
        }
      }
      // console.log('this.currentOption', currentOption);
      // console.log('this.symbolValue', this.symbolValue);
      // console.log('keyArr===', keyArr);

      console.log("pppp====", this.formData);
      this.$refs.argFormRef.validate((valid)=> {
        if (valid) {
          let url = 'FILTERADD', params;
          if (this.argId) {
            // 更新
            url = 'FILTERUPDATE';
            params = {
              ...this.formData,
              id: this.argId,
              symbols: keyArr
            };
          } else {
            url = 'FILTERADD';
            params = {
              ...this.formData,
              symbols: keyArr
            };
          }

          this.$service.post(url, this.formatFormData(params), {needLoading: true, allback: 1}).then((res) => {
            if(res.errNo === 0) {
            this.$message.success('保存成功');
            // this.isDisabledSure = false;
            this.$emit('update:dialogVisible', false);
            this.$emit('succeed');
          } else {
            this.$message.error(res.errStr);
          }
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
      // this.defaultFormat();
    },
  },
  beforeDestroy() {
    this.$emit('resetEditId');
  }
};
</script>

<style lang="less" scoped>

/deep/ .el-select--small {
  width: 100%;
}

.arg_style_dialog {
  /deep/ .el-dialog__body {
    padding-top: 15px;
      // height: 450px;
      // overflow: auto;
    }
}


.warn_style {
  margin-top: 10px;
  color: red;
  font-size: 13px;
  margin-left: 7px;
}
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 760px;
  min-width: 30%;
}
.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}
.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }
  .detail-value {
    width: 200px;
    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }
  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;
    span {
      padding: 0 11px;
    }
  }
  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }
  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}
/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}
/deep/ .divider-con {
  margin: 20px 0;
}
.tip {
  padding: 10px 10px 20px;
}
/deep/ .el-radio__label {
  font-size: 14px;
}
</style>
