<template>
  <el-timeline class="history-content">
    <el-timeline-item v-for="item, index in list" :key="index" :timestamp="item.timestamp" type="primary">
      <section class="space-b top-item">
        <section class="time">
        {{ dateTimeFormat(item.createTime) }}
        </section>
        <span class="user-name" effect="dark">{{ item.createUser }}</span>
      </section>
      <section>
        <section v-if="!isChangeType(item)">
          {{ operationMap[item.operationType] }}
          <template v-if="item.operationDetail.new ">{{ item.operationDetail.new }}</template>
        </section>
        <section v-else>
          {{ operationMap[item.operationType] }}从{{ item.operationDetail.old }}改为{{ item.operationDetail.new }}
        </section>
      </section>
    </el-timeline-item>
  </el-timeline>
</template>

<script>
import dayjs from 'dayjs';
export default {
  name: 'TestTask',
  props: {
    id: {
      type: String | Number,
      default: ''
    }
  },
  data() {
    return {
      list:[],
      operationMap: {
        100: '创建过滤参数',
        101: '参数名称',
        102: '参数描述',
        103: '参数备选项新增',
        104: 'API地址',
        105: 'API参数',
        106: '删除过滤参数'
      }
    };
  },
  mounted() {
    if (this.id) {
      this.getHistory();
    }
  },
  methods: {
    getHistory() {
      this.$service.get('FILTERHISTORY', { id: this.id}).then(res=>{
        this.list = res || [];
      });
    },
    hasDetail(detail){
      return !!Object.keys(detail).length;
    },
    isChangeType(data){
      const detail = data.operationDetail || {};
      const hasDetail = this.hasDetail(detail);
      return hasDetail && detail.old && detail.new;
    },
    dateTimeFormat(value){
      const res = dayjs.unix(value).format('YYYY-MM-DD HH:mm:ss');
      return res;
    }
  }
};

</script>
<style lang="less" scoped>
.history-content {
  padding-left: 24px;

  .top-item {
    position: relative;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    padding-right: 12px;
    line-height: 20px;

    .user-name {
      color: rgba(0, 0, 0, 0.65)
    }

    .time {
      font-weight: 500!important;
      color: rgba(0, 0, 0, 1);
    }
  }

  .detail {
    margin-top: 8px;

    .el-row {
      margin-top: 8px;
    }
  }
}
</style>
