<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
    <template #header-action>
      <!-- <el-button class="" icon="el-icon-plus" type="primary" size="small" @click="handleRegisterArg">注册过滤参数</el-button>
      <el-button class="" icon="el-icon-plus" type="primary" size="small" @click="addOuterApi">新建外部API过滤</el-button> -->
      <el-button class="" icon="el-icon-plus" type="primary" size="small" @click="addOuterApi">新建过滤参数</el-button>
    </template>
    <div class="search">
      <el-form size="small" ref="searchForm" inline>
        <el-form-item>
          <el-input size="small" class="keyword_input" placeholder="参数id/名称/key/创建人" v-model="formData.keyword" @change="handleSearch">
          </el-input>
        </el-form-item>
        <el-form-item label="业务线：">
          <el-select v-model="formData.appKey" placeholder="请选择" @change="handleSearch">
            <el-option v-for="item in appOptionsData" :key="item.appKey" :label="item.displayName" :value="item.appKey">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型：">
          <el-select v-model="formData.keyType" placeholder="请选择" @change="handleSearch">
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="来源">
          <el-select v-model="formData.sourceType" placeholder="请选择" @change="handleSearch">
            <el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-content">
      <template-table :hasIndex="false" class="detail-table" ref="multipleTableParent" :table-column="tableColumns" :table-list="tableList" :tableBtns="tableBtns" @operationEvent="operationEvent">
      </template-table>
      <template-pagination :pagination-init="paginationInit" @changePage="changePage" @pageSizeChange="pageSizeChange">
      </template-pagination>
      <filter-add-dialog v-if="isShowDialog" :dialogVisible.sync="isShowDialog" :argId="argId" :type="type" @succeed="handleSucceed"></filter-add-dialog>
    </div>
    <el-drawer :visible.sync="drawer.show" title="操作历史" direction="rtl" width="550px">
      <History v-if="drawer.show" :id="drawer.id"></History>
    </el-drawer>
  </Page>
</template>

<script>
import { formatDate } from '@/common/utils';
import Page from '@/components/common/page/index.vue';
import TemplatePagination from '@/components/tools/template-pagination.vue';
import TemplateTable from '@/components/tools/template-table.vue';
import FilterAddDialog from './filter-add-dialog.vue';
import History from './history.vue';
// 全部/string/folat/boolean
const typeList = [{
  label: '全部',
  value: 0
}, {
  label: 'String',
  value: 1
}, {
  label: 'Boolean',
  value: 2
}, {
  label: 'Float',
  value: 4
}];
// 全部/AB平台/外部API
const sourceList = [{
  label: '全部',
  value: 0
}, {
  label: 'AB平台',
  value: 1
}, {
  label: '外部API',
  value: 2
}];

export default {
  name: 'TestTask',
  components: {
    Page,
    TemplateTable,
    TemplatePagination,
    FilterAddDialog,
    History
  },
  data() {
    return {
      typeList,
      sourceList,
      appOptionsData: [],
      argId: '',
      isShowDialog: false,
      breadcrumbList: ['FilterArg'],
      paginationInit: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      tableBtns: [
        {
          label: '编辑',
          role: 'edit',
          disabledHandler: (index, row) => {
            // 人群包 网络ip 不可编辑
            return ['__crowd__', 'ip'].includes(row.keyName);
          }
        },
        {
          label: '删除',
          role: 'del',
          disabledHandler: (index, row) => {
            // 人群包 网络ip 不可编辑
            return ['__crowd__', 'ip'].includes(row.keyName);
          }
        },
        {
          label: '操作历史',
          role: 'history'
        }
      ],
      formData: {
        appKey: '',
        keyword: '',
        keyType: 0,
        sourceType: 0
      },
      total: 0,
      tableList: [],
      tableColumns: [
        {
          label: 'id',
          name: 'id',
        },
        {
          label: '过滤参数名称',
          name: 'displayName',
          render: (h, { row }) => {
            const { displayName } = row;
            return <p onClick={() => this.handleName(row)} style="color: #42c57a; cursor: pointer;">{displayName}</p>;
          }
        },
        {
          label: 'Key',
          name: 'keyName',
          'min-width': '105px'
        },
        {
          label: '业务线',
          name: 'appName',
          'min-width': '105px'
        },
        {
          label: '来源',
          name: 'sourceType',
          render: (h, { row }) => {
            const { sourceType } = row;
            const text = sourceType === 1 ? 'AB平台' : '外部API';
            return <p>{text}</p>;
          },
          'min-width': '100px'
        },
        {
          label: '类型',
          name: 'keyType',
          'min-width': '105px',
          render: (h, { row }) => {
            const { keyType } = row;
            let keyMap = { 1: 'String', 2: 'Boolean', 4: 'Float' };
            return <p>{keyMap[keyType]}</p>;
          }
        },
        {
          label: '创建人',
          name: 'createUser',
          'min-width': '105px'
        },
        {
          label: '创建时间',
          name: 'createTime',
          'min-width': '105px',
          render: (h, { row }) => {
            const { createTime } = row;
            return <p>{formatDate(createTime * 1000)}</p>;
          }
        },
        {
          label: '更新时间',
          name: 'updateTime',
          'min-width': '105px',
          render: (h, { row }) => {
            const { updateTime } = row;
            return <p>{formatDate(updateTime * 1000)}</p>;
          }
        }
      ],
      businessOptions: [],
      metricId: {
        flight: 0,
        history: 0,
        modify: 0,
      },
      flightVisible: false,
      historyVisible: false,
      modifyVisible: false,
      pagination: {
        pn: 1,
        rn: 10,
      },
      type: '',
      drawer: {
        show: false,
        id: null
      }
    };
  },
  computed: {},
  mounted() {
    // 业务线option
    this.getapplist();
    this.handleSearch();
  },
  methods: {
    addOuterApi() {
      this.$router.push({
        path: '/system/manage/outerfilterapi',
        query: {
          status: 'add',
        }
      });
    },
    edit(data) {
      this.$router.push({
        path: '/system/manage/outerfilterapi',
        query: {
          status: 'edit',
          id: data.id
        }
      });
    },
    view(data) {
      this.$router.push({
        path: '/system/manage/outerfilterapi',
        query: {
          status: 'view',
          id: data.id
        }
      });
    },
    // 业务线
    getapplist() {
      this.$service
        .get('APPLIST', { pn: 0, rn: 100000 }, { needLoading: true })
        .then((res) => {
          this.appOptionsData = res.list;
          this.appOptionsData.unshift({
            displayName: '全部',
            appKey: ''
          });
        })
        .catch((err) => {
          console.log(err);
        });
    },
    handleSucceed() {
      this.getList();
    },
    handleName(row) {
      this.view(row);
    },
    handleRegisterArg() {
      this.type = 'add';
      this.argId = '';
      this.isShowDialog = true;
    },
    pageSizeChange(e) {
      this.paginationInit.pageSize = e;
      this.getList();
    },
    changePage(page) {
      this.paginationInit.currentPage = page;
      this.getList();
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    operationEvent(row, btnRole) {
      switch (btnRole) {
        case 'edit':
          this.edit(row);
          break;
          case 'del':
            const { id, canDelete, displayName } = row;
            if(!canDelete){
              this.$message.error('存在运行阶段实验引用，无法删除。');
              return;
            }
            this.$confirm(`删除该参数后，该参数将从系统中移除，后续新建实验时将无法使用该参数，确定删除${displayName}吗`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              const data = this.formatFormData({ id });
              this.$service
                .post('FILTERDELETE', data, { needLoading: true })
                .then((res) => {
                  this.$message.success('删除成功');
                  this.getList();
                })
                .catch((err) => {
                  console.log(err);
                });
            });
            break;
          case 'history':
            this.drawer = {
              show: true,
              id: row.id
            };

          break;
      }
    },
    // 查询列表数据
    handleSearch() {
      this.paginationInit.currentPage = 1;
      this.getList();
    },
    getList() {
      let params = {
        pn: this.paginationInit.currentPage,
        rn: this.paginationInit.pageSize
      };
      this.$service
        .get('FILTERLIST', { ...this.formData, ...params }, { needLoading: true })
        .then((res) => {
          this.tableList = res.list || [];
          this.paginationInit.total = res.total;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 重置
    handleReset() {
      this.paginationInit.currentPage = 1;
      this.formData = {
        appKey: '',
        keyword: ''
      };
      this.getList();
    }
  }
};
</script>

<style lang="less" scoped>
.description {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search {
  position: relative;
  // display: flex;
  // justify-content: space-between;

  .el-select {
    width: 175px;
  }
}

.keyword_input {
  width: 270px;
  margin-left: -1px;

  /deep/ .el-input__suffix {
    right: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    cursor: pointer;
  }
}

/deep/ .el-table {
  font-size: 14px;
}

.btn_add {
  position: absolute;
  top: -2px;
  right: 0;
}


.table-pagination {
  margin-top: 20px;
  text-align: right;
}

.table_description {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.related_flights_text {
  color: rgb(70, 96, 239);
  cursor: pointer;
}

.table_tag {
  display: inline-block;
  border-radius: 4px;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  color: #2f2f3f;
  background: #f8f8fc;
  border: 1px solid #e7e9f5;
}

.table_tag_required {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
}

.action_edit {
  margin-right: 10px;
}

/deep/ .op-switch.is-disabled .el-switch__core,
/deep/ .op-switch.is-disabled .el-switch__label {
  background-color: rgb(155, 156, 158);
  border-color: rgb(155, 156, 158);
  cursor: pointer;
}

/deep/ .el-switch.is-disabled.is-checked .el-switch__core,
/deep/ .op-switch.is-disabled.is-checked .el-switch__label {
  border-color: #42c57a;
  background-color: #42c57a;
}

/deep/ .el-button--text-primary>span {
  font-size: 14px;
}
</style>
