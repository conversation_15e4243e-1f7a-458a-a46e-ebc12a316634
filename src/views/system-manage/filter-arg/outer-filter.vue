<template>
  <Page class="container">
    <section class="header">
      <section>
        <i class="el-icon-arrow-left" @click="cancel"></i>
        <section class="page-title"> {{ title }}</section>
      </section>
      <section>
        <template v-if="isView">
          <el-button type="primary" @click="changeToEdit">编辑</el-button>
        </template>
        <template v-else>
          <el-button @click="confirm" type="primary">保存</el-button>
          <el-button @click="cancel">取消</el-button>
        </template>
      </section>
    </section>
    <section class="base block-item">
      <h3>基础信息</h3>
      <el-form size="small" ref="baseForm" label-width="150px" style="width: 560px" :rules="rules" :model="formData"
        :disabled="isView">
        <el-form-item label="业务线：" required>
          <el-select v-model="formData.appKey" placeholder="请选择业务线" :disabled="isEdit">
            <el-option v-for="item in appOptionsData" :key="item.appKey" :label="item.displayName" :value="item.appKey">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="来源：" required>
          <el-radio-group v-model="formData.sourceType" @change="typeChange" :disabled="isEdit">
            <el-radio v-for="item in sourceList" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="参数名称：" prop="displayName">
          <el-input v-model="formData.displayName" placeholder="请输入参数名称，支持50个以内字符">
          </el-input>
        </el-form-item>
        <el-form-item label="参数描述：">
          <el-input v-model="formData.description" type="textarea" placeholder="请输入描述信息">
          </el-input>
        </el-form-item>
        <el-form-item label="参数：" props="params" v-if="isOutApiType">
          <template v-if="formData.params.length">
            <section v-for="item, index in formData.params" :key="index" class="param-item">
              <el-form-item :prop="'params.' + index + '.key'"
                :rules="{ required: true, message: 'key不可为空', trigger: 'blur' }">
                <el-input v-model="item.key" placeholder="key（必填）"></el-input>
              </el-form-item>
              <el-form-item :prop="'params.' + index + '.value'"
                :rules="{ required: true, message: '默认值不可为空', trigger: 'blur' }">
                <el-input v-model="item.value" placeholder="请输入默认值"></el-input>
              </el-form-item>
              <el-button type="text" size="small" class="icon-delete-button" icon="el-icon-delete"
                @click="removeParam(index)">
              </el-button>
              <el-button type="text" size="small" @click="addParam" circle icon="el-icon-plus"></el-button>
            </section>
          </template>
          <el-button v-else type="default" size="mini" icon="el-icon-plus" class="add-param" circle
            @click="addParam"></el-button>
        </el-form-item>
        <section v-if="!isOutApiType">
          <el-form-item label="Key：" prop="keyName"
            :rules="{ required: true, validator: validateKeyName, trigger: ['change', 'blur'] }">
            <el-input class="f-width-c" type="text" v-model="formData.keyName" :disabled="isEdit" maxlength="20"
              placeholder="代码中引用，使用20个字符以内的字母，数字，下划线"></el-input>
          </el-form-item>
        </section>
        <el-form-item label="API地址：" prop="url" class="api-form-item" v-if="isOutApiType">
          <el-input v-model="formData.url" type="textarea" placeholder="https://example/example/example/">
          </el-input>
          <section class="float-button2">
            <el-button type="text" :disabled="false" @click="goDoc">api受众实现规范</el-button>
          </section>
          <section class="float-button">
            <el-button type="text" :disabled="!formData.url || isView" @click="apiDebug">调试<i
                class="el-icon-arrow-right el-icon--right"></i></el-button>
          </section>
        </el-form-item>
        <el-form-item label="类型：" required>
          <el-select v-model="formData.keyType" placeholder="请选择" :disabled="isEdit || isOutApiType"
            @change="keyTypeChange">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="负责人：" prop="createUser">
          <search-user v-model="formData.createUser" :usedefault="true" :disabled="isView"></search-user>
        </el-form-item>

      </el-form>
    </section>
    <section class="beixuan block-item" v-if="!isBooleanType">
      <h3>备选项信息</h3>
      <el-button type="text" size="small" @click="addItem" circle icon="el-icon-plus"
        v-if="!isView && (!formData.keyValueEnums || !formData.keyValueEnums.length)">
        添加备选项
      </el-button>
      <el-table :data="formData.keyValueEnums">
        <el-table-column type="index" width="50">
        </el-table-column>
        <el-table-column label="编码取值">
          <template slot-scope="scope">
            <el-input v-model="scope.row.value" clearable :disabled="isView || (isEdit && !scope.row.isNew)"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="编码说明">
          <template slot-scope="scope">
            <el-input v-model="scope.row.description" :disabled="isView"></el-input>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="80" v-if="!isView">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="addItem" circle icon="el-icon-plus">
            </el-button>
            <el-button type="text" size="small" class="icon-delete-button" icon="el-icon-delete"
              @click="removeItem(scope.$index)"
              v-if="(!isOutApiType || (formData.keyValueEnums.length > 1 && isOutApiType)) && scope.row.isNew">
            </el-button>
          </template>
        </el-table-column>
      </el-table>

    </section>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import { formatFormData } from '@/utils/util';
import SearchUser from '@/components/search-user/index.vue';
const sourceList = [{
  label: 'AB平台',
  value: 1
}, {
  label: '外部API',
  value: 2
}];
export default {
  name: 'TestTask',
  components: {
    Page,
    SearchUser
  },
  data() {
    return {
      sourceList,
      typeOptions: [
        {
          label: 'string',
          value: 1
        },
        {
          label: 'Boolean',
          value: 2
        },
        {
          label: 'Float',
          value: 4
        },
      ],
      formData: {
        "displayName": "",
        "description": "",
        "appKey": "",
        "keyType": 1,
        "url": "",
        "keyValueEnums": [],
        "sourceType": 1,
        "params": [],
        createUser: ''
      },
      appOptionsData: [],
      typeOptionsData: [
        {
          label: 'String',
          value: 1
        },
        {
          label: 'Boolean',
          value: 2
        },
        {
          label: 'Float',
          value: 4
        },
      ],
      breadcrumbList: ['FilterArg', 'OuterFilterApi'],
      rules: {
        url: [{ required: true, message: 'API地址不可为空!', trigger: 'blur' }],
        displayName: [{ required: true, message: '参数名称不可为空!', trigger: 'blur' }],
        createUser: [{ required: true, message: '请选择负责人', trigger: 'blur' }]
      }
    };
  },
  computed: {
    title() {
      const statusMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      };
      const query = this.$route.query;
      const text = statusMap[query.status];
      return `${text}过滤规则`;
    },
    isView() {
      const query = this.$route.query;
      return query.status === 'view';
    },
    isEdit() {
      const query = this.$route.query;
      return query.status === 'edit';
    },
    isOutApiType() {
      return this.formData.sourceType === 2;
    },
    isBooleanType() {
      return this.formData.keyType === 2;
    }
  },
  created() {
    this.getapplist();
    const query = this.$route.query;
    if (query.id) {
      this.getInfo(query.id);
    }
  },
  methods: {
    validateKeyName(rule, value, callback) {
      if (!value) {
        return callback(new Error('Key不能为空'));
      }
      if (!/^[A-Za-z0-9_]+$/.test(value)) {
        callback(new Error('Key只支持输入字母、数字、下划线'));
      } else {
        callback();
      }
    },
    typeChange() {
      this.formData.keyValueEnums = [];
      if (this.isOutApiType) {
        this.formData.keyType = 1;
        this.addItem();
      } else {
        this.formData.params = [];
      }
    },
    keyTypeChange() {
      if (this.isBooleanType) {
        this.formData.keyValueEnums = [];
      }
    },
    changeToEdit() {
      this.$router.replace({
        path: this.$route.path,
        query: {
          ...this.$route.query,
          status: 'edit'
        }
      });
    },
    addItem() {
      const item = {
        value: '',
        description: '',
        isNew: true
      };
      this.formData.keyValueEnums.push(item);
    },
    removeItem(index) {
      this.formData.keyValueEnums.splice(index, 1);
    },
    addParam() {
      const item = {
        value: '',
        key: ''
      };
      this.formData.params.push(item);
    },
    removeParam(index) {
      this.formData.params.splice(index, 1);
    },
    getInfo(id) {
      this.$service
        .get('AGRVIEEW', { id }, { needLoading: true })
        .then((res) => {
          this.formData = res;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getapplist() {
      this.$service
        .get('APPLIST', { pn: 0, rn: 100000 }, { needLoading: true })
        .then((res) => {
          this.appOptionsData = res.list;
          const query = this.$route.query;
          if (this.appOptionsData.length && !query.id) {
            this.formData.appKey = this.appOptionsData[0].appKey;
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    confirm() {
      this.$refs.baseForm.validate(async (valid) => {
        if (valid) {
          const list = this.formData.keyValueEnums;
          const pass = list.every(item => item.value);
          if (!pass) {
            this.$message.error('备选项不可为空');
            return;
          }
          if (this.isOutApiType) {
            const px = {};
            Array.from(this.formData.params).forEach(x => px[x.key] = x.value.toString());
            const data = formatFormData({
              url: this.formData.url,
              params: px
            });
            await this.$service.post('DEBUGFILTERAPI', data);
          }
          const query = this.$route.query;
          const url = query.id ? 'UPDATEFILTERAPI' : 'ADDFILTERAPI';
          const keyValueEnums = this.formData.keyValueEnums.map(item => {
            return {
              value: item.value,
              description: item.description
            };
          });
          const params = formatFormData({
            ...this.formData,
            keyValueEnums: JSON.stringify(keyValueEnums)
          });
          await this.$service.post(url, params);
          this.cancel();
        }
      });
    },
    async apiDebug() {
      const px = {};
      Array.from(this.formData.params).forEach(x => px[x.key] = x.value.toString());
      const data = formatFormData({
        url: this.formData.url,
        params: px
      });
      await this.$service.post('DEBUGFILTERAPI', data);
      this.$message.success('调试成功');
    },
    goDoc() {
      window.open("https://docs.zuoyebang.cc/doc?fileId=1821450860280377345");
    },
    async cancel() {
      // 编辑过滤参数时，点击左上角返回或右上角取消按钮时，弹出返回确认窗口，窗口文案“本次编辑内容尚未保存，返回将放弃页面已填写内容，确定返回吗？”
      if (!this.isView) {
        await this.$confirm('本次编辑内容尚未保存，返回将放弃页面已填写内容，确定返回吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
      }
      this.$router.replace({
        name: 'FilterArg',
      });
    },
    handleChangKeyType(e) {
      console.log(',,,,', this.symbolOptions[e]);
      // 复选框展示数组
      let arrCheck = [];
      for (var i in this.symbolOptions[e]) {
        arrCheck.push(this.symbolOptions[e][i]);
      }
      this.symbollist = arrCheck;
      // 值回填
      if (e === 1) {
        // 字符串
        this.symbolValue = [arrCheck[0], arrCheck[1]];
      } else if (e === 2) {
        // 布尔值
        this.symbolValue = [arrCheck[0], arrCheck[1]];
      } else if (e === 4) {
        // 数字
        this.symbolValue = arrCheck;
      }
    },
  }
};
</script>

<style lang="less" scoped>
.container {
  .block-item {
    margin-top: 12px;
    margin-bottom: 32px;

    h3 {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 16px;

      &::before {
        content: ' ';
        display: inline-block;
        background-color: #42C57A;
        height: 14px;
        width: 3px;
        margin-right: 10px;
      }
    }

    .add-param {
      margin-left: 0;
      margin-bottom: 20px;
      border-color: #42c57a;
      padding: 1px;

      /deep/ i {
        font-size: 12px;
        color: #42c57a;
      }
    }

    .icon-delete-button {
      color: #f56c6c;
      margin-left: 4px;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    align-items: center;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    &>section {
      display: flex;
      align-items: center;
    }

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin-left: 12px;
    }

    i {
      cursor: pointer;
    }
  }
}

.param-item {
  display: flex;
  gap: 8px;
  margin-bottom: 10px;
  align-items: flex-start;
  height: 34px;
}

.api-form-item {
  /deep/ .el-form-item__content {
    display: flex;
    position: relative;

    textarea {
      min-height: 50px !important;
    }

    .float-button {
      position: absolute;
      right: -50px;
      bottom: 0;
      display: flex;
      align-items: flex-end;
      margin-left: 12px;

      button {
        padding: 0;

        i {
          margin-left: 0;
        }
      }
    }

    .float-button2 {
      position: absolute;
      right: -102px;
      ;
      bottom: 35px;
      display: flex;
      align-items: flex-end;
      margin-left: 12px;

      button {
        padding: 0;

        i {
          margin-left: 0;
        }
      }
    }
  }
}
</style>
