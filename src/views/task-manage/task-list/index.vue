<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
    <div class="search">
      <el-form size="small" ref="searchForm" inline>
        <el-form-item label="">
          <el-input
            size="small"
            class="keyword_input"
            placeholder="实验名称/id/创建人"
            v-model="query.keyword"
            @change="handleSearch"
          >
          </el-input>
        </el-form-item>
        <!-- <el-form-item label="id：">
          <el-input
            size="small"
            class="keyword_input"
            placeholder="实验id搜索"
            v-model="query.id"
          >
          </el-input>
        </el-form-item>
          <el-form-item>
            <el-button-group class="self-collapse-btns">
              <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
              <el-button size="small" class="reset-btn" @click="handleReset">重置</el-button>
            </el-button-group>
          </el-form-item> -->
      </el-form>
    </div>

    <div class="table-content">
      <template-table
        :hasIndex="false"
        class="detail-table"
        ref="multipleTableParent"
        :table-column="tableColumns"
        :table-list="tableList"
        :tableBtns="tableBtns"
        @operationEvent="operationEvent"
        >
      </template-table>
      <template-pagination
        :pagination-init="paginationInit"
        @changePage="changePage"
        @pageSizeChange="pageSizeChange"
      >
      </template-pagination>
    </div>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import {formatDate} from '@/common/utils';
import {debounce, cloneDeep} from 'lodash';
import TemplateTable from '@/components/tools/template-table.vue';
import TemplatePagination from '@/components/tools/template-pagination.vue';


export default {
  name: 'TestTask',
  components: {
    Page,
    TemplateTable,
    TemplatePagination
  },
  data() {
    return {
      breadcrumbList: ['TestTask'],
      paginationInit: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
    tableBtns: [
        {
          label: '查看',
          role: 'view',
          disabledHandler: (index, row) => {
            // 修改按钮是否可点击
            if (row.canEdit === 0) return true;
          }
        },
      ],
      query: {
        keyword: '',
        id: '',
      },
      total: 0,
      tableList: [],
      tableColumns: [
        {
          label: 'ID',
          name: 'id'
        },
        {
          label: '名称',
          name: 'displayName',
          'min-width': '105px'
        },
        {
          label: '时间',
          name: 'startTime',
          'min-width': '105px',
          render: (h, {row}) => {
            const { startTime,  endTime} = row;
            const formatEndTime = endTime === 0 ? '至今' : formatDate(endTime * 1000, 'yyyy-MM-dd');
            return <p>{formatDate(startTime * 1000, 'yyyy-MM-dd')} ~ {formatEndTime}</p>;
          }
        },
        {
          label: '创建人',
          name: 'createUser',
          'min-width': '105px',
          title: true,
        },
        {
          label: '状态',
          name: 'status',
          render: (h, {row}) => {
            const { status } = row;
            // 0 默认的初始态，1草稿，2调试中，3进行中，4完成
            const statusMap = {'0': '初始态', '1': '草稿', '2': '调试中', '3': '进行中', '4': '完成'};
            return <p>{statusMap[status]}</p>;
          }
        },
        {
          label: '未完成数量',
          name: 'unfinishNum',
          'min-width': '105px',
        },
        {
          label: '是否失败',
          name: 'hasFailed',
          render: (h, {row}) => {
            const { hasFailed } = row;
            return <p>{hasFailed ? '是': '否' }</p>;
          }
        }
      ],
      businessOptions: [],
      metricId: {
        flight: 0,
        history: 0,
        modify: 0,
      },
      flightVisible: false,
      historyVisible: false,
      modifyVisible: false,
      pagination: {
        pn: 1,
        rn: 10,
      },
    };
  },
  computed: {},
  methods: {
     pageSizeChange(e) {
      this.paginationInit.pageSize = e;
      this.getList();
    },
    changePage(page) {
      this.paginationInit.currentPage = page;
      this.getList();
    },
    operationEvent(row, btnRole) {
      switch (btnRole) {
        case 'view':
          console.log("----");
          this.$router.push({ 
            path: '/task/list/testView',
            query : {experimentId: row.id}
          });
          break;
      }
    },
    // 查询列表数据
    handleSearch() {
      this.paginationInit.currentPage = 1;
      this.getList();
    },
    getList() {
      let params = {
        pn: this.paginationInit.currentPage,
        rn: this.paginationInit.pageSize
      };
      this.$service
        .get('TASKLIST', {...this.query, ...params}, {needLoading: true})
        .then((res) => {
          this.tableList = res.list || [];
          this.paginationInit.total = res.total;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 重置
    handleReset() {
      this.paginationInit.currentPage = 1;
      this.query = {
        id: '',
        keyword: ''
      };
      this.getList();
    }
  },
  created() {
    this.handleSearch();
  },
};
</script>

<style lang="less" scoped>
.search {
  position: relative;

  .el-select {
    width: 175px;
  }
}

.keyword_input {
  width: 270px;
  margin-left: -1px;

  /deep/ .el-input__suffix {
    right: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    cursor: pointer;
  }
}
/deep/ .el-table {
  font-size: 14px;
}
.btn_add {
  position: absolute;
  top: 0;
  right: 0;
}

.table-pagination {
  margin-top: 20px;
  text-align: right;
}

.table_description {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.related_flights_text {
  color: rgb(70, 96, 239);
  cursor: pointer;
}

.table_tag {
  display: inline-block;
  border-radius: 4px;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  color: #2f2f3f;
  background: #f8f8fc;
  border: 1px solid #e7e9f5;
}

.table_tag_required {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
}

.action_edit {
  margin-right: 10px;
}
/deep/ .op-switch.is-disabled .el-switch__core,
/deep/ .op-switch.is-disabled .el-switch__label {
  background-color: rgb(155, 156, 158);
  border-color: rgb(155, 156, 158);
  cursor: pointer;
}
/deep/ .el-switch.is-disabled.is-checked .el-switch__core,
/deep/ .op-switch.is-disabled.is-checked .el-switch__label {
  border-color: #42c57a;
  background-color: #42c57a;
}
/deep/ .el-button--text-primary > span {
  font-size: 14px;
}
</style>