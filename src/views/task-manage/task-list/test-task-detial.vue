<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
     <div class="search">
      <el-form size="small" ref="searchForm" inline>
           <el-form-item label="类型：">
            <el-select v-model="formData.type" placeholder="请选择类型" @change="handleSearch($event, 'type')">
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select
              v-model="formData.status"
              clearable
              placeholder="请选择状态"
              @change="handleSearch($event, 'status')"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="typeValue === 2" label="指标：">
            <el-select
              v-model="sport"
              clearable
              placeholder="请选择"
              @change="handleSearch($event, 'status')"
            >
              <el-option
                v-for="item in targetOption"
                :key="item.indId"
                :label="item.indName"
                :value="item.indId"
              ></el-option>
            </el-select>
          </el-form-item>
      </el-form>
      
      <el-button-group class="self-collapse-btns">
        <!-- <el-button class="go_back" @click="goBack()" size="small" type="primary">返回</el-button> -->
      </el-button-group>
    </div>

    <div class="table-content">
      <template-table
        :hasIndex="false"
        class="detail-table"
        ref="multipleTableParent"
        :table-column="calColumns"
        :table-list="tableList"
        :tableBtns="tableBtns"
        @operationEvent="operationEvent"
        >
      </template-table>
      <template-pagination
        :pagination-init="paginationInit"
        @changePage="changePage"
        @pageSizeChange="pageSizeChange"
      >
    </template-pagination>
    </div>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import {formatDate} from '@/common/utils';
import {debounce, cloneDeep} from 'lodash';
import TemplateTable from '@/components/tools/template-table.vue';
import TemplatePagination from '@/components/tools/template-pagination.vue';


export default {
  name: 'TestView',
  components: {
    Page,
    TemplateTable,
    TemplatePagination
  },
  data() {
    return {
      targetOption: [], //指标
      sport: '',
      typeOptions: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '汇总',
          value: 1
        },
        {
          label: '指标',
          value: 2
        },
        {
          label: '留存',
          value: 3
        },
        {
          label: '回溯',
          value: 4
        },
        {
          label: '均值',
          value: 5
        }
      ],
      statusOptions: [
         {
          label: '全部',
          value: ''
        },
        {
          label: '执行中',
          value: 1
        },
        {
          label: '失败',
          value: 2
        },
        {
          label: '成功',
          value: 3
        },
      ],
      formData: {
        type: '',
        status: '',
      },
      paginationInit: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      tableBtns: [
        {
          label: '重试',
          role: 'again',
          disabledHandler: (index, row) => {
            //false可点
            return row.canRetry === 0;
          }
        },
        {
          label: '复制SQL',
          role: 'copy',
        }
      ],
      breadcrumbList: ['TestTask', 'TestView'],
      tableList: [],
      tableColumns: [
        {
          label: 'ID',
          name: 'id'
        },
        {
          label: '类型',
          name: 'type'
        },
        {
          label: '时间',
          name: 'startTime',
          'min-width': '105px',
          render: (h, {row}) => {
            const { startTime,  endTime} = row;
            const formatEndTime = endTime === 0 ? '至今' : formatDate(endTime * 1000, 'yyyy-MM-dd');
            return <p>{formatDate(startTime * 1000, 'yyyy-MM-dd')} ~ {formatEndTime}</p>;
          }
        },
        {
          label: '状态',
          name: 'status',
        },
        {
          label: '步骤',
          name: 'step',
          'min-width': '105px',
        },
        {
          label: '级别',
          name: 'timeType',
        },
        {
          label: '时间段',
          name: 'dataType',
          'min-width': '105px',
        }
        // {
        //   label: '指标名称',
        //   name: 'indName',
        // }
      ],
    typeValue: '',
    experimentId: ''
    };
  },
  computed: {
    commonTableColumns() {
      return this.tableColumns;
    },
    calColumns() {
      if(this.typeValue === 2) {
        return [...this.tableColumns, {label: '指标名称', name: 'indName',}];
      } else {
        return this.tableColumns;
      }
    }
  },
  watch: {},
  created() {
    this.handleSearch();
  },
  mounted() {
    this.experimentId = this.$route.query.experimentId;
    if(this.experimentId) {
      this.getIndicatorList({experimentId: this.experimentId});
    }
  },
  methods: {
    //获取实验指标列表
    getIndicatorList(param) {
      this.$service
        .get('REPORT_FOUND_EXPINDLIST', {...param}, {needLoading: true})
        .then((res) => {
          this.targetOption = res.indList;
        })
        .catch(() => {});
    },
    goBack() {
      this.$router.back(-1);
    },
    pageSizeChange(e) {
      this.paginationInit.pageSize = e;
      this.getList();
    },
    changePage(page) {
      this.paginationInit.currentPage = page;
      this.getList();
    },
    operationEvent(row, btnRole) {
      switch (btnRole) {
        case 'again':
          this.setAgainHandle(row);
          break;
        case 'copy':
          this.setCopyHandle(row);
      }
    },
    setCopyHandle(row) {
      this.$copy(row.sqlSentence, event);
    },
    setAgainHandle(row) {
      // this.$copy('00000', event);
      const formData = this.formatFormData({id: row.id});
      this.$confirm(`确认重试吗？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(() => {
                this.$service
                  .post('TASKRETRY', formData, {needLoading: true})
                  .then((res) => {
                    this.$message({ message: '操作成功！', type: 'success' });
                    this.handleSearch();
                  })
                  .catch((err) => {
                    console.log(err);
                  });
              })
              .catch((e) => {
                console.log(e);
                this.$message({
                  type: 'info',
                  message: '已取消',
                });
              });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    // 查询列表数据
    handleSearch(e, type) {
      if(type === 'type') {
        this.typeValue = e;
      }
      this.paginationInit.currentPage = 1;
      this.getList();
    },
    // 重置
    handleReset() {
      this.paginationInit.currentPage = 1;
      this.formData = {
        type: '',
        status: ''
      };
      if(this.sport) this.sport = '';
      this.getList();
    },
    getList() {
      let params = {
        pn: this.paginationInit.currentPage,
        rn: this.paginationInit.pageSize,
        ...this.formData
      };
      if(this.typeValue === 2) {
        params.indId = this.sport;
      } else {
        params.indId ? delete params.indId : '';
      }
      this.$service
        .get('TASKLIST_DETAIL', {experimentId: this.$route.query.experimentId, ...params}, {needLoading: true})
        .then((res) => {
          this.tableList = res.list || [];
          this.paginationInit.total = res.total;
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }
};
</script>

<style lang="less" scoped>
.search {
  position: relative;
  display: flex;
  justify-content: space-between;

  .el-select {
    width: 175px;
  }
}

.go_back {
  float: right;
  margin-bottom: 10px;
}

.keyword_input {
  width: 270px;
  margin-left: -1px;

  /deep/ .el-input__inner {
    border-radius: 0;
  }

  /deep/ .el-input__suffix {
    right: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    cursor: pointer;
  }
}
/deep/ .el-table {
  font-size: 14px;
}
.btn_add {
  position: absolute;
  top: 0;
  right: 0;
}

.table-pagination {
  margin-top: 20px;
  text-align: right;
}

.table_description {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.related_flights_text {
  color: rgb(70, 96, 239);
  cursor: pointer;
}

.table_tag {
  display: inline-block;
  border-radius: 4px;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  color: #2f2f3f;
  background: #f8f8fc;
  border: 1px solid #e7e9f5;
}

.table_tag_required {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
}

.action_edit {
  margin-right: 10px;
}
/deep/ .op-switch.is-disabled .el-switch__core,
/deep/ .op-switch.is-disabled .el-switch__label {
  background-color: rgb(155, 156, 158);
  border-color: rgb(155, 156, 158);
  cursor: pointer;
}
/deep/ .el-switch.is-disabled.is-checked .el-switch__core,
/deep/ .op-switch.is-disabled.is-checked .el-switch__label {
  border-color: #42c57a;
  background-color: #42c57a;
}
/deep/ .el-button--text-primary > span {
  font-size: 14px;
}
</style>