<template>
  <Page class="container" :hasPagination="false">
    <section class="header">
      <i class="el-icon-arrow-left" @click="handleGoBack(true)"></i>
      <section class="page-title">实验扩展分析</section>
    </section>
    <section class="card-container">
      <iframe :src="link" frameborder="0" width="100%" height="98%"></iframe>
    </section>
  </Page>
</template>
<script>
import Page from '@/components/common/page/index.vue';

export default {
  name: 'experiment-extend',
  components: {
    Page
  },
  data(){
    return {
      link: ''
    };
  },
  created() {
    const { url } = this.$route.query;
    this.link = url;
  },
  methods: {
    handleGoBack(flag = false) {
      this.$router.push({
        path: '/exp-manage/extend'
      });
    },
  }
};
</script>
<style lang="less" scoped>
.container {
  margin-top: 24px;

  /deep/ .page-container {
    padding-top: 16px;
  }

  .header {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;
    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin-left: 12px;
    }
    i{
      cursor: pointer;
    }
  }
  .card-container{
    height: calc(100vh - 180px);
    margin-top: 12px;
    overflow: auto;
  }
}
</style>