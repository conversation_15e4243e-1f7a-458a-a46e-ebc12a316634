<template>
  <Page class="container">
    <section class="header">
      <section class="page-title">业务线列表</section>
    </section>
    <p class="form-title w-full">用户进组数据</p>
    <section class="card-container">
      <el-card class="box-card" v-for="app in hitInList" :key="app.id" shadow="hover" @click.native="goToDetail(app)">
        <div slot="header">
          <span class="card-title">{{ app.displayName }}</span>
        </div>
        <section>
          支持查看实验命中、进组数据
        </section>
      </el-card>
    </section>
    <p class="form-title w-full">用户留存数据</p>
    <section class="card-container">
      <el-card class="box-card" v-for="app in retentionList" :key="app.id" shadow="hover"
        @click.native="goToDetail(app)">
        <div slot="header">
          <span class="card-title">{{ app.displayName }}</span>
        </div>
        <section>
          综合应用功能，进行渗透分析和留存分析
        </section>
      </el-card>
    </section>
  </Page>
</template>
<script>
import Page from '@/components/common/page/index.vue';

export default {
  name: 'experiment-extend',
  components: {
    Page
  },
  data() {
    return {
      hitInList: [], // 进组数据
      retentionList: [], //留存数据
    };
  },
  created() {
    this.getapplist();
  },
  methods: {
    getapplist() {
      this.$service
        .get('APPREPORTLIST', { pn: 0, rn: 100000 }, { needLoading: true })
        .then((res) => {
          this.hitInList = res.hitIn || [];
          this.retentionList = res.retention || [];
        })
        .catch((err) => {
          console.log(err);
        });
    },
    goToDetail(app) {
      this.$router.push({
        name: 'ExperimentExtendDetail',
        query: {
          url: app.reportUrl || 'https://bluewhale.zuoyebang.cc/#/shareReport/fc9eb446-76b8-45b3-8e90-dc532d420d83'
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.container {
  margin-top: 24px;

  /deep/ .page-container {
    padding-top: 16px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
    }
  }

  .card-container {
    display: flex;
    flex-wrap: wrap;

    .card-title {
      font-size: 14px;
      font-weight: 500;
    }

    .box-card {
      margin: 8px 16px 8px 0;
      cursor: pointer;
      width: 280px;

      /deep/ .header {}
    }
  }
}
</style>