<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-17 17:12:19
 * @LastEditors: zhuyue
 * @LastEditTime: 2021-08-04 16:37:48
 * @Description: 实验列表
-->

<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
    <template #header-action>
      <el-button class="add-button" type="default" @click="createWhiteListGroup" icon="el-icon-circle-plus" v-if="activeName === 'second'">新建分组</el-button>
      <template v-else>
        <el-button class="add-button" type="default" @click="updateGroup" icon="el-icon-circle-plus" v-if="multipleSelection.length">设置分组</el-button>
        <el-button class="add-button" type="default" @click="createWhiteList" icon="el-icon-circle-plus">新建白名单</el-button>
        <el-button class="add-button" type="default" @click="createWhiteListBatch" icon="el-icon-circle-plus">批量创建</el-button>
      </template>
    </template>
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane label="白名单" name="first">
        <template v-if="activeName == 'first'">
          <div class="select-container">
            <div>
              <el-form ref="searchForm" inline>
                <el-form-item label="">
                  <el-input class="key-world-input" placeholder="请输入cuid/uid、白名单id/名称" v-model="formData.keyword" clearable @change="handleSearch"></el-input>
                </el-form-item>
                <el-form-item label="业务线：">
                  <el-select v-model="formData.appKey" style="width: 150px" placeholder="请选择业务线" @change="appKeyChange">
                    <el-option v-for="item in appList1" :key="item.appKey" :label="item.displayName" :value="item.appKey"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="账号体系：">
                  <el-select style="width: 150px" v-model="formData.accountSystem" clearable placeholder="请选择" @change="handleSearch">
                    <el-option v-for="item in accSystemData" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="分组：">
                  <el-select style="width: 150px" v-model="formData.whitelistGroupId" clearable placeholder="请选择分组" @change="handleSearch">
                    <el-option v-for="item in allWhiteGroupList" :key="item.id" :label="item.displayName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <!-- <el-form-item>
                <el-button-group class="self-collapse-btns">
                  <el-button type="primary" @click="handleSearch">查询</el-button>
                  <el-button class="reset-btn" @click="handleReset">重置</el-button>
                </el-button-group>
              </el-form-item> -->
              </el-form>
            </div>
          </div>
          <white-table :tableList="tableList" :appKey="formData.appKey" @handelEditWhiteList="handelEditWhiteList" @handleSearch="handleSearch" @handleSelectionChange="handleSelectionChange"></white-table>
          <el-pagination v-show="total" layout="total,sizes, prev, pager, next, jumper" :page-size="pagination.rn" :page-sizes="[10, 20, 50, 100]" @size-change="pageSizeChange" :current-page="pagination.pn" :total="total"
            @current-change="pageNoChange" style="margin-top: 16px; float: right;"></el-pagination>
        </template>
      </el-tab-pane>
      <el-tab-pane label="白名单分组" name="second">
        <WhiteGroupTable ref="whiteGroup" v-if="activeName === 'second'"></WhiteGroupTable>
      </el-tab-pane>
    </el-tabs>
    <batch-create-white-dialog v-if="isShowBatchDialog" :dialogVisible.sync="isShowBatchDialog" :editId="editId" @handleSearch="handleSearch" @resetEditId="resetEditId" :whiteGroupList="whiteGroupList"></batch-create-white-dialog>
    <create-white-dialog v-if="isShowDialog" :dialogVisible.sync="isShowDialog" :editId="editId" @handleSearch="handleSearch" @resetEditId="resetEditId" :whiteGroupList="whiteGroupList"></create-white-dialog>
    <update-whitegroup-dialog v-if="updateGroupDialog" :dialogVisible.sync="updateGroupDialog" @handleSearch="handleSearch" :whiteList="multipleSelection" :whiteGroupList="whiteGroupList"></update-whitegroup-dialog>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import { mapGetters } from 'vuex';
import BatchCreateWhiteDialog from './components/batch-create-white-dialog.vue';
import CreateWhiteDialog from './components/create-white-dialog.vue';
import UpdateWhitegroupDialog from './components/update-white-group-dialog.vue';
import WhiteGroupTable from './components/white-group-list.vue';
import WhiteTable from './components/white-table.vue';

export default {
  name: 'WhiteList', //白名单列表
  components: {
    Page,
    WhiteTable,
    CreateWhiteDialog,
    BatchCreateWhiteDialog,
    WhiteGroupTable,
    UpdateWhitegroupDialog
  },
  // mixins: [PageCommon],
  data() {
    return {
      activeName: 'first',
      editId: 0,
      isShowDialog: false,
      isShowBatchDialog: false,
      breadcrumbList: ['White'],
      formData: {
        appKey: 'all',
        keyword: '',
        accountSystem: '',
        whitelistGroupId: ''
      },
      pagination: {
        pn: 1,
        rn: 10
      },
      total: 0,
      tableList: [],
      whiteGroupList: [],
      multipleSelection: [],
      updateGroupDialog: false
    };
  },
  computed: {
    ...mapGetters(['appList1', 'accSystemData'], 'message'),
    allWhiteGroupList() {
      return [{
        displayName: '全部',
        id: ''
      }, ...this.whiteGroupList];
    }
  },
  created() {
    this.$store.dispatch('getAppList');
    this.getWhiteGroupList();
    this.handleSearch();
  },
  activated() {
    this.getList();
  },
  methods: {
    updateGroup() {
      this.updateGroupDialog = true;
    },
    handleSelectionChange(selection) {
      this.multipleSelection = selection.map(item => ({
        id: item.id
      }));
    },
    appKeyChange() {
      console.log('formData.appKey', this.formData.appKey);
      this.formData.whitelistGroupId = '';
      this.getWhiteGroupList();
      this.handleSearch();
    },
    // 重置editId
    resetEditId(val) {
      this.editId = 0;
    },
    getWhiteGroupList() {
      // WHITE_GROUP_LIST
      const params = {
        pn: 0,
        rn: 10000,
        appKey: this.formData.appKey
      };
      this.$service
        .get('WHITE_GROUP_LIST', { ...params })
        .then(res => {
          this.whiteGroupList = res.list;
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 重置
    handleReset() {
      this.pagination.pn = 1;
      this.formData = {
        appKey: 'all',
        keyword: ''
      };
      this.getList();
    },
    //
    pageSizeChange(rn) {
      this.pagination.rn = rn;
      this.getList();
    },
    //
    pageNoChange(pn) {
      this.pagination.pn = pn;
      this.getList();
    },
    // 查询列表数据
    handleSearch() {
      this.pagination.pn = 1;
      this.getList();
    },
    // 列表数据
    getList() {
      this.$service
        .get('WHITE_LIST', { ...this.formData, ...this.pagination }, { needLoading: true })
        .then((data) => {
          this.tableList = data.list;
          this.total = data.total;
          this.pagination.rn = data.rn;
          this.pagination.pn = data.pn;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 创建白名单
    createWhiteList() {
      this.isShowDialog = true;
    },
    createWhiteListBatch() {
      this.isShowBatchDialog = true;
    },
    createWhiteListGroup() {
      this.$refs.whiteGroup.createWhiteList();
    },
    // 编辑白名单
    handelEditWhiteList(id) {
      this.editId = id;
      // debugger
      this.isShowDialog = true;
    },
    handleTabClick(tab) {
      if (tab.name === "first") {
        this.getWhiteGroupList();
        this.handleSearch();
      } else if (tab.name === "second") {
        this.getWhiteGroupList();
      }
    }
  },
};
</script>

<style lang="less" scoped>
.select-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  padding: 20px 0 5px;
  // /deep/ .el-form .el-form-item .el-form-item__label {
  //   padding: 0 !important;
  // }
}

.add-button {
  /deep/ i {
    color: #42c57a;
  }
}

/deep/ .page-tab {
  margin: 0;
}

/deep/ .key-world-input {
  width: 251px;
}

/deep/ .el-button--medium>span {
  font-size: 14px;
  vertical-align: middle;
}

.btn_add {
  position: absolute;
  top: 20px;
  right: 0;
}

.self-collapse-btns {
  height: 33px;
}
</style>