<!--
 * @Description:
 * @Author: huy<PERSON>ei
 * @LastEditors  : Please set LastEditors
 * @Date: 2021-06-17 17:12:19
 * @LastEditTime : Please set LastEditTime
 -->
<template>
  <div>
    <zyb-table :table-data="tableList" :table-columns="tableColumns" @handleSelectionChange="handleSelectionChange">
      <template v-slot:custom="{ data: { item, scope } }">
        <template v-if="item.prop === 'experimentCount'">
          <zyb-text @click="showAssociateExps(scope.row)">{{ scope.row.experimentCount }}</zyb-text>
        </template>
        <template v-else-if="item.prop === 'action'">
          <zyb-text @click="editWhiteList(scope.row.id)">编辑</zyb-text>
          <zyb-text class="warn-tip" :disabled="!scope.row.canRemove" @click="deleteWhiteList(scope.row)">
            删除
          </zyb-text>
        </template>
      </template>
      <template v-slot:selection>
        <el-table-column type="selection" width="55" :selectable="selectable">
        </el-table-column>
      </template>
    </zyb-table>

    <associate-dialog v-if="isShowDialog" type="white" :dialogVisible.sync="isShowDialog" :checkedId="checkedId" :appKey="appKey" :title="title"></associate-dialog>
  </div>
</template>
<script>
import { formatDate } from '@/common/utils';
import ZybTable from '@/components/zyb-table/index.vue';
import ZybText from '@/components/zyb-text/index.vue';
import AssociateDialog from './associate-dialog.vue';

export default {
  name: 'white-table',
  components: { AssociateDialog, ZybTable, ZybText },
  data() {
    return {
      title: '当前白名单所在实验和固化',
      checkedId: 0,
      isShowDialog: false,
      currentTaskId: 0,
      showRobotDetail: false,
      tableColumns: [
        {
          label: 'ID',
          prop: 'id'
        },
        {
          label: '业务线',
          prop: 'appDisplayName',
          title: true,
          line: 2
        },
        {
          label: '名称',
          prop: 'displayName',
          title: true,
          line: 2
        },
        {
          label: '分组名称',
          prop: 'whitelistGroupDisplayName',
          title: true,
          line: 2
        },
        {
          label: '创建人',
          prop: 'createUser'
        },
        {
          label: '账号体系',
          prop: 'accountSystem',
          render: (scope) => {
            return scope.row.accountSystem == 1 ? 'cuid' : 'uid';
          }
        },
        {
          label: '创建时间',
          prop: 'createTime',
          render: (scope) => {
            return this.handleFormatDate(scope.row.createTime);
          }
        },
        {
          label: '所在实验和固化',
          prop: 'experimentCount',
          custom: true
        },
        {
          label: '操作',
          prop: 'action',
          custom: true
        }
      ],
      multipleSelection: []
    };
  },
  props: {
    tableList: {
      type: Array
    }
  },
  mounted() { },
  computed: {},
  filters: {},
  methods: {
    selectable(row) {
      return !row.whitelistGroupId;
    },
    handleSelectionChange(selection) {
      this.$emit('handleSelectionChange', selection);
    },
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'yyyy-MM-dd HH:mm:ss');
      }
      return '-';
    },
    // 编辑白名单
    editWhiteList(id) {
      this.$emit('handelEditWhiteList', id);
    },
    // 删除白名单
    deleteWhiteList(data) {
      const { experimentCount = 0 } = data;
      const text = experimentCount === 0 ? `确定删除白名单：${data.displayName}?` : '删除白名单后，引用当前白名单的实验将移除该白名单，确定删除吗？';
      this.$confirm(text, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handelDelete(data.id);
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    //删除白名单
    handelDelete(id) {
      const data = this.formatFormData({ id });
      this.$service.post('DELETEWHITE_LIST', data).then((res) => {
        this.$message.success('删除成功');
        this.$emit('handleSearch');
      });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    // 获取关联实验
    showAssociateExps(data) {
      this.checkedId = data.id;
      this.appKey = data.appKey;
      this.isShowDialog = true;
    }
  }
};
</script>
<style lang="less" scoped>
.warn-tip {
  color: #fa574b;

  &:hover {
    color: #fa574b;
  }
}

/deep/ .warn-tip:focus,
/deep/ .warn-tip:hover {
  color: #fa574b;
}

/deep/ .el-button.is-disabled {
  background-color: transparent;
  color: #bbbcbf;
}

/deep/ .el-table {
  font-size: 14px;
}

/deep/ .el-button--text-primary>span {
  font-size: 14px;
}
</style>
