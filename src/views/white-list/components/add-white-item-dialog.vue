<template>
  <el-dialog title="添加白名单" :visible="dialogVisible" :before-close="() => dialogClose()" width="950px">
    <section class="container">
      <section class="left">
        <el-input v-model="form.keyword" placeholder="白名单名称/创建人/CUID/UID" @blur="queryList" clearable></el-input>
        <el-table  row-key="id" ref="multipleTable" :data="list" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <el-table-column prop="id" label="ID" show-overflow-tooltip></el-table-column>
          <el-table-column prop="displayName" label="名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="cuid" label="CUID/UID" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.cuid || scope.row.uid }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createUser" label="创建人"></el-table-column>
        </el-table>
        <el-pagination class="pagination-container" v-show="total" layout="total, prev, pager, next" :page-size="pagination.rn"
          :page-sizes="[10, 20, 50, 100]" @size-change="pageSizeChange" :current-page="pagination.pn" :total="total"
          @current-change="pageNoChange"></el-pagination>
      </section>
      <section class="right">
        <p class="header">
          <span>
            已选（{{ whiteList.length }}）
          </span>
          <el-button type="text" @click="removeAll()">清空列表</el-button>
        </p>
        <section class="list">
          <p class="list-item" v-for="item in whiteList" :key="item.id">
            <el-tooltip popper-class="list-item-text-tooltip" effect="dark" :content="item.displayName" placement="left">
              <span>{{ item.displayName }}</span>
            </el-tooltip>
            <el-button type="text" icon="el-icon-circle-close" @click="removeItem(item.id)"></el-button>
          </p>
        </section>
      </section>
    </section>
    <section slot="footer" class="dialog-footer">
      <el-button @click="dialogClose()">取 消</el-button>
      <el-button type="primary" @click="updateAction">确定</el-button>
    </section>
  </el-dialog>
</template>
<script>
import { formatFormData } from '@/utils/util';
export default {
  name: 'AssociateDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    whitelistGroupId: {
      type: Number
    },
    appKey: {
      type: String
    },
    accountSystem: {
      type: Number
    }
  },
  computed: {
  },
  data() {
    return {
      whiteList: [],
      form: {
        keyword: ''
      },
      list: [],
      pagination: {
        pn: 1,
        rn: 10
      },
      total: 0,
      stop: false
    };
  },
  mounted() { },
  methods: {
    pageSizeChange(rn) {
      this.pagination.rn = rn;
      this.queryList();
    },
    //
    pageNoChange(pn) {
      this.pagination.pn = pn;
      this.queryList();
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
    },
    updateAction() {
      const formData = formatFormData({
        whitelistGroupId: this.whitelistGroupId,
        list: this.whiteList.map(item => ({
          id: item.id
        }))
      });
      let api = 'WHITE_GROUP_UPDATE_WHITELIST';
      this.$service
        .post(api, formData, { needLoading: false })
        .then((data) => {
          this.dialogClose();
          this.$emit('refresh');
          this.$message.success('添加白名单成功！');
        })
        .catch((err) => {
          console.log(err);
        });
    },
    queryList() {
      this.$service
        .get('NO_GROUP_WHITELIST', {
          ...this.form,
          ...this.pagination,
          appKey: this.appKey,
          accountSystem: this.accountSystem
        }, { needLoading: false })
        .then((data) => {
          console.log('datatatat', data);
          this.list = data.list || [];
          this.total = data.total;
          this.toggleRowSelection();
        })
        .catch((err) => {
          console.log(err);
        });
    },
    toggleRowSelection() {
      this.stop = true;
      this.$refs.multipleTable.clearSelection();
      setTimeout(() => {
        this.whiteList.forEach(row => {
          const data = this.list.find(data => data.id === row.id);
          if (data) {
            this.$refs.multipleTable.toggleRowSelection(data, true);
          }
        });
        this.stop = false;
      }, 10);
    },
    removeAll() {
      this.$refs.multipleTable.clearSelection();
      this.whiteList = [];
    },
    removeItem(id) {
      this.whiteList = this.whiteList.filter(item => item.id !== id);
      this.toggleRowSelection();
    },

    handleSelectionChange(val) {
      if (this.stop) {
        return;
      }
      this.whiteList = this.whiteList.filter(item => {
        return this.list.every(a => a.id !== item.id);
      });
      val.forEach(element => {
        this.whiteList.push(element);
      });
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  overflow: hidden;

  .left {
    flex: 1;
    width: 500px;

    .el-input {
      width: 240px;
      margin-bottom: 12px;
    }
  }

  .right {
    flex-shrink: 0;
    width: 300px;
    box-sizing: border-box;
    border-left: 1px solid #e1e2e8;
    margin-left: 16px;
    padding-left: 16px;
    .list{
      max-height: 500px;
      overflow: auto;
    }

    .header,
    .list-item {
      display: flex;
      height: 32px;
      justify-content: space-between;
      align-items: center;

    }

    .list-item {
      border-bottom: 1px solid #e1e2e8;
      &> span{
        display: inline-block;
        width: 90%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

  }
}
</style>

<style lang="less">
.list-item-text-tooltip {
  max-width: 600px;
}
.pagination-container{
  margin-top: 20px!important;
}
</style>