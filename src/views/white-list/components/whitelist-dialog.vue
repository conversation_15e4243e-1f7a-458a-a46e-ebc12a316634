<template>
  <el-dialog :title="title" :visible="dialogVisible" :before-close="dialogClose" width="70%">
    <section>
      <el-row style="margin-bottom: 16px">
        <el-col :span="8">
          <el-input v-model="query.keyword" placeholder="白名单名称" @blur="queryList" clearable></el-input>
        </el-col>
      </el-row>
      <el-table :data="list" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center">
        </el-table-column>
        <el-table-column prop="id" label="ID"></el-table-column>
        <el-table-column prop="displayName" label="白名单名称"></el-table-column>
        <el-table-column prop="cuid" label="UID/CUID">
          <template slot-scope="scope">
            <span>{{ scope.row.cuid || scope.row.uid }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="text" @click="remove([{ id: scope.row.id }])">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-row style="margin-top: 12px">
        <el-button type="danger" @click="remove()" v-if="multipleSelection.length">批量移除</el-button>
      </el-row>
    </section>
  </el-dialog>
</template>
<script>
export default {
  name: 'WhitelistDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: Number
    },
    title: {
      type: String
    },

  },
  data() {
    return {
      list: [],
      query: {
        whitelistGroupId: this.id,
        rn: 3000,
        pn: 1,
        keyword: ''
      },
      multipleSelection: []
    };
  },
  created() {
    this.queryList();
  },
  mounted() { },
  methods: {
    queryList() {
      this.$service
        .get('WHITE_LIST', { ...this.query, appKey: 'all' }, { needLoading: true })
        .then((data) => {
          this.list = data.list || [];
        })
        .catch((err) => {
          console.log(err);
        });
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val.map(item => ({
        id: item.id
      }));
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    remove(ids = this.multipleSelection) {
      const formatData = this.formatFormData({
        list: ids,
        whitelistGroupId: 0
      });
      this.$service
        .post('WHITE_GROUP_UPDATE_WHITELIST', formatData)
        .then(() => {
          this.$message.success('移除成功');
          this.queryList();
          this.$emit('refresh');
        })
        .catch((err) => {
          console.log(err);
        });
    }

  }
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 65%;
  min-width: 30%;
}

/deep/ .el-dialog {
  .el-dialog__body {
    padding-top: 12px;
  }
}

.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}

.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;

  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }

  .detail-value {
    width: 200px;

    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }

  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;

    span {
      padding: 0 11px;
    }
  }

  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }

  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}

/deep/ .divider-con {
  margin: 20px 0;
}

.tip {
  display: flex;
  padding: 10px 10px 20px;

  h3 {
    font-weight: 700;
  }
}

.p-con {
  width: 88%;
  margin: 0 auto;
}

// /deep/ .status-btn {
//   width: 50px;
//   color: #fff;
// }
// /deep/ .status-btn:hover {
//   color: #fff;
// }</style>
