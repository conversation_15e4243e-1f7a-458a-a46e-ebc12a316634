<!--
 * @Description:
 * @Author: huy<PERSON>ei
 * @LastEditors  : Please set LastEditors
 * @Date: 2021-06-17 17:12:19
 * @LastEditTime : Please set LastEditTime
 -->
<template>
  <div>
    <zyb-table :table-data="tableList" :table-columns="tableColumns">
      <template v-slot:custom="{ data: { item, scope } }">
        <template v-if="item.prop === 'displayName'">
          <el-popover placement="top" trigger="hover">
            <p style="text-align: center">{{ scope.row.displayName }}</p>
            <span slot="reference">{{ scope.row.displayName }}</span>
          </el-popover>
        </template>
        <template v-else-if="item.prop === 'whitelistCount'">
          <el-button type="text" @click="showWhitelist(scope.row)">{{ scope.row.whitelistCount }}</el-button>
        </template>
        <template v-else-if="item.prop === 'action'">
          <zyb-text @click="editWhiteList(scope.row.id)">编辑</zyb-text>
          <zyb-text @click="addWhiteList(scope.row)">添加白名单</zyb-text>
          <zyb-text class="warn-tip" @click="deleteWhiteList(scope.row)">
            删除
          </zyb-text>
        </template>
      </template>
    </zyb-table>
    <associate-dialog v-if="isShowDialog" type="white" :dialogVisible.sync="isShowDialog" :checkedId="checkedId"
      :appKey="appKey" :title="title"></associate-dialog>
    <add-white-item-dialog v-if="addWhiteListDialog.show" type="white" :dialogVisible.sync="addWhiteListDialog.show"
      @refresh="refresh" :whitelistGroupId="addWhiteListDialog.id" :appKey="addWhiteListDialog.appKey" :accountSystem="addWhiteListDialog.accountSystem"></add-white-item-dialog>
    <whitelist-dialog v-if="whitelistDialog.show" type="white" :dialogVisible.sync="whitelistDialog.show"
      :id="whitelistDialog.id" title="白名单列表" @refresh="refresh"></whitelist-dialog>
  </div>
</template>
<script>
import { formatDate } from '@/common/utils';
import ZybTable from '@/components/zyb-table/index.vue';
import ZybText from '@/components/zyb-text/index.vue';
import AssociateDialog from './associate-dialog.vue';
import WhitelistDialog from './whitelist-dialog.vue';
import AddWhiteItemDialog from './add-white-item-dialog.vue';

export default {
  name: 'white-table',
  components: { AssociateDialog, ZybTable, ZybText, WhitelistDialog, AddWhiteItemDialog },
  data() {
    return {
      title: '当前白名单所在实验和固化',
      checkedId: 0,
      isShowDialog: false,
      currentTaskId: 0,
      showRobotDetail: false,
      tableColumns: [
        {
          label: 'ID',
          prop: 'id'
        },
        {
          label: '分组名称',
          prop: 'displayName',
          custom: true
        },
        {
          label: '业务线',
          prop: 'appDisplayName',
          title: true,
          line: 1
        },
        {
          label: '账号体系',
          prop: 'accountSystem',
          render: (scope) => {
            return scope.row.accountSystem == 1 ? 'cuid' : 'uid';
          }
        },
        {
          label: '白名单数量',
          prop: 'whitelistCount',
          custom: true
        },
        {
          label: '创建人',
          prop: 'createUser'
        },
        {
          label: '创建时间',
          prop: 'createTime',
          render: (scope) => {
            return this.handleFormatDate(scope.row.createTime);
          }
        },
        {
          label: '操作',
          prop: 'action',
          width: 200,
          custom: true
        }
      ],
      whitelistDialog: {
        show: false,
        id: null
      },
      addWhiteListDialog: {
        show: false,
        id: null,
        appKey: ''
      }
    };
  },
  props: {
    tableList: {
      type: Array
    }
  },
  mounted() {
  },
  computed: {},
  filters: {},
  methods: {
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'yyyy-MM-dd HH:mm:ss');
      }
      return '-';
    },
    // 编辑白名单
    editWhiteList(id) {
      this.$emit('handelEditWhiteList', id);
    },
    // 删除白名单
    deleteWhiteList(data) {
      const text = '删除白名单分组后，分组下白名单的分组置为空，确定删除分组吗？';
      this.$confirm(text, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handelDelete(data.id);
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    refresh() {
      console.log('hahahahah');
      this.$emit('handleSearch');
    },
    //删除白名单
    handelDelete(id) {
      const data = this.formatFormData({ id });
      this.$service.post('WHITE_GROUP_DELETE', data).then((res) => {
        this.$message.success('删除成功');
        this.$emit('handleSearch');
      });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    // 获取关联实验
    showAssociateExps(data) {
      this.checkedId = data.id;
      this.appKey = data.appKey;
      this.isShowDialog = true;
    },
    showWhitelist(data) {
      this.$router.push({
        path: '/exp-manage/group-white-list',
        query: {
          id: data.id,
          appKey: data.appKey,
          accountSystem: data.accountSystem
        }
      });
      // this.whitelistDialog.show = true;
      // this.whitelistDialog.id = data.id;
    },
    addWhiteList(row) {
      this.addWhiteListDialog.show = true;
      this.addWhiteListDialog.id = row.id;
      this.addWhiteListDialog.appKey = row.appKey;
      this.addWhiteListDialog.accountSystem = row.accountSystem;
    }
  }
};
</script>
<style lang="less" scoped>
.warn-tip {
  color: #fa574b;

  &:hover {
    color: #fa574b;
  }
}

/deep/ .warn-tip:focus,
/deep/ .warn-tip:hover {
  color: #fa574b;
}

/deep/ .el-button.is-disabled {
  background-color: transparent;
  color: #bbbcbf;
}

/deep/ .el-table {
  font-size: 14px;
}

/deep/ .el-button--text-primary>span {
  font-size: 14px;
}
</style>
