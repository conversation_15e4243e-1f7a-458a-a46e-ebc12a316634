<template>
  <el-dialog title="批量设置分组" :visible="dialogVisible" :before-close="dialogClose" width="400px">
    <el-form ref="mutuformBase" :model="formData" label-width="100px" :rules="rules">
      <el-form-item label="分组" required prop="groupId">
        <el-select v-model="formData.groupId" placeholder="请选择分组">
          <el-option v-for="item in whiteGroupList" :key="item.id" :label="item.displayName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <!-- 确定取消按钮组 -->
    <section slot="footer" class="dialog-footer">
      <el-button @click="dialogClose">取 消</el-button>
      <el-button type="primary" @click="validateUpdate">确定</el-button>
    </section>
  </el-dialog>
</template>
<script>
export default {
  name: 'create-white-dialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    whiteGroupList: {
      type: Array
    },
    whiteList: {
      type: Array
    }
  },
  data() {
    return {
      formData: {
        groupId: ''
      },
      rules: {
        groupId: [{ required: true, message: '请选择分组', trigger: 'blur' }],
      }
    };
  },
  created() {
  },
  mounted() { },
  methods: {
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
      // this.defaultFormat();
    },
    validateUpdate() {
      this.$refs['mutuformBase'].validate((valid) => {
        if (valid) {
          this.updateAction();
        } else {
          console.log('提交失败');
          return false;
        }
      });
    },
    updateAction() {
      const formData = this.formatFormData({
        whitelistGroupId: this.formData.groupId,
        list: this.whiteList
      });
      let api = 'WHITE_GROUP_UPDATE_WHITELIST';
      let tip = '设置分组成功';
      this.$service
        .post(api, formData, { needLoading: true })
        .then((data) => {
          this.dialogClose();
          this.$emit('handleSearch');
          this.$message.success(tip);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 760px;
  min-width: 30%;
}

.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}

.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;

  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }

  .detail-value {
    width: 200px;

    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }

  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;

    span {
      padding: 0 11px;
    }
  }

  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }

  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}

/deep/ .divider-con {
  margin: 20px 0;
}

.tip {
  padding: 10px 10px 20px;
}
</style>
