<template>
  <el-dialog :title="title" :visible="dialogVisible" :before-close="dialogClose">
    <el-form ref="mutuformBase" :model="addData" label-width="100px" :rules="rules">
      <el-form-item label="业务线：" required prop="appKey">
        <el-select :disabled="disableEdit" v-model="addData.appKey" placeholder="请选择业务线" @change="handleChangAppKey">
          <el-option v-for="item in appList" :key="item.appKey" :label="item.displayName"
            :value="item.appKey"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账号体系：" required>
        <!-- 账号体系，cuid：1，uid: 2-->
        <el-radio-group v-model="addData.accountSystem" :disabled="disableEdit" @change="changeAccountSystem">
          <el-radio :label="1">cuid</el-radio>
          <el-radio :label="2">uid</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="分组：">
        <el-select v-model="addData.whitelistGroupId" clearable placeholder="请选择分组">
          <el-option v-for="item in whiteGroupList" :key="item.id" :label="item.displayName"
            :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="id：" prop="ids" required>
        <span slot="label">
          <span>id:
            <el-popover placement="top" width="200" trigger="hover" content="请根据账号体系的类型填入对应的用户id">
              <i slot="reference" class="el-icon-question"></i>
            </el-popover>
          </span>
        </span>
        <el-input type="textarea" v-model="addData.ids" class="f-width-c" rows="3" resize="vertical" maxlength="1000"
          placeholder="最多输入1000个，回车分割"></el-input>
      </el-form-item>
      <el-form-item label="负责人：" prop="createUser">
        <search-user v-model="addData.createUser" :usedefault="true"></search-user>
      </el-form-item>
    </el-form>
    <!-- 确定取消按钮组 -->
    <section slot="footer" class="dialog-footer">
      <el-button @click="dialogClose">取 消</el-button>
      <el-button type="primary" @click="validateCreate">确定</el-button>
    </section>
  </el-dialog>
</template>
<script>
import * as _ from 'lodash';
import { mapGetters } from 'vuex';
import SearchUser from '@/components/search-user/index.vue';

export default {
  name: 'create-white-dialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    editId: {
      type: Number
    }
  },
  components: {
    SearchUser
  },
  computed: {
    disableEdit() {
      return this.editId ? true : false;
    },
    ...mapGetters(['appList'], 'message')
  },
  data() {
    return {
      whiteGroupList: [],
      addData: {
        appKey: '',
        displayName: '',
        accountSystem: 1, // 账号体系，cuid：1，uid: 2
        whitelistGroupId: '',
        ids: '',
        createUser: ''
      },
      title: '新建白名单',
      rules: {
        appKey: [{ required: true, message: '请选择业务线', trigger: 'blur' }],
        displayName: [
          { required: true, message: '请输入名称，支持10个以内字符', trigger: 'blur' },
          { required: true, message: '请输入名称，支持10个以内字符', trigger: 'change' }
        ],
        ids: [{ required: true, message: '用户id不可为空', trigger: 'blur' }],
        createUser: [{ required: true, message: '请选择负责人', trigger: 'blur' }],
      }
    };
  },
  created() {
    this.addData.appKey = this.appList[0].appKey;
    this.getWhiteGroupList();
    if (this.editId) {
      // 获取详情
      this.getMutuDetai();
      this.title = '编辑白名单';
    }
  },
  mounted() { },
  methods: {
    handleChangAppKey(val) {
      //   this.$emit("changeAppKey",val)
      this.addData.whitelistGroupId = '';
      this.getWhiteGroupList();
    },
    // 改变账号体系的时候
    changeAccountSystem() {
      this.addData.id = '';
      this.addData.whitelistGroupId = '';
      this.getWhiteGroupList();
    },
    getWhiteGroupList() {
      const params = {
        pn: 0,
        rn: 10000,
        appKey: this.addData.appKey,
        accountSystem: this.addData.accountSystem
      };
      this.$service
        .get('WHITE_GROUP_LIST', { ...params })
        .then(res => {
          this.whiteGroupList = res.list || [];
          console.log('this.whiteGroupList', this.whiteGroupList);
        })
        .catch(err => {
          console.log(err);
        });
    },
    validateCreate() {
      this.$refs['mutuformBase'].validate((valid) => {
        if (valid) {
          // 校验uid情况必须是数字
          if (this.addData.accountSystem === 2) {
            const { ids } = this.addData;
            const idArr = ids.split('\n');
            const isNumber = idArr.every(value => {
              return !!Number.isInteger(value - 0);
            });
            if (!isNumber) {
              this.$message.warning('uid仅支持输入数字！');
              return;
            }
          }
          this.handleCreateMutu();
        } else {
          console.log('提交失败');
          return false;
        }
      });
    },
    handleCreateMutu() {

      const formData = this.formatFormData(this.addData);
      let api = this.editId ? 'EDITWHITE_LIST' : 'ADDWHITE_LIST';
      let tip = this.editId ? '编辑成功' : '新建成功';
      this.$service
        .post(api, formData, { needLoading: true })
        .then((data) => {
          this.dialogClose();
          this.$emit('handleSearch');
          this.$message.success(tip);
        })
        .catch((err) => {
          console.log(err);
        });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          if (key === 'ids') {
            const ids = data[key].split('\n');
            formData.append(key, JSON.stringify(ids));
          } else {
            formData.append(key, data[key]);
          }
        }
      }
      return formData;
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
      // this.defaultFormat();
    },
    //获取互斥责详情
    getMutuDetai() {
      this.$service
        .get('WHITELIST_VIEW', { id: this.editId }, { needLoading: true })
        .then((data) => {
          //debugger;
          data.uid = data.accountSystem === 2 ? data.cuid : '';
          this.addData = data;
        })
        .catch((err) => {
          console.log(err);
        });
    }
  },
  beforeDestroy() {
    this.$emit('resetEditId');
  }
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 760px;
  min-width: 30%;
}

.el-icon-question {
  color: #42c57a;
}

.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}

.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;

  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }

  .detail-value {
    width: 200px;

    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }

  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;

    span {
      padding: 0 11px;
    }
  }

  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }

  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}

/deep/ .divider-con {
  margin: 20px 0;
}

.tip {
  padding: 10px 10px 20px;
}
</style>
