<template>
  <div class="container">
    <el-form ref="searchForm" inline>
      <el-form-item label="">
        <el-input v-model="formData.keyword" placeholder="实验id/名称/创建人" @change="handleSearch">
        </el-input>
      </el-form-item>
      <el-form-item label="状态：">
        <el-select v-model="formData.status" placeholder="请选择状态" @change="handleSearch">
          <el-option
            v-for="item in expStatus"
            :key="item.label"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <zyb-table :table-data="detailData" :table-columns="tableColumns">
      <template v-slot:custom="{ data: { item, scope } }">
        <template v-if="item.prop === 'displayName'">
          <a @click="editExp(scope.row.id)">{{ scope.row.displayName }}</a>
        </template>
      </template>
    </zyb-table>

    <el-pagination
      v-show="total"
      class="table_pagination"
      layout="total, prev, pager, next, jumper"
      :page-size="pagination.rn"
      :page-sizes="[10, 20, 50, 100]"
      @size-change="pageSizeChange"
      :current-page="pagination.pn"
      :total="total"
      @current-change="pageNoChange"
    ></el-pagination>
  </div>
</template>
<script>
import { formatDate } from '@/common/utils';
import Page from '@/components/common/page/index.vue';
import ZybTable from '@/components/zyb-table/index.vue';
import { mapGetters } from 'vuex';
const fns = {
  white: 'whitelistflowexperiment',
  crowd: 'flowexperiment'
};
export default {
  name: 'AssociateExp',
  components: { Page, ZybTable },
  props: {
    checkedId: {
      type: Number
    },
    appKey: {
      type: String
    },
    type: String
  },
  computed: {
    ...mapGetters(['appList'], 'message')
  },
  data() {
    return {
      formData: {
        status: '',
        appKey: this.appKey,
        id: this.checkedId
      },
      pagination: {
        pn: 1,
        rn: 10
      },
      total: 0,
      expStatus: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '草稿',
          value: 1
        },
        {
          label: '调试中',
          value: 2
        },
        {
          label: '运行中',
          value: 3
        },
        {
          label: '结束',
          value: 4
        },
        {
          label: '待运行',
          value: 5
        },
        {
          label: '已暂停',
          value: 6
        },
        {
          label: '已冻结',
          value: 7
        },

        {
          label: '发布中',
          value: 8
        },
        {
          label: '已回滚',
          value: 9
        }
      ],
      detailData: [],
      title: '当前白名单所在实验和固化',
      statusConf: {
        0: ['默认', '#f50'],
        1: ['草稿', '#2db7f5'],
        2: ['调试中', '#87d068'],
        3: ['发布中', '#108ee9'],
        4: ['结束', 'gray'],
        5: ['继承待发布', 'yellow'],
        6: ['暂停', 'gray'],
        7: ['冻结', 'gray']
      },
      tableColumns: [
        {
          label: 'ID',
          prop: 'id'
        },
        {
          label: '实验名称',
          prop: 'displayName',
          title: true,
          line: 2,
          custom: true
        },
        {
          label: '描述',
          prop: 'description',
          title: true,
          line: 2
        },
        {
          label: '创建人',
          prop: 'createUser'
        },
        {
          label: '实验状态',
          prop: 'status',
          render: (scope) => {
            return this.statusConf[scope.row.status][0];
          }
        },
        {
          label: '实验开启时间',
          prop: 'startTime',
          title: true,
          render: (scope) => {
            return this.handleFormatDate(scope.row.startTime);
          }
        },
        {
          label: '实验结束时间',
          prop: 'endTime',
          title: true,
          render: (scope) => {
            return this.handleFormatDate(scope.row.endTime);
          }
        }
      ]
    };
  },
  created() {
    if (this.checkedId) {
      // 获取详情
      //debugger;
      this.handleSearch();
    }
  },
  mounted() {},
  methods: {
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'yyyy-MM-dd');
      }
      return '-';
    },
    // 编辑实验
    editExp(id) {
      const { href } = this.$router.resolve({ path: '/traffic-control/exp/list', query: {
          path: '/traffic-control/exp/view',
          query: JSON.stringify({id: id})
        }
      });
      window.open("/static"+href, "_blank");
    },
    handleSearch() {
      this.getAssociatedExps();
    },
    pageSizeChange(rn) {
      this.pagination.rn = rn;
      this.getAssociatedExps();
    },
    //
    pageNoChange(pn) {
      this.pagination.pn = pn;
      this.getAssociatedExps();
    },

    // dialogClose() {
    //   this.$emit('update:dialogVisible', false);
    //   // this.defaultFormat();
    // },
    //获取白名单关联实验
    getAssociatedExps() {
      this.$service
        .get(
          fns[this.type],
          { ...this.formData, ...this.pagination },
          { allback: 0, needLoading: true }
        )
        .then((data) => {
          //debugger;
          // this.detailData = data;
          this.detailData = data.list;
          this.total = data.total;
          this.pagination.rn = data.rn;
          this.pagination.pn = data.pn;
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 65%;
  min-width: 30%;
}
.com-title {
  font-weight: 600;
  font-size: 14px;
  line-height: 36px;
}
.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}
.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }
  .detail-value {
    width: 200px;
    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }
  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;
    span {
      padding: 0 11px;
    }
  }
  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }
  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}
/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}
/deep/ .divider-con {
  margin: 20px 0;
}
.tip {
  display: flex;
  padding: 10px 10px 20px;
  h3 {
    font-weight: 700;
  }
}
.p-con {
  width: 88%;
  margin: 0 auto;
}
/deep/ .status-btn {
  width: 50px;
  //color: #fff;
}
/deep/ .status-btn:hover {
  //color: #fff;
}

.table_pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
