<template>
  <el-dialog :title="title" :visible="dialogVisible" :before-close="dialogClose">
    <el-form ref="mutuformBase" :model="addData" label-width="100px" :rules="rules">
      <el-form-item label="业务线：" required prop="appKey">
        <el-select :disabled="disableEdit" v-model="addData.appKey" placeholder="请选择业务线" @change="handleChangAppKey">
          <el-option v-for="item in appList" :key="item.appKey" :label="item.displayName"
            :value="item.appKey"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="名称：" required prop="displayName">
        <el-input class="f-width-c" type="text" v-model="addData.displayName" placeholder="请输入白名单名称"></el-input>
      </el-form-item>
      <el-form-item label="账号体系：" required>
        <!-- 账号体系，cuid：1，uid: 2-->
        <el-radio-group v-model="addData.accountSystem" :disabled="disableEdit" @change="changeAccountSystem">
          <el-radio :label="1">cuid</el-radio>
          <el-radio :label="2">uid</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="cuid：" prop="cuid" v-if="addData.accountSystem === 1" required>
        <el-input :disabled="disableEdit" type="text" v-model="addData.cuid" class="f-width-c" maxlength="100"
          show-word-limit placeholder="请输入cuid，支持100个以内字符"></el-input>
      </el-form-item>
      <el-form-item label="uid：" prop="uid" v-if="addData.accountSystem === 2" required>
        <el-input :disabled="disableEdit" type="text" v-model="addData.uid" class="f-width-c" maxlength="100"
          show-word-limit placeholder="请输入uid，仅支持数字"></el-input>
      </el-form-item>
      <el-form-item label="分组：">
        <el-select v-model="addData.whitelistGroupId" clearable placeholder="请选择分组">
          <el-option v-for="item in whiteGroupList" :key="item.id" :label="item.displayName"
            :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="负责人：" prop="createUser">
        <search-user v-model="addData.createUser" :usedefault="true"></search-user>
      </el-form-item>
      <el-form-item label="描述：" prop="description">
        <el-input type="textarea" v-model="addData.description" class="f-width-c" rows="3" resize="vertical"
          maxlength="1000" show-word-limit placeholder="请输入描述信息，支持1000个以内字符"></el-input>
      </el-form-item>
    </el-form>
    <!-- 确定取消按钮组 -->
    <section slot="footer" class="dialog-footer">
      <el-button @click="dialogClose">取 消</el-button>
      <el-button type="primary" @click="validateCreate">确定</el-button>
    </section>
  </el-dialog>
</template>
<script>
import * as _ from 'lodash';
import { mapGetters } from 'vuex';
import SearchUser from '@/components/search-user/index.vue';

export default {
  name: 'create-white-dialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    editId: {
      type: Number
    }
  },
  components: {
    SearchUser
  },
  computed: {
    disableEdit() {
      return this.editId ? true : false;
    },
    ...mapGetters(['appList'], 'message')
  },
  data() {
    var checkUid = (rule, value, callback) => {
      console.log(typeof value);
      //debugger;
      if (!Number.isInteger(value - 0)) {
        callback(new Error('请输入，仅支持数字'));
      } else {
        callback();
      }
    };
    return {
      whiteGroupList: [],
      addData: {
        appKey: '',
        displayName: '',
        description: '',
        accountSystem: 1, // 账号体系，cuid：1，uid: 2
        cuid: '',
        uid: '',
        whitelistGroupId: '',
        createUser: ''
      },
      title: '新建白名单',
      rules: {
        appKey: [{ required: true, message: '请选择业务线', trigger: 'blur' }],
        displayName: [
          { required: true, message: '白名单名称不可为空', trigger: 'blur' },
          { required: true, message: '白名单名称不可为空', trigger: 'change' }
        ],
        cuid: [
          { required: true, message: 'cuid不可为空', trigger: 'blur' },
          { required: true, message: 'cuid不可为空', trigger: 'change' }
        ],
        uid: [
          { required: true, message: 'uid不可为空', trigger: 'blur' },
          { required: true, message: 'uid不可为空', trigger: 'change' },
          { validator: checkUid, trigger: 'blur' }
        ],
        createUser: [{ required: true, message: '请选择负责人', trigger: 'blur' }],
      }
    };
  },
  created() {
    if (this.editId) {
      // 获取详情
      this.getMutuDetai();
      this.title = '编辑白名单';
    } else {
      this.addData.appKey = this.appList[0].appKey;
      this.getWhiteGroupList();
    }
  },
  mounted() { },
  methods: {
    handleChangAppKey(val) {
      //   this.$emit("changeAppKey",val)
      this.addData.whitelistGroupId = '';
      this.getWhiteGroupList();
    },
    // 改变账号体系的时候
    changeAccountSystem() {
      this.addData.id = '';
      this.addData.whitelistGroupId = '';
      this.getWhiteGroupList();
    },
    getWhiteGroupList() {
      const params = {
        pn: 0,
        rn: 10000,
        appKey: this.addData.appKey,
        accountSystem: this.addData.accountSystem
      };
      this.$service
        .get('WHITE_GROUP_LIST', { ...params })
        .then(res => {
          this.whiteGroupList = res.list;
        })
        .catch(err => {
          console.log(err);
        });
    },
    validateCreate() {
      this.$refs['mutuformBase'].validate((valid) => {
        if (valid) {
          this.handleCreateMutu();
        } else {
          console.log('提交失败');
          return false;
        }
      });
    },
    handleCreateMutu() {
      this.addData.cuid = this.addData.accountSystem === 2 ? this.addData.uid : this.addData.cuid;
      const formData = this.formatFormData(this.addData);
      let api = this.editId ? 'EDITWHITE_LIST' : 'ADDWHITE_LIST';
      let tip = this.editId ? '编辑成功' : '新建成功';
      this.$service
        .post(api, formData, { needLoading: true })
        .then((data) => {
          this.dialogClose();
          this.$emit('handleSearch');
          this.$message.success(tip);
        })
        .catch((err) => {
          console.log(err);
        });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
      // this.defaultFormat();
    },
    //获取互斥责详情
    getMutuDetai() {
      this.$service
        .get('WHITELIST_VIEW', { id: this.editId }, { needLoading: true })
        .then((data) => {
          //debugger;
          data.uid = data.accountSystem === 2 ? data.cuid : '';
          this.addData = data;
          this.addData.whitelistGroupId = data.whitelistGroupId || '';
          this.getWhiteGroupList();
        })
        .catch((err) => {
          console.log(err);
        });
    }
  },
  beforeDestroy() {
    this.$emit('resetEditId');
  }
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 760px;
  min-width: 30%;
}

.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}

.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;

  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }

  .detail-value {
    width: 200px;

    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }

  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;

    span {
      padding: 0 11px;
    }
  }

  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }

  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}

/deep/ .divider-con {
  margin: 20px 0;
}

.tip {
  padding: 10px 10px 20px;
}
</style>
