<template>
  <div>
    <el-dialog :title="title" :visible="dialogVisible" :before-close="dialogClose" width="70%">
      <el-tabs v-model="activeName">
        <el-tab-pane label="实验" name="first">
          <associate-exp :checkedId="checkedId" :appKey="appKey" :type="type"></associate-exp>
        </el-tab-pane>
        <el-tab-pane label="流控实验" name="second">
          <associate-liukong-exp :checkedId="checkedId" :appKey="appKey" :type="type"></associate-liukong-exp>
        </el-tab-pane>
        <el-tab-pane label="固化" name="third">
          <associateFeat :checkedId="checkedId" :appKey="appKey" :type="type"></associateFeat>
        </el-tab-pane>
      </el-tabs>
      <!-- 关联实验 -->
      <!-- 分割线 -->
      <!-- <el-divider></el-divider> -->
      <!-- 关联固化 -->
      <!-- 确定取消按钮组 -->
      <!-- <section slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogClose">确定</el-button>
      </section> -->
    </el-dialog>
  </div>
</template>
<script>
import { formatDate } from '@/common/utils';
import { mapGetters } from 'vuex';
import AssociateExp from './associateExp.vue';
import AssociateFeat from './associateFeat.vue';
import AssociateLiukongExp from './associateLiuKongExp.vue';
export default {
  components: { AssociateExp, AssociateFeat, AssociateLiukongExp },
  name: 'AssociateDialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    checkedId: {
      type: Number
    },
    appKey: {
      type: String
    },
    type: String,
    title: {
      type: String
    },

  },
  computed: {
    ...mapGetters(['appList'], 'message')
  },
  data() {
    return {
      detailData: [],
      //title: '当前白名单所在实验和固化',
      statusConf: {
        0: ['默认', '#f50'],
        1: ['草稿', '#2db7f5'],
        2: ['调试中', '#87d068'],
        3: ['发布中', '#108ee9'],
        4: ['结束', 'gray'],
        5: ['继承待发布', 'yellow'],
        6: ['暂停', 'gray'],
        7: ['冻结', 'gray']
      },
      activeName: 'first'
    };
  },
  filters: {
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'yyyy-MM-dd');
      }
      return '-';
    }
  },
  created() {
    if (this.checkedId) {
      // 获取详情
      //this.getAssociatedExps();
    }
  },
  mounted() { },
  methods: {
    dialogClose() {
      this.$emit('update:dialogVisible', false);
      // this.defaultFormat();
    },
    //获取白名单关联实验
    getAssociatedExps() {
      this.$service
        .get(
          'GLTESTWHITE_LIST',
          { id: this.checkedId, appKey: this.appKey },
          { allback: 0, needLoading: true }
        )
        .then((data) => {
          this.detailData = data;
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 65%;
  min-width: 30%;
}

/deep/ .el-dialog {
  .el-dialog__body {
    padding-top: 12px;
  }
}

.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}

.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;

  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }

  .detail-value {
    width: 200px;

    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }

  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;

    span {
      padding: 0 11px;
    }
  }

  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }

  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}

/deep/ .divider-con {
  margin: 20px 0;
}

.tip {
  display: flex;
  padding: 10px 10px 20px;

  h3 {
    font-weight: 700;
  }
}

.p-con {
  width: 88%;
  margin: 0 auto;
}

// /deep/ .status-btn {
//   width: 50px;
//   color: #fff;
// }
// /deep/ .status-btn:hover {
//   color: #fff;
// }</style>
