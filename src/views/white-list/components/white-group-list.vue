<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-17 17:12:19
 * @LastEditors: zhu<PERSON>e
 * @LastEditTime: 2021-08-04 16:37:48
 * @Description: 实验列表
-->

<template>
  <div class="select-container">
    <el-form ref="searchForm" inline>
      <el-form-item label="">
        <el-input class="key-world-input" placeholder="支持分组名称搜索" v-model="formData.keyword" clearable @change="handleSearch"></el-input>
      </el-form-item>
      <el-form-item label="业务线：">
        <el-select v-model="formData.appKey" style="width: 150px" placeholder="请选择业务线" @change="handleSearch">
          <el-option v-for="item in appList1" :key="item.appKey" :label="item.displayName" :value="item.appKey"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账号体系：">
        <el-select style="width: 150px" v-model="formData.accountSystem" clearable placeholder="请选择" @change="handleSearch">
          <el-option v-for="item in accSystemData" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <white-table :tableList="tableList" :appKey="formData.appKey" @handelEditWhiteList="handelEditWhiteList" @handleSearch="handleSearch"></white-table>
    <div slot="pagination">
      <el-pagination v-show="total" layout="total,sizes, prev, pager, next, jumper" :page-size="pagination.rn" :page-sizes="[10, 20, 50, 100]" @size-change="pageSizeChange" :current-page="pagination.pn" :total="total"
        @current-change="pageNoChange"></el-pagination>
    </div>
    <create-white-dialog v-if="isShowDialog" :dialogVisible.sync="isShowDialog" :editId="editId" @handleSearch="handleSearch" @resetEditId="resetEditId"></create-white-dialog>
  </div>

</template>

<script>
import Page from '@/components/common/page/index.vue';
import { mapGetters } from 'vuex';
import BatchCreateWhiteDialog from './batch-create-white-dialog.vue';
import CreateWhiteDialog from './create-white-group-dialog.vue';
import WhiteTable from './white-group-table.vue';
export default {
  name: 'WhiteList', //白名单列表
  components: {
    Page,
    WhiteTable,
    CreateWhiteDialog,
    BatchCreateWhiteDialog
  },
  // mixins: [PageCommon],
  data() {
    return {
      activeName: 'first',
      editId: 0,
      isShowDialog: false,
      isShowBatchDialog: false,
      breadcrumbList: ['White'],
      formData: {
        appKey: 'all',
        keyword: '',
        accountSystem: '',
      },
      pagination: {
        pn: 1,
        rn: 10
      },
      total: 0,
      tableList: [],
    };
  },
  computed: {
    ...mapGetters(['appList1', 'accSystemData'], 'message'),
  },
  created() {
    this.$store.dispatch('getAppList');
    this.handleSearch();
  },
  methods: {
    // 重置editId
    resetEditId(val) {
      this.editId = 0;
    },
    // 重置
    handleReset() {
      this.pagination.pn = 1;
      this.formData = {
        appKey: 'all',
        keyword: ''
      };
      this.getList();
    },
    //
    pageSizeChange(rn) {
      this.pagination.rn = rn;
      this.getList();
    },
    //
    pageNoChange(pn) {
      this.pagination.pn = pn;
      this.getList();
    },
    // 查询列表数据
    handleSearch() {
      this.pagination.pn = 1;
      this.getList();
    },
    // 列表数据
    getList() {
      this.$service
        .get('WHITE_GROUP_LIST', { ...this.formData, ...this.pagination }, { needLoading: true })
        .then((data) => {
          this.tableList = data.list;
          this.total = data.total;
          this.pagination.rn = data.rn;
          this.pagination.pn = data.pn;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 创建白名单
    createWhiteList() {
      this.isShowDialog = true;
    },
    // 编辑白名单
    handelEditWhiteList(id) {
      this.editId = id;
      // debugger
      this.isShowDialog = true;
    }
  }
};
</script>

<style lang="less" scoped>
.select-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px 0 5px;
}


/deep/ .key-world-input {
  width: 251px;
}

/deep/ .el-button--medium>span {
  font-size: 14px;
  vertical-align: middle;
}

.btn_add {
  position: absolute;
  top: 20px;
  right: 0;
}

.self-collapse-btns {
  height: 33px;
}

/deep/ .el-pagination {
  text-align: right;
  margin: 12px 0;
}
</style>