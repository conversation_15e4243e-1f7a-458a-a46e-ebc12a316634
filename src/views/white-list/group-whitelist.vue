<template>
  <Page class="container" :breadcrumbList="[]" :hasPagination="false">
    <section class="header">
      <section>
        <i class="el-icon-arrow-left" @click="handleGoBack(true)"></i>
        <span class="page-title">白名单清单</span>
      </section>
      <section class="right">
        <el-button class="add-button" type="default" @click="addWhiteList" icon="el-icon-circle-plus">添加白名单</el-button>
      </section>
    </section>
    <section>
      <el-row style="margin-bottom: 16px">
        <el-col :span="8">
          <el-input v-model="query.keyword" placeholder="白名单名称" @blur="queryList" clearable></el-input>
        </el-col>
      </el-row>
      <el-table :data="list" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center">
        </el-table-column>
        <el-table-column prop="id" label="ID" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="displayName" label="白名单名称" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column prop="cuid" label="UID/CUID" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span>{{ scope.row.cuid || scope.row.uid }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="text" @click="remove([{ id: scope.row.id }])">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </section>
    <section class="pagination-container">
      <el-button type="danger" @click="remove()" :disabled="!multipleSelection.length">批量移除</el-button>
      <el-pagination layout="total,sizes, prev, pager, next, jumper" :page-size="pagination.rn"
        :page-sizes="[10, 20, 50, 100]" @size-change="pageSizeChange" :current-page="pagination.pn" :total="total"
        @current-change="pageNoChange"></el-pagination>
    </section>
    <add-white-item-dialog v-if="addWhiteListDialog.show" type="white" :dialogVisible.sync="addWhiteListDialog.show"
    @refresh="queryList" :whitelistGroupId="addWhiteListDialog.id" :appKey="addWhiteListDialog.appKey" :accountSystem="addWhiteListDialog.accountSystem"></add-white-item-dialog>
  </Page>
</template>
<script>
import Page from '@/components/common/page/index.vue';
import AddWhiteItemDialog from './components/add-white-item-dialog.vue';

export default {
  name: 'WhitelistDialog',
  components: {
    Page,
    AddWhiteItemDialog
  },
  data() {
    const id = this.$route.query.id;
    return {
      list: [],
      query: {
        whitelistGroupId: id,
        keyword: ''
      },
      multipleSelection: [],
      pagination: {
        pn: 1,
        rn: 10
      },
      total: 0,
      addWhiteListDialog: {
        show: false,
        id: null,
        appKey: ''
      }
    };
  },
  created() {
    this.queryList();
  },
  mounted() { },
  methods: {
    addWhiteList() {
      const row = this.$route.query;
      this.addWhiteListDialog.show = true;
      this.addWhiteListDialog.id = +row.id;
      this.addWhiteListDialog.appKey = row.appKey;
      this.addWhiteListDialog.accountSystem = +row.accountSystem;
    },
    handleGoBack(flag = false) {
      this.needRouterConfirm = flag;
      this.$router.push({
        path: '/exp-manage/white'
      });
    },
    queryList() {
      this.$service
        .get('WHITE_LIST', { ...this.query, ...this.pagination, appKey: 'all' }, { needLoading: true })
        .then((data) => {
          this.list = data.list || [];
          this.total = data.total;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    pageSizeChange(rn) {
      this.pagination.rn = rn;
      this.queryList();
    },
    //
    pageNoChange(pn) {
      this.pagination.pn = pn;
      this.queryList();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val.map(item => ({
        id: item.id
      }));
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    remove(ids = this.multipleSelection) {
      const formatData = this.formatFormData({
        list: ids,
        whitelistGroupId: 0
      });
      this.$service
        .post('WHITE_GROUP_UPDATE_WHITELIST', formatData)
        .then(() => {
          this.$message.success('移除成功');
          this.queryList();
          this.$emit('refresh');
        })
        .catch((err) => {
          console.log(err);
        });
    }

  }
};
</script>

<style lang="less" scoped>
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e1e3e9;
  padding-bottom: 6px;
  margin-bottom: 12px;

  .add-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 12px;
    /deep/ i {
      color: #42c57a;
      font-size: 14px!important;
      width: 14px!important;
      height: 14px!important;
      margin: 0!important;
    }
  }
  section {
    display: flex;
    align-items: center;
  }

  .right {
    /deep/ i {
      // display: inline-block;
      width: 16px;
      height: 16px;
      margin-left: 4px;
      margin-right: 6px;
      display: inline-block;
    }

    .el-button {
      margin-left: 12px;
    }

    .icon-yunhang {
      // width: 18px;
      // height: 18px;
    }
  }

  .page-title {
    font-size: 16px;
    font-weight: 500;
    line-height: 32px;
    margin: 0 8px;
  }

  i {
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
