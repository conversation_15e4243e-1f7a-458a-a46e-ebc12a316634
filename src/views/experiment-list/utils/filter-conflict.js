
function satisfyRange(value, range, compare) {
  const cmp = compare(value, range.val);
  return checkRangeSatisfaction(cmp, range.op);
}

// 辅助函数：范围是否满足
function checkRangeSatisfaction(cmpResult, op) {
  switch (op) {
    case 3: case 7: case 11: return cmpResult < 0;   // <
    case 4: case 8: case 12: return cmpResult <= 0;  // <=
    case 5: case 9: case 13: return cmpResult > 0;   // >
    case 6: case 10: case 14: return cmpResult >= 0; // >=
    default: return true;
  }
}

// 辅助函数：创建范围对象
function createRange(op, val) {
  switch (op) {
    case 3: case 7: case 11:   // <
      return { min: null, max: val, minInclusive: false, maxInclusive: false };
    case 4: case 8: case 12: // <=
      return { min: null, max: val, minInclusive: false, maxInclusive: true };
    case 5: case 9: case 13:  // >
      return { min: val, max: null, minInclusive: false, maxInclusive: false };
    case 6: case 10: case 14: // >=
      return { min: val, max: null, minInclusive: true, maxInclusive: false };
    default:
      return { min: null, max: null, minInclusive: true, maxInclusive: true };
  }
}

// 辅助函数：范围求交集
function intersectRanges(range1, range2, compare) {
  // 处理左边界
  let min, minInclusive;
  if (range1.min === null) {
    min = range2.min;
    minInclusive = range2.minInclusive;
  } else if (range2.min === null) {
    min = range1.min;
    minInclusive = range1.minInclusive;
  } else {
    const cmp = compare(range1.min, range2.min);
    if (cmp > 0) {
      min = range1.min;
      minInclusive = range1.minInclusive;
    } else if (cmp < 0) {
      min = range2.min;
      minInclusive = range2.minInclusive;
    } else {
      min = range1.min;
      minInclusive = range1.minInclusive && range2.minInclusive;
    }
  }

  // 处理右边界
  let max, maxInclusive;
  if (range1.max === null) {
    max = range2.max;
    maxInclusive = range2.maxInclusive;
  } else if (range2.max === null) {
    max = range1.max;
    maxInclusive = range1.maxInclusive;
  } else {
    const cmp = compare(range1.max, range2.max);
    if (cmp < 0) {
      max = range1.max;
      maxInclusive = range1.maxInclusive;
    } else if (cmp > 0) {
      max = range2.max;
      maxInclusive = range2.maxInclusive;
    } else {
      max = range1.max;
      maxInclusive = range1.maxInclusive && range2.maxInclusive;
    }
  }

  // 检查范围有效性
  if (min !== null && max !== null) {
    const cmp = compare(min, max);
    if (cmp > 0) return null; // min > max
    if (cmp === 0) {
      if (!(minInclusive && maxInclusive)) return null; // [min, max) 或 (min, max]
    }
  }

  return { min, max, minInclusive, maxInclusive };
}

// 辅助函数：判断是否为单点范围
function isSinglePoint(range, compare) {
  return (
    range.min !== null &&
    range.max !== null &&
    compare(range.min, range.max) === 0 &&
    range.minInclusive &&
    range.maxInclusive
  );
}

export const versionOrdinal = (version) => {
  const maxByte = 255; // 1<<8 - 1
  const vo = [];       // 结果字符数组
  let j = -1;          // 当前数字块的标记索引

  for (let i = 0; i < version.length; i++) {
    const c = version[i];

    // 处理非数字字符
    if (c < '0' || c > '9') {
      vo.push(c);
      j = -1;
      continue;
    }

    // 遇到新数字块
    if (j === -1) {
      vo.push(String.fromCharCode(0)); // 添加长度标记位
      j = vo.length - 1;              // 记录标记位索引
    }

    // 获取当前数字块长度
    const currentLength = vo[j].charCodeAt(0);

    // 处理前导零替换（如 "01" -> "1"）
    if (currentLength === 1 && vo[j + 1] === '0') {
      vo[j + 1] = c;  // 替换前导零
      continue;
    }

    // 检查长度限制
    if (currentLength + 1 > maxByte) {
      throw new Error("invalid version");
    }

    // 追加数字并更新长度标记
    vo.push(c);
    vo[j] = String.fromCharCode(currentLength + 1);
  }

  return vo.join('');
};

export const checkFilterConflict = (filterRule) => {
  const { filterList } = filterRule;
  const groups = {};

  // 1. 按 filterConfId 分组
  (filterList||[]).forEach(item => {
    const id = item.filterConfId;
    if (!groups[id]) groups[id] = [];
    groups[id].push(item);
  });

  // 比较函数工厂（数字/版本号）
  const getComparator = (type, op) => {
    if (type === 4) { // float
      return (a, b) => {
        const numA = parseFloat(a);
        const numB = parseFloat(b);
        if (isNaN(numA)) return a.localeCompare(b);
        if (isNaN(numB)) return b.localeCompare(a);
        return numA - numB;
      };
    } else if (type === 2) { // bool
      return (a, b) => {
        return a.localeCompare(b);
      };
    } else if (type === 1 && [7,8,9,10].includes(op)) { // version
      return (a, b) => {
        const av = versionOrdinal(a);
        const bv = versionOrdinal(b);
        if (av < bv) {
          return -1;
        } else if (av > bv) {
          return 1;
        } else {
          return 0;
        }
      };
    } else if (type === 1) { // 字符串字典序
      return (a, b) => {
        return a.localeCompare(b);
      };
    }
  };

  const rangeConflict = (ranges, equals, notEquals) => {
    if (ranges.length > 0) {
      let globalRange = { min: null, max: null, minInclusive: true, maxInclusive: true };
      const compare = getComparator(ranges[0].type, ranges[0].op);
      for (const range of ranges) {
        const newRange = createRange(range.op, range.val);
        globalRange = intersectRanges(globalRange, newRange, compare);
        if (globalRange === null) return true; // 范围交集为空
      }

      // 冲突检查 5: 单点范围与不等于条件冲突
      if ((equals||[]).length === 0 && isSinglePoint(globalRange, compare)) {
        const point = globalRange.min;
        const notEqAll = notEquals.flat();
        if (notEqAll.some(neqVal => compare(point, neqVal) === 0)) {
          return true;
        }
      }
    }
    return false;
  };

  const intersection = (a, b) => [...new Set(a)].filter(x => new Set(b).has(x));
  const difference = (a, b) => [...new Set(a)].filter(x => !(new Set(b).has(x)));

  // 2. 处理每个分组
  for (const groupId in groups) {
    const conditions = groups[groupId];
    if (conditions.length < 2) continue;

    // 分类收集条件
    const equals = [];   // 存储所有等于条件的值数组
    const notEquals = []; // 存储所有不等于条件的值数组
    const ranges = [];    // 存储范围条件
    const versionRanges = [];    // 存储版本范围条件

    conditions.forEach(cond => {
      const op = cond.keyType;
      const values = cond.keyValue;

      if (op === 1) equals.push(values);      // 等于（IN）
      else if (op === 2) notEquals.push(values); // 不等于（NOT IN）
      else if ([3,4,5,6,11,12,13,14].includes(op)) ranges.push({ op, val: values[0], type: cond.filterType}); // 范围操作符取第一个值
      else if ([7,8,9,10].includes(op)) versionRanges.push({ op, val: values[0], type: cond.filterType}); // 范围操作符取第一个值
    });


    // 冲突检查 1: 多个等于条件是否有交集
    if (equals.length > 0) {
      // 计算所有等于条件的交集
      let ei = equals[0].slice();
      for (let i = 1; i < equals.length; i++) {
        ei = intersection(ei,equals[i]);
        if (ei.length === 0) return false; // 无交集则冲突
      }

      // 冲突检查 2: 等于条件与不等于条件冲突
      const notEqAll = notEquals.flat(); // 所有不等于条件的并集
      if (notEqAll.length > 0) {
        ei = difference(ei, notEqAll);
        // console.log(ei);
        if (ei.length === 0) return false; // 等值和不等值全部冲突
      }
      // 冲突检查 3: 等于条件与范围条件冲突
      if (ranges.length > 0) {
        const violatesRange = ei.some(val =>
          ranges.some(range => !satisfyRange(val, range, getComparator(range.type, range.op)))
        );
        if (violatesRange) return false;
      }

      if (versionRanges.length > 0) {
        const violatesRange = ei.some(val =>
          versionRanges.some(range => !satisfyRange(val, range, getComparator(range.type, range.op)))
        );
        if (violatesRange) return false;
      }
    }

    // 冲突检查 4: 范围条件之间的冲突
    if (rangeConflict(ranges)) {
      console.log(ranges);
      return false;
    }
    if (rangeConflict(versionRanges)) {
      console.log(versionRanges);
      return false;
    }
  }
  return true; // 无冲突
};
