<template>
  <div class="visual_editor_iframe">
    <iframe ref="my_iframe" class="my_iframe" :src="url" :style="{ width: iframeSize }"></iframe>
  </div>
</template>

<script>
import InitIframe from './init-iframe.js';

export default {
  props: {
    url: String,
    iframeSize: String,
    mode: Number
  },
  data() {
    return {
      myIframe: null
    };
  },
  watch: {
    mode(newVal) {
      this.myIframe.swithMode(newVal);
    }
  },
  methods: {
    init() {
      this.myIframe = new InitIframe({
        iframe: this.$refs.my_iframe,
        iframeOnload: this.iframeOnload,
        clickElement: this.clickElement,
        hideElement: this.hideElement,
        clickTarElement: this.clickTarElement
      });
    },
    refresh() {
      this.$refs.my_iframe.contentWindow.location.reload(true);
      this.init();
    },
    iframeOnload() {
      this.$emit('iframe-onload');
    },
    clickElement(data) {
      this.$emit('click-element', data);
    },
    hideElement() {
      this.$emit('hide-element');
    },
    clickTarElement(xpath) {
      this.$emit('click-tar-element', xpath);
    }
  },
  mounted() {
    // this.init();
  }
};
</script>

<style lang="less" scoped>
.visual_editor_iframe {
  flex: 1;
  background: radial-gradient(#e1e4e8 5%, transparent 16%) 0 0,
    radial-gradient(#e1e4e8 5%, transparent 20%) 8px 9px;
  background-size: 16px 16px;
  background-color: #f3f4f5;
  text-align: center;
}

.my_iframe {
  max-width: 100%;
  height: 100%;
}
</style>