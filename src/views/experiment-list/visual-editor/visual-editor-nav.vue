<template>
  <div class="visual_editor_nav">
    <div class="left">
      <span class="exp_name">{{ currentVersion.displayName }}</span>
      <span class="exp_url">{{ url }}</span>
      <i class="el-icon-refresh-right exp_refresh" @click="hanldeRefresh"></i>
    </div>

    <div class="right">
      <el-dropdown
        size="default"
        trigger="click"
        @command="handleCommand"
        @visible-change="handleVisibleChange"
      >
        <div :class="['size_link', { size_link_focus: visible }]">
          <i :class="currentSize.icon"></i>
          <span class="size_lebel">{{ currentSize.label }}</span>
          <span class="size_value">{{ currentSize.value }}</span>
          <i class="el-icon-arrow-down size_icon"></i>
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="item in sizeOptions" :key="item.type" :command="item.type">
            <i class="dropdown_item_icon" :class="item.icon"></i>
            <span class="size_lebel">{{ item.label }}</span>
            <span v-if="item.type !== 4" class="size_value">{{ item.value }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <el-input-number
        v-if="currentSize.type === 4"
        class="size_input"
        size="mini"
        v-model="customSize"
        controls-position="right"
        @change="handleCustomSizeChange"
        :min="375"
      />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    size: String,
    url: String,
    currentVersion: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      currentSize: {},
      customSize: 800,
      sizeOptions: [
        { label: '全屏宽度', value: '100%', type: 1, icon: 'el-icon-monitor' },
        { label: '平板电脑', value: '768px', type: 2, icon: 'el-icon-mobile' },
        { label: '移动端', value: '375px', type: 3, icon: 'el-icon-mobile-phone' },
        { label: '自定义宽度', value: '', type: 4, icon: 'el-icon-set-up' }
      ],
      visible: false
    };
  },
  methods: {
    hanldeRefresh() {
      this.$emit('refresh');
    },
    handleCommand(val) {
      const opt = this.sizeOptions.find((f) => f.type === val);
      this.currentSize = opt;
      const iframeSize = opt.value || this.customSize + 'px';
      this.$emit('update:iframeSize', iframeSize);
    },
    handleVisibleChange(val) {
      this.visible = val;
    },
    handleCustomSizeChange(val) {
      this.$emit('update:iframeSize', val + 'px');
    }
  },
  created() {
    this.currentSize = this.sizeOptions[0];
  }
};
</script>

<style lang="less" scoped>
.visual_editor_nav {
  padding: 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left {
  font-size: 14px;
}

.exp_name {
  color: #606266;
  font-weight: 700;
  margin-right: 10px;
}

.exp_url {
  color: #c4c4c4;
  margin-right: 10px;
}

.exp_refresh {
  cursor: pointer;
}

.dropdown_item_icon {
  margin-right: 0;
}

.size_link {
  width: 178px;
  height: 28px;
  line-height: 28px;
  cursor: pointer;
}

.size_link_focus {
  color: #c4c4c4;
}

.size_lebel {
  margin: 0 24px;
}

.size_value {
  color: #c4c4c4;
}

.size_icon {
  color: #c4c4c4;
  float: right;
  margin: 7px 5px 0 0 !important;
}

.size_input {
  margin-left: 10px;
}
</style>