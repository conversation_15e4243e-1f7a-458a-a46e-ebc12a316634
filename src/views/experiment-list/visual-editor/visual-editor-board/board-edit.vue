<template>
  <div class="board_edit">
    <div class="collapse_box">
      <el-collapse v-model="activeName" accordion>
        <el-collapse-item
          v-for="item in collapseList"
          :key="item.name"
          :title="item.title"
          :name="item.name"
        >
          <component
            class="comp"
            :is="item.comp"
            :curEl="curEl"
            :active-name="activeName"
            :editable="editable"
          ></component>
        </el-collapse-item>
      </el-collapse>
    </div>
    <div class="back_styles" @click="handleBackStyles">恢复默认样式</div>
  </div>
</template>

<script>
import BoardEditFont from './board-edit-font.vue';
import BoardEditBorder from './board-edit-border.vue';
import BoardEditImg from './board-edit-img.vue';
import BoardEditLayout from './board-edit-layout.vue';
import BoardEditAdd from './board-edit-add.vue';

export default {
  components: {
    BoardEditFont,
    BoardEditBorder,
    BoardEditImg,
    BoardEditLayout,
    BoardEditAdd
  },
  props: {
    curEl: {
      type: Object,
      default: () => ({
        styles: {},
        attrs: {},
        layout: {},
        els: []
      })
    }
  },
  inject: ['backDefaultStyles'],
  data() {
    return {
      activeName: '1',
      collapseList: [
        {
          name: '1',
          title: '字体排版',
          comp: 'board-edit-font'
        },
        {
          name: '2',
          title: '边框',
          comp: 'board-edit-border'
        },
        {
          name: '3',
          title: '图片',
          comp: 'board-edit-img'
        },
        {
          name: '4',
          title: '布局',
          comp: 'board-edit-layout'
        },
        {
          name: '5',
          title: '新增元素',
          comp: 'board-edit-add'
        }
      ]
    };
  },
  computed: {
    editable() {
      return !!this.curEl.xpath;
    }
  },
  methods: {
    handleBackStyles() {
      this.$confirm('该操作会取消掉之前所有元素修改记录，是否确认执行？', '恢复默认样式', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.backDefaultStyles();
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    }
  }
};
</script>

<style lang="less" scoped>
.board_edit {
  height: 100%;
  position: relative;
  box-sizing: border-box;
}

.collapse_box {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.back_styles {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: #cbced526;
  color: #ee0000;
  transition: all 0.3s ease-out;
  cursor: pointer;

  &:hover {
    color: white;
    background: #ee0000;
  }
}

/deep/.el-collapse-item__header {
  padding-left: 16px;
  font-size: 12px;
}

/deep/.el-collapse-item.is-active {
  .el-collapse-item__wrap {
    border-bottom: 0;
  }
}

/deep/.el-collapse-item__content {
  padding-bottom: 0;
  font-size: 12px;
}

/deep/.comp {
  .flex_box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .form_box_row {
    display: flex;
    align-items: center;
    width: 50%;
    padding: 0 8px;
    overflow: hidden;

    &:first-child {
      padding-left: 0;
    }

    &:last-child {
      padding-right: 0;
    }
  }

  .form_label_row {
    color: #cfcfcf;
    font-size: 14px;
    padding-right: 8px;
  }

  .form_box_columns {
    margin-bottom: 16px;

    textarea {
      resize: none;
    }
  }

  .form_label_columns {
    color: #b3b3b3;
    margin-bottom: 6px;
  }
}
</style>