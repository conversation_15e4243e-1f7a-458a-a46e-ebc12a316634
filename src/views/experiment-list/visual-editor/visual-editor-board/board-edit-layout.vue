<template>
  <div class="board_edit_layout">
    <div class="form_box_columns">
      <div class="form_label_columns">能见度</div>
      <icon-select v-model="curEl.layout.opacity" :options="opacityOpts" :disabled="!editable" />
    </div>

    <div class="select_box">
      <span>请选择当前元素移动到目标元素的位置</span>
    </div>

    <div class="where_box">
      <el-select
        v-model="curEl.layout.where"
        size="mini"
        :disabled="!editable"
        :style="{ width: '50%' }"
      >
        <el-option
          v-for="item in whereOpts"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <icon-select v-model="curEl.layout.where" :options="whereIconOpts" :disabled="true" />
    </div>

    <div class="where_tips">{{ whereTips }}</div>

    <div class="select_box">
      <div
        :class="['select_btn', { select_btn_disabled: !editable }]"
        @click="!editable ? '' : _selectElement()"
      >
        <span>{{ selectTar ? '取消' : '选择目标元素' }}</span>
        <i class="iconfont znzt-miaozhun"></i>
      </div>
    </div>

    <div class="form_box_columns">
      <div class="form_label_columns">编辑偏移值</div>
      <div class="flex_box">
        <div class="form_box_row">
          <span class="form_label_row">上</span>
          <el-input-number
            v-model="curEl.layout.top"
            controls-position="right"
            size="mini"
            :disabled="!editable"
          />
        </div>

        <div class="form_box_row">
          <span class="form_label_row">右</span>
          <el-input-number
            v-model="curEl.layout.right"
            controls-position="right"
            size="mini"
            :disabled="!editable"
          />
        </div>
      </div>
      <div class="flex_box">
        <div class="form_box_row">
          <span class="form_label_row">下</span>
          <el-input-number
            v-model="curEl.layout.bottom"
            controls-position="right"
            size="mini"
            :disabled="!editable"
          />
        </div>

        <div class="form_box_row">
          <span class="form_label_row">左</span>
          <el-input-number
            v-model="curEl.layout.left"
            controls-position="right"
            size="mini"
            :disabled="!editable"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import IconSelect from './icon-select';

export default {
  components: {
    IconSelect
  },
  props: {
    curEl: {
      type: Object,
      default: () => ({
        styles: {},
        attrs: {},
        layout: {},
        els: []
      })
    },
    editable: Boolean
  },
  inject: ['selectElement'],
  data() {
    return {
      opacityOpts: [
        {
          icon: 'znzt-kejian',
          value: 1
        },
        {
          icon: 'znzt-bukejian',
          value: 0.1
        },
        {
          icon: 'znzt-yichu',
          value: 0
        }
      ],
      whereOpts: [
        {
          label: '之前',
          value: 'beforebegin',
          tips: '将元素移动到所选元素的外侧、紧邻其前'
        },
        {
          label: '内部之前',
          value: 'afterbegin',
          tips: '将元素移到所选元素的内侧的最顶部'
        },
        {
          label: '内部之后',
          value: 'beforeend',
          tips: '将元素移到所选元素的内侧的最底部'
        },
        {
          label: '之后',
          value: 'afterend',
          tips: '将元素移动到所选元素的外侧，紧邻其后'
        }
      ],
      whereIconOpts: [
        {
          icon: 'znzt-insert-before',
          value: 'beforebegin'
        },
        {
          icon: 'znzt-insert-after',
          value: 'afterend'
        },
        {
          icon: 'znzt-insert-in-before',
          value: 'afterbegin'
        },
        {
          icon: 'znzt-insert-in-after',
          value: 'beforeend'
        }
      ],
      selectTar: false
    };
  },
  computed: {
    whereTips() {
      const opt = this.whereOpts.find((f) => f.value === this.curEl.layout.where);
      return opt ? opt.tips : '';
    }
  },
  watch: {
    'curEl.xpath'() {
      this.selectTar && (this.selectTar = false);
    }
  },
  methods: {
    _selectElement() {
      this.selectTar = !this.selectTar;
      this.selectElement(this.selectTar);
    }
  }
};
</script>

<style lang="less" scoped>
.board_edit_layout {
  padding: 0 8px;
}

.where_box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.where_tips {
  color: #b3b3b3;
  margin-bottom: 16px;
}

.opacity_box {
  display: flex;
  align-items: center;
}

.opacity_slider {
  flex: 1;
  margin: 0 24px 0 12px;
}

.select_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.select_btn {
  padding: 5px 24px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  line-height: 1;
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;

  i {
    font-size: 16px;
  }

  &:hover {
    color: #42c57a;
    border-color: #c6eed7;
    background-color: #ecf9f2;
  }
}

.select_btn_disabled,
.select_btn_disabled:hover {
  color: #c0c4cc;
  cursor: not-allowed;
  background-color: #fff;
  border-color: #ebeef5;
}
</style>