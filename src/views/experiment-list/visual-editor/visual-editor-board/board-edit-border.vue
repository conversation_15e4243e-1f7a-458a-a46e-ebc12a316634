<template>
  <div class="board_edit_border">
    <div class="flex_box">
      <div class="form_box_row">
        <i class="form_label_row iconfont znzt-border-style"></i>
        <el-select v-model="curEl.styles.borderStyle" size="mini" :disabled="!editable">
          <el-option
            v-for="item in borderStyleOpts"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>

      <div class="form_box_row">
        <i class="form_label_row iconfont znzt-border-width"></i>
        <el-input-number
          v-model="curEl.styles.borderWidth"
          controls-position="right"
          size="mini"
          :disabled="!editable"
        />
      </div>
    </div>

    <div class="flex_box">
      <div class="form_box_row">
        <i class="form_label_row iconfont znzt-border-radius"></i>
        <el-input-number
          v-model="curEl.styles.borderRadius"
          controls-position="right"
          size="mini"
          :disabled="!editable"
        />
      </div>
      <div class="form_box_row">
        <el-color-picker
          class="color_label"
          v-model="curEl.styles.borderColor"
          size="mini"
          :disabled="!editable"
        ></el-color-picker>
        <div class="color_text">{{ curEl.styles.borderColor }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    curEl: {
      type: Object,
      default: () => ({
        styles: {},
        attrs: {},
        layout: {},
        els: []
      })
    },
    editable: Boolean
  },
  data() {
    return {
      borderStyleOpts: [
        {
          label: '无',
          value: 'none'
        },
        {
          label: '点',
          value: 'dotted'
        },
        {
          label: '内嵌',
          value: 'inset'
        },
        {
          label: '外嵌',
          value: 'groove'
        },
        {
          label: '虚线',
          value: 'dashed'
        },
        {
          label: '实线',
          value: 'solid'
        },
        {
          label: '双线',
          value: 'double'
        }
      ],
      value: ''
    };
  }
};
</script>

<style lang="less" scoped>
.board_edit_border {
  padding: 0 8px;
}

.color_label {
  margin-right: 8px;
}

.color_text {
  cursor: pointer;
  position: relative;
}
</style>