<template>
  <div class="board_edit_add">
    <div class="title">选择相对于所选元素的新元素的位置</div>

    <div class="where_box">
      <el-select v-model="where" size="mini" :disabled="!editable" :style="{ width: '50%' }">
        <el-option
          v-for="item in whereOpts"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <icon-select v-model="where" :options="whereIconOpts" disabled />
    </div>

    <div class="where_tips">{{ whereTips }}</div>

    <div class="where_box">
      <span class="html_title">编辑HTML</span>
      <a :class="{ html_save_disabled: !editable }" @click="!editable ? '' : handleSubmit()">
        保存
      </a>
    </div>

    <codemirror ref="myCm" class="codemirror" v-model="code" :options="options"></codemirror>
  </div>
</template>

<script>
import IconSelect from './icon-select';
import { codemirror } from 'vue-codemirror';
// 核心样式
import 'codemirror/lib/codemirror.css';
// 引入主题后还需要在 options 中指定主题才会生效
import 'codemirror/theme/rubyblue.css';
import 'codemirror/mode/python/python.js';

export default {
  components: {
    IconSelect,
    codemirror
  },
  inject: ['htmlSubmit'],
  props: {
    activeName: String,
    editable: Boolean,
    curEl: {
      type: Object,
      default: () => ({
        styles: {},
        attrs: {},
        layout: {},
        els: []
      })
    }
  },
  data() {
    return {
      where: 'beforebegin',
      whereOpts: [
        {
          label: '之前',
          value: 'beforebegin',
          tips: '将在所选元素的外侧、紧邻其前插入新的HTML内容'
        },
        {
          label: '内部之前',
          value: 'afterbegin',
          tips: '将在所选元素内侧的最顶部插入新的HTML内容'
        },
        {
          label: '内部之后',
          value: 'beforeend',
          tips: '将在所选元素内则的最底部插入新的HTML内容'
        },
        {
          label: '之后',
          value: 'afterend',
          tips: '将在所选元素的外侧、紧邻其后插入新的HTML内容'
        }
      ],
      whereIconOpts: [
        {
          icon: 'znzt-insert-before',
          value: 'beforebegin'
        },
        {
          icon: 'znzt-insert-after',
          value: 'afterend'
        },
        {
          icon: 'znzt-insert-in-before',
          value: 'afterbegin'
        },
        {
          icon: 'znzt-insert-in-after',
          value: 'beforeend'
        }
      ],
      code: `<!-- button>
  <span>
    say hello to you
  </span>
</button -->`, // 编辑器绑定的值
      // 默认配置
      options: {
        tabSize: 2, // 缩进格式
        theme: 'monokai', // 主题，对应主题库 JS 需要提前引入
        lineNumbers: true, // 显示行号
        line: true,
        styleActiveLine: true, // 高亮选中行
        hintOptions: {
          completeSingle: true // 当匹配只有一项的时候是否自动补全
        }
      }
    };
  },
  computed: {
    whereTips() {
      const opt = this.whereOpts.find((f) => f.value === this.where);
      return opt ? opt.tips : '';
    }
  },
  watch: {
    activeName(newVal) {
      if (newVal === '5') {
        this.$refs.myCm.codemirror.refresh();
      }
    }
    // 'curEl.els'(newVal) {
    //   if (newVal.length) {
    //     const last = newVal[newVal.length - 1];
    //     this.where = last.where;
    //     this.code = last.code;
    //   }
    // }
  },
  methods: {
    handleSubmit() {
      this.htmlSubmit({
        where: this.where,
        code: this.code
      });
    }
  }
};
</script>

<style lang="less" scoped>
.board_edit_add {
  padding: 0 8px;
}

.title {
  margin-bottom: 16px;
}

.where_box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.where_tips {
  color: #b3b3b3;
  margin-bottom: 16px;
}

.html_title {
  color: #b3b3b3;
}

.codemirror {
  margin-bottom: 16px;
}

.html_save_disabled,
.html_save_disabled:hover {
  color: #c0c4cc;
  cursor: not-allowed;
  background-color: #fff;
  border-color: #ebeef5;
}
</style>