<template>
  <div class="board_edit_img">
    <div class="form_box_columns">
      <div class="form_label_columns">图片名称</div>
      <el-input v-model="curEl.attrs.alt" size="mini" :disabled="disabled" />
    </div>

    <div class="form_box_columns">
      <div class="form_label_columns">图片地址</div>
      <el-input v-model="curEl.attrs.src" size="mini" :disabled="disabled" />
    </div>

    <el-alert
      class="img_alert"
      title="建议使用URL替换图片，上传将受大小、格式及安全策略影响可能无法正常展示"
      type="warning"
      :closable="false"
      show-icon
    ></el-alert>

    <div class="flex_box">
      <div class="form_box_row">
        <span class="form_label_row">W</span>
        <el-input-number
          v-model="curEl.styles.width"
          :min="1"
          controls-position="right"
          size="mini"
          :disabled="disabled"
        />
      </div>

      <div class="form_box_row">
        <span class="form_label_row">H</span>
        <el-input-number
          v-model="curEl.styles.height"
          :min="1"
          controls-position="right"
          size="mini"
          :disabled="disabled"
        />
      </div>
    </div>

    <el-upload
      :disabled="disabled"
      class="upload_box"
      :action="uploadConfig.action"
      :show-file-list="false"
      :on-success="(res) => handleImageSuccess(curEl, res)"
      :before-upload="beforeImageUpload"
    >
      <el-button class="upload_btn" type="primary" :disabled="disabled">上传</el-button>
    </el-upload>
  </div>
</template>

<script>
const actions = {
  local: '/testAddress/earthworm/mis/upload/uploadimg',
  test: '/earthworm/mis/upload/uploadimg',
  server: '/earthworm/mis/upload/uploadimg'
};

export default {
  props: {
    curEl: {
      type: Object,
      default: () => ({
        styles: {},
        attrs: {},
        layout: {},
        els: []
      })
    },
    editable: Boolean
  },
  computed: {
    disabled() {
      return !this.editable || this.curEl.tagName !== 'img';
    },
    uploadConfig() {
      return {
        action: this.computedEnv()
      };
    }
  },
  methods: {
    // 适配文件上传环境地址
    computedEnv() {
      let hostName = location.hostname;
      let url = '';
      if (/^localhost$/gi.test(hostName) || /^10./gi.test(hostName)) {
        url = actions.local;
      } else if (/docker|test/gi.test(hostName)) {
        url = actions.test;
      } else {
        url = actions.server;
      }
      return url;
    },
    // 上传图片之前
    beforeImageUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      const isImg = 'png,jpg,jpeg'.indexOf(file.type.split('/')[1]) !== -1;

      if (!isImg) {
        this.$message.error('上传图片只能是 JPG、JPEG、PNG 格式!');
      }

      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isLt2M && isImg;
    },
    // 处理图片上传成功
    handleImageSuccess(curEl, res, file) {
      curEl.attrs.src = res.data.url;
    }
  }
};
</script>

<style lang="less" scoped>
.board_edit_img {
  padding: 0 8px;
}

.img_alert {
  margin-bottom: 16px;
}

.upload_box {
  width: 100%;
  margin-bottom: 16px;
}

/deep/ .el-upload {
  width: 100%;
}

.upload_btn {
  width: 100%;
}
</style>