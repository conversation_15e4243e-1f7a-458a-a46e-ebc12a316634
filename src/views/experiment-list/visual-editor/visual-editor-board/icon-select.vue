<template>
  <div :class="['icon_select', { disabled }]">
    <div
      :class="['optipn_item', { active: value === item.value }]"
      v-for="item in options"
      :key="item.value"
      @click="handleSelect(item.value)"
    >
      <i :class="['iconfont', item.icon]"></i>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    value: String | Number,
    options: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleSelect(val) {
      this.$emit('input', val);
    }
  }
};
</script>

<style lang="less" scoped>
.icon_select {
  display: flex;
}

.optipn_item {
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 4px;

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    margin-right: 0;
  }

  &:hover {
    background: #f1f3f5;
  }
}

.active {
  background: #dee1e4 !important;
}

.disabled {
  cursor: not-allowed;

  .optipn_item {
    pointer-events: none;
  }
}
</style>