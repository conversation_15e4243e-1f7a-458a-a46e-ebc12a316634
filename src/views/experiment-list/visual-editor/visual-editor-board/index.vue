<template>
  <div class="visual_editor_board">
    <div class="tabs">
      <span
        v-for="item in tabs"
        :key="item.value"
        :class="['tabs_babel', { active: item.value === current }]"
        @click="current = item.value"
      >
        {{ item.label }}
      </span>
    </div>

    <div class="tabs_content">
      <keep-alive>
        <component :is="currentTab.comp" :curEl="curEl" :actionsRecord="actionsRecord" />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import BoardEdit from './board-edit.vue';
import BoardRecord from './board-record.vue';

export default {
  components: {
    BoardEdit,
    BoardRecord
  },
  props: {
    curEl: {
      type: Object,
      default: () => ({
        styles: {},
        attrs: {},
        layout: {},
        els: []
      })
    },
    actionsRecord: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      current: 1,
      tabs: [
        { label: '可视化编辑', value: 1, comp: 'board-edit' },
        { label: '操作记录', value: 2, comp: 'board-record' }
      ]
    };
  },
  computed: {
    currentTab() {
      return this.tabs.find((f) => f.value === this.current);
    }
  }
};
</script>

<style lang="less" scoped>
.visual_editor_board {
  width: 250px;
  min-width: 250px;
  border-right: 1px solid #eee;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.tabs {
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.tabs_babel {
  color: #b3b3b3;
  font-weight: 500;
  margin-right: 12px;
  transition: all 0.2s ease;
  cursor: pointer;

  &:last-child {
    margin-right: 0;
  }

  &.active {
    color: #333333;
  }
}

.tabs_content {
  flex: 1;
  overflow: hidden;
}
</style>