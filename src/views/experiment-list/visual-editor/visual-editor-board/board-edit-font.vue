<template>
  <div class="board_edit_font">
    <div class="flex_box">
      <div class="form_box_row">
        <i class="form_label_row iconfont znzt-font-size"></i>
        <el-input-number
          v-model="curEl.styles.fontSize"
          controls-position="right"
          size="mini"
          :min="12"
          :disabled="disabled"
        />
      </div>

      <div class="form_box_row">
        <i class="form_label_row iconfont znzt-biaoti"></i>
        <el-select size="mini" disabled>
          <el-option v-for="item in []" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>

    <div class="flex_box">
      <div class="form_box_row">
        <i class="form_label_row iconfont znzt-jiacu"></i>
        <el-select v-model="curEl.styles.fontWeight" size="mini" :disabled="disabled">
          <el-option
            v-for="item in ['100', '200', '300', '400', '500', '600', '700', '800', '900']"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </div>
      <div class="form_box_row">
        <el-color-picker
          class="color_label"
          v-model="curEl.styles.color"
          size="mini"
          :disabled="disabled"
        ></el-color-picker>
        <div class="color_text">{{ curEl.styles.color }}</div>
      </div>
    </div>

    <div class="flex_box">
      <div class="form_box_row">
        <i class="form_label_row iconfont znzt-xiahuaxian"></i>
        <el-select v-model="curEl.styles.textDecorationLine" size="mini" :disabled="disabled">
          <el-option
            v-for="item in textDecorationLineOpts"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="form_box_row">
        <i class="form_label_row iconfont znzt-font-style"></i>
        <el-select v-model="curEl.styles.fontStyle" size="mini" :disabled="disabled">
          <el-option
            v-for="item in ['normal', 'italic', 'oblique']"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </div>
    </div>

    <div class="form_box_columns">
      <div class="form_label_columns">文本</div>
      <el-input type="textarea" v-model="curEl.attrs.innerText" :rows="4" :disabled="disabled" />
    </div>

    <div class="icon_select_box">
      <icon-select
        v-model="curEl.styles.textAlign"
        :options="textAlignOpts"
        :disabled="!editable"
      />
      <icon-select
        v-model="curEl.styles.verticalAlign"
        :options="verticalAlignOpts"
        :disabled="!editable"
      />
    </div>

    <div class="form_box_columns">
      <div class="form_label_columns">链接</div>
      <el-input
        type="textarea"
        v-model="curEl.attrs.href"
        :rows="4"
        :disabled="disabled || curEl.tagName !== 'a'"
      />
    </div>

    <div class="form_box_columns">
      <div class="form_label_columns">打开方式</div>
      <el-select
        v-model="curEl.attrs.target"
        size="mini"
        :style="{ width: '100%' }"
        :disabled="disabled || curEl.tagName !== 'a'"
      >
        <el-option
          v-for="item in targetOpts"
          :key="item.value"
          :label="`${item.label}（${item.value}）`"
          :value="item.value"
        />
      </el-select>
    </div>
  </div>
</template>

<script>
import IconSelect from './icon-select';

export default {
  components: {
    IconSelect
  },
  props: {
    curEl: {
      type: Object,
      default: () => ({
        styles: {},
        attrs: {},
        layout: {},
        els: []
      })
    },
    editable: Boolean
  },
  data() {
    return {
      textDecorationLineOpts: [
        {
          label: '无',
          value: 'none'
        },
        {
          label: '下划线',
          value: 'underline'
        },
        {
          label: '上划线',
          value: 'overline'
        },
        {
          label: '贯穿/删除线',
          value: 'line-through'
        }
      ],
      textAlignOpts: [
        {
          icon: 'znzt-text-align-left',
          value: 'left'
        },
        {
          icon: 'znzt-text-align-center',
          value: 'center'
        },
        {
          icon: 'znzt-text-align-right',
          value: 'right'
        },
        {
          icon: 'znzt-justify',
          value: 'justify'
        }
      ],
      verticalAlignOpts: [
        {
          icon: 'znzt-top',
          value: 'top'
        },
        {
          icon: 'znzt-middle',
          value: 'middle'
        },
        {
          icon: 'znzt-bottom',
          value: 'bottom'
        }
      ],
      targetOpts: [
        {
          label: '默认',
          value: '_self'
        },
        {
          label: '新标签',
          value: '_blank'
        },
        {
          label: '当前窗口',
          value: '_top'
        },
        {
          label: '父级frame',
          value: '_parent'
        }
      ]
    };
  },
  computed: {
    disabled() {
      return !this.editable || ['img'].includes(this.curEl.tagName);
    }
  }
};
</script>

<style lang="less" scoped>
.board_edit_font {
  padding: 0 8px;
}

.color_label {
  margin-right: 8px;
}

.color_text {
  cursor: pointer;
  position: relative;
}

.icon_select_box {
  margin-bottom: 12px;
  display: flex;
}
</style>