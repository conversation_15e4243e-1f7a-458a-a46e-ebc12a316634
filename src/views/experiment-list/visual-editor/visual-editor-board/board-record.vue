<template>
  <div class="board_record">
    <div
      v-for="item in actionsRecord"
      :key="item.xpath"
      :class="{ record_box: !opens[item.xpath] }"
    >
      <div class="record_title">
        <div
          :class="['record_icon', { open_icon: opens[item.xpath] }]"
          @click="handleClick(item.xpath)"
        >
          <i class="el-icon-arrow-up"></i>
        </div>
        <div class="record_tag_name">{{ item.rData.tagName.toUpperCase() }}</div>
        <div class="record_dian">·</div>
        <el-popover
          class="record_xpath_pop"
          placement="right"
          width="200"
          trigger="hover"
          :content="item.xpath"
        >
          <div
            slot="reference"
            class="record_xpath"
            @mouseover="handleShowEl(item)"
            @mouseout="handleHideEl"
          >
            {{ item.xpath }}
          </div>
        </el-popover>
      </div>
      <div :class="['record_content', { open: opens[item.xpath] }]">
        <el-timeline>
          <el-timeline-item
            v-for="change in item.changes"
            :key="change.key"
            :timestamp="`${change.user || '未知'} ${new Date(change.time).Format(
              'YYYY-MM-DD hh:mm'
            )}`"
          >
            <div v-if="change.type !== 'els'">
              <span class="record_key">{{ change.key }}</span>
              <span>: {{ change.value }}</span>
            </div>
            <div v-else>
              <span class="record_key">新增元素</span>
              <span>: {{ change.value }}</span>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    <div v-if="!actionsRecord.length" class="record_empty">暂无记录</div>
  </div>
</template>

<script>
export default {
  props: {
    actionsRecord: {
      type: Array,
      default: () => []
    }
  },
  inject: ['showChanged', 'hideChanged'],
  data() {
    return {
      opens: {}
    };
  },
  methods: {
    handleClick(key) {
      this.$set(this.opens, key, !this.opens[key]);
    },
    handleShowEl(item) {
      this.showChanged(item);
    },
    handleHideEl() {
      this.hideChanged();
    }
  }
};
</script>

<style lang="less" scoped>
.board_record {
  padding: 8px;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
}

.record_box {
  margin-bottom: 12px;
}

.record_title {
  display: flex;
  align-items: center;
}

.record_icon {
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  cursor: pointer;
  min-width: 20px;
  transition: transform 0.3s ease;
}

.open_icon {
  transform: rotate(180deg);
}

.record_tag_name {
  font-weight: bold;
}

.record_dian {
  color: @zyb-green-1;
  margin: 0 3px;
}

.record_xpath_pop {
  flex: 1;
  overflow: hidden;
}

.record_xpath {
  color: @zyb-green-1;

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.record_content {
  height: 0;
  overflow: hidden;
  transition: height 3s ease;
}

.record_key {
  color: #606266;
  font-weight: bold;
}

/deep/.el-timeline-item__content {
  font-size: 12px;
  word-break: break-all;
  line-height: 1.5;
}

/deep/.el-timeline-item__timestamp {
  font-size: 12px;
  word-break: break-all;
  line-height: 1.5;
}

.open {
  height: auto;
  padding: 12px 0 0 20px;
}

.record_empty {
  text-align: center;
  padding: 24px;
  color: #cacaca;
}
</style>