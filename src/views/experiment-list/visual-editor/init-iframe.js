import { Loading } from 'element-ui';
import { getEl } from './constant.js';
import { cloneDeep, merge } from 'lodash';

export default class InitIframe {
  constructor(opt) {
    this.iframe = opt.iframe;
    this.maxHeight = 0;
    this.currentEl = null;
    this.clickElement = opt.clickElement;
    this.hideElement = opt.hideElement;
    this.clickTarElement = opt.clickTarElement;
    this.iframeOnload = opt.iframeOnload;
    this.el_tips = null;
    this.el_path = null;
    this.el_close = null;
    this.el_mask = null;
    this.isHover = false;
    this.mode = 2;
    this.loadings = [];
    this.loading = Loading.service({
      fullscreen: false,
      target: document.getElementsByClassName('visual_editor')[0],
      text: '加载中...'
    });
    this.initing = true;
    this.init();
    this.selectTargetEl = false;
  }

  init() {
    this.iframe.onload = () => {
      const doc = this.iframe.contentWindow.document;

      this.maxHeight = doc.body.scrollHeight;

      // 清除事件
      doc.body.innerHTML = doc.body.innerHTML;

      // 生成所需元素
      this.createElements(doc);

      // 准备工作
      this.ready(doc);

      // 初始化嵌套的iframe
      this.initChildIframes(doc);
    };
  }

  createElements(doc) {
    const el_tips = document.createElement('div');
    el_tips.className = 'el_tips';
    el_tips.style.position = 'absolute';
    el_tips.style.background = '#42c57a';
    el_tips.style.display = 'none';
    el_tips.style.color = 'white';
    el_tips.style.zIndex = 999;
    el_tips.style.borderRadius = '4px';
    el_tips.style.overflow = 'hidden';
    el_tips.style.fontSize = '14px';

    const el_path = document.createElement('span');
    el_path.className = 'el_path';
    el_path.style.padding = '2px 4px';
    el_path.style.display = 'inline-block';

    const el_close = document.createElement('span');
    el_close.className = 'el_close';
    el_close.style.background = '#0da24d';
    el_close.style.padding = '2px 6px';
    el_close.style.cursor = 'pointer';
    el_close.style.display = 'none';
    el_close.innerText = 'x';

    const el_mask = document.createElement('div');
    el_mask.className = 'el_mask';
    el_mask.style.position = 'absolute';
    el_mask.style.background = '#42c57a1a';
    el_mask.style.display = 'none';
    el_mask.style.zIndex = 999;
    el_mask.style.border = '2px solid #42c57a';
    el_mask.style.borderRadius = '4px';
    el_mask.style.pointerEvents = 'none';

    this.el_tips = el_tips;
    this.el_path = el_path;
    this.el_close = el_close;
    this.el_mask = el_mask;

    el_tips.appendChild(el_path);
    el_tips.appendChild(el_close);
    doc.body.appendChild(el_tips);
    doc.body.appendChild(el_mask);

    // 选择元素的hover
    const tar_mask = document.createElement('div');
    tar_mask.className = 'tar_mask';
    tar_mask.style.position = 'absolute';
    tar_mask.style.background = '#0077fa1a';
    tar_mask.style.display = 'none';
    tar_mask.style.zIndex = 999;
    tar_mask.style.border = '2px solid #0077fa';
    tar_mask.style.borderRadius = '4px';
    tar_mask.style.pointerEvents = 'none';

    this.tar_mask = tar_mask;

    doc.body.appendChild(tar_mask);

    // 修改元素的hover
    const changed_mask = document.createElement('div');
    changed_mask.className = 'changed_mask';
    changed_mask.style.position = 'absolute';
    changed_mask.style.background = '#ffb56f1a';
    changed_mask.style.display = 'none';
    changed_mask.style.zIndex = 999;
    changed_mask.style.border = '2px solid #ffb56f';
    changed_mask.style.borderRadius = '4px';
    changed_mask.style.pointerEvents = 'none';

    this.changed_mask = changed_mask;

    doc.body.appendChild(changed_mask);
  }

  ready(doc) {
    // 鼠标经过元素周边显示红框
    doc.body.onmouseover = ev => {
      if (this.mode === 1) return;
      if (!this.currentEl) {
        this.showElements(ev);
      } else if (this.selectTargetEl) {
        this.showTarElements(ev);
      }
    };

    // 鼠标单击一个元素
    doc.body.onclick = ev => {
      ev.stopPropagation();
      ev.preventDefault();
      if (this.mode === 1) return;
      if (this.selectTargetEl) {
        this.handleClickTarTag(ev);
        return;
      }
      this.handleClickTag(ev);
    };
  }

  showElements(ev, el) {
    const node = el || ev.target || ev.srcElement;
    if (node.className === 'custom_add') return;
    this.isHover = true;
    const top = this.getOffset(node, 'top');
    const left = this.getOffset(node, 'left');
    const width = node.offsetWidth;
    const height = node.offsetHeight;
    const tag = node.tagName.toLowerCase();
    let parentTag = node.parentNode.tagName.toLowerCase();
    if (parentTag === 'ab-placeholder') {
      parentTag = node.parentNode.parentNode.tagName.toLowerCase();
    }

    const tipsTop =
      top - 24 > 0 ? top - 24 : top + height + 24 < this.maxHeight ? top + height + 6 : top + 6;
    this.el_tips.style.top = tipsTop + 'px';
    this.el_tips.style.left = left - 2 + 'px';
    this.el_tips.style.display = 'block';

    this.el_path.innerText = `${parentTag} > ${tag}`;

    this.el_mask.style.top = top - 2 + 'px';
    this.el_mask.style.left = left - 2 + 'px';
    this.el_mask.style.width = width + 'px';
    this.el_mask.style.height = height + 'px';
    this.el_mask.style.display = 'block';

    node.onmouseout = () => {
      !this.currentEl && this.hideElements();
    };
  }

  hideElements() {
    this.el_tips.style.display = 'none';
    this.el_close.style.display = 'none';
    this.el_mask.style.display = 'none';
    this.isHover = false;
    this.hideTarElements();
  }

  showTarElements(ev, el) {
    const node = el || ev.target || ev.srcElement;
    if (node.className === 'custom_add') return;
    const top = this.getOffset(node, 'top');
    const left = this.getOffset(node, 'left');
    const width = node.offsetWidth;
    const height = node.offsetHeight;

    this.tar_mask.style.top = top - 2 + 'px';
    this.tar_mask.style.left = left - 2 + 'px';
    this.tar_mask.style.width = width + 'px';
    this.tar_mask.style.height = height + 'px';
    this.tar_mask.style.display = 'block';

    node.onmouseout = () => {
      this.hideTarElements();
    };
  }

  hideTarElements() {
    this.tar_mask.style.display = 'none';
  }

  showChangedElements(item, actionsRecord) {
    const wheres = {
      beforebegin: 'previousSibling',
      afterbegin: 'firstChild',
      beforeend: 'lastChild',
      afterend: 'nextSibling'
    };
    const findXpath = action => {
      if (action.cData.layout.targetXpath) {
        const nextElChildren = this.getElementByXpath(action.cData.layout.targetXpath)[
          wheres[action.cData.layout.where]
        ].childNodes;
        let nextEl;
        nextElChildren.forEach(v => {
          if (!nextEl && v.className !== 'custom_add') {
            nextEl = v;
          }
        });
        const xpath = this.getXPath(nextEl);
        const obj = actionsRecord.find(f => f.xpath === xpath);
        if (obj) {
          return findXpath(obj);
        } else {
          return xpath;
        }
      } else {
        return action.xpath;
      }
    };

    const xpath = findXpath(item);

    const node = this.getElementByXpath(xpath);
    // console.log('\x1b[36m\x1b[0m [LOG]: node', node);
    const top = this.getOffset(node, 'top');
    const left = this.getOffset(node, 'left');
    const width = node.offsetWidth;
    const height = node.offsetHeight;

    this.changed_mask.style.top = top - 2 + 'px';
    this.changed_mask.style.left = left - 2 + 'px';
    this.changed_mask.style.width = width + 'px';
    this.changed_mask.style.height = height + 'px';
    this.changed_mask.style.display = 'block';
  }

  hideChangedElements() {
    this.changed_mask.style.display = 'none';
  }

  getOffset(node, type) {
    const attr = type === 'top' ? 'offsetTop' : 'offsetLeft';
    let num = node[attr];
    let parent = node.offsetParent;

    while (parent) {
      num += parent[attr];
      const tagName = parent.tagName.toLowerCase();
      if (tagName === 'body' && parent.getAttribute('data-iframe-id')) {
        const id = parent.getAttribute('data-iframe-id');
        function findIframe(el) {
          if (el.contentWindow.document.getElementById(id)) {
            return el.contentWindow.document.getElementById(id);
          } else {
            const iframes = el.contentWindow.document.getElementsByTagName('iframe');
            for (let i = 0; i < iframes.length; i++) {
              const result = findIframe(iframes[i]);
              if (result) return result;
            }
          }
        }
        parent = findIframe(this.iframe);
      } else {
        parent = parent.offsetParent;
      }
    }

    return num;
  }

  rgb2hex(color) {
    let arr = [];
    let strHex;
    if (/^(rgb|RGB)/.test(color)) {
      arr = color.replace(/(?:\(|\)|rgb|RGB)*/g, '').split(',');
      strHex =
        '#' +
        ((1 << 24) + (arr[0] << 16) + (arr[1] << 8) + parseInt(arr[2])).toString(16).substr(1);
    } else {
      strHex = color;
    }
    return strHex;
  }

  handleClickTag(ev, ele) {
    if (!this.isHover) return;
    let el = ele || ev.target || ev.srcElement;
    if (el.className === 'el_path') return;
    let xpath = this.getXPath(el);
    if (el.className === 'el_close' || (this.currentEl && this.currentEl !== xpath)) {
      this.currentEl = null;
      this.hideElement();
      this.hideElements();
    } else if (!this.currentEl) {
      this.currentEl = xpath;
      this.el_close.style.display = 'inline-block';
      // window.el = el;
      const obj = this.getTagInfo(el, xpath);
      this.clickElement(obj);
    }
  }

  getTagInfo(el, xpath) {
    let style = null;
    if (window.getComputedStyle) {
      style = window.getComputedStyle(el, null);
    } else {
      style = el.currentStyle;
    }
    const obj = {
      xpath,
      tagName: el.tagName.toLowerCase(),
      styles: {
        // 字体排版
        fontSize: parseInt(style.fontSize),
        fontWeight: style.fontWeight,
        color: this.rgb2hex(style.color),
        textDecorationLine: style.textDecorationLine,
        fontStyle: style.fontStyle,
        textAlign: style.textAlign,
        verticalAlign: style.verticalAlign,
        // 边框
        borderStyle: style.borderStyle,
        borderWidth: parseInt(style.borderWidth),
        borderRadius: parseInt(style.borderRadius),
        borderColor: this.rgb2hex(style.borderColor),
        // 图片
        width: parseInt(style.width) || undefined,
        height: parseInt(style.height) || undefined
      },
      attrs: {
        // 字体排版
        innerText: el.innerText,
        href: el.href,
        target: el.target,
        // 图片
        alt: el.alt,
        src: el.src
      },
      layout: {
        opacity: parseInt(style.opacity),
        bottom: parseInt(style.bottom) || undefined,
        left: parseInt(style.left) || undefined,
        right: parseInt(style.right) || undefined,
        top: parseInt(style.top) || undefined
      }
    };
    return obj;
  }

  handleClickTarTag(ev) {
    this.selectTargetEl = false;
    let el = ev.target || ev.srcElement;
    let xpath = this.getXPath(el);
    this.clickTarElement(xpath);
    this.hideTarElements();
  }

  initChildIframes(fatherDoc) {
    let iframes = fatherDoc.getElementsByTagName('iframe');

    if (iframes.length > 0) {
      for (let i = 0; i < iframes.length; i++) {
        const iframe = iframes[i];
        const id = 'iframe' + parseInt(Math.random() * 1000);
        iframe.setAttribute('id', id);
        this.loadings.push(id);
        iframe.onload = () => {
          this.loadings = this.loadings.filter(f => f !== id);
          let doc = iframe.contentWindow.document;

          let xpath = this.getXPath(iframe);
          doc.body.setAttribute('data-xpath', xpath);
          doc.body.setAttribute('data-iframe-id', id);

          // 清除事件
          doc.body.innerHTML = doc.body.innerHTML;

          this.ready(doc);

          this.initChildIframes(doc); // 执行递归
        };
      }
    } else if (!this.loadings.length) {
      setTimeout(() => {
        this.loading.close();
      }, 500);
      this.initing = false;
      this.iframeOnload();
    }
  }

  getXPath(element) {
    let tag = element.tagName.toLowerCase();
    let xpath;

    if (tag == 'body') {
      // 递归到body处，结束递归
      if (!element.getAttribute('data-xpath')) {
        // 根body
        return tag;
      } else {
        // 若为嵌套的iframe的body，则加上iframe的路径
        return element.getAttribute('data-xpath') + '/' + tag;
      }
    }

    let ix = 1;
    let siblings = element.parentNode.children;

    for (let i = 0, l = siblings.length; i < l; i++) {
      let sibling = siblings[i];

      // 如果这个元素是siblings数组中的元素，则执行递归操作
      // if (sibling == element && element.getAttribute('id')) {
      //   // 若元素还有id，结束递归
      //   xpath = '/' + tag + "[@id='" + element.getAttribute('id') + "']" + '[' + ix + ']';
      //   return this.getXPath(element.parentNode) + xpath;
      // } else
      if (sibling == element) {
        xpath = '/' + tag + '[' + ix + ']';
        return this.getXPath(element.parentNode) + xpath;

        // 如果不符合，判断是否是element元素，并且是否是相同元素，如果是相同的就开始累加
      } else if (sibling.nodeType == 1 && sibling.tagName == element.tagName) {
        ix++;
      }
    }
  }

  swithMode(mode) {
    this.currentEl = null;
    this.mode = mode;
    if (this.initing) return;
    this.hideElement();
    this.hideElements();
  }

  getElementByXpath(xpath) {
    const arr = xpath.split('/');
    let el = null;
    arr.forEach(v => {
      if (v === 'body') {
        el = this.iframe.contentWindow.document.body;
      } else {
        const tagName = v.split('[')[0];
        const indexStr = v.split('[')[1];
        const index = indexStr.split(']')[0] - 1;
        const tags = [];
        el.childNodes.forEach(f => {
          if (f.tagName && f.tagName.toLowerCase() === tagName) {
            tags.push(f);
          }
        });
        el = tags[index];
      }
    });
    return el;
  }

  parseDom(str) {
    if (!str) return '';
    const div = document.createElement('div');
    div.innerHTML = str;
    const child = div.childNodes[0];
    if (child.nodeType === 3) return '';
    child.className = 'custom_add';
    return div.innerHTML;
  }

  updatePage(change, record) {
    const { xpath, type, key, value } = change;
    let el = this.getElementByXpath(xpath);

    if (type === 'styles') {
      if (['fontSize', 'borderWidth', 'borderRadius', 'width', 'height'].includes(key)) {
        el.style[key] = value + 'px';
      } else {
        el.style[key] = value;
      }
    } else if (type === 'attrs') {
      el[key] = value;
    } else if (type === 'layout') {
      if (key === 'opacity') {
        el.style[key] = value;
      } else if (['top', 'right', 'bottom', 'left'].includes(key)) {
        el.style.position = 'relative';
        el.style[key] = value ? value + 'px' : 0;
      } else if (key === 'targetXpath') {
        if (value) {
          // 移动操作 复制一份 放到相应位置
          const wheres = {
            beforebegin: 'before',
            afterbegin: 'prepend',
            beforeend: 'append',
            afterend: 'after'
          };
          const tar = this.getElementByXpath(value);
          const el_clone = el.cloneNode(true);
          const placeholder = document.createElement('ab-placeholder');
          placeholder.append(el_clone);
          const where = record.cData.layout.where;
          tar[wheres[where]](placeholder);
          // 隐藏原来的
          el.style.display = 'none';
          if (this.currentEl === xpath) {
            this.currentEl = null;
            this.showElements('', el_clone);
            this.handleClickTag('', el_clone);
          }
        } else {
          // 撤销操作 删除新的
          const wheres = {
            beforebegin: 'previousSibling',
            afterbegin: 'firstChild',
            beforeend: 'lastChild',
            afterend: 'nextSibling'
          };
          const tar = this.getElementByXpath(change.preValue);
          const where = record.cData.layout.where;
          const preBox = tar[wheres[where]];
          const pre = preBox.childNodes[0];
          const preXpath = this.getXPath(pre);
          preBox.remove();

          // 显示原来的
          el.style.display = '';
          if (this.currentEl === preXpath) {
            this.currentEl = null;
            this.showElements('', el);
            this.handleClickTag('', el);
          }
        }
      }
    } else if (type === 'els') {
      console.log('\x1b[36m\x1b[0m [LOG]: els', change);
      const wheres = {
        beforebegin: 'previousSibling',
        afterbegin: 'firstChild',
        beforeend: 'lastChild',
        afterend: 'nextSibling'
      };
      if (el[wheres[key]] && el[wheres[key]].className === 'custom_add') {
        el[wheres[key]].outerHTML = this.parseDom(value.trim());
      } else {
        el.insertAdjacentHTML(key, this.parseDom(value.trim()));
      }
    }

    if (this.currentEl) {
      const ele = this.currentEl === xpath ? el : this.getElementByXpath(this.currentEl);
      this.showElements('', ele);
    }
  }
}
