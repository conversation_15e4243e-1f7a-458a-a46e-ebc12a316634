<template>
  <div class="visual_editor">
    <visual-editor-nav
      :url="url"
      :current-version="currentVersion"
      :iframe-size.sync="iframeSize"
      @refresh="handleRefresh"
    />

    <div class="visual_editor_wapper">
      <visual-editor-board :curEl="curEl" :actions-record="actionsRecord" />

      <div class="visual_editor_content">
        <visual-editor-operation
          :version-list="versionList"
          :version.sync="version"
          :mode.sync="mode"
          :current-version="currentVersion"
          :backable="backable"
          :forwardable="forwardable"
          @back-off="handleBackOff"
          @forward="handleForward"
          @exp-save="handleSave"
        />

        <visual-editor-iframe
          ref="iframe"
          :url="url"
          :iframe-size="iframeSize"
          :mode="mode"
          @iframe-onload="iframeOnload"
          @click-element="clickElement"
          @hide-element="hideElement"
          @click-tar-element="clickTarElement"
        />
      </div>
    </div>
  </div>
</template>

<script>
import VisualEditorNav from './visual-editor-nav';
import VisualEditorBoard from './visual-editor-board/index.vue';
import VisualEditorOperation from './visual-editor-operation';
import VisualEditorIframe from './visual-editor-iframe';
import { cloneDeep, transform, isEqual, isObject, findLastIndex, merge } from 'lodash';
import { getEl } from './constant.js';
import { mapGetters } from 'vuex';

export default {
  components: {
    VisualEditorNav,
    VisualEditorBoard,
    VisualEditorOperation,
    VisualEditorIframe
  },
  provide() {
    return {
      selectElement: this.selectElement,
      htmlSubmit: this.htmlSubmit,
      showChanged: this.handleShowChanged,
      hideChanged: this.handleHideChanged,
      backDefaultStyles: this.backDefaultStyles
    };
  },
  data() {
    return {
      id: '',
      version: '',
      type: '',
      url: '',
      versionList: [],
      mode: 2,
      iframeSize: '100%',
      xpath: '',
      rawEl: {}, // 原始数据
      curEl: getEl(), // 副本数据
      isBack: false,
      changesRecord_: [],
      changesRecord: [],
      actionsRecord_: [],
      actionsRecord: [],
      versionChanges: []
    };
  },
  computed: {
    ...mapGetters(['userInfo']),
    currentVersion() {
      return this.versionList.find((f) => f.id == this.version) || {};
    },
    backable() {
      return !!this.changesRecord.length;
    },
    forwardable() {
      return !!this.changesRecord_ && this.changesRecord_.length > this.changesRecord.length;
    }
  },
  watch: {
    version(newVal, oldVal) {
      if (oldVal) {
        const index = this.versionChanges.findIndex((f) => f.id == oldVal);
        this.versionChanges[index].changes = cloneDeep(this.changesRecord);
        this.versionChanges[index].changeNum = this.actionsRecord.length;
      }
      const obj = this.versionChanges.find((f) => f.id == newVal);
      this.changesRecord = cloneDeep(obj.changes);
      this.handleRefresh();
    },
    curEl: {
      handler(newVal) {
        if (!this.xpath) return;
        // 撤销引起的修改不做处理
        if (this.isBack) {
          this.isBack = false;
          return;
        }
        // 进行操作即重置撤销副本
        if (this.changesRecord_) {
          this.changesRecord_ = [];
          this.actionsRecord_ = [];
        }
        // 操作后比对前后数据
        const index = this.actionsRecord.findIndex((f) => f.xpath === this.xpath);
        const diffEl = index > -1 ? this.actionsRecord[index].cData : this.rawEl;
        const diff = this.difference(newVal, diffEl);
        const value = Object.values(diff)[0];
        // 存在差异，记录改动
        if (value) {
          this.setChangeRecord(diff);
        }
      },
      deep: true
    }
  },
  methods: {
    setChangeRecord(diff) {
      const type = Object.keys(diff)[0];
      const value = Object.values(diff)[0];
      const key = Object.keys(value)[0];
      const change = {
        xpath: this.xpath,
        type,
        key,
        value: value[key],
        time: new Date().getTime(),
        user: this.userInfo.uname
      };
      const sublingChange = this.changesRecord[this.changesRecord.length - 1];
      if (
        sublingChange &&
        sublingChange.xpath === this.xpath &&
        sublingChange.type === type &&
        sublingChange.key === key
      ) {
        this.changesRecord[this.changesRecord.length - 1] = change;
      } else {
        this.changesRecord.push(change);
      }
      // 计算操作记录
      this.setActionRecord(change);
    },
    setActionRecord(change) {
      // console.log('\x1b[36m\x1b[0m [LOG]: change', change);
      const index = this.actionsRecord.findIndex((f) => f.xpath === change.xpath);
      if (index > -1) {
        const changeKeyIndex = this.actionsRecord[index].changes.findIndex(
          (f) => f.type === change.type && f.key === change.key
        );
        if (changeKeyIndex > -1) {
          this.actionsRecord[index].changes.splice(changeKeyIndex, 1);
        }
        this.actionsRecord[index].changes.push(change);
        this.actionsRecord[index].cData = this.computeData(
          cloneDeep(this.actionsRecord[index].cData),
          [change]
        );
      } else {
        let rData;
        if (this.rawEl.xpath === change.xpath) {
          rData = this.rawEl;
        }
        if (this.actionsRecord_.length) {
          const obj = this.actionsRecord_.find((f) => f.xpath === change.xpath);
          obj && (rData = obj.rData);
        }
        if (!rData) {
          const el = this.$refs.iframe.myIframe.getElementByXpath(change.xpath);
          const data = this.$refs.iframe.myIframe.getTagInfo(el, change.xpath);
          rData = merge(getEl(), data);
        }
        this.actionsRecord.push({
          xpath: change.xpath,
          rData: cloneDeep(rData),
          changes: [change],
          cData: this.computeData(cloneDeep(rData), [change])
        });
      }
      const record = this.actionsRecord.find((f) => f.xpath === change.xpath);
      this.$refs.iframe.myIframe.updatePage(change, record);
    },
    backActionRecord(change) {
      // console.log('\x1b[36m\x1b[0m [LOG]: back', change);
      const index = this.actionsRecord.findIndex((f) => f.xpath === change.xpath);
      if (this.actionsRecord[index].changes.length > 1) {
        const newChanges = [];
        this.changesRecord.forEach((v) => {
          if (change.xpath === v.xpath) {
            const index = newChanges.findIndex((f) => f.type === v.type && f.key === v.key);
            if (index > -1) {
              newChanges.splice(index, 1);
            }
            newChanges.push(v);
          }
        });
        this.actionsRecord[index].changes = newChanges;
        this.actionsRecord[index].cData = this.computeData(
          cloneDeep(this.actionsRecord[index].rData),
          newChanges
        );
      } else {
        this.actionsRecord.splice(index, 1);
      }

      let record = this.actionsRecord.find((f) => f.xpath === change.xpath);
      let data;
      if (record) {
        data = record.cData;
      } else {
        record = this.actionsRecord_.find((f) => f.xpath === change.xpath);
        data = record.rData;
      }
      const backChange = {
        ...change,
        value: data[change.type][change.key],
        preValue: change.value
      };
      // console.log('\x1b[36m\x1b[0m [LOG]: backChange', backChange);
      this.$refs.iframe.myIframe.updatePage(backChange, record);
    },
    computeData(data, changes) {
      changes.forEach((change) => {
        data[change.type][change.key] = change.value;
      });

      return data;
    },
    handleRefresh() {
      this.mode = this.currentVersion.displayName === '对照版本' ? 1 : 2;
      this.actionsRecord = [];
      this.changesRecord_ = [];
      this.actionsRecord_ = [];
      this.$refs.iframe.refresh();
      this.hideElement();
    },
    // 点击页面元素
    clickElement(data) {
      this.xpath = data.xpath;
      const record = this.actionsRecord.find((f) => f.xpath === data.xpath);
      if (record) {
        this.rawEl = cloneDeep(record.rData);
        this.curEl = cloneDeep(record.cData);
      } else {
        this.rawEl = merge(getEl(), data);
        this.curEl = cloneDeep(this.rawEl);
      }
    },
    hideElement() {
      if (this.xpath) {
        this.xpath = '';
        this.rawEl = {};
        this.curEl = getEl();
      }
    },
    selectElement(selectTar) {
      this.$refs.iframe.myIframe.selectTargetEl = selectTar;
    },
    clickTarElement(xpath) {
      const change = {
        xpath: this.xpath,
        type: 'layout',
        key: 'targetXpath',
        value: xpath,
        time: new Date().getTime(),
        user: this.userInfo.uname
      };
      this.changesRecord.push(change);
      this.setActionRecord(change);
    },
    htmlSubmit(data) {
      const change = {
        xpath: this.xpath,
        type: 'els',
        key: data.where,
        value: data.code,
        time: new Date().getTime(),
        user: this.userInfo.uname
      };
      this.changesRecord.push(change);
      this.setActionRecord(change);
    },
    difference(object, base) {
      function changes(object, base) {
        return transform(object, function (result, value, key) {
          if (!isEqual(value, base[key])) {
            result[key] =
              isObject(value) && isObject(base[key]) ? changes(value, base[key]) : value;
          }
        });
      }
      return changes(object, base);
    },
    handleBackOff(reset) {
      if (!this.changesRecord.length) return;
      if (!this.changesRecord_.length) {
        this.changesRecord_ = cloneDeep(this.changesRecord);
        this.actionsRecord_ = cloneDeep(this.actionsRecord);
      }
      const change = this.changesRecord.pop();
      this.backActionRecord(change);
      this.updateCurEl();
      if (reset) {
        if (this.changesRecord.length) {
          this.handleBackOff(true);
        } else {
          this.changesRecord_ = [];
          this.actionsRecord_ = [];
        }
      } else {
        this.$message.success('撤销成功');
      }
    },
    handleForward() {
      const len = this.changesRecord.length;
      this.changesRecord = this.changesRecord_.slice(0, len + 1);
      this.setActionRecord(this.changesRecord_.slice(len, len + 1)[0]);
      this.updateCurEl();
      this.$message.success('重做成功');
    },
    updateCurEl() {
      if (this.xpath) {
        this.isBack = true;
        const obj = this.actionsRecord.find((f) => f.xpath === this.xpath);
        this.curEl = obj ? cloneDeep(obj.cData) : cloneDeep(this.rawEl);
      }
    },
    handleSave() {
      if (!window.opener) return;
      const index = this.versionChanges.findIndex((f) => f.id == this.version);
      this.versionChanges[index].changes = cloneDeep(this.changesRecord);
      this.versionChanges[index].changeNum = this.actionsRecord.length;
      window.opener.postMessage({ type: 'vcs', data: this.versionChanges }, '/');
      this.$message.success('保存成功，若编辑完成，需返回上一个标签页进入「下一步」');
    },
    iframeOnload() {
      // console.log('\x1b[36m\x1b[0m [LOG]: iframeOnload', this.changesRecord);
      this.changesRecord.forEach((change) => this.setActionRecord(change));
    },
    handleShowChanged(item) {
      // console.log('\x1b[36m\x1b[0m [LOG]: item', item);
      this.$refs.iframe.myIframe.showChangedElements(item, this.actionsRecord);
    },
    handleHideChanged() {
      this.$refs.iframe.myIframe.hideChangedElements();
    },
    backDefaultStyles() {
      this.handleBackOff(true);
    }
  },
  created() {
    if (!window.opener) {
      this.$notify({
        title: '提示',
        message: '此页面不可编辑，请从新增/编辑实验页面跳转才可进行编辑 or 上一个标签页已关闭',
        type: 'warning',
        duration: 0,
        showClose: false
      });
      return;
    }
    const { id, version, type, url } = this.$route.query;
    this.id = id;
    this.type = type;
    const item = localStorage.getItem(`__VERSION__${id}`);
    this.versionList = item ? JSON.parse(item) : [];

    if (type === 'add') {
      this.versionChanges = this.versionList.map((v, index) => ({
        id: v.id,
        changes: [],
        changeNum: 0
      }));
      this.url = '#/demo';
      this.version = version;
    } else {
      this.$service
        .get('CHANGELIST', { experimentId: id }, { needLoading: true })
        .then((res) => {
          this.versionChanges = this.versionList.map((v, index) => {
            const vs = res.find((f) => f.versionId == v.id);
            return {
              id: v.id,
              changes: vs ? JSON.parse(vs.changes) : [],
              changeNum: vs ? vs.changeNum : 0
            };
          });
          this.url = '#/demo';
          this.version = version;
        })
        .catch((err) => {
          console.log(err);
        });
    }
  },
  beforeDestroy() {
    this.$notify.closeAll();
  }
};
</script>

<style lang="less" scoped>
.visual_editor {
  height: calc(100vh - 98px) !important;
  display: flex;
  flex-direction: column;
  background: #fff;
  border: 1px solid #e6e6e6;
  box-sizing: border-box;
  border-radius: 4px;
  margin: 24px;
}

.visual_editor_wapper {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.visual_editor_content {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>