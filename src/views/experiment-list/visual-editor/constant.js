export const getEl = () => {
  return {
    xpath: '',
    tagName: '',
    styles: {
      // 字体排版
      fontSize: undefined,
      fontWeight: '',
      color: '',
      textDecorationLine: '',
      fontStyle: '',
      textAlign: '',
      verticalAlign: '',
      // 边框
      borderStyle: '',
      borderWidth: undefined,
      borderRadius: undefined,
      borderColor: '',
      // 图片
      width: undefined,
      height: undefined
    },
    attrs: {
      // 字体排版
      innerText: '',
      href: '',
      target: '',
      // 图片
      alt: '',
      src: ''
    },
    layout: {
      opacity: 1,
      where: 'beforebegin',
      targetXpath: '',
      bottom: undefined,
      left: undefined,
      right: undefined,
      top: undefined
    },
    els: {
      beforebegin: '',
      afterbegin: '',
      beforeend: '',
      afterend: ''
    }
  };
};
