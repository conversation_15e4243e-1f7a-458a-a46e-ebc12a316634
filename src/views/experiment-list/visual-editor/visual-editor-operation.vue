<template>
  <div class="visual_editor_operation">
    <div>
      <el-radio-group
        v-model="currentMode"
        size="mini"
        @change="handleModeChange"
        :disabled="currentVersion.displayName === '对照版本'"
      >
        <el-radio :label="1">预览模式</el-radio>
        <el-radio :label="2">编辑模式</el-radio>
      </el-radio-group>
      <el-tooltip
        content="预览模式下，您可以查看当前版本。编辑模式下，您可以更新和新增当前页面的各种元素。"
        placement="top"
      >
        <i class="el-icon-warning-outline radio_tips"></i>
      </el-tooltip>
    </div>

    <el-dropdown size="default" @command="handleCommand">
      <span class="el-dropdown-link">
        {{ currentVersion.displayName }}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in versionList"
          :key="item.id"
          :command="item.id"
          :disabled="item.id == version"
        >
          {{ item.displayName }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <div>
      <el-button-group size="mini" class="action_btns">
        <el-tooltip content="撤销" placement="top">
          <el-button
            icon="el-icon-back"
            size="mini"
            @click="handleBackOff"
            :disabled="!backable"
          ></el-button>
        </el-tooltip>
        <el-tooltip content="重做" placement="top">
          <el-button
            icon="el-icon-right"
            size="mini"
            @click="handleForward"
            :disabled="!forwardable"
          ></el-button>
        </el-tooltip>
      </el-button-group>
      <el-button type="primary" size="mini" @click="handleSave" :disabled="isCheck">保存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    versionList: {
      type: Array,
      default: []
    },
    version: Number | String,
    currentVersion: {
      type: Object,
      default: () => {}
    },
    mode: {
      type: Number,
      default: 2
    },
    backable: Boolean,
    forwardable: Boolean
  },
  data() {
    return {
      currentMode: 2
    };
  },
  computed: {
    isCheck() {
      return this.$route.query.type === 'check';
    }
  },
  watch: {
    mode(newVal) {
      this.currentMode = newVal;
    }
  },
  methods: {
    handleModeChange(val) {
      this.$emit('update:mode', val);
    },
    handleCommand(val) {
      this.$emit('update:version', val);
    },
    handleBackOff() {
      this.$emit('back-off');
    },
    handleForward() {
      this.$emit('forward');
    },
    handleSave() {
      this.$emit('exp-save');
    }
  }
};
</script>

<style lang="less" scoped>
.visual_editor_operation {
  height: 45px;
  padding: 16px;
  border-bottom: 1px solid #eee;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.radio_tips {
  font-size: 14px;
  cursor: pointer;
  margin-left: 12px;
  margin-top: 3px;
}

.el-dropdown-link {
  color: @zyb-green-1;
  cursor: pointer;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.action_btns {
  vertical-align: top;
  margin-right: 36px;
}
</style>