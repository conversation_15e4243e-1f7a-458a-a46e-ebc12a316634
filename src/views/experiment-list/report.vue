<template>
  <Page class="container">
    <div class="report-page">
      <section class="report-page-title">
        <a style="color: inherit" class="box-content-title">
          「{{ summaryData.experiment ? summaryData.experiment.displayName : '' }}」
          的实验报告
        </a>
        <span>
          更新时间: {{ handleFormatDate(summaryData.experiment.dataTime) }}
        </span>
      </section>
      <div class="user_wrapper">
        <p class="report-preview-text">报告概览
          <label class="report-preview-label">当前实验所选样本总量为{{ (summaryData.experiment ? summaryData.experiment.flow / 10 : 0) + '%' }}!</label>
        </p>
        <div class="ex_versions_wrap">
          <swiper ref="mySwiper" class="swiper-content" :options="swiperOption">
            <swiper-slide>
              <div class="total_wrapper">
                <p class="total_number">{{ summaryData.totalNum ? (summaryData.totalNum).toLocaleString() : 0 }}</p>
                <p class="total_text">总进组用户数</p>
              </div>
            </swiper-slide>
            <template v-for="(item, index) in summaryData.versionList">
              <swiper-slide :key="index + 2">
                <p class="ex_show_name">{{ item.versionName }} ({{ item.flowPersent + '%' }})</p>
                <div class="ex_show">
                  <el-progress :width="81" type="circle" :color="getComputedNum(item)" :stroke-width="6" :percentage="item.numPersent"></el-progress>
                  <p class="ex_show_number">进组人数：<span :style="{ color: getComputedNumText(item) }">{{ (item.num).toLocaleString() }}</span> </p>
                </div>
              </swiper-slide>
            </template>
            <div v-if="btnPrew" slot="button-prev" class="swiper-button-prev"></div>
            <div v-if="btnNext" slot="button-next" class="swiper-button-next"></div>
          </swiper>
        </div>
      </div>
    </div>
    <!-- <ex-history v-if="expId"></ex-history> -->
    <div class="analysis-btns">
      <div @click="foundationAnalysis" :class="['basicBtn', { selectedAnalysis: isFoundationAnalysis }]">
      </div>
    </div>
    <!-- <foundation-analysis v-if="summaryData.experiment.type === 1 && isFoundationAnalysis" :colorList="colorList" :summaryData="summaryData"></foundation-analysis> -->
    <!-- <advance-analysis v-if="summaryData.experiment.type === 1 && isAdvanceAnalysis"></advance-analysis> -->
    <foundation-analysis v-if="false" :colorList="colorList" :summaryData="summaryData"></foundation-analysis>
    <advance-analysis v-if="false"></advance-analysis>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import { mapGetters } from 'vuex';
import * as _ from 'lodash';
import AdvanceAnalysis from './components/advance/advance-analysis.vue';
import FoundationAnalysis from './components/foundation/foundation-analysis.vue';
import 'vue-slick-carousel/dist/vue-slick-carousel.css';
import 'vue-slick-carousel/dist/vue-slick-carousel-theme.css';
import { formatDate } from '@/common/utils';


export default {
  name: 'ExpReport',
  components: {
    Page,
    AdvanceAnalysis,
    FoundationAnalysis,
  },
  data() {
    return {
      btnPrew: true,
      btnNext: true,
      swiperOption: {
        allowTouchMove: false,
        // slidesPerView: 1,
        slidesPerView: 'auto',
        spaceBetween: 20,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        on: {
          slideChange: () => {
          },
          init: () => {
          }
        }
      },
      breadcrumbList: ['ExperimentList', 'ExpReport'],
      expId: this.$route.query.id,
      isFoundationAnalysis: true,
      isAdvanceAnalysis: false,
      summaryData: {
        experiment: {},
        versionList: []
      },
      colorList: [
        'rgb(96, 96, 107)',
        'rgb(28, 164, 116)',
        'rgb(237, 133, 64)',
        'rgb(51, 112, 255)',
        'rgb(24, 175, 240)',
        'rgb(0, 208, 182)',
        'rgb(15, 191, 96)',
        'rgb(185, 219, 13)',
        'rgb(255, 205, 0)',
        'rgb(245, 133, 5)',
        'rgb(93, 120, 242)',
        'rgb(108, 48, 199)',
        'rgb(0, 137, 213)'
      ]
    };
  },
  computed: {
    ...mapGetters(['expReportData'], 'message')
  },
  created() {
    //this.$store.dispatch('getReportData', this.$route.query.id);
    this.getSummaryData();
  },
  mounted() { },
  methods: {
    getComputedNum(item) {
      console.log('item.......', item.enterException);
      if (item.enterException === 0) {
        return 'rgb(0, 137, 213)';
      } else {
        return 'rgb(231, 76, 60)';
      }
    },
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'yyyy-MM-dd HH:mm:ss');
      }
      return '-';
    },
    getComputedNumText(item) {
      if (item.enterException === 0) {
        return '';
      } else {
        // 红
        return 'rgb(231, 76, 60)';
      }
    },
    // 基础分析
    foundationAnalysis() {
      this.isFoundationAnalysis = true;
      this.isAdvanceAnalysis = false;
    },
    // 高级分析
    advanceAnalysis() {
      this.isAdvanceAnalysis = true;
      this.isFoundationAnalysis = false;
    },
    // 获取实验数据汇总数据
    getSummaryData() {
      const params = {
        experimentId: this.$route.query.id
      };
      this.$service
        .get('REPORT_FOUND_SUMMARY', { ...params }, { allback: 0, needLoading: true })
        .then(res => {
          this.summaryData = res;
          console.log('summaryData', res);
          this.$store.dispatch('getHistoryData', { id: this.expId });
          this.isFoundationAnalysis &&= this.summaryData.experiment.type === 1;
          this.isAdvanceAnalysis &&= this.summaryData.experiment.type === 1;
        })
        .catch(err => {
          console.log(err);
        });
    },
  },
  beforeDestroy() { }
};
</script>

<style lang="less" scoped>
.container {
  margin: 0 !important;

  /deep/ .page-cont {
    margin: 0;

    .page-container {
      padding: 0;
    }
  }
}

.swiper-button-prev {
  width: 50px;
  height: 50px;
  left: -13px;
  background-image: url(./assets/left.png);
  background-size: 100% 100%;
}

.swiper-button-prev:after {
  content: '';
}

.swiper-button-next {
  width: 50px;
  height: 50px;
  right: -13px;
  background-image: url(./assets/right.png);
  background-size: 100% 100%;
}

.swiper-button-next:after {
  content: '';
}

.swiper-button-disabled {
  display: none;
}

.report-preview-text {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 700;
  padding-left: 20px;
}

.report-preview-label {
  font-size: 13px;
  padding-left: 20px;
  color: red;
}

/deep/.el-collapse-item__content {
  padding-bottom: 0;
  ;
}

/deep/.slick-slide {
  width: 140px;
}

.ex_versions_wrap {
  // overflow-y: hidden;
  // height: 145px;
  margin: 0 10px;
}

.swiper-content {
  .swiper-slide {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    width: 150px;
    height: auto;
    // height: 152px;
  }
}

.total_wrapper {
  height: 127px;
  // width: 130px !important;
  text-align: center;
  position: relative;

  .total_number {
    position: absolute;
    left: 50%;
    top: 55%;
    transform: translate(-50%, -50%);
    text-align: center;
    font-weight: bold;
    font-size: 18px;
  }

  .total_text {
    font-size: 16px;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    bottom: 0px;
    white-space: nowrap;
  }
}

.ex_show_name {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  // position: absolute;
  width: 160px;
  // left:50%;
  line-height: 20px;
  // transform:translate3d(-50%,0,0);
  // white-space: nowrap;
  text-align: center;
  font-size: 13px;
  font-weight: bold;

  // display: -webkit-box;
  // -webkit-box-orient: vertical;
  // -webkit-line-clamp: 2;
  // overflow: hidden;
  // margin-bottom: 5px;
}

.ex_show {
  text-align: center;

  // padding-top: 40px;
  .ex_show_number {
    text-align: center;
    font-size: 12px;
    margin-top: 7px;
  }
}

.user_wrapper {
  border: 1px solid #f0f1f5;
  padding-top: 20px;
  padding-bottom: 20px;
  // display: flex;
  // justify-content: space-between;
}

.test2 {
  margin-right: 20px !important;
  width: 20px;
  height: 20px;
  background: red;
}

.report-page {
  border: #e7e9f5;
  // min-height: 100%;
}

.report-page-box {
  box-sizing: border-box;
  width: 100%;
  //height: 180px;
  //border: 1px solid#e7e9f5;
  padding: 24px;
}

.report-v {
  padding-left: 28px;
}

/deep/ .report-page-box .el-card__body {
  display: flex;
}

.report-person {
  font-size: 13px;
  padding: 12px 24px 12px 0;
  margin-left: 60px;

  p {
    padding-top: 15px;
  }
}

/deep/ .el-card h4 {
  font-size: 16px;
  font-weight: 600;
}

.report-person-num {
  font-size: 24px;
  margin: 2px 0 0;
  font-weight: 700;
  color: #2f2f3f;
}

.report-notice {
  background: #fffbe6;
  padding: 8px 15px;
  margin-top: 5px;
  border-radius: 4px;
}

.report-notice-icon {
  margin-right: 10px;
}

.report-version {
  // border: 1px solid gainsboro;
  height: 159px;
  padding: 10px;
  border-top: none;
  overflow: hidden;
}

.report-version-item {
  height: 100%;
  width: 138px;
  float: left;
}

.report-version-item-num {
  height: 100%;
  min-width: 140px;
  max-width: 160px;
  float: left;
  margin-right: 2%;
  margin-top: 20px;
}

.count {
  min-width: 140px;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.report-version-item-num-p {
  font-size: 20px;
  font-weight: 700;
  padding-top: 20px;
}

.report-desc {
  width: 200px;
  line-height: 20px;
  margin-top: 15px;
}

.report-version-redio {
  height: 100%;
  width: 138px;
  float: left;
  margin: 0 20px;
}

.report-version-item-notice {
  margin-right: 2%;
  margin-top: 20px;
  text-align: left;
  height: 100px;
}

.report-page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  margin-top: 1px;
  span {
    font-size: 14px;
  }
}

.box-content-title {
  font-size: 18px;
  display: block;
  font-weight: 500;
  border-bottom: none;
  padding: 0;
}

/deep/ .el-card.is-always-shadow,
/deep/ .el-card.is-hover-shadow:focus,
/deep/ .el-card.is-hover-shadow:hover {
  box-shadow: none;
}

/deep/ .el-card {
  border-bottom: none;
}

/deep/ .el-card:last-child {
  border-bottom: 1px solid #f0f1f5;
}

.analysis-btns {
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
}

.basicBtn {
  font-size: 16px;
  color: #4f4f5b;
  margin-right: 25px;
  cursor: pointer;
}

.selectedAnalysis {
  font-size: 18px;
  font-weight: 600;
  color: #42c57a;
}
</style>
