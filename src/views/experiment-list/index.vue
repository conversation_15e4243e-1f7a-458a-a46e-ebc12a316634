<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-06-17 17:12:19
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-11-26 14:58:28
 * @Description: 实验列表
-->

<template>
  <Page class="container">
    <section class="header">
      <section class="page-title">实验列表</section>
      <el-button class="btn_add" type="primary" @click="createExp">新建实验</el-button>
    </section>
    <!-- AB系统快速了解模块 -->
    <ab-quick-guide v-if="showQuickGuide" @close="handleCloseQuickGuide" />
    <el-tabs v-model="formData.status" @tab-click="handleSearch">
      <el-tab-pane
        v-for="item in expStatusList"
        :key="item.value"
        :label="item.label"
        :name="item.value + ''"
      ></el-tab-pane>
    </el-tabs>
    <div class="select-container">
      <div>
        <el-form ref="searchForm" inline>
          <el-form-item label="">
            <el-input
              placeholder="实验名称/id/参数/创建人"
              v-model="formData.keyword"
              clearable
              @change="handleSearch"
              :maxlength="200"
            ></el-input>
          </el-form-item>
          <el-form-item label="父子实验">
            <el-select
              style="width: 150px"
              v-model="formData.relationShipType"
              clearable
              placeholder="请选择"
              @change="handleSearch"
            >
              <el-option label="全部" :value="0"></el-option>
              <el-option label="父实验" :value="1"></el-option>
              <el-option label="子实验" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="实验标签：">
            <el-select
              style="width: 150px"
              filterable
              v-model="formData.labels"
              clearable
              multiple
              placeholder="请选择"
              @change="handleSearch"
            >
              <el-option
                v-for="item in labelList"
                :key="item.id"
                :label="item.displayName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="业务线：">
            <el-select
              v-model="formData.appKey"
              style="width: 150px"
              placeholder="请选择appKey"
              @change="handleSearch"
            >
              <el-option
                v-for="item in appList1"
                :key="item.appKey"
                :label="item.displayName"
                :value="item.appKey"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="账号体系：">
            <el-select
              style="width: 150px"
              v-model="formData.accountSystem"
              clearable
              placeholder="请选择"
              @change="handleSearch"
            >
              <el-option
                v-for="item in accSystemData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="实验类型：">
            <el-select
              style="width: 150px"
              v-model="formData.clientType"
              clearable
              placeholder="请选择"
              @change="handleSearch"
            >
              <el-option
                v-for="item in clientTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="实验模式：">
            <el-select
              style="width: 150px"
              v-model="formData.type"
              clearable
              placeholder="请选择"
              @change="handleSearch"
            >
              <el-option
                v-for="item in expTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间：">
            <el-date-picker
              v-model="formData.createTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleSearch"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="">
            <el-radio-group v-model="formData.onlyMine" @change="handleSearch">
              <el-radio-button :label="0">全部</el-radio-button>
              <el-radio-button :label="1">我的</el-radio-button>
              <el-radio-button :label="2">收藏</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <ex-table
      :tableList="tableList"
      @handleSearch="handleSearch"
      :appKey="formData.appKey"
    ></ex-table>

    <div slot="pagination">
      <el-pagination
        v-show="total"
        layout="total,sizes, prev, pager, next, jumper"
        :page-size="pagination.rn"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="pageSizeChange"
        :current-page="pagination.pn"
        :total="total"
        @current-change="pageNoChange"
      ></el-pagination>
    </div>

    <select-ex-dialog v-if="visible" :dialog-visible.sync="visible" />
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import ExTable from './components/ex-table.vue';
//import { statusList } from './status.js';
import { formatDate } from '@/common/utils';
import * as _ from 'lodash';
import { mapGetters } from 'vuex';
import SelectExDialog from './components/select-ex-dialog.vue';
import AbQuickGuide from './components/ab-quick-guide.vue';
export default {
  name: 'ExperimentList',
  components: {
    Page,
    ExTable,
    SelectExDialog,
    AbQuickGuide
  },
  // mixins: [PageCommon],
  data() {
    return {
      breadcrumbList: ['ExperimentList'],
      //statusList,
      formData: {
        appKey: 'all',
        keyword: '',
        status: '',
        accountSystem: '', // 账号体系
        type: '', // 实验模式
        clientType: 0,
        onlyMine: 0,
        relationShipType: 0,
        labels: []
      },
      pagination: {
        pn: 1,
        rn: 10
      },
      total: 0,
      tableList: [],
      showTableModal: false,
      currentTaskId: 0,
      activityIdList: [],
      addVisible: false,
      expTypes: [
        {
          value: '',
          label: '全部'
        },
        {
          value: 1,
          label: '编程实验'
        },
        {
          value: 5,
          label: '云控配置实验'
        }
      ],
      clientTypes: [
        {
          value: 0,
          label: '全部'
        },
        {
          value: 1,
          label: '客户端'
        },
        {
          value: 2,
          label: '服务端'
        }
      ],
      visible: false,
      labelList: [],
      showQuickGuide: true // 控制快速了解模块的显示
    };
  },
  computed: {
    ...mapGetters(['expStatusList', 'appList1', 'accSystemData'], 'message')
  },
  created() {
    this.$store.dispatch('getAppList');
    const prevParams = localStorage.getItem('ab_list_params');
    if (prevParams) {
      const params = JSON.parse(prevParams);
      this.formData = params;
    }
    this.handleSearch();
    this.getLabelList();
  },
  methods: {
    getLabelList() {
      this.$service
        .get('LABLELIST', {}, { allback: 0, needLoading: true })
        .then(res => {
          this.labelList = res.list || [];
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 处理数据格式
    formatData(data) {
      return data.reduce((pre, cur) => {
        pre.push({
          label: cur.activityName,
          value: cur.activityId
        });
        return pre;
      }, []);
    },
    // 重置
    handleReset() {
      this.pagination.pn = 1;
      this.formData = {
        appKey: 'all',
        keyword: '',
        status: ''
      };
      this.getList();
    },
    pageSizeChange(rn) {
      this.pagination.rn = rn;
      this.getList();
    },
    pageNoChange(pn) {
      this.pagination.pn = pn;
      this.getList();
    },
    // 查询列表数据
    handleSearch() {
      this.pagination.pn = 1;
      this.getList();
    },
    // 列表数据
    getList() {
      const status = this.formData.status ? this.formData.status * 1 : '';
      const { createTime = [], onlyMine = 0 } = this.formData;
      const params = {
        ...this.formData,
        ...this.pagination,
        status,
        labels: JSON.stringify(this.formData.labels),
        onlyMine: onlyMine === 2 ? 0 : onlyMine,
        favorite: onlyMine === 2
      };
      if (createTime && createTime.length) {
        params.createTimeStart = +createTime[0] / 1000;
        params.createTimeEnd = +createTime[1] / 1000;
      }
      this.$service
        .get('AB_LIST', params, { needLoading: true })
        .then(data => {
          this.tableList = this.formatResult(data);
          this.total = data.total;
          if (data.total === 0 && data.hasFlowControl) {
            this.$confirm(`实验为流控实验，是否跳转到流控列表页继续搜索`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              localStorage.setItem(
                "ab_flow_list_params",
                JSON.stringify({
                  ...this.formData,
                  type: 6,
                  accountSystem: this.formData.accountSystem ? this.formData.accountSystem : 0,
                  createTime: []
                })
              );
              this.$router.push({
                path: '/traffic-control/exp/list'
              });
            }).catch(() => { });
          } else {
            this.pagination.rn = data.rn;
            this.pagination.pn = data.pn;
            localStorage.setItem(
              'ab_list_params',
              JSON.stringify({
                ...this.formData,
                createTime:
                  this.formData.createTime && this.formData.createTime.length
                    ? [+createTime[0], +createTime[1]]
                    : []
              })
            );
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 格式化返回结果
    formatResult(data) {
      //this.tableList = data.list;
      let copyData = _.cloneDeep(data);
      copyData.list.forEach(item => {
        item.copySwitch = _.cloneDeep(item.switch);
        // 当为暂停状态下，switch的状态要高亮
        item.copySwitch = +item.switch ? false : true;
        // 当实验结束，switch的状态要禁用并变成灰色
        if (item.status == 4) {
          item.copySwitch = false;
        }
        item.startTime = item.startTime ? formatDate(item.startTime * 1000, 'yyyy-MM-dd') : '-';
        item.endTime = item.endTime ? formatDate(item.endTime * 1000, 'yyyy-MM-dd') : '至今';
        if (item.status == 1 || item.status == 2) {
          item.actionTime = '-';
        } else {
          item.actionTime = `${item.startTime} ~ ${item.endTime}`;
        }

        const versionIdList = [];
        item.versionInfoList = this.formatVersion(item.versionList);
      });
      return copyData.list;
    },
    formatVersion(arr) {
      let temp = [];
      let map = {};
      for (let i = 0, len = arr.length; i < len; i++) {
        const displayName = arr[i].displayName;
        const key = map[displayName];
        if (!key) {
          map[displayName] = {
            displayName: arr[i].displayName,
            versionId: arr[i].versionId,
            keyName: [],
            keyValue: [],
            flow: arr[i].flow
          };
          map[displayName].keyName.push(arr[i].keyName);
          map[displayName].keyValue.push(arr[i].keyValue);
        } else {
          map[displayName].keyName.push(arr[i].keyName);
          map[displayName].keyValue.push(arr[i].keyValue);
        }
      }
      //debugger;
      Object.keys(map).forEach(item => {
        temp.push(map[item]);
      });
      //debugger
      return temp;
      //debugger;
    },
    // 创建实验
    createExp() {
      this.visible = true;
    },
    // 关闭快速了解模块
    handleCloseQuickGuide() {
      this.showQuickGuide = false;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  margin-top: 24px;

  /deep/ .page-container {
    padding-top: 16px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
    }
  }
}

.head-title {
  width: 100%;
  margin-top: 20px;

  p {
    padding: 0 20px;
    box-sizing: border-box;
    line-height: 26px;
    color: #fa574b;
  }
}

.select-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  padding: 8px 0px 5px;
}

/deep/ .page-tab {
  margin: 0;
}

// /deep/ .el-form {
//   .el-form-item:nth-child(2) .el-input {
//     width: 180px;
//   }
// }

/deep/ .el-pagination button,
/deep/ .el-pagination span:not([class*='suffix']) {
  font-size: 14px;
}

.self-collapse-btns {
  height: 33px;
}
</style>
