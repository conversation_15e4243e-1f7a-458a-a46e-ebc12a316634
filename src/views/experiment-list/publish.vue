<template>
  <Page class="container" :breadcrumbList="[]" :hasPagination="false">
    <section class="header">
      <section>
        <i class="el-icon-arrow-left" @click="handleGoBack(true)"></i>
        <span class="page-title">{{ title }}</span>
      </section>
      <section>
        <el-button @click="handleSubmit()" type="primary">
          发布
        </el-button>
        <el-button @click="handleGoBack(true)" type="" class="go-back-btn" v-if="!isCheck">取消</el-button>
      </section>
    </section>
    <section class="main">
      <Detail ref='publishDetail' :id="id" :pushId="pushId" :isView="isCheck" />
    </section>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import Detail from './components/publish-deitail.vue';
export default {
  name: 'PublishExperiment',
  components: {
    Page,
    Detail
  },
  async beforeRouteLeave(to, from, next) {
    if (this.isCheck || !this.needRouterConfirm || to.path === '/feat/list/detail') {
      next();
      return;
    }
    const action = await this.$confirm(`点击确定将放弃页面已填写内容，返回实验列表，确定返回吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .catch(() => {
        return 'cancel';
      });
    if (action === 'confirm') {
      next();
    }
  },
  data() {
    const isView = this.$route.query.isView === 'true';
    return {
      id: this.$route.query.id,
      pushId: this.$route.query.pushId,
      isCheck: isView,
      needRouterConfirm: false
    };
  },

  computed: {
    title() {
      let res = '编辑发布计划';
      const isAdd =  !this.pushId;
      if (this.isCheck) {
        res = '查看发布计划';
      }
      if(isAdd){
        res = '新建发布计划';
      }
      return res;
    },
  },
  watch: {},
  created() {
  },
  methods: {
    // 返回列表页面
    handleGoBack(flag = false) {
      this.needRouterConfirm = flag;
      this.$router.push({
        path: '/exp-manage/list'
      });
    },
    async handleSubmit() {
      const target = this.$refs.publishDetail;
      if (target) {
        await target.submit();
        this.handleGoBack();
      }
    }
  },
};
</script>

<style lang="less" scoped>
.container {
  overflow: auto;
  // height: calc(100vh - 100px);
  // padding: 24px;
  padding: 12px 16px;
  box-sizing: border-box;
  display: flex;

  /deep/ .page-container {
    padding-top: 0px;
    padding-bottom: 6px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .main {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .report-tab.container {
      height: auto;
    }

    .base-form {
      flex: 1;
      overflow: auto;
    }
  }

  /deep/ .page-cont {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    margin: 0;
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    section {
      display: flex;
      align-items: center;
    }

    .right {
      /deep/ i {
        // display: inline-block;
        width: 16px;
        height: 16px;
        margin-left: 4px;
        margin-right: 6px;
        display: inline-block;
      }

      .el-button {
        margin-left: 12px;
      }

      .icon-yunhang {
        // width: 18px;
        // height: 18px;
      }
    }

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin: 0 8px;
    }

    i {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .el-tag {

      // @zyb-red-1: #fa574b;
      // @zyb-red-2: #f9685d;
      // @zyb-red-3: #de4f43;
      // @zyb-orange-1: #f78502;
      // @zyb-orange-2: #f9b001;
      // @zyb-green-4: #42c57a;
      // @zyb-lake-blue: #00cccd;
      // @zyb-purple: #8276de;
      // @zyb-blue: #5392ff;
      &.el-tag--default {
        color: #5392ff;
        background-color: #d9ecff;
        border-color: #d9ecff;
      }
    }
  }
}

.ex_steps {
  margin-top: 36px;
}

.op-btns {
  padding-top: 10px;

  // .go-back-btn {
  //   float: right;
  // }
}

.stepSuc :hover {
  cursor: pointer;
}

.stepErr :hover {
  cursor: not-allowed;
}

/deep/ .el-step__head.is-success {
  color: #42c57a;
  border-color: #42c57a;
}

/deep/ .el-step__main .is-success {
  color: #42c57a;
}

/deep/ .el-step__icon {
  width: 32px;
  height: 32px;
}

/deep/ .el-step.is-horizontal .el-step__line {
  top: 15px;
}
</style>
