<template>
  <Page class="container" :breadcrumbList="[]" :hasPagination="false">
    <section class="header">
      <section>
        <i class="el-icon-arrow-left" @click="handleGoBack(true)"></i>
        <span class="page-title">{{ title }}</span>
        <template v-if="isCheck">
          <i class="el-icon-star-on" v-if="addData.favorite" style="color: #FFC845; margin-left: 10px;"></i>
          <!-- <el-tag :type="exstatus.type" style="marginLeft: 20px">{{ exstatus.name }}</el-tag> -->
          <el-tag type="success" style="marginLeft: 5px">{{ exstatus.name }}</el-tag>
          <el-tag type="warning" style="marginLeft: 5px">
            {{ addData.type === 5 ? '云控配置实验' : '编程实验' }}
          </el-tag>
          <el-tag type="warning" style="marginLeft: 5px"
            v-if="addData.childExperimentList && Array.from(addData.childExperimentList).length > 0">
            父实验
          </el-tag>
          <el-tag type="warning" style="marginLeft: 5px" v-if="addData.parentId">子实验</el-tag>
          <el-tag type="warning" style="marginLeft: 5px" v-if="
            addData.childExperimentList && Array.from(addData.extendsExperimentList).length > 0
          ">
            继承
          </el-tag>
          <el-tag type="warning" style="marginLeft: 5px" v-if="addData.hasCorrect">已固化</el-tag>
        </template>
      </section>
      <section class="right" v-if="isCheck && ![-1].includes(addData.status)">
        <!-- 历史记录 -->
        <ex-history></ex-history>
        <ex-relation></ex-relation>
        <template v-if="isAuthLevel2">
          <el-tooltip class="item" effect="dark" content="开启实验" placement="top" v-if="statusAwaitRun()">
            <i class="icon2-yunhang iconfont2" @click="handleComfirm(2)"></i>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="解冻实验" placement="top" v-if="statusIsDongJie()">
            <i class="icon2-jiedong iconfont2" @click="handleComfirm(7)"></i>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="继续实验" placement="top" v-if="statusIsPause()">
            <i class="icon2-yunhang iconfont2" @click="handleComfirm(3)"></i>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="暂停实验" placement="top"
            v-if="statusIsRunning() && !statusIsPause()">
            <i class="icon2-zanting iconfont2" @click="handleComfirm(1)"></i>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="结束实验" placement="top"
            v-if="statusIsRun() || statusIsPub() || statusIsRollback() || statusAwaitRun()">
            <i class="icon2-tingzhi iconfont2" @click="handleComfirm(5)"></i>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="冻结实验" placement="top"
            v-if="statusIsRunning() && addData.isResetUser == 0">
            <i class="icon2-dongjie iconfont2" @click="handleComfirm(6)"></i>
          </el-tooltip>
        </template>
        <el-tooltip class="item" effect="dark" content="诊断" placement="top"
          v-if="statusIsRun() || statusIsDebug() || statusIsPub() || statusIsRollback()">
          <i class="icon2-zhenduan iconfont2" @click="goDiagnosis"></i>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="回滚" placement="top" v-if="statusIsPub()">
          <i class="el-icon-refresh" style="font-size: 18px;position: relative;top: -2px;"
            @click="handleRollBack()"></i>
        </el-tooltip>
        <template v-if="isAuthLevel2">
          <el-tooltip class="item" effect="dark" content="创建子实验" placement="top"
            v-if="statusIsRun() && !addData.parentId">
            <i class="icon2-fuzishiyan iconfont2" @click="createChildExp"></i>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="实验继承" placement="top" v-if="statusIsRun()">
            <i class="icon2-a-fuzhi4 iconfont2" @click="createExtendExp"></i>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="发布实验" placement="top"
            v-if="addData.canPush || statusIsRollback()">
            <i class="icon2-qizi iconfont2" @click="editPublish"></i>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="查看固化详情" placement="top"
            v-if="(statusIsRun() || statusIsStop()) && addData.hasCorrect">
            <i class="icon2-qizi iconfont2" @click="viewFeature"></i>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="代码示例" placement="top">
            <i class="icon2-daimashili iconfont2" @click="drawer = true"></i>
          </el-tooltip>
        </template>
        <el-button @click="copyExp" v-if="!statusIsDraft()">复制</el-button>
        <el-button @click="editExp" type="primary"
          v-if="!statusIsStop() && isAuthLevel2 && !statusIsPub() && !statusIsRollback()">
          编辑
        </el-button>
        <el-button @click="editPublish" type="primary" v-if="statusIsPub()">编辑发布计划</el-button>
      </section>
      <div v-if="!isCheck">
        <!-- 非代发布状态 -->
        <template v-if="!statusIsAwaitPub()">
          <el-button v-if="statusIsDebug() || statusIsDraft() || isCopy" @click="handleSubmit(2)" type="primary">
            提交&调试
          </el-button>
          <el-button v-if="statusIsDraft() || isCopy" @click="handleSubmit(1)" type="primary">
            保存草稿
          </el-button>
          <el-button v-if="statusIsRun() && !isExtend" type="primary" @click="handleSubmit(3)">
            确认修改
          </el-button>
          <el-button v-if="statusIsRun() && isExtend" type="primary" @click="handleSubmit(3)">
            提交
          </el-button>
        </template>
        <template v-if="statusIsAwaitPub()">
          <el-button type="primary" @click="handleSubmit(5)">确认修改</el-button>
        </template>
        <el-button v-if="!isCheck" @click="handleGoBack(true)" type="" class="go-back-btn">
          取消
        </el-button>
      </div>
    </section>
    <el-tabs v-model="tab" v-if="isCheck" @tab-click="handleTabClick">
      <el-tab-pane label="实验详情" name="1"></el-tab-pane>
      <el-tab-pane label="白名单测试" name="5"></el-tab-pane>
      <el-tab-pane label="实验发布计划" name="3" v-if="statusIsPub() || this.pushId"></el-tab-pane>
      <el-tab-pane label="实验报告" name="2" v-if="
        statusIsRunning() ||
        statusIsPub() ||
        statusIsPause() ||
        statusIsDongJie() ||
        statusIsRollback() ||
        statusIsStop()
      "></el-tab-pane>
      <el-tab-pane label="发布报告" name="4" v-if="statusIsPub() || this.pushId"></el-tab-pane>
    </el-tabs>
    <section class="main" :class="{ 'edit-mode': !isCheck }">
      <section v-if="tab === '1'" class="content-container">
        <section class="content-left">
          <ex-baseInfo></ex-baseInfo>
          <!-- 基础信息 -->
          <ex-metric :exType="Number(ex)" ref="exMetric"></ex-metric>
          <!-- 受众 -->
          <ex-solution></ex-solution>
          <!-- 流量分配-->
          <ex-sample v-if="!this.isCloudControl()" ref="exMetrics"></ex-sample>
          <!-- 指标 -->
        </section>
        <ExDocument class="content-right" v-if="!isCheck" />
      </section>
      <ExpReport :id="this.expId" v-if="tab === '2'" class="report-tab"></ExpReport>
      <PublishDetail :id="this.expId" :pushId="this.pushId" v-if="tab === '3'" class="report-tab" :isView="true">
      </PublishDetail>
      <PublishReport :id="this.expId" :pushId="this.pushId" v-if="tab === '4'" class="report-tab"></PublishReport>
      <ExWhiteList :id="this.expId" :pushId="this.pushId" v-if="tab === '5'" class="report-tab"></ExWhiteList>
    </section>
    <el-drawer title="代码示例" :visible.sync="drawer" size="50%" class="code-example-drawer">
      <show-example-code :portType="addData.clientType" :addData="addData"></show-example-code>
    </el-drawer>
    <!-- 实验验证对话框 -->
    <ExperimentValidationDialog ref="validationDialog" :visible.sync="validationDialogVisible"
      :validations="currentValidations" @confirm="handleValidationConfirm" @cancel="handleValidationCancel" />
    <ExGuide ref="exGuideRef" />
    <ExCron :title="cron.title" :tips="cron.tips" :visible.sync="cron.visible" :text="cron.text"
      @confirm="confirmTime" />
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import ExBaseInfo from './components/ex-baseInfo';
import ExMetric from './components/ex-metric';
import ExSample from './components/ex-sample';
import ExSolution from './components/ex-solution';
import ExHistory from './components/history';
import ExRelation from './components/ex-relation.vue';
import ShowExampleCode from './components/show-example-code.vue';
import ExpReport from './report';
import PublishDetail from './components/publish-deitail.vue';
import PublishReport from './components/publish-report.vue';
import ExWhiteList from './components/ex-whitelist.vue';
import ExperimentValidationDialog from './components/experiment-validation-dialog.vue';
import * as _ from 'lodash';
import { mapGetters } from 'vuex';
import { getUrlByLocation } from '../../utils/util';
import { canStartExp, editExp } from './utils';
import ExDocument from './components/ex-document.vue';
import ExGuide from './components/ex-guide.vue';
import ExCron from './components/ex-cron.vue';
import { checkFilterConflict } from './utils/filter-conflict';
export default {
  name: 'AddExperiment',
  components: {
    Page,
    ExBaseInfo,
    ExMetric,
    ExSolution,
    ExSample,
    ExHistory,
    ExpReport,
    ShowExampleCode,
    PublishDetail,
    PublishReport,
    ExWhiteList,
    ExRelation,
    ExperimentValidationDialog,
    ExDocument,
    ExGuide,
    ExCron
  },
  async beforeRouteLeave(to, from, next) {
    if (this.isCheck || !this.needRouterConfirm || to.path === '/feat/list/detail') {
      next();
      return;
    }
    const action = await this.$confirm(
      `点击确定将放弃页面已填写内容，返回实验列表，确定返回吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).catch(() => {
      return 'cancel';
    });
    if (action === 'confirm') {
      next();
    }
  },
  provide() {
    return {
      getConflicFilterrules: () => this.conflicFilterrules
    };
  },
  data() {
    const pushId = this.$route.query.pushId || undefined;
    return {
      //seltedData: [],
      // 已选步骤
      stepSuc: [0],
      breadcrumbList: ['ExperimentList', 'addExp'],
      stepData: [
        { index: 0, title: '实验基本信息', icon: '' },
        { index: 1, title: '选择关注指标', icon: '' },
        { index: 2, title: '实验方案及目标受众', icon: '' },
        { index: 3, title: '样本量及比例配置', icon: '' }
        // {index: 3, title: '查看', icon: ''},
      ],
      pushId: pushId,
      tab: pushId ? '3' : '1',
      expId: this.$route.query.id,
      parentId: this.$route.query.parentId,
      isAdd: this.$route.query.type === 'add' || false,
      isEdit: this.$route.query.type === 'edit' || false,
      isCheck: this.$route.query.type === 'check' || false,
      isCopy: this.$route.query.subType === 'copy' || false,
      isExtend: this.$route.query.isExtend === '1' || false,
      ex: this.$route.query.ex, // 实验类型：1编程实验 2可视化实验 5:云控实验
      correctId: this.$route.query.correctId || '', // 从固化新建实验
      ops: {
        1: {
          opName: '暂停',
          opTip: '暂停成功',
          data: {
            status: 6
          },
          api: 'SET_STATUS',
          warningTip: '暂停实验后会没有用户命中实验，确定暂停实验吗？'
        },
        2: {
          opName: '开始',
          opTip: '开始成功',
          warningTip: '开启实验后，进组用户可实时查看。确定开始吗？',
          api: 'START_TEST'
        },
        3: {
          opName: '继续',
          opTip: '继续成功',
          warningTip: '确定继续实验吗？',
          data: {
            status: 3
          },
          api: 'SET_STATUS'
        },
        4: {
          opName: '删除',
          opTip: '删除成功',
          warningTip: '删除的实验将从系统移除，删除后无法查看相关信息，确定删除吗？',
          api: 'DELETE_TEST'
        },
        5: {
          opName: '结束',
          opTip: '结束成功',
          warningTip: '结束实验后，当前实验结果将为最终结果，而且无法重新启动实验，确定结束吗？',
          api: 'OVER_TEST'
        },
        6: {
          opName: '冻结',
          opTip: '冻结成功',
          warningTip: '冻结实验后，除已命中实验用户外，不再有新增用户命中实验。确定冻结实验吗？',
          api: 'SET_STATUS',
          data: {
            status: 7
          }
        },
        7: {
          opName: '解冻',
          opTip: '解冻成功',
          warningTip: '确定解冻实验吗？',
          api: 'SET_STATUS',
          data: {
            status: 3
          }
        }
      },
      needRouterConfirm: false,
      drawer: false,
      preFlow: 0,
      validationDialogVisible: false,
      currentValidations: {},
      pendingStatus: null,
      cron: {
        visible: false,
        title: "",
        tips: '',
        text: '开始'
      },
      conflicFilterrules: [],
    };
  },

  computed: {
    ...mapGetters(
      ['addData', 'originGroupFlow', 'whiteList', 'metricData', 'statusMap'],
      'message'
    ),
    stepClassObj(val) {
      return val => {
        return {
          stepSuc: this.stepSuc.includes(val),
          stepErr: !this.stepSuc.includes(val)
        };
      };
    },
    isAuthLevel2() {
      return this.addData.authLevel === 2;
    },
    isChild() {
      return !!this.$route.query.parentId;
    },
    title() {
      if (this.isCheck) {
        return this.addData.displayName;
      }
      const isCreateChild = this.isAdd && this.$route.query.parentId && !this.correctId;
      if (isCreateChild) {
        return '创建子实验';
      }
      if (this.isExtend) {
        return '实验继承';
      }
      if (this.isEdit && !this.isCopy) {
        return '编辑实验';
      }
      return '新增实验';
    },
    exstatus() {
      const status = this.addData.status;
      const clientType = this.addData.clientType;
      let statusR = {};
      const statusMap = {
        1: {
          name: '草稿',
          type: 'default'
        },
        2: {
          name: '调试中',
          type: 'default'
        },
        3: {
          name: '运行中',
          type: 'success'
        },
        4: {
          name: '已结束',
          type: 'info'
        },
        5: {
          name: '待发布',
          type: 'default'
        },
        6: {
          name: '已暂停',
          type: 'warning'
        },
        7: {
          name: '已冻结',
          type: 'warning'
        },
        8: {
          name: '发布中',
          type: 'success'
        },
        9: {
          name: '已回滚',
          type: 'success'
        }
      };
      statusR = statusMap[status] || {};
      return statusR;
    }
  },
  watch: {
    addData() {
      if (!this.preFlow) {
        this.preFlow = this.addData.flow;
      }
    }
  },
  created() {
    this.parentId && this.$store.commit('setFatherId', this.parentId);
    // 获取应用列表
    this.$store.dispatch('getAppList');
    // 直接从实验列表页面点击添加按钮过来创建实验
    // const isSimpleCreate = this.isAdd && !this.$route.query.parentId && !this.correctId;
    // if (isSimpleCreate) {
    //   this.$store.dispatch('getFilterList');
    //   this.$store.dispatch('getMutuList');
    //   this.$store.dispatch('getWhiteList');
    //   this.$store.dispatch('getMetricList');
    // }
    // 从列表中点击创建子实验按钮过来创建子实验
    const isCreateChild = this.isAdd && this.$route.query.parentId && !this.correctId;
    if (isCreateChild) {
      //;
      this.$store.dispatch('getFatherExpDetail', { id: this.parentId, isCreateChild });
    }
    // 从固化列表过来创建实验
    const isCorrectCreate = this.isAdd && this.correctId;
    if (isCorrectCreate) {
      //;
      this.$store.dispatch('getExpDetail', {
        id: this.expId,
        parentId: this.parentId,
        correctId: this.correctId,
        isCopy: this.isCopy
      });
    }
    // 编辑或查看
    if (this.$route.query.id) {
      this.$store.dispatch('getExpDetail', {
        id: this.expId,
        parentId: this.parentId,
        correctId: this.correctId,
        isCopy: this.isCopy
      });
    }
  },
  methods: {
    handleTabClick() {
      // this.$store.commit('resetAddData');
      this.$store.dispatch('getExpDetail', {
        id: this.expId
      });
    },
    isCloudControl() {
      return +this.ex === 5;
    },
    statusAwaitRun() {
      return this.statusIsAwaitPub() || this.statusIsDebug();
    },
    statusIsRun() {
      return this.statusIsRunning() || this.statusIsPause() || this.statusIsDongJie();
    },
    statusIsDraft() {
      // 草稿状态
      return this.addData.status === 1;
    },
    statusIsDebug() {
      // 调试状态
      return this.addData.status === 2;
    },
    statusIsRunning() {
      // 运行中状态
      return this.addData.status === 3;
    },
    statusIsStop() {
      // 结束状态
      return this.addData.status === 4;
    },
    statusIsAwaitPub() {
      // 待发布状态
      return this.addData.status === 5;
    },
    statusIsPause() {
      // 暂停状态
      return this.addData.status === 6;
    },
    statusIsDongJie() {
      // 冻结状态
      return this.addData.status === 7;
    },
    statusIsPub() {
      // 发布状态
      return this.addData.status === 8;
    },
    statusIsRollback() {
      // 回滚状态
      return this.addData.status === 9;
    },
    showGudingFeature() {
      const data = this.addData;
      const version = data.versionList ? data.versionList[0] : {};
      return data.type === 1 && version.featureList && version.featureList.length === 1;
    },
    // 验证第一步中的邮箱
    validateEmails() {
      let flag = true;
      this.addData.myEmails.forEach(item => {
        if (!item.value) {
          flag = false;
        }
      });
      return flag;
    },
    isJson(str) {
      if (typeof str == 'string') {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
            return false;
          }
        } catch (e) {
          console.log('error：' + str + '!!!' + e);
          return false;
        }
      } else {
        this.$message.error('请输入字符串');
      }
    },
    // 验证第一步
    validateOneStep() {
      let isRight = true;
      const isCloudControl = +this.ex === 5;
      if (!this.addData.displayName) {
        this.$message.warning('实验名称不能为空！');
        isRight = false;
        return isRight;
      }
      // 运控实验不校验实验时长
      if (!isCloudControl && !this.addData.duration) {
        this.$message.warning('实验时长不能为空！');
        isRight = false;
        return isRight;
      }
      if (!this.addData.appKey) {
        this.$message.warning('业务线不能为空！');
        isRight = false;
        return isRight;
      }
      if (this.addData.isExpireNotice && !this.addData.dingToken) {
        this.$message.warning('请输入钉钉token');
        isRight = false;
        return isRight;
      }
      if (!this.addData.createUser) {
        this.$message.warning('请选择负责人');
        isRight = false;
        return isRight;
      }
      return true;
    },
    validateSecondStep() {
      let isRight = true;
      if (!this.addData.flow) {
        this.$message.warning('流量分配不能为空');
        isRight = false;
        return isRight;
      }
      // 如果是从发布中的实验来创建继承实验
      if (this.addData.status === 5 && this.addData.originFlow > this.addData.flow) {
        this.$message.warning(
          `继承实验时样本总量不支持调小，原样本总量为${this.addData.originFlow}%，当前样本总量为${this.addData.flow}%，如有缩量需求，请继承后再次编辑进行调整`
        );
        isRight = false;
        return isRight;
      }
      // 发布中、已回滚不支持调小
      if (
        (this.statusIsPub() || this.statusIsRollback()) &&
        this.addData.originFlow > this.addData.flow
      ) {
        this.$message.warning(
          `实验发布后，样本总量不支持调小，原样本总量为${this.addData.originFlow}%，当前样本总量为${this.addData.flow}%，请重新调整`
        );
        isRight = false;
        return isRight;
      }
      const filterRuleLen = this.addData.filterRule.length;
      if (filterRuleLen) {
        for (let i = 0; i < filterRuleLen; i++) {
          let currtFilter = this.addData.filterRule[i];
          const filterList = currtFilter.filterList;

          for (let j = 0, len = filterList.length; j < len; j++) {
            const curList = filterList[j];
            const isNumber = typeof curList.keyValue === 'number';
            if (
              curList.filterConfId === '' ||
              !curList.keyType ||
              (!isNumber && !curList.keyValue.length)
            ) {
              this.$message.warning('无效的受众规则');
              isRight = false;
              return isRight;
            }
          }
        }
      }
      return true;
    },
    // 验证第三步
    validateThirdStep() {
      let isRight = true;
      const versions = this.addData.versionList,
        trueVersions = versions.slice(1),
        trueVerLen = trueVersions.length;
      const selectedWhiteList = [];
      let sum = 0;
      for (let j = 0; j < trueVerLen; j++) {
        selectedWhiteList.push(...trueVersions[j].mywhitelist);
        sum += 1 * trueVersions[j].flow;
      }
      if (isNaN(sum)) {
        this.$message.warning('版本流量不能为空!');
        isRight = false;
        return isRight;
      }
      // 为了简单解决前端精度问题
      if (sum <= 99.9) {
        this.$message.warning('各版本流量占比总和不足100%，请重新分配');
        isRight = false;
        return isRight;
      }

      if (this.isRepeat(selectedWhiteList)) {
        this.$message.warning('每个版本的白名单不能重复');
        isRight = false;
        return isRight;
      }
      let displayNameArr = [];
      if (this.ex == 1 || this.ex == 5) {
        const versions = this.addData.versionList,
          params = versions[0].featureList,
          paramsLen = params.length,
          typeTest = /^\w+$/, //只能为字母、数字、下划线
          trueVersions = versions.slice(1),
          trueVerLen = trueVersions.length;
        for (let i = 0; i < paramsLen; i++) {
          if (!params[i].keyName) {
            this.$message.warning('参数名不能为空');
            isRight = false;
            return isRight;
          }
          // 参数名只能为字母、数字、下划线
          if (!typeTest.test(params[i].keyName)) {
            this.$message.warning('实验版本参数名只能为字母、数字、下划线');
            isRight = false;
            return isRight;
          }
        }
        for (let j = 0; j < trueVerLen; j++) {
          const featureList = trueVersions[j].featureList;
          for (let k = 0, featLen = featureList.length; k < featLen; k++) {
            if (featureList[k].keyValue === '') {
              this.$message.warning('参数值不能为空');
              isRight = false;
              return isRight;
            } else if (featureList[k].keyType === 5 && !this.isJson(featureList[k].keyValue)) {
              this.$message.warning('请输入正确的JSON');
              isRight = false;
              return isRight;
            }
          }
        }

        let tempArr = [],
          newArr = [];
        for (let j = 0; j < trueVerLen; j++) {
          const featureList = trueVersions[j].featureList;
          const name = trueVersions[j].displayName;
          displayNameArr.push(name);
          for (let k = 0, featLen = featureList.length; k < featLen; k++) {
            if (tempArr.indexOf(featureList[k].keyName) === -1) {
              newArr.push({
                keyName: featureList[k].keyName,
                keyType: featureList[k].keyType,
                list: [featureList[k].keyValue]
              });
              tempArr.push(featureList[k].keyName);
            } else {
              for (let j = 0; j < newArr.length; j++) {
                if (newArr[j].keyName == featureList[k].keyName) {
                  newArr[j].list.push(featureList[k].keyValue);
                }
              }
            }
          }
        }
      }

      for (let i = 0, len = displayNameArr.length; i < len; i++) {
        // 除了boolean 类型外，其他都不允许重复
        const name = displayNameArr[i];
        if (!name) {
          this.$message.warning('实验版本名称不可为空');
          isRight = false;
          return isRight;
        }
        if (this.isRepeat(displayNameArr)) {
          this.$message.warning('每个版本的版本名称不能重复');
          isRight = false;
          return isRight;
        }
      }
      if (this.isChild) {
        // 关联父实验不能为空
        const isRight = trueVersions.every(item => item.relationIds.length);
        if (!isRight) {
          this.$message.warning('请选择父实验版本');
          return false;
        }
      }
      return true;
    },
    // 验证第四步
    validateFourthStep() {
      return true;
    },
    // 下一步
    onNextStep() {
      let flag = false;
      //;
      flag = this.validateOneStep();
      //;
      if (!flag) {
        return;
      }
      flag = this.validateSecondStep();
      if (!flag) {
        return;
      }
      flag = this.validateThirdStep();
      if (!flag) {
        return;
      }
      flag = this.validateFourthStep();
      //
      if (!flag) {
        return;
      }
      // if (flag) {
      //   this.stepSuc.push(++this.current);
      // }
    },
    isRepeat(arr) {
      var hash = {};
      for (var i in arr) {
        if (hash[arr[i]]) {
          return true;
        }
        hash[arr[i]] = true;
      }
      return false;
    },

    // 返回列表页面
    handleGoBack(flag = false) {
      this.needRouterConfirm = flag;
      this.$router.push({
        path: '/exp-manage/list'
      });
    },
    validateBeforeSubmit() {
      const flag1 = this.validateOneStep();
      if (!flag1) {
        return false;
      }
      const flag2 = this.validateSecondStep();
      if (!flag2) {
        return false;
      }
      const flag3 = this.validateThirdStep();
      if (!flag3) {
        return false;
      }
      const flag4 = this.validateFourthStep();
      //;
      return flag1 && flag2 && flag3 && flag4;
    },
    async getProcessList() {
      const { list = [] } = await this.$service.get(
        'GETPROCESSLIST',
        {
          appKey: this.addData.appKey,
          checkUser: 1
        },
        { allback: 0, needLoading: false }
      );
      return list;
    },
    // 统一验证实验修改的各种情况
    async validateExperimentChanges() {
      const { flow } = this.addData;
      const validations = {};
      const processList = await this.getProcessList();

      // 1. 实验缩量验证
      if (this.preFlow > flow && this.statusIsRun()) {
        const target = processList.find(item => item.type === 5);
        const needApproval = target && target.status === 1;

        validations.trafficReduction = {
          preFlow: this.preFlow,
          flow: flow,
          needApproval: needApproval
        };
      }

      // 2. 分组流量变更验证
      if (this.statusIsRun() && this.isEdit && !this.isExtend) {
        const versions = this.addData.versionList;
        const trueVersions = versions.slice(1);
        const currentGroupFlow = trueVersions.map(v => v.flow);
        const isEqual = _.isEqual(currentGroupFlow, this.originGroupFlow);

        if (!isEqual) {
          const target = processList.find(item => item.type === 8);
          const needApproval = target && target.status === 1;
          const isResetUser = this.addData.isResetUser; // 0 进组不出组  1 进组出组

          const messages = [
            '当前实验为进组不出组，已进组用户保持命中状态，后续新进组用户会按照新的流量占比进行分流，请谨慎操作。',
            '当前实验为进组出组，部分已进组用户可能因流量调整跳组。提交修改后，将生成新的实验报告版本，重新统计调整后的实验指标数据，请谨慎操作。'
          ];

          validations.groupFlowChange = {
            message: messages[isResetUser],
            needApproval: needApproval
          };
        }
      }

      // 3. 实验时长缩短验证
      if (this.addData.type === 1 && this.statusIsRun()) {
        const currentTimeInSeconds = Math.floor(Date.now() / 1000);
        const runningDays = Math.floor((currentTimeInSeconds - this.addData.startTime) / 86400);
        const duration = +this.addData.duration;

        if (duration < runningDays) {
          const message =
            duration < runningDays
              ? `本次操作缩短实验时长，实验已运行${runningDays}天，修改后实验时长${duration}天，确定修改后实验将立即结束，请谨慎操作。`
              : `本次操作缩短实验时长，实验已运行${runningDays}天，修改后实验时长${duration}天，确定修改后实验将于今天结束，请谨慎操作。`;

          validations.durationShorten = {
            message: message
          };
        }
      }

      return validations;
    },
    // 处理提交
    async handleSubmit(status) {
      // 提交之前再次验证
      const flag = this.validateBeforeSubmit();
      this.conflicFilterrules = [];
      this.addData.filterRule.forEach((filterRule) => {
        const noconflic = checkFilterConflict(filterRule);
        if (!noconflic) {
          this.conflicFilterrules.push(filterRule.id);
        }
      });
      if (!flag || this.conflicFilterrules.length) return;

      // 检查是否需要验证实验修改
      const validations = await this.validateExperimentChanges();

      if (Object.keys(validations).length > 0) {
        // 有验证项，显示统一验证对话框
        this.showValidationDialog(validations, status);
      } else {
        // 没有验证项，直接提交
        this.submitAction(status, '', false);
      }
    },
    // 显示验证对话框
    showValidationDialog(validations, status) {
      this.currentValidations = validations;
      this.pendingStatus = status;
      this.validationDialogVisible = true;
    },
    // 处理验证确认
    handleValidationConfirm(result) {
      this.validationDialogVisible = false;
      this.submitAction(this.pendingStatus, result.reason, true);
    },
    // 处理验证取消
    handleValidationCancel() {
      this.validationDialogVisible = false;
      this.currentValidations = {};
      this.pendingStatus = null;
    },
    submitAction(status, reason, hasConfirm = false) {
      // status == 1: 保存草稿
      // status == 2; 提交&测试
      const guide = this.$refs.exGuideRef.handelGuideCheck()
      console.log('guide', guide)
      return
      status = this.statusIsRun() && this.isExtend ? 5 : status;
      const data = this.formatAddData(
        [6, 7].includes(this.addData.status) ? this.addData.status : status
      );
      if (reason) {
        data.reason = reason;
      }
      const formData = this.formatFormData(data);
      console.log('\x1b[36m\x1b[0m [LOG]: formData', data);
      if (reason) {
        this.handleUpdateExp(formData, status, hasConfirm);
        return;
      }
      // 如果是在新增时候
      if (!this.isEdit || this.isCopy) {
        if (status == 2) {
          if (hasConfirm) {
            this.handleCreateExp(formData, status);
            return;
          }
          this.$confirm(`确认提交&测试吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.handleCreateExp(formData, status);
            })
            .catch(e => {
              console.log(e);
              this.$message({
                type: 'info',
                message: '已取消'
              });
            });
        } else {
          // 如果用户点击了保存草稿
          this.handleCreateExp(formData, status);
        }
      } else {
        // 如果是在编辑
        if (status == 2 || status == 3 || status == 5) {
          if (this.isExtend) {
            if (hasConfirm) {
              this.handleCreateExp(formData, status);
              return;
            }
            this.$confirm(`确认提交继承实验吗？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                this.handleCreateExp(formData, status);
              })
              .catch(e => {
                console.log(e);
                this.$message({
                  type: 'info',
                  message: '已取消'
                });
              });
          } else {
            if (hasConfirm) {
              this.handleUpdateExp(formData, status);
              return;
            }
            const tip = status == 2 ? '确认提交&测试吗？' : '确认修改吗？';
            this.$confirm(tip, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                this.handleUpdateExp(formData, status);
              })
              .catch(e => {
                console.log(e);
                this.$message({
                  type: 'info',
                  message: '已取消'
                });
              });
          }
        } else {
          // 如果用户点击了保存草稿

          this.handleUpdateExp(formData, status);
        }
      }
    },
    // 创建实验
    handleCreateExp(formData, status) {
      const tip = {
        1: '添加草稿成功',
        2: '提交&测试成功',
        3: '提交成功',
        5: '提交继承实验成功'
      };
      this.$service
        .post('ADD_TEST', formData, { allback: 0, needLoading: true })
        .then(res => {
          this.$message.success(`${tip[status]}`);
          this.handleGoBack();
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 编辑实验
    handleUpdateExp(formData, status, hasConfirm) {
      const tip = {
        1: '保存草稿成功',
        2: '提交&测试成功',
        3: '确认修改成功',
        5: '确认修改成功'
      };
      this.$service
        .post('VIEW_TEST', formData, { allback: 0, needLoading: true })
        .then(res => {
          const tips = hasConfirm ? '流程提交成功' : tip[status];
          this.$message.success(`${tips}`);
          this.handleGoBack();
        })
        .catch(err => {
          console.log(err);
        });
    },
    confirmTime(data) {
      const { taskTime } = data;
      const { row, info, op, reason } = this.cron;
      this.handleAction(row, op, info, {
        reason,
        taskTime: taskTime
      });
    },
    // 组装提交数据
    formatAddData(status) {
      const data = _.cloneDeep(this.addData);
      if (this.isEdit && !this.isCloudControl()) {
        const ref = this.$refs['exMetrics'];
        //;
        const seltedData = this.$refs['exMetrics'].seltedData;
        //;
        const temp = [];
        //const selectedMetric = seltedData;
        const indicators1 = this.addData.indicators;
        //  seltedData.forEach((item)=>{
        //    indicators.forEach(list=>{
        //      if(item !== list.indId){
        //        indicators.splice
        //      }
        //    })
        //  })
        const indicators = [];
        const selectedMetric = seltedData;
        selectedMetric.forEach(item => {
          indicators.push({
            indId: item,
            isCore: 0
          });
        });
        indicators.forEach(item => {
          indicators1.forEach(list => {
            if (item.indId === list.indId) {
              item.id = list.id;
            }
          });
        });

        data.indicators = indicators;
        //;
        // seltedData.forEach((item) => {
        //   indicators.forEach((list) => {
        //     if (item == list.indId && list.id) {
        //       //item.id = list.id;
        //       temp.push({
        //         id: list.id,
        //         indId: item, //指标ID
        //         isCore: 0, // 是否为核心指标 0:否 1:是
        //       });
        //     }else {
        //        temp.push({
        //         indId: item, //指标ID
        //         isCore: 0, // 是否为核心指标 0:否 1:是
        //       });
        //     }
        //   });
        // });
        // data.indicators = temp;
        //;
      }
      // 从固化过来新建实验
      if (this.correctId) {
        data.correctId = this.correctId;
      }
      // 创建继承实验
      if (this.isExtend) {
        data.extendsExperimentId = data.id;
      }
      data.type = this.ex;
      data.status = status;
      data.isExpireNotice = data.isExpireNotice ? 1 : 0; //
      data.isLongNotice = data.isLongNotice ? 1 : 0;
      if (data.newExclusiveId) {
        data.exclusiveId = data.newExclusiveId;
        // ;
      }
      data.isFlowAvg = data.isFlowAvg ? 1 : 0; //版本之间是否平均分配流量0否1是
      data.flow = data.flow ? data.flow * 10 : 0; //版本流量前端是按照100来展示，但是后端用的是1000
      data.versionList.splice(0, 1); // 由于从第二个开始才是真正的版本对象，第一个是为了展示参数部分
      data.versionList.forEach(version => {
        version.featureList.forEach(feature => {
          feature.keyValue = feature.keyValue + '';
        });
      });
      data.filterRule.forEach(rule => {
        rule.filterList.forEach(list => {
          if (Array.isArray(list.keyValue)) {
            list.keyValue = JSON.stringify(Array.from(list.keyValue).map(kv => kv + ''));
          } else {
            list.keyValue = JSON.stringify([list.keyValue + '']);
          }
          this.delRedundantKey(list, 'symbolList');
          this.delRedundantKey(list, 'useEnum');
          this.delRedundantKey(list, 'keyValueEnums');
        });
      });
      // 组装指标数据
      if (this.isAdd) {
        const indicators = [];
        const selectedMetric = this.addData.selectedMetric;
        selectedMetric.forEach(item => {
          indicators.push({
            indId: item,
            isCore: 0
          });
        });
        data.indicators = indicators;
      }
      if (this.isChild) {
        data.togetherEnd = data.togetherEnd ? 1 : 0;
        data.parentId = this.parentId / 1;
      } else {
        data.togetherEnd = 0;
        data.parentId = 0;
        data.versionList.forEach(version => {
          version.relationIds = [];
        });
      }

      //;
      // 处理第一步邮件接收人
      // data.emails = data.myEmails.map((item) => `${item.value}@zuoyebang.com`);

      this.delRedundantKey(data, 'newExclusiveId');
      this.delRedundantKey(data, 'selectedMetric');
      this.delRedundantKey(data, 'myEmails');
      return this.formatWhiteList(data);
    },
    // 组装白名单数据
    formatWhiteList(data) {
      data.versionList.forEach(item => {
        item.flow = parseInt(item.flow * 10);
        this.delRedundantKey(item, 'fixedName');
        item.whitelist = item.mywhitelist.map(item => ({
          whitelistId: item
        }));
        this.delRedundantKey(item, 'mywhitelist');
      });
      return data;
    },
    // 删除冗余字段
    delRedundantKey(obj, key) {
      if (obj.hasOwnProperty(key)) {
        delete obj[key];
      }
    },
    // 转换成formData格式提交给后台
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    //具体动作
    handleOperate(row, op, ext) {
      const opData = op.data ? op.data : {};
      const data = {
        experimentId: row.id,
        appKey: row.appKey,
        ...opData,
        ...ext
      };
      const formData = this.formatFormData(data);
      this.$service.post(op.api, formData, { allback: 0, needLoading: true }).then(res => {
        this.$message.success(op.opTip);
        window.location.reload();
      });
    },
    // 确认操作
    async handleComfirm(type) {
      const op = this.ops[type];
      const row = this.addData;
      let info = '';
      const childExpName =
        row.childExperimentList && row.childExperimentList.length
          ? row.childExperimentList.map(v => v.displayName)
          : '';
      if (type == 1) {
        info = childExpName
          ? `当前实验关联了子实验：${childExpName};实验暂停后，暂停期间父实验无进组用户，关联父实验版本的子实验版本也将没有进组用户，确定暂停吗？`
          : '暂停后不再有正常用户命中实验，仅对白名单用户生效；暂停期间该实验无进组用户，确定暂停吗？';
      } else if (type == 6) {
        info = childExpName
          ? `当前实验关联了子实验：${childExpName};实验冻结后，父实验不再有新用户进组，已经命中父实验用户，仍作为关联父实验版本的子实验受众继续命中子实验，确定冻结吗？`
          : '冻结实验后，保持当前进组数据，不再有新用户进组，确定冻结实验吗？';
      } else if (type == 5) {
        info = childExpName
          ? `当前的实验关联了子实验：${childExpName};结束实验后，子实验也将同时结束，父实验和子实验结果将为最终结果，结束的实验无法重新启动，确定结束吗？`
          : '结束实验后，当前实验结果将为最终结果，而且无法重新启动实验，确定结束吗？';
      } else if (type == 2 && row.status == 5) {
        info = row.extendsNotice || '开启后原实验自动结束，确定开启继承实验吗？';
      } else {
        info = op.warningTip;
      }
      let reason = '';
      // 调试下的开启实验才需要校验
      if (type === 2 && this.statusIsDebug()) {
        const res = await canStartExp(row, {
          alert: this.$alert,
          prompt: this.$prompt,
          service: this.$service
        });
        if (!res || !res.valid) {
          return;
        }
        reason = res.reason;
      }
      if (type === 2) {
        this.cron = {
          visible: true,
          title: '实验开启',
          row,
          info,
          op,
          reason
        };
        return;
      }
      if (type === 5) {
        this.cron = {
          visible: true,
          title: '实验结束',
          text: '结束',
          tips: row.isExtended ? '当前的实验关联了子实验，结束实验后，子实验也将同时结束，父实验和子实验结果将为最终结果，结束的实验无法重新启动！' : '结束实验后，当前实验结果将为最终结果，而且无法重新启动实验！',
          row,
          info,
          op,
          reason
        };
        return;
      }
      this.handleAction(row, op, info, {
        reason
      });
    },
    handleAction(row, op, info, ext = {}) {
      if (info) {
        this.$confirm(info, '提示', {
          customClass: 'ex-table-message',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.handleOperate(row, op, ext);
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      } else {
        this.handleOperate(row, op, ext);
      }
    },
    // 诊断实验
    goDiagnosis() {
      const row = this.addData;
      const hash = '/exp-manage/diagnosis';
      const params = {
        accSystem: row.accountSystem,
        id: row.id,
        name: encodeURIComponent(row.displayName)
      };
      const url = getUrlByLocation(hash, params);
      window.open(url);
    },
    // 固化feature
    turnFeature() {
      const row = this.addData;
      const isExtended = !!row.isExtended;
      if (isExtended) {
        this.$message.warning('当前实验已被继承，不支持固化至feature！');
        return;
      }
      const hash = '/feat/list/detail';
      const params = {
        id: row.correctId > 0 ? row.correctId : '', // 固化id
        experimentId: row.id, //实验id
        type: row.correctId > 0 ? 'detail' : ''
      };
      const url = getUrlByLocation(hash, params);
      window.open(url);
    },
    viewFeature() {
      const id = this.addData.correctId || '';
      const hash = '/feat/list/manage';
      const params = {
        id
      };
      const url = getUrlByLocation(hash, params);
      window.open(url);
    },
    // 创建继承实验
    createExtendExp() {
      const row = this.addData;
      const hasCorrect = !!row.hasCorrect;
      if (hasCorrect) {
        this.$message.warning('当前实验已固化，不支持继承');
        return;
      }
      if (!!row.hasRunningChild) {
        this.$message.warning('当前实验存在未结束的子实验，不支持继承');
        return;
      }
      this.assembleHistoryDay(row);
      const hash = '/exp-manage/list/edit';
      const params = {
        id: row.id,
        type: 'edit',
        ex: row.type,
        isExtend: 1
      };
      if (this.parentId / 1) {
        params.parentId = this.parentId;
      }
      this.$router.push({ path: hash, query: params });
    },
    // 组装实验历史需要的部分数据
    assembleHistoryDay(row) {
      localStorage.setItem('displayName', row.displayName);
      const statusVal = this.handleStatus(row.status);
      localStorage.setItem('status', statusVal);
    },
    handleStatus(status) {
      //const statusData = statusList;
      return this.statusMap[status];
    },
    createChildExp() {
      const row = this.addData;
      const hash = '/exp-manage/list/add';
      const params = {
        type: 'add',
        parentId: row.id,
        ex: row.type
      };
      this.$router.push({ path: hash, query: params });
    },
    // 编辑实验
    editExp() {
      const row = this.addData;
      this.assembleHistoryDay(row);
      editExp(row, this.$router, this.$service, this.$message);
    },
    editPublish() {
      const row = this.addData;
      const path = '/exp-manage/list/publish';
      const query = {
        id: row.id
      };
      if (row.pushId) {
        query.pushId = row.pushId;
      }
      this.$router.push({
        path,
        query
      });
    },
    copyExp() {
      const row = this.addData;
      this.assembleHistoryDay(row);

      // 如果当前实验parentId存在，则弹出弹窗
      if (row.parentId) {
        this.$confirm('当前实验为子实验，是否复制父子关系', '复制提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning',
          closeOnClickModal: false,
          distinguishCancelAndClose: true,
          customClass: 'ex-table-message'
        })
          .then(() => {
            // 用户点击"是"，继续复制操作
            this.doCopyExp(row, true);
          })
          .catch(action => {
            if (action === 'cancel') {
              // 用户点击"否"，不复制父子关系
              this.doCopyExp(row, false);
            } else {
              // 用户点击X关闭，取消复制操作
              this.$message({
                type: 'info',
                message: '已取消复制'
              });
            }
          });
      } else {
        // 不是子实验，直接复制
        this.doCopyExp(row, false);
      }
    },

    // 执行复制实验操作
    doCopyExp(row, keepParentRelation) {
      const hash = '/exp-manage/list/edit';
      const params = {
        id: row.id,
        type: 'edit',
        subType: 'copy',
        ex: row.type
      };

      // 如果需要保持父子关系，则添加parentId参数
      if (keepParentRelation && this.parentId / 1) {
        params.parentId = this.parentId;
      }

      const url = getUrlByLocation(hash, params);
      // window.open(url);
      location.href = url;
    },
    getType(type) {
      console.log('type', type);
    },
    async handleRollBack() {
      await this.$confirm(
        '回滚后，实验将作废发布计划，实验进入已回滚状态，在已回滚状态下实验执行发布前的实验策略，在已回滚状态下仅支持再次发布实验，请谨慎选择。',
        '实验回滚提示'
      );
      await this.$service.get('ROLLBACKPUSHEXP', {
        experimentId: this.addData.id,
        pushId: this.addData.pushId
      });
      await this.$router.push({
        path: '/exp-manage/list'
      });
    }
  },
  beforeDestroy() {
    // 组件销毁前需要重置store中的addData，否则再次打开新建还有
    this.$store.commit('resetAddData');
  }
};
</script>

<style lang="less" scoped>
.container {
  // overflow: auto;
  // height: calc(100vh - 50px);
  // background: transparent;
  // padding: 12px 16px;
  // box-sizing: border-box;
  // display: flex;

  /deep/ .page-container {
    padding-top: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .main {
    // height: calc(100vh - 210px);
    overflow: auto;

    &.edit-mode {
      // height: calc(100vh - 180px);
    }

    .report-tab.container {
      height: auto;
    }

    .content-container {
      display: flex;
      height: 100%;
      overflow: hidden;
      padding-right: 300px;
      position: relative;

      .content-left {
        flex: 1;
        height: 100%;
        overflow: auto;
      }

      .content-right {
        border: 1px solid #e1e3e9;
        border-radius: 4px;
        position: fixed;
        right: 48px;
        top: 130px;
        bottom: 36px;
        padding: 2px 6px !important;
        // height: 100%;
        margin: 0;

        &::-webkit-scrollbar {
          width: 0;
        }

        // height: calc(100vh - 180px);

        /deep/ .document-item {
          padding: 4px 0;
        }
      }
    }

    .base-form,
    .steps-content {
      margin-top: 0px;
      border: none;

      /deep/ h3 {
        font-size: 16px;
        font-weight: 700;
      }
    }
  }

  /deep/ .page-cont {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    margin: 0;
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    section {
      display: flex;
      align-items: center;
    }

    .right {
      /deep/ i {
        // display: inline-block;
        width: 16px;
        height: 16px;
        margin-left: 4px;
        margin-right: 6px;
        display: inline-block;
      }

      .el-button {
        margin-left: 12px;
      }

      .icon-yunhang {
        // width: 18px;
        // height: 18px;
      }
    }

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin: 0 8px;
    }

    i {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .el-tag {

      // @zyb-red-1: #fa574b;
      // @zyb-red-2: #f9685d;
      // @zyb-red-3: #de4f43;
      // @zyb-orange-1: #f78502;
      // @zyb-orange-2: #f9b001;
      // @zyb-green-4: #42c57a;
      // @zyb-lake-blue: #00cccd;
      // @zyb-purple: #8276de;
      // @zyb-blue: #5392ff;
      &.el-tag--default {
        color: #5392ff;
        background-color: #d9ecff;
        border-color: #d9ecff;
      }
    }
  }
}

.ex_steps {
  margin-top: 36px;
}

.op-btns {
  padding: 20px 0px;

  // .go-back-btn {
  //   float: right;
  // }
}

.stepSuc :hover {
  cursor: pointer;
}

.stepErr :hover {
  cursor: not-allowed;
}

/deep/ .el-step__head.is-success {
  color: #42c57a;
  border-color: #42c57a;
}

/deep/ .el-step__main .is-success {
  color: #42c57a;
}

/deep/ .el-step__icon {
  width: 32px;
  height: 32px;
}

/deep/ .el-step.is-horizontal .el-step__line {
  top: 15px;
}
</style>

<style lang="less">
.code-example-drawer {
  .el-drawer__header {
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 500;
    color: black;

    span {
      font-weight: bold;
    }
  }
}

.icon2-daimashili {
  position: relative;
  top: -1px;

  &::before {
    font-size: 14px;
  }
}
</style>
