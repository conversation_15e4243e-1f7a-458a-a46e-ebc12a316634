import { formatFormData } from '@/utils/util';

export const canStartExp = async (row, option = {}) => {
  const { service, alert, prompt } = option;
  const { needProcess, processSwitch } = await service.get('CHECKAPPSTATUS', { experimentId: row.id });
  if (!needProcess) {
    return {
      valid: true
    };
  }
  if (!processSwitch) {
    setTimeout(async () => {
      // 开启实验并且达到试验上限了 并且流程关闭
      const htmlString = `
    <section class="suoliang-tips">
    <p>当前业务线运行实验数量已达上限，不支持开启新的实验。</p>
    <p>建议关闭已得出结果的实验。</p>
    <p>或者联系业务线负责人调整实验数量运行最大上限。</p>
    </section>
    `;
      await alert(
          htmlString,
          '实验开启提示',
          {
            dangerouslyUseHTMLString: true,
          }
      );
    }, 0);
    return {
      valid: false
    };
  }
  // 开启实验并且达到试验上限了 并且流程启用
  const htmlString = `
<section class="suoliang-tips">
<p>当前业务线运行实验数量已达上限，不支开启新的实验。</p>
<p>您可以关闭已得出结果的实验。</p>
<p>如果您无法关闭其他实验，可以选择申请发布新的实验，提交申请后，需由业务线负责人审批，审批通过后，即可成功开启实验。确定提交申请吗？</p>
</section>
`;
  // 是否开启了实验缩量流程
  const { value } = await prompt(
    htmlString,
    '实验开启提示',
    {
      showInput: true,
      dangerouslyUseHTMLString: true,
      inputType: 'textarea',
      inputPlaceholder: '请输入开启实验原因',
      inputErrorMessage: '开启实验原因不可为空',
      inputPattern: /\S/
    }
  );
  return {
    valid: true,
    reason: value
  };
};


export const editExp = async (row, router, service, message) => {
  const data = formatFormData({
    id: row.id
  });
  const { canEdit= true, cause= ''} = await service.post('PRECHECK', data);
  if(canEdit){
    const hash = '/exp-manage/list/edit';
    const params = {
      id: row.id,
      type: 'edit',
      ex: row.type
    };
    if (row.parentId / 1) {
      params.parentId = row.parentId;
    }
    router.push({ path: hash, query: params });
  }else{
    message.error(cause);
  }
};
