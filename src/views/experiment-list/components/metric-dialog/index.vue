<template>
  <el-dialog :title="title" :visible="dialogVisible" :before-close="closeDialog">
    <template>
      <el-form ref="metricForm" label-width="80px" label-suffix="" :model="formData" :rules="rules">
        <el-form-item label="业务线" prop="appKey">
          <!-- 由于在实验第一步已经选择了业务线所以进入第二步的默认选中，且不能修改 -->
          <el-select v-model="formData.appKey" filterable :disabled="true">
            <el-option v-for="item in appList" :key="item.appKey" :value="item.appKey"
              :label="item.displayName"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="指标名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入名称，支持50个以内字符" type="textarea" :maxlength="50"
            :rows="1"></el-input>
        </el-form-item>

        <el-form-item label="指标描述" prop="description">
          <el-input v-model="formData.description" placeholder="请输入描述信息，支持1000个以内字符" type="textarea" :maxlength="1000"
            :rows="4"></el-input>
        </el-form-item>

        <el-form-item label="指标类型" prop="description">
          <el-radio-group v-model="formData.type" :disabled="isEdit">
            <el-radio :label="1">单一指标</el-radio>
            <el-radio :label="2">
              组合指标
              <el-tooltip content="针对多个事件，可选择多指标组合" placement="top" effect="light">
                <i class="info_icon">i</i>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 设置指标 -->
        <set-metric ref="setMetric" :metricType="formData.type" :event="formData.event || []" :reset="reset"
          :disabled="isEdit" :isEdit="isEdit"></set-metric>

        <el-form-item label="数值格式">
          <el-select v-model="formData.dataFormat" style="width: 100px">
            <el-option label="数字" :value="1"></el-option>
            <el-option label="百分比" :value="2"></el-option>
          </el-select>
          <span class="form_label">小数位数</span>

          <el-input-number v-if="formData.dataFormat == 1" v-model="formData.digit" :max="5" :min="0"
            controls-position="right" @change="(val) => !val && val !== 0 && (formData.digit = -1)"></el-input-number>
          <span v-if="formData.dataFormat == 1" class="form_label">示例：{{ examples }}</span>
          <el-input-number v-if="formData.dataFormat == 2" v-model="formData.digit" :max="5" :min="0"
            controls-position="right"></el-input-number>
          <span v-if="formData.dataFormat == 2" class="form_label">示例：{{ examples }}</span>
        </el-form-item>

        <el-form-item v-if="formData.type === 2" label="指标关系" prop="relation">
          <el-input v-model="formData.relation" placeholder="请输入组合指标计算公式，示例：A/B+C" style="width: calc(100% - 35px)"
            :disabled="isEdit"></el-input>
          <el-tooltip content="允许事件编号大写字母、输入括号()、加号+、减号-、乘号*、除号/;计算公式可任意组合，仅支持一层括号的计算" placement="top" effect="light">
            <i class="info_icon">i</i>
          </el-tooltip>
        </el-form-item>

        <el-form-item label="必看指标">
          <!-- <el-tooltip
            content="当前只有集团管理员、应用管理员，可以将某个指标设置为必看指标。"
            placement="top"
            effect="light"
          > -->
          <el-switch v-model="formData.isMust" active-text="是" inactive-text="否"></el-switch>
          <!-- </el-tooltip> -->
          <!-- <el-tooltip
            popper-class="metric_el-tooltip"
            content="必看指标，指的是必须守护的业务线指标，实验功能可能对其无直接的因果关联、无法直接带来提升，但一般而言不能对其有显著负向影响。若某个指标被设置为必看指标，则该应用下的每个实验都会默认选择该必看指标为实验关注指标。 当前只有集团管理员、应用管理员，可以将某个指标设置为必看指标。"
            placement="top"
            effect="light"
          >
            <i class="info_icon">?</i>
          </el-tooltip> -->
        </el-form-item>

        <!-- <el-alert
          title="默认为“非必看指标”，如需设置为必看指标，请联系管理员"
          type="warning"
          show-icon
        ></el-alert> -->
      </el-form>
    </template>

    <!-- 底部 -->
    <template slot="footer">
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" v-if="!isEdit" @click="metricSave('next')">添加下一个</el-button>
      <el-button type="primary" @click="metricSave('save')">保存</el-button>
    </template>
  </el-dialog>
</template>

<script>
import SetMetric from './set-metric';
import { mapGetters } from 'vuex';
import { deepClone } from '@/utils/util.js';

export default {
  components: {
    SetMetric
  },
  props: {
    metricId: Number,
    dialogVisible: {
      type: Boolean,
      default: false
    },
    businessOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      percent: '10.2456',
      //businessOptions: [],
      formData: {
        appKey: '', // 由于在实验第一步的时候已经选定了appkey，所以不能修改
        name: '',
        description: '',
        type: 1,
        dataFormat: 1,
        digit: 4,
        relation: '',
        isMust: false,
        id: 0 // 新增的时候id为0
      },
      rules: {
        name: [{ required: true, message: '请输入指标名称' }],
        appKey: [{ required: true, message: '请选择业务线' }]
      },
      reset: false
    };
  },
  computed: {
    ...mapGetters(['addData', 'metricData', 'metricTableData', 'appList'], 'message'),
    isEdit() {
      return !!this.metricId;
    },
    title() {
      return this.metricId ? '修改指标' : '新增指标';
    },
    examples() {
      if (this.formData.dataFormat == 1) {
        const str = '1,024.56789';
        const index = this.formData.digit === 0 ? 5 : 6 + this.formData.digit;
        return str.substring(0, index);
      } else {
        const str = '10.24567';
        const index = this.formData.digit === 0 ? 2 : 3 + this.formData.digit;
        return str.substring(0, index) + '%';
      }
    }
  },
  watch: {
    'addData.appKey': {
      handler(newVal, oldVal) {
        //debugger;
        if (newVal) {
          this.formData.appKey = newVal;
        }
      },
      deep: true,
      immediate: true // 不添加还不行
    }
  },
  created() {
    //this.fetchAppList();
    if (this.metricId) {
      this.fetchMetricDetail();
    }
  },
  methods: {
    fetchMetricDetail() {
      this.$service
        .get(
          'INDICATOR_INFO',
          { id: this.metricId },
          {
            allback: 0,
            needLoading: true
          }
        )
        .then((res) => {
          //debugger;
          const data = deepClone(res);
          const result = this.formatResData(data);
          this.formData = result;
        });
    },
    formatResData(data) {
      data.isMust = data.isMust === 1 ? true : false;
      return data;
    },
    resetFormData() {
      this.formData = {
        appKey: this.businessOptions.length ? this.businessOptions[0].appKey : '',
        name: '',
        description: '',
        type: 1,
        dataFormat: 1,
        digit: 4,
        relation: '',
        isMust: false,
        id: 0 // 新增的时候id为0
      };
      this.reset = !this.reset;
      this.$nextTick(this.$refs['metricForm'].clearValidate);
    },
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    // 转换成formData格式提交给后台
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    metricSave(type) {
      let result1;
      this.$refs['metricForm'].validate((valid) => {
        if (valid) {
          result1 = this.formData;
        }
      });
      if (!result1) return;
      let params = {};
      this.formData.isMust = this.formData.isMust ? 1 : 0;
      if (!this.isEdit) {
        const result2 = this.$refs['setMetric'].validateEvents();
        if (!result2) return;

        params = {
          ...this.formData,
          event: result2
        };
      } else {
        params = {
          ...this.formData
        };
      }

      //debugger
      this.metricId && (params.id = this.metricId / 1);
      const message = this.metricId ? '更新成功' : '新建成功';
      const resultFormData = this.formatFormData(params);
      //debugger;
      this.$service.post('INDICATOR_EDIT', resultFormData, { needLoading: true }).then((res) => {
        this.$message.success(message);
        if (type === 'save') {
          this.$emit('fetchMmetricList');
          this.closeDialog();
          // 新建成功之后刷新列表
        } else {
          this.$emit('fetchMmetricList');
          this.resetFormData();
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  max-height: 80% !important;
  // width: 760px;
  // min-width: 30%;
}

.info_icon {
  display: inline-block;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  border: 1px solid #999999;
  text-align: center;
  color: #999999;
  line-height: 13px;
  margin: 0 8px;
  box-sizing: content-box;
}

.form_label {
  color: #777;
  font-size: 12px;
  padding: 0 8px;
}

/deep/ .el-tooltip__popper {
  width: 300px;
}

/deep/ .el-radio__label {
  font-size: 14px;
}
</style>