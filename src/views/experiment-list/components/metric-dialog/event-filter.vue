<template>
  <div class="event_filter">
    <el-select
      v-if="!isEdit"
      v-model="filter.paramName"
      class="fidld_select"
      placeholder="请选择属性"
      filterable
      :disabled="disabled"
      @change="paramNameChange"
    >
      <el-option
        v-for="(item, index) in options"
        :key="index"
        :label="item.param_cname"
        :value="item.param_name"
      ></el-option>
    </el-select>
    <el-input
      v-model="filter.paramCname"
      placeholder=""
      class="fidld_select"
      v-if="isEdit"
      :disabled="true"
    ></el-input>

    <el-select
      v-model="filter.calcType"
      class="condition_select"
      filterable
      :disabled="disabled"
      @change="calcTypeChange"
    >
      <el-option
        v-for="(item, index) in calcOptions"
        :key="index"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </el-select>

    <el-select
      v-if="!['empty', 'notempty'].includes(filter.calcType)"
      v-model="filter.filterValue"
      class="value_select"
      filterable
      multiple
      allow-create
      default-first-option
      :disabled="disabled"
    >
      <el-option
        v-for="(item, index) in valueOptions[filter.paramName] || []"
        :key="index"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </el-select>

    <i
      v-if="!disabled"
      class="filter_item_delete_icon el-icon-delete"
      @click="deleteFilter"
    ></i>
  </div>
</template>

<script>
export default {
  props: {
    filter: {
      type: Object,
      default() {
        return {};
      }
    },
    options: {
      type: Array,
      default() {
        return [];
      }
    },
    calcOptions: {
      type: Array,
      default() {
        return [];
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      valueOptions: []
    };
  },
  methods: {
    paramNameChange(val) {
      const opt = this.options.find((f) => f.param_name === val);
      this.valueOptions = opt.param_value_enums ? JSON.parse(opt.param_value_enums) : [];
      this.filter.filterValue = [];
      this.filter.paramGid = opt.param_gid;
      this.filter.paramCname = opt.param_cname;
    },
    calcTypeChange(val) {
      if (['empty', 'notempty'].includes(val)) {
        this.filter.filterValue = [];
      }
    },
    deleteFilter() {
      this.$emit('delete-filter');
    }
  }
};
</script>

<style lang="less" scoped>
.event_filter {
  margin-top: 18px;
}

.fidld_select {
  width: 120px;
  margin-right: 8px;
}

.condition_select {
  width: 120px;
  margin-right: 8px;
}

.value_select {
  width: 180px;
}

.filter_item_delete_icon {
  color: #83868f;
  font-size: 16px;
  cursor: pointer;
  margin-right: 16px;
  vertical-align: middle;
  margin-left: 10px;
}
/deep/ .el-select-dropdown__wrap {
  max-width: 300px;
}
</style>