<template>
  <div class="section-container">
    <section class="container">
      <p>发布策略累计发布用户数</p>
      <p>{{detail.totalNum}}</p>
    </section>
    <section ref='myChart' class="chart-container"></section>
  </div>
</template>

<script>
import dayjs from 'dayjs';

export default {
  name: 'PublishDetail',
  props: {
    id: Number,
    pushId: Number
  },
  data() {
    return {
      detail: {
      },
      mounted: false
    };
  },
  computed: {
  },
  created() {
    this.getPushDetail();
  },
  mounted() {
    this.mounted = true;
    if(this.detail.detailList){
      this.drawEchart();
    }
  },
  methods: {
    drawEchart() {
      let myChart = this.$echarts.init(this.$refs.myChart);
      const xAxisData = this.detail.detailList.map(item=>dayjs.unix(item.time).format('MM-DD'));
      const yAxisData = this.detail.detailList.map(item=>item.num);
      let option = {
        title: {
          text: '每日发布用户量'
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          show: true,
        },
        // series: this.yAxisData,
        series: [
          {
            name: '版本名称',
            type: 'line',
            data: yAxisData
          },
        ],
      };
      myChart.setOption(option);
    },
    async getPushDetail() {
      const res = await this.$service.get('VIEWPUSHREPORT', {
        experimentId: this.id,
        pushId: this.pushId
      });
      this.detail = {
        ...res
      };
      if(this.mounted){
        this.drawEchart();
      }
    },
  }
};
</script>

<style lang="less" scoped>
.section-container {
  min-height: 80vh;
  .container{
    background: rgba(66, 197, 122, 0.2);
    padding: 16px 24px;
    opacity: 0.9;
    border-radius: 5px;
    p{
      font-weight: bold;
      line-height: 36px;
      font-size: 16px;
      &:last-child{
        font-size: 28px;
      }
    }
  }
  .chart-container {
  width: 100%;
  height: 400px;
  margin: 20px auto;
}
}
</style>
