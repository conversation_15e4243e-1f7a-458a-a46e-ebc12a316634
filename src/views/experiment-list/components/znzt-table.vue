<template>
  <table cellspacing="0" cellpadding="0" border="0" style="width: 100%" class="znzt-table">
    <thead>
      <tr>
        <th v-for="col in tableColumns" :key="col.label" class="is-center">
          <div class="cell">
            {{ col.label }}
          </div>
        </th>
      </tr>
    </thead>

    <tbody>
      <tr class="" v-for="tdata in tableData" :key="tdata.versionId">
        <td rowspan="1" colspan="1" class="is-center">
          <div class="cell">{{ tdata.displayName }}</div>
        </td>
        <td rowspan="1" colspan="1" class="is-center">
          <div class="cell">{{ tdata.versionId }}</div>
        </td>
        <td rowspan="1" colspan="1" class="is-center">
          <div class="cell">
            <p class="keyName" v-for="(kn, idx) in tdata.keyName" :key="idx">
              {{ kn }}
            </p>
          </div>
        </td>
        <td rowspan="1" colspan="1" class="is-center">
          <div class="cell">
            <p class="keyValue" v-for="(kv, index) in tdata.keyValue" :key="index">
              {{ kv }}
            </p>
          </div>
        </td>
        <td rowspan="1" colspan="1" class="is-center">
          <div class="cell">{{ (tdata.flow / 1000 * 100).toFixed(2) }}%</div>
        </td>
        <td rowspan="1" colspan="1" class="is-center">
          <div class="cell">{{ ((tdata.flow / 1000 * 100) * (flow / 1000)).toFixed(2) }}%</div>
        </td>
      </tr>
    </tbody>
  </table>
</template>
<script>
export default {
  name: 'znzt-table',
  components: {},
  props: {
    tableColumns: {
      type: Array,
      default: function () {
        return [];
      }
    },
    tableData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    flow: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {};
  },
  computed: {},
  mounted() { },
  methods: {}
};
</script>

<style lang="less" scoped>
.znzt-table th {
  white-space: nowrap;
  overflow: hidden;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  //background-color: #f7f8fb;
  font-weight: 400;
}

.znzt-table td,
.znzt-table th {
  height: 40px;
  padding: 8px 0;
  min-width: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  text-align: left;
}

.znzt-table td.is-center,
.znzt-table th.is-center {
  text-align: center;
}

.znzt-table .cell {
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 7;
  display: -webkit-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  line-height: 20px;
  padding-left: 16px;
  padding-right: 16px;
  max-height: 140px;
  min-width: 120px;
}

.znzt-table td,
.znzt-table th,
.znzt-table th>.cell {
  position: relative;
  vertical-align: middle;
  text-overflow: ellipsis;
}

.znzt-table th>.cell,
.znzt-table th div {
  display: inline-block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.znzt-table tbody tr:hover td {
  background-color: #f5f7fa;
}

.znzt-table th,
.znzt-table td {
  border-bottom: 1px solid #ebeef5;
}

.znzt-table tbody tr td .keyValue,
.znzt-table tbody tr td .keyName {
  border-bottom: 1px solid #ebeef5;
}

.znzt-table tbody td .keyValue:last-child,
.znzt-table tbody td .keyName:last-child {
  border-bottom: none;
}
</style>
