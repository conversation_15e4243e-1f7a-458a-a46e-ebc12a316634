<template>
  <div class="ex-graph">
    <div class="h1">父子实验流量继承图</div>
    <div class="h2">
      <div>父实验</div>
      <div>当前子实验</div>
    </div>
    <div id="myChart" :style="{height: chartHeight + 'px'}"></div>
  </div>
</template>

<script>
export default {
  props: {
    fatherList: {
      type: Array,
      default: () => [],
    },
    addList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      myChart: null,
      chartHeight: 300,
      options: {
        tooltip: {
          formatter: function (params) {
            let str;
            if (params.data.target) {
              str = `${params.data.sourceName} > ${params.data.targetName}`;
            } else {
              str = `${params.data.dataRef.displayName}`;
            }
            return str;
          },
        },
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: [
          {
            type: 'graph',
            layout: 'none',
            symbolSize: 70,
            roam: false,
            edgeSymbol: ['circle', 'arrow'],
            edgeSymbolSize: [4, 10],
            edgeLabel: {
              fontSize: 20,
            },
            label: {
              show: true,
              width: 70,
              fontSize: 12,
              formatter: function (params) {
                return `${params.data.dataRef.displayName}\n${params.data.dataRef.flow}%`;
              },
              color: 'white',
              lineHeight: 18,
              overflow: 'truncate',
            },
            data: [],
            links: [],
            lineStyle: {
              opacity: 0.6,
              width: 4,
              color: '#C8CFDF',
              curveness: 0,
            },
          },
        ],
      },
    };
  },
  watch: {
    fatherList: {
      handler(newVal) {
        this.setChart();
      },
      deep: true,
    },
    addList: {
      handler(newVal) {
        this.setChart();
      },
      deep: true,
    },
  },
  methods: {
    initCharts() {
      const myChart = this.$echarts.init(document.getElementById('myChart'));
      this.myChart = myChart;
    },
    setChart() {
      if (!this.myChart || !this.fatherList.length || !this.addList.length) return;
      const father = this.fatherList
        .filter((f, i) => i !== 0)
        .map((v, i) => {
          return {
            name: 'f' + v.displayName,
            dataRef: v,
            itemStyle: {
              normal: {
                borderWidth: 4,
                shadowBlur: 10,
                shadowColor: '#5C7AD4',
                color: '#5C7AD4',
              },
            },
            x: 50,
            y: 50 + 100 * i,
          };
        });
      const child = this.addList
        .filter((f, i) => i !== 0)
        .map((v, i) => {
          return {
            name: 'c' + v.displayName,
            dataRef: v,
            itemStyle: {
              normal: {
                borderWidth: 4,
                shadowBlur: 10,
                shadowColor: '#09bcb7',
                color: '#09bcb7',
              },
            },
            x: 550,
            y: 50 + 100 * i,
          };
        });
      const data = [...father, ...child];
      const links = [];
      this.addList
        .filter((f, i) => i !== 0)
        .forEach((v) => {
          // console.log("relationIds");
          // console.log(Array.isArray(v.relationIds))
          //debugger;
          Array.isArray(v.relationIds) && v.relationIds.forEach((c) => {
            const obj = this.fatherList.find((f) => f.versionId == c);
            //debugger;
            links.push({
              source: 'f' + obj.displayName,
              target: 'c' + v.displayName,
              sourceName: obj.displayName,
              targetName: v.displayName,
            });
          });
        });

      this.options.series[0].data = data;
      this.options.series[0].links = links;
      const len = Math.max(this.fatherList.length, this.addList.length) - 1;
      this.chartHeight = 100 + len * 100;
      this.myChart.resize({height: 100 + len * 100});
      this.myChart.setOption(this.options);
    },
  },

  mounted() {
    this.initCharts();
    this.setChart();
  },
};
</script>

<style lang="less" scoped>
.ex-graph {
  width: 600px;
  margin-top: 24px;
}

.h1 {
  font-size: 14px;
  color: #606266;
  font-weight: bold;
  padding-left: 30px;
}

.h2 {
  display: flex;
  justify-content: space-between;
  padding: 0 30px;
  color: #adadad;
  margin-top: 16px;
}
</style>