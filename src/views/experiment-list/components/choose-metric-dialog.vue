<template>
  <el-dialog title="添加指标" :visible="visible" :before-close="dialogClose" width="720px">
    <section>
      <el-input v-model="filter" placeholder="请输入指标名称"></el-input>
      <section class="metric-list">
        <span class="title">必看指标</span>
        <el-checkbox-group v-model="checkList">
          <el-checkbox v-for="item in mustItems" :label="item.id" :key="item.id" :disabled="selectedList.includes(item.id)">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </section>
      <section class="metric-list">
        <span class="title">非必看指标</span>
        <el-checkbox-group v-model="checkList">
          <el-checkbox v-for="item in notMustItems" :label="item.id" :key="item.id" :disabled="selectedList.includes(item.id)">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </section>
    </section>
    <!-- 确定取消按钮组 -->
    <section slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogClose">取 消</el-button>
      <el-button size="small" type="primary" @click="confirm">
        确定
      </el-button>
    </section>
  </el-dialog>
</template>
<script>
export default {
  name: 'choose-metric-dialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: [],
    },
    selectedList: {
      type: Array,
      default: [],
    },
  },
  computed: {
    filterList() {
      if (this.filter) {
        return this.list.filter(item => item.name.indexOf(this.filter) > -1);
      }
      return this.list;
    },
    mustItems() {
      return this.filterList.filter(item => item.isMust === 1);
    },
    notMustItems() {
      return this.filterList.filter(item => item.isMust === 0);
    },
  },
  data() {
    return {
      filter: '',
      checkList: [...this.selectedList]
    };
  },
  mounted() {
    console.log('list', this.list);
    console.log('selectedList', this.selectedList);
  },
  methods: {
    dialogClose() {
      this.$emit('update:visible', false);
    },
    confirm(){
      this.$emit('update', this.checkList);
      this.$emit('update:visible', false);
    }
  },
};
</script>

<style lang="less" scoped>
.metric-list {
  margin-top: 24px;

  .title {
    margin-top: 12px;
    font-size: 14px;
    font-weight: 700;
    position: relative;
    padding-left: 12px;

    &::before {
      content: ' ';
      display: inline-block;
      position: absolute;
      left: 0px;
      width: 4px;
      height: 12px;
      background-color: #42c57a;
    }
  }

  .el-checkbox-group {
    margin-top: 6px;
    max-height: 320px;
    overflow: auto;

    .el-checkbox {
      margin-top: 8px;
      margin-right: 16px;

      /deep/ .el-checkbox__label {
        text-overflow: ellipsis;
        overflow: hidden;
        width: 170px;
        vertical-align: middle;
      }
    }
  }
}
</style>
