<template>
  <section class="exp-document">
    <el-row :gutter="20" style="width: 100%">
      <el-col v-for="(version, index) in versionList" :key="version.versionId" :xs="24" :sm="12" :md="8" :lg="6"
        :xl="4">
        <el-card class="experiment-card" :header="false">
          <section class="content">
            <div class="card-header" :style="{ backgroundColor: getRandomColor(index) }">
              <span class="title">{{ version.displayName }}({{ version.versionId }})</span>

              <div class="action-icons">
                <el-tooltip content="查看白名单" placement="top">
                  <span class="iconfont4 icon-chakanbaimingdan" style="font-size: 16px;"
                    @click="showViewDialog(version)"></span>
                </el-tooltip>
                <el-tooltip content="配置白名单" placement="top">
                  <span class="iconfont4 icon-peizhibaimingdan" style="font-size: 16px;"
                    @click="showConfigDialog(version)"></span>
                </el-tooltip>
                <el-tooltip content="移除白名单" placement="top">
                  <i class="el-icon-delete" style="font-size: 16px;" @click="showRemoveDialog(version)"></i>
                </el-tooltip>
              </div>
            </div>
            <div class="card-content">
              <div class="param-item">
                <label>参数名称:</label>
                <span class="truncate">{{ version.featureKey }}</span>
              </div>
              <div class="param-item">
                <label>参数值:</label>
                <span class="truncate">{{ version.featureValue }}</span>
              </div>
              <div class="param-item">
                <label>实验流量:</label>
                <span>{{ (version.experimentFlow / 10).toFixed(2) }}%</span>
              </div>
              <div class="param-item">
                <label>流量分配占比:</label>
                <span>{{ (version.versionFlow / 10).toFixed(2) }}%</span>
              </div>
              <div class="param-item">
                <label>实际生效流量:</label>
                <span>{{ (version.versionFlow * version.experimentFlow / 10000).toFixed(2) }}%</span>
              </div>
            </div>
          </section>
        </el-card>
      </el-col>
    </el-row>
    <!-- Config Whitelist Dialog -->
    <el-dialog :title="isCheck ? '查看白名单' : '配置白名单'" :visible.sync="configDialogVisible" v-if="configDialogVisible"
      width="50%">
      <el-form label-position="left">
        <el-form-item label="">
          <el-select :value="currentVersion.displayName" style="width: 100%" disabled>
            <el-option :label="currentVersion.displayName" :value="currentVersion.displayName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <t-cascader trigger="click" v-model="currentVersion.whitelist" multiple clearable filterable
            :show-all-levels="false" :options="getWhiteListOptions" style="width: 100%" :disabled="isCheck">
            <template #valueDisplay="{ value, selectedOptions, onClose }">
              <template v-if="value && value.length">
                <t-tag v-for="(option, index) in selectedOptions" :key="option.value" closable
                  @close="() => onClose(index)">
                  <t-tooltip :content="option.label">
                    <span class="cascader-item-name">{{ option.label }}</span>
                  </t-tooltip>
                </t-tag>
              </template>
            </template>
          </t-cascader>
        </el-form-item>
      </el-form>
      <section slot="footer" class="dialog-footer" v-if="!isCheck">
        <el-button @click="configDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="updateWhiteList">确 定</el-button>
      </section>
    </el-dialog>
  </section>
</template>

<script>
import { formatFormData } from '@/utils/util';
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      versionList: [],
      currentVersion: {},
      viewDialogVisible: false,
      configDialogVisible: false,
      whitelistOptions: [],
      isCheck: false
    };
  },
  computed: {
    ...mapGetters(['whiteList']),
    allSelectedIds() {
      const res = [];
      this.versionList.forEach(item => {
        res.push(...item.whitelist);
      });
      return res;
    },
    getWhiteListOptions() {
      const whiteList = this.currentVersion.whitelist;
      const allIds = this.allSelectedIds;
      const res = this.whiteList.map(item => {
        return {
          value: 'group-' + item.whitelistGroupDisplayName,
          label: item.whitelistGroupDisplayName,
          children: item.whitelist.map(item => {
            return {
              value: item.id,
              label: item.displayName,
              disabled: !whiteList.includes(item.id) && allIds.includes(item.id)
            };
          }),
        };
      });
      console.log('hahhaha', res);
      return res;
    },
  },
  created() {
    this.loadMore();
  },
  methods: {
    getRandomColor(index) {
      const colors = ['#42c57a', 'rgba(30, 144, 255, 1)', 'rgba(0, 206, 209, 1)', 'rgba(255, 69, 0, 1)', 'rgba(255, 140, 0, 1)', 'rgba(255, 215, 0, 1)', 'rgba(144, 238, 144, 1)', 'rgba(0, 206, 209, 1)', 'rgba(199, 21, 133, 1)', 'rgba(255, 69, 0, 0.68)', 'rgba(199, 21, 133, 0.46)', 'rgba(255, 120, 0, 1)'];
      const length = colors.length;
      return colors[index % length];
    },
    truncateText(text, maxLength = 10) {
      return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
    },
    async loadMore() {
      const res = await this.$service.get('EXWHITELIST', {
        id: this.$route.query.id,
      });
      this.versionList = (res.versionList || []).map(version => {
        return {
          ...version,
          whitelist: (version.whitelist || []).map(item => {
            return item.whitelistId;
          })
        };
      });
    },
    showViewDialog(version) {
      this.currentVersion = version;
      this.isCheck = true;
      this.configDialogVisible = true;
    },
    showConfigDialog(version) {
      this.currentVersion = version;
      this.isCheck = false;
      this.configDialogVisible = true;
    },
    async updateWhiteList() {
      const { versionId, whitelist = [] } = this.currentVersion;
      const data = {
        versionId,
        id: this.$route.query.id,
        whitelist: JSON.stringify(whitelist.map(id => ({
          whitelistId: id
        })))
      };
      await this.updateWhiteListAction(data);
      this.loadMore();
      this.configDialogVisible = false;
    },
    async updateWhiteListAction(data) {
      const formData = formatFormData(data);
      await this.$service.post('UPDATEEXWHITELIST', formData);
    },

    async showRemoveDialog(version) {
      await this.$confirm('确定移除当前版本下的白名单吗？', '删除确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      });
      const data = {
        versionId: version.versionId,
        id: this.$route.query.id,
        whitelist: "[]"
      };
      await this.updateWhiteListAction(data);
      this.loadMore();
    }
  }
};
</script>

<style lang="less" scoped>
.experiment-card {
  margin-bottom: 20px;
  position: relative;
  min-height: 240px;

  /deep/ .el-card__body {
    padding: 0px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    color: white;

    .title {
      font-weight: bold;
      margin-right: 8px;
      font-size: 16px;
    }

    .action-icons {
      display: flex;
      gap: 8px;

      span,
      i {
        cursor: pointer;
      }
    }
  }

  .card-content {
    padding: 8px 12px;

    .param-item {
      margin: 12px 0;
      display: flex;
      align-items: center;
      line-height: 32px;
      font-size: 14px;

      label {
        width: 100px;
        color: #333;
        margin-right: 8px;
        font-weight: bold;
      }

      .truncate {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }
  }
}

.whitelist-item {
  padding: 8px;
  border-bottom: 1px solid #eee;
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa;
  }
}
</style>
<style>
.t-cascader  .t-input{
  max-height: 182px;
  overflow: auto;
}
</style>
