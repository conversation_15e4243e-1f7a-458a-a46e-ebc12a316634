<template>
  <div class="indicator-container">
    <!-- 实验指标 -->
    <div class="indicator-container-item">
      <div class="">实验指标</div>
      <div class="select-indicators">
        <div class="select-indicators-item">
          <span class="select-indicators-title">指标</span>
          <el-select v-model="indicatorType" size="mini" @change="handleIndicatorTypeChange">
            <el-option
              v-for="item in indicatorList"
              :key="item.indId"
              :label="item.indName"
              :value="item.indId"
            ></el-option>
          </el-select>
        </div>
        <div class="indicator-types">
          <span>
            <i class="indicator-forward"></i>
            95%正向显著
          </span>
          <span>
            <i class="indicator-negative"></i>
            95%负向显著
          </span>
          <span>
            <i class="indicator-non-significant"></i>
            不显著
          </span>
        </div>
      </div>
      <!-- 实验指标表格 -->
      <foundation-experiment-table
        :tableData="searchResult"
        :choosedIndicator="choosedIndicator"
      ></foundation-experiment-table>
    </div>
    <!-- 统计值 -->
    <div class="indicator-container-item">
      <foundation-experiment-statistic
        :timeLevelType="timeLevelType"
        :experimentData="experiment"
        :trendResultData="trendResult"
        @searchDataType="getSearchDataType"
      ></foundation-experiment-statistic>
    </div>
  </div>
</template>
<script>
import foundationExperimentTable from './foundation-experiment-table.vue';
import foundationExperimentStatistic from './foundation-experiment-statistic.vue';
export default {
  name: 'experiment-indicators',
  components: {foundationExperimentTable, foundationExperimentStatistic},
  props: {
    timeLevelType: {
      type: Number,
      default: 0,
    },
    experiment: {
      type: Object,
      default: () => ({}),
    },
    searchResult: {
      type: Array,
      default: () => {
        [];
      },
    },
    trendResult: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      indicatorType: '',
      choosedIndicator: {}, // 选中的指标
      indicatorList: [], // 表格数据
    };
  },
  created() {
    if (this.experiment.id) {
      const param = {
        experimentId: this.experiment.id,
      };
      this.getIndicatorList(param);
    }
  },
  watch: {
    experiment(val) {
      const param = {
        experimentId: val.id,
      };
      this.getIndicatorList(param);
    },
  },
  methods: {
    // 实验指标选择改变
    handleIndicatorTypeChange(val) {
      this.$emit('indicatorTypeChange', val);
      this.indicatorList.forEach((item) => {
        if (item.indId === this.indicatorType) {
          this.choosedIndicator = item;
        }
      });
    },
    //获取实验指标列表
    getIndicatorList(param) {
      this.$service
        .get('REPORT_FOUND_EXPINDLIST', {...param}, {needLoading: true})
        .then((res) => {
          this.indicatorList = res.indList;
          if (res.indList.length > 0) {
            this.$nextTick(() => {
              this.indicatorType = res.indList[0].indId;
              this.choosedIndicator = res.indList[0];
            });
          }
        })
        .catch(() => {});
    },
    // 实验天/时级 或者累计
    getSearchDataType(param) {
      this.$emit('getSearchDataType', param);
    },
  },
};
</script>
<style scoped>
.indicator-container-item {
  font-size: 14px;
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #f0f1f5;
}
.select-indicators {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
}
.indicator-forward {
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: rgb(31, 212, 181);
}
.indicator-negative {
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: rgb(237, 84, 107);
}
.indicator-non-significant {
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: rgb(47, 47, 63);
}

.select-indicators-title {
  margin-right: 12px;
}
</style>


