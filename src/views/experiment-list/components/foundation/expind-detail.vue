<template>
  <el-table :data="tableList" class="childTable" :show-header="false">
    <el-table-column label="实验分组" align="center" prop="date" width="120"></el-table-column>
    <el-table-column label="进组人数" align="center" prop="num" width="200">
      <template slot-scope="scope">
        <span>{{ scope.row.num }}</span>
        <span class="group-number-percent">{{ scope.row.numPersent }}%</span>
      </template>
    </el-table-column>
    <el-table-column align="center" width="230">
      <template slot-scope="scope">
        {{ scope.row.indValue }}
      </template>
    </el-table-column>
    <el-table-column align="center" prop="diffAbsVal" width="200">
      <template slot-scope="scope">
        {{ scope.row.diffAbsVal }}
      </template>
    </el-table-column>
    <el-table-column align="center" prop="diffRelaVal" width="200">
      <template slot-scope="scope">
        {{ scope.row.diffRelaVal }}
      </template>
    </el-table-column>
    <el-table-column align="center" prop="confidInterval" width="240" class="se-con">
      <template slot-scope="scope">
        <!-- 如果index不等0（即不是对照组）的话才用显示置信区间 -->
        <template v-if="scope.row.index !== 0">
          <template v-if="scope.row.isShowCon">

            <confidence
                      :min="scope.row.confidInterval.min"
                      :max="scope.row.confidInterval.max"
                      :pValue="scope.row.pValue"
                      :diffRelaVal="scope.row.diffRelaVal"
                      :isConfidence="scope.row.isConfidence"
                      :color="scope.row.color"
                    ></confidence>
            <!-- <p class="p-con">
              [
              <span>{{ scope.row.confidInterval.min }}%</span>
              ,
              <span>{{ scope.row.confidInterval.max }}%</span>
              ]
            </p> -->
          </template>

          <template v-else>-</template>
        </template>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="pValue" width="200"></el-table-column>
    <el-table-column align="center" prop="MDE" width="200"></el-table-column>
  </el-table>
</template>
<script>
import confidence from './confidence.vue';
export default {
  name: 'expind-detail',
  components: {confidence},
  data() {
    return {};
  },
  computed: {},
  props: {
    tableList: {
      type: Array
    }
  },
  mounted() {},
  methods: {},
  filters: {
    filterExpType: function (val) {}
  }
};
</script>

<style lang="less" scoped>
.group-number-percent {
  margin-left: 8px;
  color: rgb(139, 139, 166);
  font-weight: bold;
}
</style>
