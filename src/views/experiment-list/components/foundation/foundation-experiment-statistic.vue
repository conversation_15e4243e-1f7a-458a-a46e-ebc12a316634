<template>
  <div class="foundation-experiment-statistic">
    <div class="statistic-notice" v-show="isStatisticTotal">
      <i class="el-icon-warning-outline"></i>
      <span>目前多天累积趋势不支持自定义时间筛选框和多维筛选分析。</span>
    </div>
    <!-- 按扭 -->
    <div class="statistic-select" v-if="timeLevel === 1">
      <el-radio v-model="radio" :label="1">统计值</el-radio>
      <!-- <el-radio v-model="radio" :label="2">p-value</el-radio> -->
      <!-- <span class="statistic-range">范围展示</span> -->
      <!-- <el-switch v-model="value1" :disabled="disabled"></el-switch> -->
      <!-- <span class="warning-notice" v-show="!isStatisticTotal"> -->
      <!-- <i class="el-icon-warning-outline warning-outline"></i> -->
      <!-- <span>暂时无置信区间，范围无法显示</span> -->
      <!-- </span> -->
    </div>

    <!-- 天级趋势数据/时级趋势数据 -->
    <div class="statistic-item" v-if="isStatisticDay">
      <!-- 无数据时展示图片 -->
      <div class="ant-empty" v-if="trendResultData.versionList.length < 0">
        <div class="ant-empty-image">
          <img src="@/static/images/no-data.jpeg" alt="" />
        </div>
        <p>尚无数据，请耐心等待实验运行</p>
      </div>
      <!-- 有数据时展示数据 -->
      <day-trend :timeLevel="timeLevel" v-else :trendResultData="trendResultData"></day-trend>
    </div>

    <!-- 累积趋势数据 -->
    <div class="statistic-item" v-if="isStatisticTotal">
      <!-- 无数据时展示图片 -->
      <div class="ant-empty" v-if="trendResultData.versionList.length < 0">
        <div class="ant-empty-image">
          <img src="@/static/images/no-data.jpeg" alt="" />
        </div>
        <p>累积趋势暂仅支持天极数据查询</p>
      </div>
      <!-- 有数据时展示数据 -->
      <total-trend :timeLevel="timeLevel" v-else :trendResultData="trendResultData"></total-trend>
    </div>

    <!-- 数据类型切换部分 -->
    <div class="statistic-type-change">
      <div
        :class="['statistic-type-change-item', {statistic_choosed: isStatisticDay}]"
        @click="choosedDay"
      >
        <div class="statistic-type-change-item-content">
          <i class="el-icon-time"></i>
          <span v-if="timeLevel === 1">天级趋势</span>
          <span v-if="timeLevel === 2">时级趋势</span>
        </div>
      </div>

      <div
        :class="['statistic-type-change-item', {statistic_choosed: isStatisticTotal}]"
        @click="choosedTotal"
      >
        <div class="statistic-type-change-item-content">
          <i class="el-icon-notebook-2"></i>
          <span>累积趋势</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import dayTrend from './day-trend.vue';
import totalTrend from './total-trend.vue';
export default {
  name: 'foundation-experiment-statistic',
  props: {
    timeLevelType: {
      type: Number,
      default: 0,
    },
    experimentData: {
      type: Object,
      default: () => ({}),
    },
    // trendResultData: {
    //   typw: Object,
    //   default: () => ({}),
    // },
  },
  components: {dayTrend, totalTrend},
  data() {
    return {
      radio: 1,
      value1: false,
      isStatisticDay: true, // 天级趋势
      isStatisticTotal: false,
      timeLevel: this.timeLevelType,
      disabled: false,
      trendResultData: {
        timeList: {
          '2021-07-01': [
            {
              versionId: 1,
              versionName: '版本名称',
              persent: 150,
              change: 8.121,
            },
            {
              versionId: 2,
              versionName: '对照实验',
              persent: 120,
              change: 8.121,
            },
          ],
          '2021-07-02': [
            {
              versionId: 1,
              versionName: '版本名称',
              persent: 150,
              change: 8.121,
            },
            {
              versionId: 2,
              versionName: '对照实验',
              persent: 120,
              change: 8.121,
            },
          ],
        },
        versionList: [
          {
            versionId: 1,
            versionName: '版本名称',
          },
          {
            versionId: 2,
            versionName: '对照实验',
          },
        ],
      },
    };
  },
  watch: {
    timeLevelType(val) {
      this.timeLevel = val;
    },
    radio(val) {
      if (val === 2) {
        this.disabled = true;
      } else {
        this.disabled = false;
      }
    },
  },
  methods: {
    choosedDay() {
      this.$emit('searchDataType', 'day');
      this.isStatisticDay = true;
      this.isStatisticTotal = false;
    },
    choosedTotal() {
      this.$emit('searchDataType', 'total');
      this.isStatisticDay = false;
      this.isStatisticTotal = true;
    },
  },
};
</script>
<style scoped lang="less">
.foundation-experiment-statistic {
  font-size: 14px;
}
.statistic-select {
  margin: 20px 0 10px 0;
}
.statistic-range {
  margin-left: 20px;
}

.statistic-item {
  height: 400px;
}
.ant-empty {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.ant-empty-image {
  height: 100px;
  margin-bottom: 80px;
}
.statistic-type-change {
  display: flex;
  justify-content: space-between;
}

.statistic-type-change-item {
  height: 55px;
  width: 50%;
  text-align: center;
  background: linear-gradient(rgba(118, 122, 138, 0.08) 1%, rgb(251, 251, 251));
  cursor: pointer;
}
.statistic_choosed {
  border-top: 4px solid #42c57a;
}

.statistic-type-change-item-content {
  padding-top: 20px;
}

.warning-notice {
  vertical-align: middle;
}

.warning-outline {
  margin: 0 0 0 10px;
}
</style>

