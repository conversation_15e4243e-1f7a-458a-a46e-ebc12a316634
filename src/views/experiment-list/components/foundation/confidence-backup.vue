<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-27 17:12:19
 * @LastEditors: z<PERSON><PERSON>e
 * @LastEditTime: 2022-01-05 17:23:25
 * @Description: 实验报告-实验关注指标-置信区间
-->

<template>
  <el-popover :close-delay="timess"  placement="right" width="300" trigger="hover">
    <div class="p-wrap">
      <div class="p-con"> 
        <p class="p-con-title">差异绝对值: </p>
        <!-- <p>差异绝对值: </p>
        <p class="nonal"></p> -->
        <span class="nonal">{{diffAbsVal}}</span> 
      </div>
      <div class="p-con"> 
        <p class="p-con-title">差异相对值:</p>
        <span class="nonal">{{diffRelaVal}}</span>
      </div>
      <div v-if="isShowCon" 
        class="p-con"
      >
        <p class="p-con-title">p-value: </p>
        <span class="nonal">{{pValue}}</span>
      </div>
      <div 
        v-if="isShowCon"
        class="p-con"
      >
        <p class="p-con-title">MDE: </p>
        <span class="nonal">{{mde}}</span>
      </div>
      <!-- <p v-if="isShowCon" class="p-con">p-value: {{ pValue }}</p>
      <p v-if="isShowCon" class="p-con">MDE: {{ pValue }}</p> -->
    </div>

    
    <div v-if="isShowCon" class="p-wrap-con" :style="[{width: (customWidth ? customWidth : '100%') },{height: (customHeight ? customHeight : 'auto') }, {marginTop: '6px',marginBottom: '10px', display: 'flex', justifyContent: 'space-between'}]">
      <p class="p-con-title">置信区间：</p>
      <div class="confidence-retain-content">
        <span v-if="isConfidence === 2" class="confidence" style="color: #8b8ba6">{{ diffRelaVal }}</span>
        <span v-else class="confidence" :style="`color: ${color}`">{{ diffRelaVal }}</span>
        <div class="color-box" :style="`background-color: ${color}`">
          <span class="min-span">{{min}}%</span>
          <div class="left-border" :style="`background-color: ${color}`"></div>
          <div class="center-border" ></div>
          <div class="right-border" :style="`background-color: ${color}`"></div>
          <span class="max-span">{{max}}%</span>
        </div>
      </div>
    </div>
    
    <!-- <p class="test2">mmmm</p> -->
    
    <!-- <div slot="reference" class="p-wrap-con" :style="[{width: (customWidth ? customWidth : '100%') },{height: (customHeight ? customHeight : '80px') }]">
      <div class="confidence-retain-content">
        <span v-if="isConfidence === 2" class="confidence" style="color: #8b8ba6">{{ diffRelaVal }}</span>
        <span v-else class="confidence" :style="`color: ${color}`">{{ diffRelaVal }}</span>
        <div class="color-box" :style="`background-color: ${color}`">
          <span class="min-span">{{min}}%</span>
          <div class="left-border" :style="`background-color: ${color}`"></div>
          <div class="center-border" ></div>
          <div class="right-border" :style="`background-color: ${color}`"></div>
          <span class="max-span">{{max}}%</span>
        </div>
      </div>
    </div> -->

     <div slot="reference" class="p-wrap-con" :style="[{width: (customWidth ? customWidth : '100%') },{height: (customHeight ? customHeight : 'auto') }]">
      {{indValue}}
    </div>

  </el-popover>
</template>

<script>
export default {
  name: 'confidence',
  components: {},
  props: {
    min: {
      type: Number | String,
      default: function () {
        return -1.382;
      }
    },
    max: {
      type: Number | String,
      default: function () {
        return -0.137;
      }
    },
    pValue: {
      type: Number | String,
      default: function () {
        return -1.382;
      }
    },
    mde: {
      type: Number | String,
      default: function () {
        return '';
      }
    },
    indValue: {
      type: Number | String,
      default: function () {
        return '';
      }
    },
    diffRelaVal: {
      type: Number | String,
      default: function () {
        return -1.382;
      }
    },
     diffAbsVal: {
      type: Number | String,
      default: function () {
        return -1.382;
      }
    },
    confidenceVal: {
      type: String,
      default: function () {
        return '95%';
      }
    },
    isConfidence: {
      type: Number,
      default: function () {
        return 1; // 1是正向显著  0:负向显著 2: 不显著
      }
    },
    color: {
      type: String,
      default: function () {
        return '';
      }
    },
    customWidth: {
      type: String
    },
    customHeight: {
      type: String
    },
    isShowCon: {
      type: Boolean,
      default: function () {
        return true;
      }
    },
  },
  data() {
    return {
      // timess: 1111111
      timess: 10
    };
  },

  computed: {},
  watch: {},
  created() {},
  methods: {},
  beforeDestroy() {}
};
</script>

<style lang="less" scoped>

.p-con-title {
  font-weight: bold;
}

/deep/ .p-wrap {
  font-size: 14px;
}

.nonal {
  font-weight: 400;
}
/deep/ .p-con {
  //padding-bottom: 20px;
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
}
/deep/ .p-wrap-con {
  // display: flex;
  // height: 80px;
  // display: flex;
  // align-items: center;
  // justify-content: space-between;
}
/deep/ .confidence-retain-content {
  // width: 70%;
  width: 50%;
  height: 4px;
  margin: 10px 1px 20px 20px;
  background-color: #d9d9e9;
  color: #8b8ba6;
  position: relative;
  text-align: center;
  font-size: 10px;
  //margin-top: 33px;
  // margin: auto;
  .confidence {
    position: absolute;
    left: 25%;
    top: -20px;
    color: rgb(31, 212, 181);
    display: inline-block;
    width: 50%;
    margin: 0 auto;
  }
  .color-box {
    width: 33%;
    height: 4px;
    position: absolute;
    left: 33%;
    background-color: #5acebb;
    .min-span {
      position: absolute;
      left: -130%;
      bottom: -25px;
    }
    .left-border {
      position: absolute;
      left: 0;
      width: 1px;
      height: 15px;
      background-color: inherit;
    }
    .center-border {
      position: absolute;
      left: 50%;
      width: 1px;
      height: 100%;
      background-color: rgb(0, 0, 0);
    }
    .right-border {
      position: absolute;
      right: 0;
      width: 1px;
      height: 15px;
      background-color: inherit;
    }
    .max-span {
      position: absolute;
      // right: -130%;
      right: -110%;
      bottom: -25px;
    }
  }
}
</style>
