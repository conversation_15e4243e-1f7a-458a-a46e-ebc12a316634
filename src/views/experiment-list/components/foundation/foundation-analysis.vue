<template>
  <div class="foundation-analysis">
    <!-- 搜索条件设置 -->
    <div class="filter-container">
      <!-- 基础分析 -->
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="实验指标分析" name="first"></el-tab-pane>
        <el-tab-pane label="留存分析" name="second"></el-tab-pane>
      </el-tabs>

      <!-- <div class="filterBtns">
        <div @click="toggleExperiment" :class="['basicBtn', { selectedAnalysis: isExperiment }]">
          实验关注指标11
        </div>
        <div
          @click="toggleRemain"
          :class="['basicBtn', { selectedAnalysis: isRemain }]"
          v-if="isShowRemainBtn"
        >
          留存指标22
        </div>
      </div> -->

      <!-- 如果是留存指标 -->
      <template v-if="isRemain">
        <!-- 筛选维度 -->
        <div style="margin-left: 10px;" class="">
          <!-- <p class="selectTypeItemLabel">222时间筛选:</p> -->
          <el-form inline :model="timeForm" size="mini">
            <!-- <el-form-item>
              <el-select v-model="timeForm.timeType" @change="timeLevelChange">
                <el-option
                  v-for="item in timeLevelList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item> -->
            <!-- <el-form-item>
              <el-select v-model="timeForm.range">
                <el-option
                  v-for="item in rangeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="实验时间：">
              <!-- 留存指标只能选择天级别 -->
              <template>
                <!-- <div v-if="timeForm.timeType === 1" class="date-pick"> -->
                <!-- 天级 -->
                <el-date-picker style="width: 200px" v-model="timeForm.startTime" type="date" value-format="timestamp" :picker-options="pickerBeginDateBefore" format="yyyy-MM-dd" :clearable="false"></el-date-picker>
                <span style="margin: 0 6px;">至</span>
                <el-date-picker style="width: 200px" v-model="timeForm.stopTime" type="date" :picker-options="pickerBeginDateAfterLeave" value-format="timestamp" format="yyyy-MM-dd" :clearable="false"></el-date-picker>
                <!-- </div> -->
              </template>
            </el-form-item>
            <el-form-item label="">
              <el-button type="primary" @click="handleSearch">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>
      <div>
        <!-- <el-button type="primary" @click="handleSearch">---查询</el-button> -->
      </div>
    </div>


    <!-- 实验指标 -->
    <div class="indicator-container" v-if="isExperiment">
      <!-- 实验指标 -->
      <!-- <div class="indicator-container-item"> -->
      <!-- <div class="">实验指标111</div> -->
      <!-- <div class="select-indicators"> -->
      <!-- <div class="select-indicators-item">
            <span class="select-indicators-title">指标666</span>
            <el-select
              v-model="indicatorId"
              placeholder="请选择"
              @change="changeIndicatorId"
              size="mini"
            >
              <el-option
                v-for="item in indicatorList"
                :key="item.indId"
                :label="item.indName"
                :value="item.indId"
              ></el-option>
            </el-select>
          </div> -->
      <!-- <div class="indicator-types">
            <span>
              <i class="indicator-forward"></i>
              95%正向显著
            </span>
            <span>
              <i class="indicator-negative"></i>
              95%负向显著
            </span>
            <span>
              <i class="indicator-non-significant"></i>
              不显著
            </span>
          </div> -->

      <!-- </div> -->
      <!-- 实验指标表格 -->
      <!-- <foundation-experiment-table
          :expindsumData="expindsumData"
          :choosedIndicator="choosedIndicator"
          :colorList="colorList"
        ></foundation-experiment-table> -->

      <el-collapse class="zhibiao2" style="margin-top: 20px; margin-botoom:10px;" v-model="activeNames4" @change="handleChange">
        <el-collapse-item title="多维实验指标数据明细" name="1">
          <template slot="title">
            <p class="ex_detail">多维实验指标数据明细</p>
            <p @click="handleExport" class="tem_export">导出数据</p>
          </template>



          <!-- 如果是实验关注指标 -->
          <template v-if="isExperiment">
            <!-- 筛选维度 -->
            <div class="ex-weidu-wrapper">
              <!-- <p class="selectTypeItemLabel">111时间筛选:</p> -->
              <el-form :model="timeForm" size="mini">
                <!-- <el-form-item>
                    <el-select v-model="timeForm.timeType" @change="timeLevelChange">
                      <el-option
                        v-for="item in timeLevelList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item> -->
                <!-- <el-form-item>
                    <el-select v-model="timeForm.range">
                      <el-option
                        v-for="item in rangeList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item> -->
                <el-form-item label="实验时间：">
                  <!-- <template>
                      <div v-if="timeForm.timeType === 2" class="date-pick">
                        <el-date-picker
                          style="width: 200px"
                          v-model="timeForm.startTime"
                          type="datetime"
                          format="yyyy-MM-dd HH:mm"
                          disabled
                          class="date-picker"
                          value-format="timestamp"
                          :clearable="false"
                        ></el-date-picker>
                        <span>至</span>
                        <el-date-picker
                          style="width: 200px"
                          v-model="timeForm.stopTime"
                          type="datetime"
                          :picker-options="pickerOptions"
                          format="yyyy-MM-dd HH:mm"
                          value-format="timestamp"
                          class="date-picker"
                          :clearable="false"
                        ></el-date-picker>
                      </div>
                    </template> -->
                  <template>
                    <div class="date-pick">
                      <!-- 天级 -->
                      <el-date-picker style="width: 200px" v-model="timeForm.startTime" type="date" value-format="timestamp" format="yyyy-MM-dd" disabled :clearable="false"></el-date-picker>
                      <span>至</span>
                      <el-date-picker style="width: 200px" v-model="timeForm.stopTime" type="date" :picker-options="pickerBeginDateAfter" value-format="timestamp" format="yyyy-MM-dd" :clearable="false"></el-date-picker>
                    </div>
                  </template>
                </el-form-item>

                <el-form-item label="实验指标：">
                  <el-select class="com_width" v-model="formData.norm" multiple collapse-tags placeholder="请选择" @change="handleTarget">
                    <el-option v-for="item in optionsIndex" :key="item.value" :label="item.label" :value="item">
                    </el-option>
                  </el-select>
                </el-form-item>

                <div class="analysis_wrapper">
                  <p class="analysis_wrapper_title">分析维度：</p>
                  <div class="analysis_wrapper_item">
                    <el-form-item label="实验分组：">
                      <el-select class="com_width" v-model="formData.group" multiple collapse-tags @change="handleGroup" placeholder="请选择">
                        <el-option v-for="item in optionsGroup" :key="item.value" :label="item.label" :value="item">
                        </el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="用户类型：">
                      <el-select class="com_width" v-model="formData.userType" multiple collapse-tags @change="handleUserType" placeholder="请选择">
                        <el-option v-for="item in optionsUserType" :key="item.value" :label="item.label" :value="item">
                        </el-option>
                      </el-select>
                    </el-form-item>

                    <el-form-item label="APP版本：">
                      <el-select class="com_width" v-model="formData.appVersions" filterable multiple collapse-tags placeholder="" @change="handleAppVersions">
                        <el-option v-for="item in optionsAppVersions" :key="item.value" :label="item.label" :value="item">
                        </el-option>
                      </el-select>
                      <el-button class="search" type="primary" @click="handleNewSearch">查询</el-button>
                    </el-form-item>
                  </div>
                </div>
              </el-form>
            </div>
          </template>
          <template-table :hasIndex="false" class="detail-table" ref="multipleTableParent" :table-column="finColumns" :table-list="tableList">
          </template-table>
          <template-pagination :pagination-init="paginationInit" @changePage="changePage" @pageSizeChange="pageSizeChange">
          </template-pagination>
        </el-collapse-item>
      </el-collapse>
      <!-- </div> -->

      <el-collapse class="zhibiao" style="margin-botoom:10px;" v-model="activeNames" @change="handleChange">
        <el-collapse-item title="实验指标数据趋势分析" name="1">
          <el-form style="padding-left: 13px;" :model="timeForm" size="mini">
            <el-form-item label="实验时间：">
              <div class="date-pick">
                <!-- 天级 -->
                <el-date-picker style="width: 200px" v-model="indexAnalyse.startTime" type="date" value-format="timestamp" format="yyyy-MM-dd" @change="handleDeal" :picker-options="pickerstartOptions" :clearable="false"></el-date-picker>
                <span>至</span>
                <el-date-picker style="width: 200px" v-model="indexAnalyse.endTime" type="date" :picker-options="pickerBeginDateAfter" value-format="timestamp" @change="handleDeal" format="yyyy-MM-dd" :clearable="false"></el-date-picker>
              </div>
            </el-form-item>
            <!-- collapse-tags -->
            <el-form-item label="实验指标：">
              <el-select v-model="indextwo" @change="handleDeal" placeholder="请选择">
                <el-option v-for="item in optionsIndexTwo" :key="item.indId" :label="item.indName" :value="item.indId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="分析维度：">
              <el-radio-group v-model="value6">
                <el-radio @change="choosedDay" :label="1">每日进组用户</el-radio>
                <el-radio @change="choosedAdd" :label="2">每日新增进组用户</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <!-- 统计值 -->
          <div class="indicator-container-item" style="border: none;">
            <div class="foundation-experiment-statistic">
              <!-- radio切换 -->
              <!-- 默认统计值 -->
              <!-- <div class="statistic-select" v-if="timeForm.timeType === 1">
                <el-radio v-model="radio" :label="1">统计值</el-radio>
                <el-radio v-model="radio" :label="2" :disabled="canP === 0">p-value</el-radio>
              </div> -->

              <!-- 天级趋势数据/时级趋势数据 / 当日新增用户趋势 折线图-->
              <div class="statistic-item" v-if="isStatisticDay || isStatisticAdd">
                <template v-if="radio === 1">
                  <!-- 无数据时展示图片 -->
                  <div class="ant-empty" v-if="dayTrendData.length === 0">
                    <div class="ant-empty-image">
                      <img src="@/static/images/no-data.jpeg" alt="" />
                    </div>
                  </div>
                  <!-- 有数据时展示数据 -->
                  <day-line-chart v-else :charData="dayTrendData"></day-line-chart>
                </template>
                <template v-if="radio === 2">
                  <!-- 无数据时展示图片 -->
                  <div class="ant-empty" v-if="pValueData.length === 0">
                    <div class="ant-empty-image">
                      <img src="@/static/images/no-data.jpeg" alt="" />
                    </div>
                  </div>
                  <!-- 有数据时展示数据 -->
                  <pvalue-line-chart v-if="pValueData.length" :charData="pValueData"></pvalue-line-chart>
                </template>
              </div>

              <!-- 累积趋势数据 -->
              <!-- 近30天累积趋势 -->
              <div class="statistic-item" v-if="isStatisticTotal">
                <template v-if="radio === 1">
                  <!-- 无数据时展示图片 -->
                  <div class="ant-empty" v-if="cumTrendData.length === 0 || timeForm.timeType === 2">
                    <div class="ant-empty-image">
                      <img src="@/static/images/no-data.jpeg" alt="" />
                    </div>
                    <p v-if="timeForm.timeType === 2">累积趋势暂仅支持天极数据查询</p>
                  </div>
                  <!-- 有数据时展示数据 -->
                  <cum-line-chart v-if="cumTrendData.length > 0 && timeForm.timeType === 1" :charData="cumTrendData"></cum-line-chart>
                </template>
                <template v-if="radio === 2">
                  <!-- 无数据时展示图片 -->
                  <div class="ant-empty" v-if="cumpValueData.length === 0">
                    <div class="ant-empty-image">
                      <img src="@/static/images/no-data.jpeg" alt="" />
                    </div>
                    <!-- <p>尚无数据，请耐心等待实验运行</p> -->
                  </div>
                  <!-- 有数据时展示数据 -->
                  <pvalue-line-chart v-if="cumpValueData.length" :charData="cumpValueData"></pvalue-line-chart>
                </template>
              </div>

              <!-- 数据类型切换部分  -->
              <!-- 原逻辑最下面切换 -->
              <!-- <div class="statistic-type-change"> -->
              <!--当日新增用户趋势  -->
              <!-- <div
                  :class="['statistic-type-change-item', { statistic_choosed: isStatisticAdd }]"
                  @click="choosedAdd"
                >
                  <div class="statistic-type-change-item-content">
                    <i class="el-icon-s-marketing"></i>
                    <span>当日新增用户趋势</span>
                  </div>
                </div> -->
              <!-- 天级趋势/时级趋势 -->
              <!-- <div
                  :class="['statistic-type-change-item', { statistic_choosed: isStatisticDay }]"
                  @click="choosedDay"
                >
                  <div class="statistic-type-change-item-content">
                    <i class="el-icon-time"></i>
                    <span v-if="timeForm.timeType === 1">天级趋势</span>
                    <span v-if="timeForm.timeType === 2">时级趋势</span>
                  </div>
                </div> -->
              <!-- 近30天累积趋势 -->
              <!-- <div
                  :class="['statistic-type-change-item', { statistic_choosed: isStatisticTotal }]"
                  @click="choosedTotal"
                >
                  <div class="statistic-type-change-item-content">
                    <i class="el-icon-notebook-2"></i>
                    <span>近30天累积趋势888</span>
                  </div>
                </div> -->
              <!-- </div> -->
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <!-- 实验指标结束 -->

    <!-- 留存指标 -->
    <div class="remain-wrap" v-if="isRemain">
      <div class="indicator-container">
        <!-- <div v-if="isSameTerm">详细数据222</div> -->
        <!-- 下载数据 -->
        <!-- <div class="indicator-download" @click="downloadData" v-if="isSameTerm" >
          <i class="el-icon-download"></i>
          <span >下载数据</span>
        </div> -->
        <!-- table -->

        <el-collapse class="liucun" style="margin-botoom:1px;" v-model="activeNames1" @change="handleChange">
          <el-collapse-item title="每日新进组用户N天后留存数据明细" name="1">
            <foundation-remain-table v-if="isSameTerm" :remainTableData="remainTableData" @handleTableEnter="handleTableEnter"></foundation-remain-table>
          </el-collapse-item>
        </el-collapse>

        <el-collapse class="liucun" style="margin-botoom:1px; margin-top:20px;" v-model="activeNames2" @change="handleChange">
          <el-collapse-item title="每日新进组用户N天后平均留存数据趋势分析" name="1">
            <template v-if="isSameTerm && sameTermData.length === 0">
              <div class="ant-empty">
                <div class="ant-empty-image">
                  <img src="@/static/images/no-data.jpeg" alt="" />
                </div>
                <!-- <p>尚无数据，请耐心等待实验运行</p> -->
              </div>
            </template>
            <template v-else>
              <!-- 同期群留存趋势 -->
              <same-term-remain v-if="isSameTerm" :charData="sameTermData"></same-term-remain>
            </template>
          </el-collapse-item>
        </el-collapse>


        <!-- <template v-if="isSameTerm && sameTermData.length === 0">
          <div class="ant-empty">
            <div class="ant-empty-image">
              <img src="@/static/images/no-data.jpeg" alt="" />
            </div> -->
        <!-- <p>尚无数据，请耐心等待实验运行</p> -->
        <!-- </div>
        </template>
        <template v-else> -->
        <!-- 同期群留存趋势 -->
        <!-- <same-term-remain v-if="isSameTerm" :charData="sameTermData"></same-term-remain>
        </template> -->

        <el-collapse class="liucun" style="margin-top:20px;" v-model="activeNames3" @change="handleChange">
          <el-collapse-item title="每日新进组用户第N天留存数据对比分析" name="1">

            <!-- <div class="n-day-remain" v-if="isNDay"> -->
            <div class="n-day-remain">
              <div class="n-day-remain-title"></div>
              <label class="n-day-remain-label">对比第N天留存指标：</label>
              <el-select v-model="dayNum" size="mini" @change="nDayChange">
                <el-option v-for="item in daysList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
              <template v-if="nDayTimeData.length === 0">
                <div class="ant-empty">
                  <div class="ant-empty-image">
                    <img src="@/static/images/no-data.jpeg" alt="" />
                  </div>
                  <!-- <p>尚无数据，请耐心等待实验运行</p> -->
                </div>
              </template>
              <!-- N日留存日趋图 -->
              <n-day-remain v-else :charData="nDayTimeData"></n-day-remain>
            </div>

          </el-collapse-item>
        </el-collapse>


        <!-- <div class="n-day-remain" v-if="isNDay">
          <div class="n-day-remain-title">N日留存日趋图</div>
          <label class="n-day-remain-label">留存指标</label>
          <el-select v-model="dayNum" size="mini" @change="nDayChange">
            <el-option
              v-for="item in daysList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option> -->
        <!-- </el-select>
          <template v-if="nDayTimeData.length === 0">
            <div class="ant-empty">
              <div class="ant-empty-image">
                <img src="@/static/images/no-data.jpeg" alt="" />
              </div> -->
        <!-- <p>尚无数据，请耐心等待实验运行</p> -->
        <!-- </div>
          </template> -->
        <!-- N日留存日趋图 -->
        <!-- <n-day-remain v-else :charData="nDayTimeData"></n-day-remain>
        </div> -->


        <!-- 底部趋势切换 -->
        <!-- <div class="statistic-type-change">
          <div
            :class="['statistic-type-change-item', { statistic_choosed: isSameTerm }]"
            @click="sameTerm"
          >
            <div class="statistic-type-change-item-content">
              <i class="el-icon-s-data"></i>
              <span>同期群留存趋势</span>
            </div>
          </div>

          <div :class="['statistic-type-change-item', { statistic_choosed: isNDay }]" @click="nDay">
            <div class="statistic-type-change-item-content">
              <i class="el-icon-s-marketing"></i>
              <span>N日留存日趋图</span>
            </div>
          </div>
        </div> -->

      </div>
    </div>
    <!-- 留存结束 -->


  </div>
</template>
<script>
import foundationRemainTable from './foundation-remain-table.vue';
import foundationExperimentTable from './foundation-experiment-table.vue';
import sameTermRemain from './same-term-remain.vue';
import nDayRemain from './n-day-remain.vue';
import dayLineChart from './day-line-chart.vue';
import pvalueLineChart from './pvalue-line-chart.vue';
import cumLineChart from './cum-line-chart.vue';
import experimentIndicators from './experiment-indicators.vue';
import remainIndicators from './remain-indicators.vue';
import { formatDate } from '@/common/utils';
import tools from '@/utils/tools';
import { cloneDeep, isEmpty, debounce } from 'lodash';
import TemplateTable from '@/components/tools/template-table.vue';
import TemplatePagination from '@/components/tools/template-pagination.vue';
import ConfidenceBackup from './confidence-backup.vue';
import Utils from '@/utils/util';

const resArr = {
  "indList": [ // 指标
    {
      "value": 171,
      "label": "错题本",
      "select": 0  //不选中
    },
    {
      "value": 125,
      "label": "每日活跃均次",
      "select": 0
    },
    {
      "value": '-1',
      "label": "测试指标",
      "select": 1
    },
    {
      "value": '-2',
      "label": "活跃次数",
      "select": 1
    }
  ],
  "versionList": [ // 版本
    {
      "value": 2330,
      "label": "对照版本",
      "select": 1
    },
    {
      "value": 2331,
      "label": "实验",
      "select": 1
    },
    {
      "value": 222,
      "label": "版本122",
      "select": 0
    }
  ],
  "userTypeList": [ // 用户类型
    {
      "value": 1,
      "label": "新增激活用户",
      "select": 1
    },
    {
      "value": 2,
      "label": "老用户",
      "select": 1
    },
    {
      "value": 3,
      "label": "老用户",
      "select": 0
    }
  ],
  "appVersionList": [  // app版本
    {
      "value": 2,
      "label": "灰度版",
      "select": 1
    },
    {
      "value": 3,
      "label": "测试版",
      "select": 0
    }
  ]
};


export default {
  name: 'foundation-analysis',
  components: {
    experimentIndicators,
    remainIndicators,
    dayLineChart,
    pvalueLineChart,
    cumLineChart,
    foundationRemainTable,
    sameTermRemain,
    nDayRemain,
    foundationExperimentTable,
    TemplateTable,
    TemplatePagination,
    ConfidenceBackup
  },
  props: {
    colorList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    summaryData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  data() {
    return {
      indexAnalyse: {
        startTime: '',
        endTime: ''
      },
      optionsIndex: [],
      optionsIndexTwo: [],
      optionsGroup: [], // 实验分组
      optionsUserType: [],
      optionsAppVersions: [],
      dropOptions: resArr,
      formData: {
        norm: [], // 指标
        group: [], // 分组
        userType: [], // 类型
        appVersions: [] // 版本
      },
      value6: 1,
      activeNames: ['1'],
      activeNames1: ['1'],
      activeNames2: ['1'],
      activeNames3: ['1'],
      activeNames4: ['1'],
      tableList: [{
        color: 'red', pValue: 9, isConfidence: 1, diffRelaVal: 2, id: 11, displayName: '333',
        description: '99090', confidInterval: { min: 0.5, max: 0.9 }
      },
      {
        color: 'green', pValue: 2, isConfidence: 2, diffRelaVal: 3, id: 11, displayName: '333',
        description: 'hhhh', confidInterval: { min: 0.5, max: 0.9 }
      }
      ],
      tableColumns: [
        // {
        //   label: '日期',
        //   name: 'id',
        // fixed: true,
        // width: '100px'
        // },
        // {
        //   label: '实验分组',
        //   name: 'displayName',
        //   width: '100px',
        //   fixed: true,
        // },
        // {
        //   label: '用户类型',
        //   name: 'description',
        //   'min-width': '105px',
        //   width: '100px',
        //   fixed: true,
        //  render: (h, {row}) => {
        //   const { description } = row;
        //   const test = 1
        //   const min = 0.5
        //   return (
        //       <ConfidenceBackup
        //         min={row.confidInterval.min}
        //         max={row.confidInterval.max}
        //         pValue={row.pValue}
        //         diffRelaVal={row.diffRelaVal}
        //         isConfidence={test}
        //         color={row.color}
        //       ></ConfidenceBackup>
        //     );
        // }
        // },
        // {
        //   label: 'APP版本',
        //   name: 'appKey',
        //   fixed: 'fixed',
        //   width: '100px'
        // },
        // {
        //   label: '累计进组用户数',
        //   name: 'createUser',
        //   'min-width': '105px',
        //   width: '200px'
        // },
        // {
        //   label: '更新人',
        //   name: 'updateUser',
        //   'min-width': '105px',
        //   width: '200px',
        //   render: (h, {row}) => {
        //     const { updateUser } = row;
        //     const text =  updateUser ? updateUser : '-';
        //     return <p>{ text }</p>;
        //   }
        // }
      ],
      paginationInit: {
        total: 0,
        pageSize: 20,
        currentPage: 1
      },
      options: [{
        value: 'huangjin',
        label: '黄金糕'
      }, {
        value: 'shuangpin',
        label: '双皮奶'
      }, {
        value: 'jian',
        label: '蚵仔煎'
      }, {
        value: 'longxu',
        label: '龙须面'
      }, {
        value: 'kaoya',
        label: '北京烤鸭'
      }],
      indextwo: '',
      value3: [],
      // value3: ['选项4', '选项5'],
      // value3: [{
      //   value: '选项4',
      //   label: '龙须面'
      // },
      // {
      //   value: '选项5',
      //   label: '北京烤鸭'
      // }],
      activeName: 'first',
      experiment: {}, // 实验数据
      isShowRemainBtn: true, //初始化是否展示留存按钮
      timeForm: {
        timeType: 1, // 默认是天级
        range: 1,
        startTime: '', // 开始时间
        stopTime: '' // 结束时间
      },
      isExperiment: true,
      isRemain: false,
      indDataFormat: 1, // 指标数值类型 1:数字 2:百分比
      timeLevelList: [
        {
          id: 2,
          name: '小时级'
        },
        {
          id: 1,
          name: '天级'
        }
      ],
      rangeList: [
        {
          id: 1,
          name: '范围'
        }
      ],
      expindsumData: [], // 实验指标汇总数据
      indicatorId: '', // 指标id
      expId: this.$route.query.id,
      dayTrendData: [], // 实验天级和时级数据/日增趋势数据
      pValueData: [], // p-Value数据
      cumpValueData: [], // 指标累计p值
      cumTrendData: [], // 累积趋势数据
      saveDataType: '', //留存数据类型
      nDaySaved: 1, //
      // 分割
      startTime: '', // 实验开始时间
      pickerOptions: {}, // 时间控件选择区间
      // 分割
      choosedIndicator: {}, // 选中的指标
      indicatorList: [], // 表格数据
      radio: 1, // 默认是统计值
      value1: false,
      isStatisticDay: false, // 天级趋势
      isStatisticTotal: false, // 近30天累积趋势
      isStatisticAdd: true, // 当日新增用户趋势
      disabled: false,
      // 留存
      isSameTerm: true,
      isNDay: false,
      remainTableData: [], // 留存table数据
      sameTermData: [], // 同期群留存趋势数据
      daysList: [],
      dayNum: 1,
      nDayTimeData: [], // N日留存日趋数据
      confidenceVal: '95%', // 默认是置信度95%
      canP: 0, //默认是否展示p-v
      canflag: true,
      canflag1: true,
      canflag2: true,
      targetColumns: [],
      oldStartTiem: '',
      dateColumns: [{ label: '日期', name: 'date', }]
    };
  },
  created() { },
  mounted() {
    // this.getDaysList();
    // this.getDropList()
  },
  computed: {
    // 结束时间大于开始时间
    pickerBeginDateBefore() {
      return {
        disabledDate: (time) => {
          let initStart = this.startTime; // 实验初始化的的开始时间
          let startTime = this.timeForm.startTime;
          let stopTime = this.timeForm.stopTime;
          // console.log("kkkkk")
          // console.log(initStart, stopTime)
          // console.log('time.getTime()', time.getTime())
          //debugger
          // if (startTime) {
          //   return time.getTime() > stopTime || time.getTime() < initStart;
          // }

          if (startTime) {
            return time.getTime() > stopTime || (time.getTime() + 24 * 3600 * 1000) < initStart;
          }
          // if (startTime) {
          //   return (time.getTime() + 24 * 3600 * 1000) < (initStart);
          // }

          // return true; //禁
          // return false; //打开
        }
      };
    },
    pickerstartOptions() {
      return {
        disabledDate: (time) => {
          let startTime = this.timeForm.startTime;
          let status = this.experiment.status;
          let endTime = this.experiment.endTime;
          if (startTime) {
            return time.getTime() <= this.oldStartTiem * 1000 - 8.64e7;
          }
          // if (startTime && status !== 4) {
          //   return time.getTime() > Date.now() - 8.64e7 || time.getTime() <= startTime - 8.64e7;
          // }
          console.log('startTime', startTime);
          // if (status === 4) {
          //   if (tools.isToday(endTime * 1000)) {
          //     return (
          //       time.getTime() >= endTime * 1000 - 8.64e7 || time.getTime() <= startTime - 8.64e7
          //     );
          //   }
          //   return time.getTime() >= endTime * 1000 || time.getTime() <= startTime - 8.64e7;
          // }
        }
      };
    },
    pickerBeginDateAfterLeave() {
      return {
        disabledDate: (time) => {
          let startTime = this.timeForm.startTime;
          let status = this.experiment.status;
          let endTime = this.experiment.endTime;
          if (startTime && status !== 4) {
            return time.getTime() > Date.now() - 8.64e7 || time.getTime() <= startTime - 8.64e7;
            // return time.getTime() > Date.now() - 8.64e7 || time.getTime() <= startTime - 8.64e7 - (24 * 3600 * 1000);
          }
          if (status === 4) {
            if (tools.isToday(endTime * 1000)) {
              return (
                time.getTime() >= endTime * 1000 - 8.64e7 || time.getTime() <= startTime - 8.64e7
                // time.getTime() >= endTime * 1000 - 8.64e7 || time.getTime() <= startTime - 8.64e7 - (24 * 3600 * 1000)
              );
            }
            return time.getTime() >= endTime * 1000 || time.getTime() <= startTime - 8.64e7;
            // return time.getTime() >= endTime * 1000 || time.getTime() <= startTime - 8.64e7 - (24 * 3600 * 1000);
          }
        }
      };
    },
    pickerBeginDateAfter() {
      return {
        disabledDate: (time) => {
          let startTime = this.timeForm.startTime;
          let status = this.experiment.status;
          let endTime = this.experiment.endTime;
          if (startTime && status !== 4) {
            return time.getTime() > Date.now() - 8.64e7 || time.getTime() <= startTime - 8.64e7;
          }
          if (status === 4) {
            if (tools.isToday(endTime * 1000)) {
              return (
                time.getTime() >= endTime * 1000 - 8.64e7 || time.getTime() <= startTime - 8.64e7
              );
            }
            return time.getTime() >= endTime * 1000 || time.getTime() <= startTime - 8.64e7;
          }
        }
      };
    },
    finColumns() {
      return [...this.dateColumns, ...this.tableColumns, ...this.targetColumns];
    }
  },
  watch: {
    finColumns(newV1, oldV1) {
      // console.log('this.finColumns====', this.finColumns)
      if (newV1.length > 12) {
        [...this.dateColumns, ...this.tableColumns].forEach((item) => {
          item.fixed = true;
          item.width = '100px';
        });
        this.targetColumns.forEach((it) => {
          it.width = '100px';
        });
      } else {
        [...this.dateColumns, ...this.tableColumns].forEach((item) => {
          item.fixed = false;
          item.width = 'auto';
        });
        this.targetColumns.forEach((it) => {
          it.width = 'auto';
        });
      }
    },
    // summaryData(newV, oldV) {
    //   if (!isEmpty(newV)) {
    //     this.experiment = newV.experiment;
    //     this.oldStartTiem = newV.experiment.startTime;

    //     // console.log('newV.experiment.startTime', newV.experiment.startTime);
    //     //debugger;
    //     this.initFilter();
    //     // 获取指标列表
    //     this.getDropList();
    //     // 趋势图指标需要
    //     this.getIndicatorList();
    //   }
    // },
    summaryData: {
      handler: function (newV, oldV) {
        if (!isEmpty(newV)) {
          this.experiment = newV.experiment;
          this.oldStartTiem = newV.experiment.startTime;
          this.initFilter();
          // 获取指标列表
          this.getDropList();
          // 趋势图指标需要
          this.getIndicatorList();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 获取指标下拉值
    getDropList() {
      // 指标
      // this.optionsIndex = this.dropOptions.indList
      // this.optionsGroup = this.dropOptions.versionList
      // this.optionsUserType = this.dropOptions.userTypeList
      // this.optionsAppVersions = this.dropOptions.appVersionList

      // this.formData.norm = this.dropOptions.indList.filter((item) => item.select == 1)
      // this.formData.group = this.dropOptions.versionList.filter((item) => item.select == 1)
      // this.formData.userType = this.dropOptions.userTypeList.filter((item) => item.select == 1)
      // this.formData.appVersions = this.dropOptions.appVersionList.filter((item) => item.select == 1)

      // if(this.formData.norm.length > 0) {
      //   this.formData.norm.forEach((item) => {
      //       item.name = item.value
      //   })
      //   this.targetColumns.push(...this.formData.norm)
      // }

      // if(this.formData.group.length > 0) {
      //   this.tableColumns.push({
      //     label: '分组',
      //     name: 'group',
      //   });
      // }

      // if(this.formData.userType.length > 0) {
      //   this.tableColumns.push({
      //     label: '用户类型',
      //     name: 'user',
      //   });
      // }

      // if(this.formData.appVersions.length  > 0) {
      //   this.tableColumns.push({
      //     label: 'APP版本',
      //     name: 'app',
      //   });
      // }

      // console.log('this.tableColumns===', this.tableColumns);

      // console.log('this.formData.norm', this.formData.norm);

      // optionsGroup: [], // 实验分组
      // optionsUserType: [],
      // optionsAppVersions: [],

      // const param = {
      //   experimentId: this.expId
      // };
      this.$service
        .get('EXPINDPARAMS', { experimentId: this.expId }, { needLoading: false })
        .then((res) => {
          this.dropOptions = res;
          this.optionsIndex = this.dropOptions.indList;

          this.optionsGroup = this.dropOptions.versionList;
          this.optionsUserType = this.dropOptions.userTypeList;
          this.optionsAppVersions = this.dropOptions.appVersionList;

          this.formData.norm = this.dropOptions.indList.filter((item) => item.select == 1);
          this.formData.group = this.dropOptions.versionList.filter((item) => item.select == 1);
          this.formData.userType = this.dropOptions.userTypeList.filter((item) => item.select == 1);
          this.formData.appVersions = this.dropOptions.appVersionList.filter((item) => item.select == 1);

          // 汇总数据
          this.getDataollect(2);
          // 趋势图
          this.choosedAdd();

          // if(this.formData.norm.length > 0) {
          //   this.formData.norm.forEach((item) => {
          //       item.name = item.value
          //       item.render = (h, {row, index}) => {
          //         const key = item.name
          //         const { indValue, isShowCon, isCompareVersion, date } = row[key];
          //         // if(isCompareVersion == 1 || !isShowCon) {
          //         // console.log('isCompareVersion===', date)
          //         // console.log('row[key]===', row[key])
          //         if(isCompareVersion == 1) {
          //           return (<span>{indValue}</span>)
          //         } else {
          //           return (
          //             <ConfidenceBackup
          //               min={row[key].confidInterval.min}
          //               max={row[key].confidInterval.max}
          //               pValue={row[key].pValue}
          //               diffRelaVal={row[key].diffRelaVal}
          //               isConfidence={row[key].isConfidence}
          //               color={row[key].color}
          //               indValue={row[key].indValue}
          //               diffAbsVal={row[key].diffAbsVal}
          //             ></ConfidenceBackup>
          //           );
          //         }
          //       };
          //   })
          //   this.targetColumns.push(...this.formData.norm)
          // }

          // if(this.formData.group.length > 0) {
          //   this.tableColumns.push({
          //     label: '分组',
          //     name: 'versionName',
          //   });
          // }

          // if(this.formData.userType.length > 0) {
          //   this.tableColumns.push({
          //     label: '用户类型',
          //     name: 'userType',
          //   });
          // }

          // if(this.formData.appVersions.length  > 0) {
          //   this.tableColumns.push({
          //     label: 'APP版本',
          //     name: 'appVersion',
          //   });
          // }
        });
    },
    handleExport(ev) {
      ev.stopPropagation();
      let groups = this.formData.group.map((item) => {
        return item.value;
      });
      let userTypes = this.formData.userType.map((item) => {
        return item.value;
      });
      let norms = this.formData.norm.map((item) => {
        return item.value;
      });
      let apps = this.formData.appVersions.map((item) => {
        return item.value;
      });


      const { startTime, stopTime } = this.genCommonParms();
      const param = {
        experimentId: this.expId,
        startTime: startTime,
        stopTime: stopTime,
        indList: JSON.stringify(norms),
        versionList: JSON.stringify(groups),
        userTypeList: JSON.stringify(userTypes),
        appVersionList: JSON.stringify(apps),
        pn: this.paginationInit.currentPage,
        rn: this.paginationInit.pageSize
      };

      const params = {
        ...this.formatForm(param)
      };
      const url = 'earthworm/mis/report/exportindsummulti';
      // const uri = Utils.exportFun(url, params);
      const uri = this.exportFun(url, params);
      const link = document.createElement('a');
      link.href = `${window.location.origin}/${uri}`;
      // link.href = `${'https://test20.suanshubang.com/'}/${uri}`;
      link.download = '数据导出';
      link.click();
      // event.preventDefault()
      // console.log('kkkk', ev);
    },
    // 格式化返回的表单数据
    formatForm(form) {
      const tmp = Object.create({});
      Object.keys(form).forEach((item) => {
        // if (item === 'date') {
        if (item === 'makeBillTimeStart') {
          tmp.createTimeStart = form[item] ? form[item][0] / 1000 : ''; // 以秒为单位
          tmp.createTimeEnd = form[item] ? form[item][1] / 1000 : '';
        } else {
          tmp[item] = form[item];
        }
      });
      return tmp;
    },
    exportFun(url, formItem) {
      let param = '';
      for (const field in formItem) {
        if (formItem[field]) {
          if (Array.isArray(formItem[field])) {
            formItem[field].forEach((item) => {
              param += field + '[]' + '=' + item + '&';
            });
          } else {
            param += field + '=' + formItem[field] + '&';
          }
        }
      }
      param = param.substring(0, param.length - 1);
      const locationUrl =
        url.indexOf('?') !== -1 ? `${url}&${param}` : `${url}?${param}`;
      return locationUrl;
    },
    handleAppVersions(ev) {
      return;
      console.log('app', ev);
      if (ev.length >= 1 && this.canflag) {
        this.canflag = false;
        this.tableColumns.push({
          label: 'APP版本',
          name: 'app',
          // fixed: true,
          // width: '100px'
        });
      } else if (ev.length == 0) {
        this.canflag = true;
        for (var i = 0; i < this.tableColumns.length; i++) {
          if (this.tableColumns[i].name === 'app') {
            this.tableColumns.splice(i, 1);
          }
        }
      }
    },
    handleUserType(ev) {
      return;
      console.log('app', ev);
      if (ev.length >= 1 && this.canflag1) {
        this.canflag1 = false;
        this.tableColumns.push({
          label: '用户类型',
          name: 'user',
          // fixed: true,
          // width: '100px'
        });
      } else if (ev.length == 0) {
        this.canflag1 = true;
        for (var i = 0; i < this.tableColumns.length; i++) {
          if (this.tableColumns[i].name === 'user') {
            this.tableColumns.splice(i, 1);
          }
        }
      }
    },
    handleGroup(ev) {
      return;
      console.log('app', ev);
      if (ev.length >= 1 && this.canflag2) {
        this.canflag2 = false;
        this.tableColumns.push({
          label: '分组',
          name: 'group',
          // fixed: true,
          // width: '100px'
        });
      } else if (ev.length == 0) {
        this.canflag2 = true;
        for (var i = 0; i < this.tableColumns.length; i++) {
          if (this.tableColumns[i].name === 'group') {
            this.tableColumns.splice(i, 1);
          }
        }
      }
    },
    // 实验指标
    handleTarget(ev) {
      return;
      // let renderFun = (label, name) => {
      //   return {
      //           label: label,
      //           name: name,
      //           'min-width': '105px',
      //           width: '100px',
      //           fixed: true,
      //           render: (h, {index, row}) => {
      //             const { description } = row;
      //             const test = 1
      //             const min = 0.5
      //             return (
      //                 <ConfidenceBackup
      //                   min={row.confidInterval.min}
      //                   max={row.confidInterval.max}
      //                   pValue={row.pValue}
      //                   diffRelaVal={row.diffRelaVal}
      //                   isConfidence={test}
      //                   color={row.color}
      //                 ></ConfidenceBackup>
      //               );
      //           }
      //         }
      // }
      // console.log('formData', this.formData);
      this.targetColumns = [];
      const arrTarget = ev;

      console.log('arrTarget', arrTarget);

      if (arrTarget.length >= 1) {
        arrTarget.forEach((item) => {
          item.name = item.value;
          item.render = (h, { row }) => {
            const { description } = row;
            const test = 1;
            const min = 0.5;
            return (
              <ConfidenceBackup
                min={row.confidInterval.min}
                max={row.confidInterval.max}
                pValue={row.pValue}
                diffRelaVal={row.diffRelaVal}
                isConfidence={test}
                color={row.color}
              ></ConfidenceBackup>
            );
          };
        });
      }
      this.targetColumns.push(...arrTarget);
    },
    handleChange(val) {
      console.log(val);
    },
    pageSizeChange(e) {
      this.paginationInit.currentPage = 1;
      this.paginationInit.pageSize = e;
      this.getDataollect(1);
    },
    changePage(page) {
      this.paginationInit.currentPage = page;
      this.getDataollect(1);
      // this.handleNewSearch();
    },
    // 初始化筛选条件
    initFilter() {
      this.startTime = this.experiment.startTime * 1000;
      //debugger;
      const exStartTime = formatDate(this.startTime, 'yyyy-MM-dd');
      const today = formatDate(Date.now(), 'yyyy-MM-dd');
      const isSameTime = exStartTime === today;
      //  如果实验没有结束
      if (this.experiment.status !== 4) {
        // 判断开始日期是否和今日为同一天，若是只有时级
        if (isSameTime) {
          // this.timeLevelList.splice(1, 1);
          // this.timeForm.timeType = 2;
          //debugger;
          this.timeLevelChange(2);
          this.isShowRemainBtn = false;
        } else {
          // this.timeLevelList = [
          //   {
          //     id: 2,
          //     name: '小时级'
          //   },
          //   {
          //     id: 1,
          //     name: '天级'
          //   }
          // ];
          // this.timeForm.timeType = 1;
          this.timeLevelChange(1);
          this.isShowRemainBtn = true;
        }
      } else {
        // 实验已经结束
        // this.timeLevelList = [
        //   {
        //     id: 1,
        //     name: '天级'
        //   }
        // ];
        // this.timeForm.timeType = 1;
        this.timeLevelChange(1);
        this.isShowRemainBtn = true;
      }
    },
    handleClick(ev) {
      console.log('ev', ev.name);
      if (ev.name === 'first') {
        this.toggleExperiment();
      } else {
        this.toggleRemain();
      }
    },
    // 切换实验关注指标
    toggleExperiment() {
      this.isExperiment = true;
      this.isRemain = false;
      this.initFilter();
      // 获取指标列表
      // this.getIndicatorList();
    },
    // 切换留存指标
    toggleRemain() {
      this.isExperiment = false;
      this.isRemain = true;
      //this.timeLevelList.splice(0, 1);
      this.timeLevelList = [
        {
          id: 1,
          name: '天级'
        }
      ];
      this.startTime = this.experiment.startTime * 1000;
      this.timeForm.timeType = 1;
      this.timeLevelChange(1);
      //获取留存table数据
      this.getRemainTableData();
      // 获取同期群留存趋势
      this.getRemainSameTermData();
      // 获取N日留存日趋图

      this.nDay();

    },
    // 时间筛选类型变化时 时间赋值
    timeLevelChange(val) {
      const that = this;
      //debugger;
      // 2小时级 1天
      let year;
      if (val === 2) {
        try {
          year = new Date().getFullYear();
        } catch (e) {
          year = '2012';
        }
        let month = new Date().getMonth() + 1;
        let day = new Date().getDate();
        let dateTime = new Date(year + '-' + month + '-' + day).getTime();
        this.pickerOptions = {
          disabledDate(time) {
            //debugger;
            return time.getTime() > Date.now() || time.getTime() < dateTime;
          }
        };
        //debugger;
        this.timeForm.startTime = dateTime;
        this.timeForm.stopTime = Date.now();
        this.indexAnalyse.startTime = this.startTime;
        this.indexAnalyse.endTime = Date.now();
      } else {
        // 初始值设置
        this.timeForm.startTime = this.startTime;
        //  实验已经结束
        if (this.experiment.status === 4) {
          if (tools.isToday(this.experiment.endTime * 1000)) {
            this.timeForm.stopTime = this.experiment.endTime * 1000 - 8.64e7;
            return;
          }
          this.timeForm.stopTime = this.experiment.endTime * 1000;
        } else {
          this.timeForm.stopTime = Date.now() - 8.64e7;
        }

        // 趋势分析图时间
        // 初始值设置
        // this.indexAnalyse.startTime = this.oldStartTiem * 1000
        // this.indexAnalyse.endTime = this.oldStartTiem * 1000
        this.indexAnalyse.startTime = this.startTime;
        //  实验已经结束
        if (this.experiment.status === 4) {
          if (tools.isToday(this.experiment.endTime * 1000)) {
            this.indexAnalyse.endTime = this.experiment.endTime * 1000 - 8.64e7;
            return;
          }
          this.indexAnalyse.endTime = this.experiment.endTime * 1000;
        } else {
          this.indexAnalyse.endTime = Date.now() - 8.64e7;
        }

      }
    },
    //获取实验指标列表 趋势图需要使用
    getIndicatorList() {
      const param = {
        experimentId: this.expId
      };
      this.$service
        .get('REPORT_FOUND_EXPINDLIST', { ...param }, { needLoading: true })
        .then((res) => {
          //debugger;
          // this.indicatorList = res.indList;
          this.optionsIndexTwo = res.indList;
          this.indextwo = this.optionsIndexTwo[0].indId;

          // 获取实验指标数据天级趋势或时级趋势数据
          this.getExpindDayOrTimeData();
          // 获取p-value数据
          this.getPValue();

          // if (res.indList.length > 0) {
          //   this.$nextTick(() => {
          //     this.indicatorId = res.indList[0].indId;
          //     this.indDataFormat = res.indList[0].indDataFormat;
          //     this.canP = res.indList[0].canP;
          //     // 获取实验指标汇总数据
          //     // this.getExpindsum();
          //     this.getDataollect(2);
          //     this.choosedAdd();
          //   });
          // }
        })
        .catch(() => { });
    },
    // 获取数据
    getDataollect(ev) {
      if (ev !== 1) {
        this.tableColumns = [];
        this.targetColumns = [];

        if (this.formData.group.length > 0) {
          this.tableColumns.push({
            label: '分组',
            name: 'versionName',
          });
        }

        if (this.formData.userType.length > 0) {
          this.tableColumns.push({
            label: '用户类型',
            name: 'userType',
          });
        }

        if (this.formData.appVersions.length > 0) {
          this.tableColumns.push({
            label: 'APP版本',
            name: 'appVersion',
          });
        }
      }


      let groups = this.formData.group.map((item) => {
        return item.value;
      });
      let userTypes = this.formData.userType.map((item) => {
        return item.value;
      });
      let norms = this.formData.norm.map((item) => {
        return item.value;
      });

      let apps = this.formData.appVersions.map((item) => {
        return item.value;
      });

      // if (!this.timeForm.startTime || !this.timeForm.stopTime) {
      //   this.$message.warning('请选择时间');
      //   return;
      // }


      const { startTime, stopTime } = this.genCommonParms();
      const param = {
        experimentId: this.expId,
        startTime: startTime,
        stopTime: stopTime,
        // indList: '[' + norms.join() + ']',
        // versionList: '[' + groups.join() + ']',
        // userTypeList: '[' + userTypes.join() + ']',
        indList: JSON.stringify(norms),
        versionList: JSON.stringify(groups),
        userTypeList: JSON.stringify(userTypes),
        appVersionList: JSON.stringify(apps),
        pn: this.paginationInit.currentPage,
        rn: this.paginationInit.pageSize
      };
      // console.log('param', param);

      //   this.tableList = [
      //     {
      //       "versionId": 221,
      //       "versionName": "对照版本111",
      //       "date": "2021-12-1711",
      //       "-1": {
      //         "diffAbsVal": "10",
      //         "diffRelaVal": "18",
      //         "confidInterval": {min: 3, max: 3},
      //         "pValue": "",
      //         "MDE": "",
      //         "num": "",
      //         "numPersent": "",
      //         "indValue": 1// 指标显示值
      //       },
      //       "-2": {
      //         "diffAbsVal": "22",
      //         "diffRelaVal": "25",
      //         "confidInterval": {min: -1, max: -1},
      //         "pValue": "",
      //         "MDE": "",
      //         "num": "",
      //         "numPersent": "",
      //         "indValue": 3
      //       }
      //     },
      //    {
      //     "versionId": 386,
      //     "versionName": "对照版本",
      //     "date": "2021-12-17",
      //     "-1": {
      //       "diffAbsVal": "66",
      //       "diffRelaVal": "55",
      //       "confidInterval": {min: 2, max: 2},
      //       "pValue": "",
      //       "MDE": "",
      //       "num": "",
      //       "numPersent": "",
      //       "indValue": 20
      //     },
      //     "-2": {
      //       "diffAbsVal": "77",
      //       "diffRelaVal": "66",
      //       "confidInterval": {min: 0, max: 2},
      //       "pValue": "2",
      //       "MDE": "2",
      //       "num": "3",
      //       "numPersent": "5",
      //       "indValue": 888
      //     }
      //   }
      // ]

      // // isShowCon 是否对照
      // // getExpindsum() 汇总
      // const reg = /^-{0,1}[\d|\.]*$/;
      // if(this.tableList.length) {
      //   this.tableList.forEach((item, idx) => {
      //     Object.keys(item).forEach(element => {
      //         if(reg.test(element)) {
      //           this.formatConfidInterval(item[element])
      //           this.formatDiffRelaVal(item[element])
      //           this.formatChildData(item[element], item, idx);
      //         }
      //     });
      //     // 只处理一层
      //     this.formatIndDataFormatNew(item)
      //   })
      // }

      // http://test20.suanshubang.com/earthworm/mis/report/expindsummulti
      this.$service
        .get('EXPINDSUMMULTI', { ...param }, { needLoading: true })
        .then((res) => {
          this.tableList = res.list;
          this.paginationInit.total = res.total;
          // isShowCon 是否对照
          // getExpindsum() 汇总
          const reg = /^-{0,1}[\d|\.]*$/;
          if (this.tableList.length) {

            if (this.optionsIndex.length) {
              this.tableList.forEach((target) => {
                Object.keys(target).forEach(element => {
                  if (reg.test(element)) {
                    this.optionsIndex.forEach((every) => {
                      if (every.value == element) {
                        target[element].indDataFormat = every.indDataFormat;
                      }
                    });
                  }
                });
              });
            }


            this.tableList.forEach((item, idx) => {
              Object.keys(item).forEach(element => {
                if (reg.test(element)) {
                  this.formatConfidInterval(item[element]);
                  this.formatDiffRelaVal(item[element]);
                  this.formatChildData(item[element], item, idx);
                }
              });
              // 只处理一层
              this.formatIndDataFormatNew(item);
            });


            // console.log("this.optionsIndex+++++", this.optionsIndex);
            // console.log("this.tableList>>>>>", this.tableList);

            // 指标列添加
            if (this.formData.norm.length > 0 && ev !== 1) {
              this.formData.norm.forEach((item) => {
                item.name = item.value + '';
                item.render = (h, { row, index }) => {
                  const key = item.name;
                  const { isShowCon, indValue } = row[key];
                  const { isCompareVersion } = row;
                  // console.log('isCompareVersion', isCompareVersion)
                  //
                  //  || !isShowCon


                  if (isCompareVersion == 1) {
                    // if(isCompareVersion == 1) {
                    return (<span>{indValue ? indValue : ''}</span>);
                  } else {
                    return (
                      <ConfidenceBackup
                        min={row[key].confidInterval.min}
                        max={row[key].confidInterval.max}
                        pValue={row[key].pValue}
                        diffRelaVal={row[key].diffRelaVal}
                        isConfidence={row[key].isConfidence}
                        color={row[key].color}
                        indValue={row[key].indValue}
                        diffAbsVal={row[key].diffAbsVal}
                        isShowCon={isShowCon}
                        mde={row[key].MDE}
                      ></ConfidenceBackup>
                    );
                  }
                };
              });
              this.targetColumns.push(...this.formData.norm);
            }
            // console.log('this.tableList-->', this.tableList);
          }
        })
        .catch(() => { });
    },
    // 获取实验指标汇总数据
    getExpindsum() {
      const { startTime, stopTime } = this.genCommonParms();
      const param = {
        startTime,
        stopTime,
        experimentId: +this.expId,
        indId: this.indicatorId,
        timeType: this.timeForm.timeType
      };
      //debugger;
      this.$service
        .get('REPORT_FOUND_EXPINDSUM', { ...param }, { needLoading: false })
        .then((res) => {
          //debugger;
          res.versionList.forEach((item, idx) => {
            // if (item.diffRelaVal !== '' && item.diffRelaVal !== '-') {
            //   item.diffRelaVal = item.diffRelaVal + '%';
            // }
            this.formatDiffRelaVal(item);
            item.index = idx; // 此处是为了让对照组不显示执行区间；
            this.formatConfidInterval(item);
            item.detailList.forEach((list, idx) => {
              // if (list.diffRelaVal !== '' && list.diffRelaVal !== '-') {
              //   list.diffRelaVal = list.diffRelaVal + '%';
              // }
              this.formatChildData(list, item, idx);
              this.formatDiffRelaVal(list);
              this.formatConfidInterval(list);
            });
            this.formatIndDataFormat(item);
          });

          this.indicatorList.forEach((item) => {
            if (item.indId === this.indicatorId) {
              this.choosedIndicator = item;
            }
          });
          this.expindsumData = res.versionList;
        })
        .catch(() => { });
    },
    formatChildData(list, item, idx) {
      list.versionId = item.versionId + '' + idx;
      list.originVersionId = item.versionId;
      // 修改list无date item里有
      list.versionName = list.date || item.date;
    },
    formatDiffRelaVal(item) {
      //debugger;
      if (item.diffRelaVal !== '' && item.diffRelaVal !== '-') {
        item.diffRelaVal = item.diffRelaVal + '%';
      }
      //debugger;
    },
    formatConfidInterval(item) {
      // console.log('item', item)
      if (Array.isArray(item.confidInterval) && item.confidInterval.length) {
        if (item.confidInterval[0] === 0 && item.confidInterval[1] === 0) {
          item.isShowCon = false;
        }
        if (item.confidInterval[0] > 0 && item.confidInterval[1] > 0) {
          item.isShowCon = true;
          item.isConfidence = 1;
          item.color = 'rgb(31, 212, 181)';
        }
        if (item.confidInterval[0] < 0 && item.confidInterval[1] < 0) {
          item.isShowCon = true;
          item.isConfidence = 0;
          item.color = 'rgb(237, 84, 107)';
        }
        if (item.confidInterval[0] < 0 && item.confidInterval[1] > 0) {
          item.isShowCon = true;
          item.isConfidence = 2;
          item.color = '#d9d9e9';
        }
      } else {
        if (Array.isArray(item.confidInterval) && !item.confidInterval.length) {
          item.isShowCon = false;
        }
        if (item.confidInterval.min === 0 && item.confidInterval.max === 0) {
          item.isShowCon = false;
        }
        if (item.confidInterval.min > 0 && item.confidInterval.max > 0) {
          item.isShowCon = true;
          item.isConfidence = 1;
          item.color = 'rgb(31, 212, 181)';
        }
        if (item.confidInterval.min < 0 && item.confidInterval.max < 0) {
          item.isShowCon = true;
          item.isConfidence = 0;
          item.color = 'rgb(237, 84, 107)';
        }
        if (item.confidInterval.min < 0 && item.confidInterval.max > 0) {
          item.isShowCon = true;
          item.isConfidence = 2;
          item.color = '#d9d9e9';
        }
      }
      // console.log('item---', item)
    },
    formatIndDataFormatNew(item, ele) {
      // console.log('item++++', item)
      const reg = /^-{0,1}[\d|\.]*$/;
      // this.indDataFormat = 2

      // console.log('this.indDataFormat==', this.indDataFormat);
      // 第一层
      // this.formatIndDataFormat2New(item);
      // 第二层
      Object.keys(item).forEach(element => {
        if (reg.test(element)) {
          if (item[element].indDataFormat == 2) {
            // 百分比
            this.formatIndDataFormat2New(item[element]);
          } else if (item[element].indDataFormat == 1) {
            // 数字
            this.formatIndDataFormat1New(item[element]);
          }
          // this.formatIndDataFormat2New(item[element]);
        }
      });

      // console.log('this.indDataFormat==', this.indDataFormat);
      // if (this.indDataFormat === 2) {
      //   // 第一层
      //   this.formatIndDataFormat2New(item);
      //   // 第二层
      //   Object.keys(item).forEach(element => {
      //     if(reg.test(element)) {
      //       this.formatIndDataFormat2New(item[element]);
      //     }
      //   });
      // }

      // if (this.indDataFormat === 1) {
      //   this.formatIndDataFormat1New(item);
      //   Object.keys(item).forEach(element => {
      //     if(reg.test(element)) {
      //       this.formatIndDataFormat1New(item[element]);
      //     }
      //   });
      // }
    },
    formatIndDataFormat2New(item) {
      item.indValue = item.indValue ? item.indValue + '%' : '0.000%';
      item.numPersent = item.numPersent ? item.numPersent : '0';
      item.num = item.num ? item.num : '0';
      if (item.diffAbsVal && item.diffAbsVal !== '-') {
        item.diffAbsVal = item.diffAbsVal === '' ? '' : item.diffAbsVal + '%';
      }
      if (item.MDE && item.MDE !== '-') {
        item.MDE = item.MDE === '' ? '' : item.MDE + '%';
      }
    },
    formatIndDataFormat1New(item) {
      item.indValue = item.indValue ? item.indValue.toLocaleString() : '0';
      item.numPersent = item.numPersent ? item.numPersent : '0';
      item.num = item.num ? item.num : '0';
      if (item.diffAbsVal && item.diffAbsVal !== '-') {
        item.diffAbsVal = item.diffAbsVal === '' ? '' : item.diffAbsVal;
      }
    },
    formatIndDataFormat(item) {
      if (this.indDataFormat === 2) {
        this.formatIndDataFormat2(item);
        item.detailList.forEach((list) => {
          this.formatIndDataFormat2(list);
        });
      }
      if (this.indDataFormat === 1) {
        this.formatIndDataFormat1(item);
        item.detailList.forEach((list) => {
          this.formatIndDataFormat1(list);
        });
      }
    },
    formatIndDataFormat2(item) {
      item.indValue = item.indValue ? item.indValue + '%' : '0.000%';
      item.numPersent = item.numPersent ? item.numPersent : '0';
      item.num = item.num ? item.num : '0';
      if (item.diffAbsVal !== '-') {
        item.diffAbsVal = item.diffAbsVal === '' ? '' : item.diffAbsVal + '%';
      }
      if (item.MDE !== '-') {
        item.MDE = item.MDE === '' ? '' : item.MDE + '%';
      }
    },
    formatIndDataFormat1(item) {
      item.indValue = item.indValue ? item.indValue : '0';
      item.numPersent = item.numPersent ? item.numPersent : '0';
      item.num = item.num ? item.num : '0';
      if (item.diffAbsVal !== '-') {
        item.diffAbsVal = item.diffAbsVal === '' ? '' : item.diffAbsVal;
      }
    },
    // 获取任意一天的开始时间
    handleStartTime(time) {
      const nowTimeDate = new Date(time);
      return nowTimeDate.setHours(0, 0, 0, 0);
    },
    // 获取任意一天的结束时间
    handleEndTime(time) {
      const nowTimeDate = new Date(time);
      return nowTimeDate.setHours(23, 59, 59, 999);
    },
    formatTime(time) {
      // 将毫秒级别时间戳转换为秒级别
      return +time.substr(0, 10);
    },
    // 实验指标选择改变
    changeIndicatorId(val) {
      this.indicatorList.forEach((item) => {
        if (item.indId === this.indicatorId) {
          this.indDataFormat = item.indDataFormat;
          this.canP = item.canP;
        }
      });
      // this.getExpindsum();
      this.getDataollect(2);
      // 默认为当日新增用户趋势
      this.choosedAdd();
    },
    // 下面趋势
    // 获取实验指标数据天级趋势或时级趋势数据
    getExpindDayOrTimeData(isAddDay) {
      const { startTime, stopTime } = this.genNewParms();
      // console.log('this.indexAnalyse', this.indexAnalyse);

      // 如果没有选中指标，则不清请求
      if (!this.indextwo) return;
      const param = {
        startTime: startTime,
        stopTime: stopTime,
        experimentId: +this.expId,
        // indId: this.indicatorId,
        indId: this.indextwo,
        timeType: this.timeForm.timeType,
        calRange: isAddDay ? 2 : 1 // 1: 天级/时级  2: 当日新增用户趋势
      };

      this.$service
        .get('REPORT_FOUND_EXPINDDAY', { ...param }, { needLoading: false })
        .then((res) => {
          this.dayTrendData = this.formatExpindDayOrTimeData(res.timeList);
        })
        .catch(() => { });
    },
    // 获取实验指标p-value的曲线数据
    getPValue(isAddDay) {
      const { startTime, stopTime } = this.genNewParms();
      // 如果没有选中指标，则不清请求
      // console.log("this.indextwo-----", this.indextwo);
      if (!this.indextwo) {
        // this.$message.warning('请选择指标222');
        return;
      }

      const param = {
        startTime: startTime,
        stopTime: stopTime,
        experimentId: +this.expId,
        indId: this.indextwo,
        timeType: this.timeForm.timeType,
        calRange: isAddDay ? 2 : 1 // 1: 天级/时级  2: 当日新增用户趋势
      };
      //debugger;
      this.$service
        .get('GETPVALUE', { ...param }, { needLoading: false })
        .then((res) => {
          //debugger;
          this.pValueData = this.formatPValueData(res.timeList);
        })
        .catch(() => { });
    },
    // 获取实验指标累积趋势p-value的曲线数据
    getExpindcump() {
      const { startTime, stopTime } = this.genCommonParms();
      // 如果没有选中指标，则不清请求
      if (!this.indicatorId) return;
      const param = {
        startTime,
        stopTime,
        experimentId: +this.expId,
        indId: this.indicatorId
      };
      //debugger;
      this.$service
        .get('EXPINDCUMP', { ...param }, { needLoading: false })
        .then((res) => {
          //debugger;
          this.cumpValueData = this.formatPValueData(res.timeList);
        })
        .catch(() => { });
    },
    // 处理天级趋势或时级趋势数据或累积趋势数据
    formatExpindDayOrTimeData(data) {
      let result = [];
      //debugger;
      Object.keys(data).forEach((item) => {
        data[item].forEach((list, idx) => {
          result.push({
            time: item,
            versionName: list.versionName,
            persent: list.persent,
            change: idx == 0 ? '' : list.change,
            indDataFormat: this.indDataFormat // 指标数值类型 1:数字 2:百分比
          });
        });
      });
      //debugger;
      return result;
    },
    // 处理p-value
    formatPValueData(data) {
      let result = [];
      //debugger;
      Object.keys(data).forEach((item) => {
        data[item].forEach((list, idx) => {
          result.push({
            time: item,
            versionName: list.versionName,
            value: list.value,
            indDataFormat: this.indDataFormat // 指标数值类型 1:数字 2:百分比
          });
        });
      });
      //debugger;
      return result;
    },
    // 获取实验累计趋势数据
    getExpindCumData() {
      const { startTime, stopTime } = this.genCommonParms();
      // 如果没有选中指标，则不清请求
      if (!this.indicatorId) return;
      const param = {
        startTime,
        stopTime,
        experimentId: +this.expId,
        indId: this.indicatorId
      };
      //debugger;
      this.$service
        .get('REPORT_FOUND_EXPINDCUM', { ...param }, { needLoading: false })
        .then((res) => {
          //debugger;
          this.cumTrendData = this.formatExpindDayOrTimeData(res.timeList);
        })
        .catch(() => { });
    },
    handleDeal() {
      if (this.value6 === 1) {
        this.choosedDay();
      } else {
        this.choosedAdd();
      }
    },
    choosedDay() {
      this.radio = 1;
      this.isStatisticDay = true;
      this.isStatisticTotal = false;
      this.isStatisticAdd = false;

      // 获取实验指标数据天级趋势或时级趋势数据
      this.getExpindDayOrTimeData();
      // 获取p-value数据
      this.getPValue();
    },
    choosedTotal() {
      this.radio = 1;
      this.isStatisticDay = false;
      // this.isStatisticTotal = true;
      this.isStatisticAdd = false;
      // 获取累计趋势数据
      this.getExpindCumData();

      // 获取累计趋势p-value
      this.getExpindcump();
      //debugger
    },
    choosedAdd() {
      this.radio = 1;
      this.isStatisticDay = false;
      this.isStatisticAdd = true;
      // 获取日增趋势数据
      this.getExpindDayOrTimeData(2);
      // 获取日增趋势p-value
      this.getPValue(2);
    },
    // 新查询
    handleNewSearch() {
      this.paginationInit.currentPage = 1;
      // 如果是当前处于实验tab下 isExperiment
      if (this.isExperiment) {
        // 获取指标列表
        // this.getIndicatorList();
        this.getDataollect(2);
      } else {
        //
        //获取留存table数据
        this.getRemainTableData();
        // 获取同期群留存趋势
        this.getRemainSameTermData();
        this.getDaysList();
      }
    },
    //实验多维度指标
    getExMoreList() {
      const param = {
        experimentId: this.expId
      };
      this.$service
        .get('REPORT_FOUND_EXPINDLIST', { ...param }, { needLoading: true })
        .then((res) => {
          //debugger;
          this.indicatorList = res.indList;
          if (res.indList.length > 0) {
            this.$nextTick(() => {
              this.indicatorId = res.indList[0].indId;
              this.indDataFormat = res.indList[0].indDataFormat;
              this.canP = res.indList[0].canP;
              // 获取实验指标汇总数据
              // this.getExpindsum();
              this.getDataollect(2);
              this.choosedAdd();
            });
          }
        })
        .catch(() => { });
    },
    // 点击查询
    handleSearch() {
      this.tableColumns = [];
      this.targetColumns = [];

      if (this.formData.norm.length > 0) {
        this.formData.norm.forEach((item) => {
          item.name = item.value;
        });
        this.targetColumns.push(...this.formData.norm);
      }

      if (this.formData.group.length > 0) {
        this.tableColumns.push({
          label: '分组',
          name: 'appVersion',
        });
      } else {
        // for(var i = 0; i < this.tableColumns.length; i++) {
        //   if(this.tableColumns[i].name === 'group') {
        //     this.tableColumns.splice(i, 1);
        //   }
        // }
        // console.log('this.tableColumns', this.tableColumns);
      }

      if (this.formData.userType.length > 0) {
        this.tableColumns.push({
          label: '用户类型',
          name: 'userType',
        });
      }

      if (this.formData.appVersions.length > 0) {
        this.tableColumns.push({
          label: 'APP版本',
          name: 'versionName',
        });
      }
      // console.log('this.tableColumns', this.tableColumns)
      // console.log('this.formData', this.formData);
      let groups = this.formData.group.map((item) => {
        return item.value;
      });
      let userTypes = this.formData.userType.map((item) => {
        return item.value;
      });
      let norms = this.formData.norm.map((item) => {
        return item.value;
      });

      if (!this.timeForm.startTime || !this.timeForm.stopTime) {
        this.$message.warning('请选择时间');
        return;
      }
      // 如果是当前处于实验tab下
      if (this.isExperiment) {
        // 获取指标列表
        // this.getIndicatorList();
      } else {
        //获取留存table数据
        this.getRemainTableData();
        // 获取同期群留存趋势
        this.getRemainSameTermData();
        this.getDaysList();
      }
    },

    // 获取留存table详细数据,留存只有天级别选项
    getRemainTableData() {
      const { startTime, stopTime } = this.genCommonParms();
      const param = {
        startTime,
        stopTime,
        experimentId: +this.expId
      };
      //debugger;
      this.$service
        .get('REPORT_FOUND_EXPINDDETAIL', { ...param }, { needLoading: true })
        .then((res) => {
          //debugger;
          this.remainTableData = this.formatRemainTableData(res.versionList);
        })
        .catch(() => { });
    },
    // 处理留存table数据
    formatRemainTableData(data) {
      let temp = cloneDeep(data);
      temp.forEach((item) => {
        item.originVersionId = item.versionId;
        // if (item.confidInterval) {
        //   this.formatConfidInterval(item);
        // }
        item.detailList.forEach((list, idx) => {
          list.isCompare = item.isCompare; // 是否是对照版本
          list.versionId = item.versionId + '' + idx;
          list.originVersionId = item.versionId;
          list.versionName = list.date;
          // if (list.confidInterval) {
          //   this.formatConfidInterval(list);
          // }
        });
      });
      //debugger;
      return temp;
    },
    // 处理留存table hover后的请求数据
    handleTableEnter: debounce(function (row, column, cell, event) {
      console.log(row, column, cell, event);
      let versionId = row.originVersionId;
      let date = row.date || ''; // 可能不存在，不存在的话意味着hover在父层
      let pos = column.label.indexOf('天后');
      let day = pos > -1 ? 'no' + column.label.slice(0, pos) : '';
      let isEmpty = row[day] && row[day]['value'] ? false : true;
      // console.log(10000000);
      console.log(versionId, date, pos, day, row[day], isEmpty);
      //debugger;
      if (!day || row.isCompare || isEmpty) return; //

      // 如果已经请求过了，就不再请求
      let isRequested = false;

      this.remainTableData.forEach((item, index) => {
        if (date) {
          if (item.versionId === versionId) {
            item.detailList.forEach((list, idx) => {
              //debugger
              if (list['date'] === date) {
                if (list[day] && list[day]['diffAbsVal'] !== undefined) {
                  isRequested = true;
                }
              }
            });
          }
        } else {
          //debugger
          if (item.versionId === versionId) {
            if (item[day] && item[day]['diffAbsVal'] !== undefined) {
              isRequested = true;
            }
          }
        }
      });
      //debugger;
      //console.log("isRequested:"+isRequested);
      if (isRequested) return;

      const { startTime, stopTime } = this.genCommonParms();
      let param = {
        experimentId: +this.expId,
        versionId,
        day,
        date,
        startTime,
        stopTime
        //date
      };

      this.$service
        .get('EXPRETDAYP', { ...param }, { needLoading: false })
        .then((res) => {
          let copyData = cloneDeep(this.remainTableData);
          console.log('copyData');
          console.log(copyData);
          copyData.forEach((item, index) => {
            //debugger;
            if (date) {
              if (item.versionId === versionId) {
                item.detailList.forEach((list, idx) => {
                  if (list['date'] === date) {
                    if (res.confidInterval) {
                      this.formatConfidInterval(res);
                    }
                    list[day] = res;
                  }
                });
              }
            } else {
              if (item.versionId === versionId) {
                if (res.confidInterval) {
                  //debugger;
                  this.formatConfidInterval(res);
                }
                item[day] = res;
              }
            }
          });
          //debugger;
          this.remainTableData = copyData;
        })
        .catch(() => { });
    }, 400),
    // 获取留存同期群留存趋势数据
    getRemainSameTermData() {
      const { startTime, stopTime } = this.genCommonParms();
      const param = {
        startTime,
        stopTime,
        experimentId: +this.expId
      };
      //debugger;
      this.$service
        .get('REPORT_FOUND_EXPSAMETIMERET', { ...param }, { needLoading: false })
        .then((res) => {
          //debugger;
          this.sameTermData = this.formatRemainSameTermData(res.list);
        })
        .catch(() => { });
    },
    // 格式化同期群留存趋势数据
    formatRemainSameTermData(data) {
      let result = [];
      //debugger;
      data.forEach((item) => {
        item.versionList.forEach((list) => {
          result.push({
            time: item.time + '天后',
            versionName: list.versionName,
            persent: list.persent
          });
        });
      });
      //debugger;
      return result;
    },

    // 获取实验天数
    getDaysList() {
      const param = {
        experimentId: this.expId
      };
      this.$service
        .get('REPORT_FOUND_EXPRETDAYNUM', { ...param }, { needLoading: false })
        .then((res) => {
          const days = [];
          for (let i = 1; i <= res.dayNum; i++) {
            const day = {
              id: i,
              name: `第${i}天留存`
            };
            days.push(day);
          }
          this.daysList = days;
          this.dayNum = this.daysList.length > 0 ? this.daysList[0].id : '';
          this.getNRemainData();
        });
    },
    // 改变留存指标天数时候获取N日留存日趋图
    nDayChange() {
      this.getNRemainData();
    },
    // 获取N日留存日趋图
    getNRemainData() {
      if (this.dayNum === '') {
        return;
      }
      const { startTime, stopTime } = this.genCommonParms();
      const param = {
        startTime,
        stopTime,
        experimentId: +this.expId,
        dayNum: this.dayNum
      };
      this.$service
        .get('REPORT_FOUND_EXPNTIMERET', { ...param }, { needLoading: false })
        .then((res) => {
          console.log('获取N日留存日趋图');
          this.nDayTimeData = this.formatNRemainData(res.list);
        })
        .catch(() => { });
    },
    // 格式化N日留存日趋图数据
    formatNRemainData(data) {
      let result = [];
      //debugger;
      data.forEach((item) => {
        item.versionList.forEach((list) => {
          result.push({
            time: item.time + '进组',
            versionName: list.versionName,
            persent: list.persent,
            num: list.num
          });
        });
      });
      //debugger;
      return result;
    },
    downloadData() {
      const { startTime, stopTime } = this.genCommonParms();
      //debugger;
      let url =
        process.env.VUE_APP_PROXY +
        `/earthworm/mis/report/expretdown?experimentId=${this.expId}&startTime=${startTime}&stopTime=${stopTime}`;
      //debugger;
      this.$downloadUrl(url);
    },
    sameTerm() {
      this.isSameTerm = true;
      this.isNDay = false;
    },
    nDay() {
      // this.isSameTerm = false;
      // this.isNDay = true;
      // 获取留存指标天数
      this.getDaysList();
    },
    // 生成共同的开始时间和结束时间请求参数
    genNewParms() {
      const startTime = +this.formatTime(this.indexAnalyse.startTime.toString());
      const exStopTime = formatDate(this.indexAnalyse.endTime, 'yyyy-MM-dd');
      let stopTime = 0;
      // 如果是天级
      if (this.timeForm.timeType === 1) {
        stopTime = +this.formatTime((this.handleEndTime(exStopTime) + 1).toString());
      }
      // 如果是时级
      // if (this.timeForm.timeType === 2) {
      //   stopTime = +this.formatTime(this.timeForm.stopTime.toString());
      // }
      // const startTime = 11
      // const stopTime = 22
      return { startTime, stopTime };
    },
    // 生成共同的开始时间和结束时间请求参数
    genCommonParms() {
      const startTime = +this.formatTime(this.timeForm.startTime.toString());
      const exStopTime = formatDate(this.timeForm.stopTime, 'yyyy-MM-dd');
      let stopTime = 0;
      // 如果是天级
      if (this.timeForm.timeType === 1) {
        stopTime = +this.formatTime((this.handleEndTime(exStopTime) + 1).toString());
      }
      // 如果是时级
      if (this.timeForm.timeType === 2) {
        stopTime = +this.formatTime(this.timeForm.stopTime.toString());
      }
      return { startTime, stopTime };
    }
  }
};
</script>
<style scoped lang="less">
/deep/.el-tabs__item {
  font-size: 16px;
}

/deep/.el-tabs__item.is-active {
  font-weight: 600;
}

.ex-weidu-wrapper {
  padding-left: 13px;
}

.tem_export {
  color: #42c57a;
  position: absolute;
  right: 40px;
}

/deep/ .el-collapse {
  border: none;
}

/deep/ .zhibiao .el-collapse-item__header {
  background: #f7f8fb;
  margin-top: 20px;
  height: 35px;
  font-size: 14px;
  padding-left: 13px;
  margin-bottom: 20px;
  font-weight: 600;
}

/deep/ .zhibiao2 .el-collapse-item__header {
  position: relative;
  background: #f7f8fb;
  height: 35px;
  font-size: 14px;
  padding-left: 13px;
  margin-bottom: 20px;

  .ex_detail {
    font-weight: 600;
  }
}

/deep/ .zhibiao2 .el-collapse-item__wrap {
  border: none;
}

/deep/ .liucun .el-collapse-item__header {
  background: #f7f8fb;
  // margin-top: 40px;
  height: 35px;
  font-size: 14px;
  padding-left: 13px;
  font-weight: 600;
  // margin-bottom: 20px;
}

.search {
  margin-left: 20px;
}

.com_width /deep/ .el-input--mini .el-input__inner {
  width: 427px;
  // width: 260px;
}

.analysis_wrapper {
  display: flex;

  .analysis_wrapper_title {
    font-size: 14px;
    color: #686a6e;
    margin-right: 12px;
    padding-top: 7px;
  }

  .analysis_wrapper_item {
    padding-top: 5px;
    width: 665px;
  }
}

/deep/ .el-form--inline .el-form-item {
  margin-right: 5px;
}

.filter-container {
  font-size: 14px;
  // margin-top: 10px;
  // padding: 20px;
  // border: 1px solid #f0f1f5;
}

.filterBtns {
  display: flex;
  justify-content: flex-start;
  margin: 0 0 20px 0;
}

.basicBtn {
  color: #25292e;
  background: #fafbfc;
  border: 1px solid #e1e4f0;
  //box-shadow: 0 1px 1px rgb(0 0 0 / 5%);
  border-radius: 4px;
  font-size: 13px;
  padding: 6px 30px;
  cursor: pointer;
}

.selectedAnalysis {
  color: #ffffff;
  font-size: 14px;
  background: #42c57a;
  border: 1px solid #42c57a;
  box-sizing: border-box;
  border-radius: 2px;
}

.selectType {
  //display: flex;
  margin: 20px 0;
}

.selectTypeItem {
  flex: 1;

  /deep/.el-form-item__content {
    width: 100px;
  }

  .filterListStyle {
    padding: 5px 0;
    margin-left: 20px;

    /deep/ .el-select {
      width: 120px;
      margin-right: 6px;
    }

    .setVariant-select-tags {
      width: 30%;
    }
  }

  /deep/ .el-date-editor--daterange.el-input,
  .el-date-editor--daterange.el-input__inner,
  .el-date-editor--datetimerange.el-input,
  .el-date-editor--datetimerange.el-input__inner,
  .el-date-editor--timerange.el-input,
  .el-date-editor--timerange.el-input__inner {
    width: 280px;
  }

  /deep/ .el-button--medium {
    padding: 0 20px;
    border-radius: 4px;
  }
}

.selectTypeItem-time {
  padding-right: 15px;
  height: 100%;
  position: relative;
}

.selectTypeItem-filter {
  border-left: 1px solid gainsboro;
  padding-left: 15px;
  height: 100%;
  position: relative;
}

.selectTypeItemLabel {
  margin: 0 8px 16px 0;
}

.add-rule-qie {
  top: 0;
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
}

.add-rule-btn {
  margin-left: 20px;
}

.selectTypeItem-time-filter {
  position: absolute;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
}

.date-picker {
  display: inline-block;
}

/deep/ .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 135px;
}

.date-pick {

  //width: 310px;
  span {
    padding: 0 6px;
  }
}

// 实验table
.indicator-container-item {
  font-size: 14px;
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #f0f1f5;
  // border: 1px solid red;
}

.select-indicators {
  margin-top: 12px;
  display: flex;
  justify-content: space-between;
}

.indicator-forward {
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: rgb(31, 212, 181);
}

.indicator-negative {
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: rgb(237, 84, 107);
}

.indicator-non-significant {
  display: inline-block;
  vertical-align: top;
  margin-top: 2px;
  margin-right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: rgb(47, 47, 63);
}

.select-indicators-title {
  margin-right: 12px;
}

.foundation-experiment-statistic {
  font-size: 14px;
}

.statistic-select {
  margin: 20px 0 10px 0;
}

.statistic-range {
  margin-left: 20px;
}

.statistic-item {
  height: 450px;
}

.ant-empty {
  height: 450px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.ant-empty-image {
  height: 100px;
  margin-bottom: 80px;
}

.statistic-type-change {
  display: flex;
  justify-content: space-between;
}

.statistic-type-change-item {
  height: 55px;
  width: 50%;
  text-align: center;
  background: linear-gradient(rgba(118, 122, 138, 0.08) 1%, rgb(251, 251, 251));
  cursor: pointer;
}

.statistic_choosed {
  border-top: 4px solid #42c57a;
}

.statistic-type-change-item-content {
  padding-top: 20px;
}

.warning-notice {
  vertical-align: middle;
}

.warning-outline {
  margin: 0 0 0 10px;
}

// 留存
.remain-wrap {
  .indicator-container {
    font-size: 14px;
    margin-top: 10px;
    // padding: 20px;
    // border: 1px solid #f0f1f5;
  }

  .indicator-download {
    color: #42c57a;
    float: right;
    cursor: pointer;
  }

  .statistic-type-change {
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
  }

  .statistic-type-change-item {
    height: 55px;
    width: 50%;
    text-align: center;
    background: linear-gradient(rgba(118, 122, 138, 0.08) 1%, rgb(251, 251, 251));
    cursor: pointer;
  }

  .statistic_choosed {
    border-top: 4px solid #42c57a;
  }

  .statistic-type-change-item-content {
    padding-top: 20px;
  }

  .n-day-remain-title {
    margin: 10px 0 20px 0;
  }

  .n-day-remain-label {
    padding-left: 14px;
    margin-top: 10px;
    margin-right: 10px;
  }

  .n-day-echarts {
    width: 100%;
  }

  .n-day-chart {
    width: 100%;
    height: 400px;
    margin: 20px auto;
  }
}
</style>
