<template>
  <div class="filter-container">
    <!-- 基础分析 -->
    <div class="filterBtns">
      <div @click="experiment" :class="['basicBtn', {selectedAnalysis: isExperiment}]">
        实验关注指标
      </div>
      <div
        @click="remain"
        :class="['basicBtn', {selectedAnalysis: isRemain}]"
        v-if="experimentStartToday"
      >
        留存指标
      </div>
    </div>
    <!-- 筛选维度 -->
    <div class="selectType">
      <div class="selectTypeItem">
        <div class="selectTypeItem-time">
          <div class="selectTypeItem-time-filter">
            <p class="selectTypeItemLabel">时间筛选:</p>
            <el-form inline :model="timeForm" size="mini">
              <el-form-item>
                <el-select v-model="timeForm.timeLevel" @change="timeLevelChange">
                  <el-option
                    v-for="item in timeLevelList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-select v-model="timeForm.range">
                  <el-option
                    v-for="item in rangeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <template>
                  <div v-if="timeForm.timeLevel === 2" class="date-pick">
                    <!-- 小时级 -->
                    <el-date-picker
                      v-model="timeForm.startTime"
                      type="datetime"
                      format="yyyy-MM-dd HH:mm"
                      disabled
                      class="date-picker"
                      value-format="timestamp"
                    ></el-date-picker>
                    <span>至</span>
                    <el-date-picker
                      v-model="timeForm.endTime"
                      type="datetime"
                      :picker-options="pickerOptions"
                      @change="datetimerangeChange"
                      format="yyyy-MM-dd HH:mm"
                      value-format="timestamp"
                      class="date-picker"
                    ></el-date-picker>
                  </div>
                </template>
                <template>
                  <div v-if="timeForm.timeLevel === 1" class="date-pick">
                    <!-- 天级 -->
                    <el-date-picker
                      v-model="timeForm.startTime"
                      type="date"
                      value-format="timestamp"
                      format="yyyy-MM-dd"
                      disabled
                    ></el-date-picker>
                    <span>至</span>
                    <el-date-picker
                      v-model="timeForm.endTime"
                      type="date"
                      :picker-options="pickerOptions"
                      @change="daterangeChange"
                      value-format="timestamp"
                      format="yyyy-MM-dd"
                    ></el-date-picker>
                  </div>
                </template>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <!-- 过滤筛选 -->
      <div class="selectTypeItem">
        <div class="selectTypeItem-filter">
          <p class="selectTypeItemLabel">过滤筛选:</p>
          <div class="add-rule-qie" v-if="timeForm.filterList.length > 1 && showFilters">且</div>
          <div v-if="showFilters" class="filterListStyle">
            <div v-for="(solist, index) in timeForm.filterList" :key="`filterList${index}`">
              <el-select
                size="mini"
                class="setVariant-select"
                v-model="solist.keyName"
                @change="filterRuleOne(index)"
              >
                <el-option
                  v-for="item in filterListOption"
                  :key="item.id"
                  :label="item.displayName"
                  :value="item.keyName"
                ></el-option>
              </el-select>
              <el-select
                :disabled="solist.filterRuleSecondDisabled"
                size="mini"
                class="setVariant-select"
                v-model="solist.keyType"
                @change="filterRuleTwo(index)"
              >
                <el-option
                  v-for="(item, index) in filterListKeyType"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-input
                :disabled="solist.filterRuleThirdTags"
                size="mini"
                v-model="solist.keyValue"
                class="setVariant-select-tags"
              ></el-input>
              <i class="el-icon-close icon-close" @click="deleteRules(index)"></i>
            </div>
          </div>
          <el-button
            v-if="showFilters"
            class="add-rule-btn"
            @click="addRules"
            icon="el-icon-plus"
            type="text"
          >
            过滤条件
          </el-button>
          <el-button
            icon="el-icon-plus"
            @click="addFilters"
            v-if="!showFilters"
            type="primary"
            size="medium"
            disabled
          >
            添加筛选组
          </el-button>
        </div>
      </div>
    </div>
    <el-button type="primary" size="medium" @click="handleSearch">查询</el-button>
  </div>
</template>
<script>
import {formatDate} from '@/common/utils';

export default {
  name: 'filter-foundation',
  props: {
    experimentData: {
      type: Object,
      default: () => ({}),
    },
    indicatorType: {
      type: Number,
      default: 0,
    },
    searchDataType: {
      type: String,
      default: '',
    },
    // 同期/ N
    saveDataType: {
      type: String,
      default: '',
    },
    nDaySaved: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      experimentStartToday: true, //实验在今天开始
      showFilters: false,
      isExperiment: true,
      isRemain: false,
      timeForm: {
        timeLevel: 1,
        range: 1,
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        filterList: [
          {
            keyName: '',
            keyType: '',
            keyValue: '',
            filterConfId: 1,
            filterRuleSecondDisabled: true, // 过滤维度条件2
            filterRuleThirdTags: true, // 过滤维度条件3
          },
        ], // 过滤维度
      },
      startTime: '', // 实验开始时间
      timeLevelList: [
        {
          id: 2,
          name: '小时级',
        },
        {
          id: 1,
          name: '天级',
        },
      ],
      rangeList: [
        {
          id: 1,
          name: '范围',
        },
      ],
      pickerOptions: {}, // 时间控件选择区间
      indicator: 0,
      filterListOption: [
        {
          id: 1,
          displayName: '条件一',
          keyName: '条件一',
        },
      ], // 过滤维度筛选条件1
      filterListKeyType: [
        {
          label: '条件2',
          value: '1',
        },
      ], //过滤维度筛选条件2
      searchResult: [], // 查询结果
    };
  },
  created() {
    this.startTime = this.experimentData.startTime * 1000;
    // 判断开始日期是否和今日为同一天，若是只有时级
    if (formatDate(this.startTime, 'yyyy-MM-dd') === formatDate(Date.now(), 'yyyy-MM-dd')) {
      this.timeForm.timeLevel = 2;
      this.timeLevelChange(2);
      this.experimentStartToday = false;
    } else {
      this.timeForm.timeLevel = 1;
      this.timeLevelChange(1);
      this.experimentStartToday = true;
    }
    // 实验关注指标
    if (this.isExperiment) {
      const param = {
        experimentId: this.experimentData.id, //实验id
        indId: this.indicator, // 指标id
        startTime: this.timeForm.startTime,
        stopTime: this.timeForm.endTime,
        timeType: this.timeForm.timeLevel, // 1:天级 2小时
      };
      this.handleSearch(param, 'exp');
    }
    // 留存指标
    if (this.isRemain) {
      const param = {
        experimentId: this.experimentData.id, //实验id
        startTime: this.timeForm.startTime,
        stopTime: this.timeForm.endTime,
      };
      this.handleSearch(param);
    }
  },
  watch: {
    experimentData() {
      this.startTime = this.experimentData.startTime * 1000;
      // 判断开始日期是否和今日为同一天，若是只有时级
      if (formatDate(this.startTime, 'yyyy-MM-dd') === formatDate(Date.now(), 'yyyy-MM-dd')) {
        this.timeLevelList.splice(1, 1);
        this.timeForm.timeLevel = 2;
        this.timeLevelChange(2);
        this.experimentStartToday = false;
      } else {
        this.timeLevelList = [
          {
            id: 2,
            name: '小时级',
          },
          {
            id: 1,
            name: '天级',
          },
        ];
        this.timeForm.timeLevel = 1;
        this.timeLevelChange(1);
        this.experimentStartToday = true;
      }
    },
    indicatorType(val) {
      this.indicator = val;
    },
    // 时/天或者累计
    searchDataType(val) {
      let url = val === 'total' ? 'REPORT_FOUND_EXPINDCUM' : 'REPORT_FOUND_EXPINDDAY';
      const param = {
        experimentId: this.experimentData.id, //实验id
        indId: this.indicator, // 指标id
        startTime: this.timeForm.startTime,
        stopTime: this.timeForm.endTime,
        timeType: this.timeForm.timeLevel, // 1:天级 2小时
      };
      // this.getTrendData(url, param);
    },
    // n日留存查询
    nDaySaved(val) {
      const param = {
        experimentId: this.experimentData.id,
        dayNum: val,
        startTime: this.timeForm.startTime,
        stopTime: this.timeForm.endTime,
      };
      // this.getNDayData(param);
    },
    // 同期 / N
    saveDataType(val) {
      // 同期
      if (val === 'sameTerm') {
        const param = {
          experimentId: this.experimentData.id,
          startTime: this.timeForm.startTime,
          stopTime: this.timeForm.endTime,
        };
        // this.getSameTimeData(param);
      }
      // N天
      if (val === 'nDay') {
        const param = {
          experimentId: this.experimentData.id,
          dayNum: val,
          startTime: this.timeForm.startTime,
          stopTime: this.timeForm.endTime,
        };
        // this.getNDayData(param);
      }
    },
    // 监听此组建中的timeForm
    timeForm: {
      handler(n, o) {
        //debugger;
        if (n) {
          //debugger;
          this.$emit('updateTimeForm', this.timeForm);
        }
      },
      deep: true,
    },
  },
  methods: {
    // 实验关注指标
    experiment() {
      this.isExperiment = true;
      this.isRemain = false;
      if (formatDate(this.startTime, 'yyyy-MM-dd') !== formatDate(Date.now(), 'yyyy-MM-dd')) {
        this.timeLevelList = [
          {
            id: 2,
            name: '小时级',
          },
          {
            id: 1,
            name: '天级',
          },
        ];
      }
      this.$emit('isExperiment', true);
    },
    // 留存指标
    remain() {
      this.isExperiment = false;
      this.isRemain = true;
      this.timeLevelList.splice(0, 1);
      this.$emit('isRemain', true);
      this.timeForm.timeLevel = 1;
      this.timeLevelChange(1);
      // 获取同期留存的数据->趋势数据
      const param = {
        experimentId: this.experimentData.id,
        startTime: this.timeForm.startTime,
        stopTime: this.timeForm.endTime,
      };
      this.getSameTimeData(param);
    },
    // 时间筛选类型变化时
    timeLevelChange(val) {
      this.$emit('timeLevel', val);
      const that = this;
      // 小时级
      if (val === 2) {
        let year = new Date().getFullYear();
        let month = new Date().getMonth() + 1;
        let day = new Date().getDate();
        let dateTime = new Date(year + '-' + month + '-' + day).getTime();
        this.pickerOptions = {
          disabledDate(time) {
            return time.getTime() > Date.now() || time.getTime() < dateTime;
          },
        };
        //debugger;
        this.timeForm.startTime = dateTime;
        this.timeForm.endTime = Date.now();
      } else {
        // 天级
        this.pickerOptions = {
          disabledDate(time) {
            const startTime = that.startTime;
            if (startTime) {
              return time.getTime() > Date.now() - 8.64e7 || time.getTime() < startTime - 8.64e7;
            }
          },
        };
        // 初始值设置
        this.timeForm.startTime = this.startTime;
        this.timeForm.endTime = Date.now() - 8.64e7;
      }
    },
    // 小时级
    datetimerangeChange(val) {
      let hours = [];
      hours.push(new Date(val[0]).getHours());
      hours.push(new Date(val[1]).getHours());
      this.$emit('showHoursData', hours);
    },
    // 天级时间改变
    daterangeChange(val) {
      this.$emit('showDaysData', val);
      const param = {
        experimentId: this.experimentData.id, //实验id
        indId: this.indicator, // 指标id
        startTime: this.timeForm.startTime,
        stopTime: this.timeForm.endTime,
        timeType: this.timeForm.timeLevel, // 1:天级 2小时
      };
      this.handleSearch(param);
    },
    // 点击查询
    handleSearch(param, searchType) {
      // 实验或者留存
      let url = searchType === 'exp' ? 'REPORT_FOUND_EXPINDSUM' : 'REPORT_FOUND_EXPINDDETAIL';
      this.$service
        .get(url, {...param}, {needLoading: true})
        .then((res) => {
          // table数据
          this.$emit('getSearchResult', res.versionList);
        })
        .catch(() => {});
    },
    // 天/时 或者累计
    getTrendData(url, param) {
      this.$service.get(url, {...param}, {needLoading: true}).then((res) => {
        this.$emit('getTrendResult', res);
      });
    },
    // 留存数据-同期 ->趋势数据
    getSameTimeData(param) {
      this.$service
        .get('REPORT_FOUND_EXPSAMETIMERET', {...param}, {needLoading: true})
        .then((res) => {
          this.$emit('getSameTimeData', res);
        })
        .catch(() => {});
    },
    // n日留存查询
    getNDayData(param) {
      this.$service
        .get('REPORT_FOUND_EXPNTIMERET', {...param}, {needLoading: true})
        .then((res) => {
          this.$emit('getNDayTimeData', res);
        })
        .catch(() => {});
    },
    // 删除过滤维度
    deleteRules(param) {
      this.timeForm.filterList.splice(param, 1);
      if (this.timeForm.filterList.length === 0) {
        this.showFilters = false;
        this.timeForm.filterList = [
          {
            keyName: '',
            keyType: '',
            keyValue: '',
            filterConfId: 1,
            filterRuleSecondDisabled: true, // 过滤维度条件2
            filterRuleThirdTags: true, // 过滤维度条件3
          },
        ];
      }
    },
    // 增加过滤维度
    addRules() {
      this.timeForm.filterList.push({
        keyName: '',
        keyType: '',
        keyValue: '',
        filterConfId: 1,
        filterRuleSecondDisabled: true,
        filterRuleThirdTags: true,
      });
    },
    // 过滤筛选条件一选择发生变化时
    filterRuleOne(param) {
      this.timeForm.filterList[param].filterRuleSecondDisabled = false;
    },
    // 过滤筛选条件二选择发生变化时
    filterRuleTwo(param) {
      this.timeForm.filterList[param].filterRuleThirdTags = false;
    },
    // 增加过滤组
    addFilters() {
      this.showFilters = true;
    },
  },
};
</script>
<style scoped lang="less">
/deep/ .el-form--inline .el-form-item {
  margin-right: 5px;
}
.filter-container {
  font-size: 14px;
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #f0f1f5;
}
.filterBtns {
  display: flex;
  justify-content: flex-start;
  margin: 0 0 20px 0;
}
.basicBtn {
  color: #25292e;
  background: #fafbfc;
  border: 1px solid #e1e4f0;
  box-shadow: 0 1px 1px rgb(0 0 0 / 5%);
  border-radius: 4px;
  font-size: 13px;
  padding: 6px 30px;
  cursor: pointer;
}
.selectedAnalysis {
  color: #ffffff;
  font-size: 14px;
  background: #42c57a;
  border: 1px solid #42c57a;
  box-sizing: border-box;
  border-radius: 2px;
}

.selectType {
  display: flex;
  margin: 20px 0;
}

.selectTypeItem {
  flex: 1;

  /deep/.el-form-item__content {
    width: 100px;
  }

  .filterListStyle {
    padding: 5px 0;
    margin-left: 20px;

    /deep/ .el-select {
      width: 120px;
      margin-right: 6px;
    }
    .setVariant-select-tags {
      width: 30%;
    }
  }

  /deep/ .el-date-editor--daterange.el-input,
  .el-date-editor--daterange.el-input__inner,
  .el-date-editor--datetimerange.el-input,
  .el-date-editor--datetimerange.el-input__inner,
  .el-date-editor--timerange.el-input,
  .el-date-editor--timerange.el-input__inner {
    width: 280px;
  }
  /deep/ .el-button--medium {
    padding: 0 20px;
    border-radius: 4px;
  }
}

.selectTypeItem-time {
  padding-right: 15px;
  height: 100%;
  position: relative;
}

.selectTypeItem-filter {
  border-left: 1px solid gainsboro;
  padding-left: 15px;
  height: 100%;
  position: relative;
}

.selectTypeItemLabel {
  margin: 0 8px 16px 0;
  font: 13px;
}

.add-rule-qie {
  top: 0;
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
}

.add-rule-btn {
  margin-left: 20px;
}

.selectTypeItem-time-filter {
  position: absolute;
  top: 50%;
  width: 100%;
  transform: translateY(-50%);
}

.date-picker {
  display: inline-block;
}

/deep/ .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 135px;
}

.date-pick {
  width: 310px;
  span {
    padding: 0 6px;
  }
}
</style>
