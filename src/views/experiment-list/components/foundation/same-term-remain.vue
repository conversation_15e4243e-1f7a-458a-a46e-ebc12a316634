<template>
  <div class="same-term-remain">
    <!-- <div>同期群留存趋势555</div> -->
    <!-- echarts -->
    <div class="same-term-echarts">
      <div id="line-chart3"></div>
    </div>
    <!-- notice -->
    <div class="same-term-notice">
      <el-alert type="warning" show-icon title="">
        <template slot="title">
          <div class="iconSize">
            <p>
              当日"已进组用户"
              表示当日曝光进组的总用户数，包括之前已进组的老用户和初次到访的"新进组用户"。例如：
            </p>
            <p>第一日实验版本A的到访用户数为10000，当日"新进组用户"为10000;</p>
            <p>
              第二日实验版本A的到访用户数为10400，其中9200为第一日便已在A中的用户，剩余的1200为第二日"新进组用户"。第一日次日留存为9200/10000=92%;
            </p>
            <p>
              第三日实验版本A的到访用户数为10200，其中8000为第一日便已在A中的用户、1100为第二日进入A中的用户，剩余的1100为第三日"新进组用户"。第一日2日留存为8000/10000=80%，
              第二日次日留存为1100/1200=91.67%。以此类推。
            </p>
            <p>更多详情请参考帮助中心。</p>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>
<script>
import G2 from '@antv/g2';
import {Chart} from '@antv/g2';
import DataSet from '@antv/data-set';
export default {
  name: 'same-term-remain',
  data() {
    return {
      // versionList: ['对照版本', '实验版本1', '实验版本2'],
      // xAxisData: [], // x轴
      // yAxisData: [], //y轴
      // warningTitle: "当日'已进组用户' <br/> dddd",
      chart: null
    };
  },
  props: {
    charData: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  created() {
    // for (let i = 1; i < 30; i++) {
    //   this.xAxisData.push(`${i}天后`);
    // }
  },
  mounted() {
    this.drawChart(this.charData);
  },
  watch: {
    charData(val, oldVal) {
      this.drawChart(val);
    }
  },
  methods: {
    drawChart(data) {
      this.chart && this.chart.destroy();
      this.chart = new Chart({
        container: 'line-chart3',
        forceFit: true,
        autoFit: true,
        height: 400,
        padding: [40, 40, 100, 100]
      });

      this.chart.source(data);
      this.chart.scale({
        time: {
          range: [0.02, 1]
        },
        persent: {
          min: 0,
          nice: true
        }
      });
      this.chart.scale('time', {
        range: [0.01, 1],
        tickCount: 12
      });
      this.chart.tooltip({
        showCrosshairs: true, // 展示 Tooltip 辅助线
        shared: true,
        itemTpl: `
        <div style="margin-bottom: 10px;list-style:none;">
        <span style="background-color:{color};" class="g2-tooltip-marker"></span>
        {name}: &nbsp;&nbsp;&nbsp;{value}
        </div>
        `
      });
      this.chart.axis('persent', {
        label: {
          formatter: val => {
            return val + ' %';
          }
        }
      });
      this.chart
        .line()
        .position('time*persent')
        .color('versionName')
        .shape('smooth')
        .tooltip('time*persent*versionName', function (time, persent, versionName) {
          // tooltip的第一个参数写上需要显示的字段'item1*item2*...'；第二个参数为一个函数，该函数的参数为需要显示的字段。
          return {
            name: versionName,
            value: persent + '%', // 这里也可以通过调用其他自定义函数的方式，去对数据进行更深层次的变换。但要注意this的指向问题！
          };
        });
      this.chart
        .point()
        .position('time*persent')
        .color('versionName')
        .shape('circle');

      this.chart.render();
    }
    // drawEchart() {
    //   let myChart = this.$echarts.init(this.$refs.myChart);
    //   myChart.setOption({
    //     tooltip: {trigger: 'axis'},
    //     legend: {
    //       data: [...this.versionList],
    //     },
    //     xAxis: {
    //       type: 'category',
    //       boundaryGap: false,
    //       data: [...this.xAxisData],
    //     },
    //     yAxis: {
    //       type: 'value',
    //       min: 0,
    //       max: 100,
    //       axisLabel: {
    //         show: true,
    //         interval: 'auto',
    //         formatter: '{value} %',
    //       },
    //       show: true,
    //     },
    //     series: [...this.yAxisData],
    //   });
    // },
  }
};
</script>
<style scoped lang="less">
/deep/ .el-alert__closebtn {
  display: none;
}
.same-term-remain {
  margin-top: 30px;
}
.same-term-charts {
  width: 100%;
}

.same-term-chart {
  width: 100%;
  height: 400px;
  margin: 20px auto;
}

.same-term-notice {
  width: 95%;
  margin: auto;
  margin-top: 30px;
}
</style>
