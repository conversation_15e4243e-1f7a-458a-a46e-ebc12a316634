<template>
  <!-- <Page class="container" :breadcrumbList="breadcrumbList"> -->
    <!-- <div class="search">
      <el-button class="btn_add" icon="el-icon-plus" type="primary" size="small" @click="handleRegisterArg">添加业务线</el-button>
    </div> -->
    <div class="table-content">
      <template-table
        :hasIndex="false"
        class="detail-table"
        ref="multipleTableParent"
        :table-column="tableColumns"
        :table-list="tableList"
        :tableBtns="tableBtns"
        @operationEvent="operationEvent"
        >
      </template-table>
      <template-pagination
        :pagination-init="paginationInit"
        @changePage="changePage"
        @pageSizeChange="pageSizeChange"
      >
      </template-pagination>
      <!-- <service-add-dialog
        v-if="isShowDialog"
        :dialogVisible.sync="isShowDialog"
        :argId="argId"
        :type="type"
        @succeed="handleSucceed"
      ></service-add-dialog> -->
    </div>
  <!-- </Page> -->
</template>

<script>
import Page from '@/components/common/page/index.vue';
import {formatDate} from '@/common/utils';
import {debounce, cloneDeep} from 'lodash';
import TemplateTable from '@/components/tools/template-table.vue';
import TemplatePagination from '@/components/tools/template-pagination.vue';
// import ServiceAddDialog from './service-add-dialog.vue';


export default {
  name: 'more',
  components: {
    Page,
    TemplateTable,
    TemplatePagination,
    // ServiceAddDialog
  },
  data() {
    return {
      appOptionsData: [],
      argId: '',
      isShowDialog: false,
      breadcrumbList: ['ServiceLine'],
      paginationInit: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
    tableBtns: [
        {
          label: '编辑',
          role: 'edit'
        }
        // {
        //   label: '删除',
        //   role: 'del',
        //   disabledHandler: (index, row) => {
        //     // 修改按钮是否可点击
        //     if (row.isDelete === 0) return true;
        //   }
        // }
      ],
      formData: {
        appKey: '',
        keyword: ''
      },
      total: 0,
      tableList: [],
      tableColumns: [
        {
          label: '业务线ID',
          name: 'id',
        },
        {
          label: '业务线名称',
          name: 'displayName'
        },
        {
          label: '描述',
          name: 'description',
          'min-width': '105px',
           render: (h, {row}) => {
            const { description } = row;
            let props = {
              props: {
                content: description
              },
            };
            return (
              <el-popover
                placement="top-start"
                width="200"
                trigger="hover"
                {...props}
                >
                <p  slot="reference" style="overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">{description}</p>
              </el-popover>
              );
          }
        },
        {
          label: 'key',
          name: 'appKey',
          // 'min-width': '105px'
        },
        {
          label: '创建人',
          name: 'createUser',
          'min-width': '105px'
        },
        {
          label: '更新人',
          name: 'updateUser',
          'min-width': '105px',
          render: (h, {row}) => {
            const { updateUser } = row;
            const text =  updateUser ? updateUser : '-';
            return <p>{ text }</p>;
          }
        },
        {
          label: '创建时间',
          name: 'createTime',
          'min-width': '105px',
          render: (h, {row}) => {
            const { createTime } = row;
            return <p>{formatDate(createTime * 1000, 'yyyy-MM-dd')}</p>;
          }
        },
        {
          label: '更新时间',
          name: 'updateTime',
          'min-width': '105px',
          render: (h, {row}) => {
            const { updateTime } = row;
            return <p>{formatDate(updateTime * 1000, 'yyyy-MM-dd')}</p>;
          }
        }
      ],
      businessOptions: [],
      metricId: {
        flight: 0,
        history: 0,
        modify: 0,
      },
      flightVisible: false,
      historyVisible: false,
      modifyVisible: false,
      pagination: {
        pn: 1,
        rn: 10,
      },
      type: ''
    };
  },
  computed: {},
  mounted() {
    this.handleSearch();
  },
  methods: {
    handleSucceed() {
      this.paginationInit.currentPage = 1;
      this.getList();
    },
    handleName(row) {
      this.type = 'view';
      this.argId = row.id;
      this.isShowDialog = true;
    },
    handleRegisterArg() {
      this.type = 'add';
      this.argId = '';
      this.isShowDialog = true;
    },
     pageSizeChange(e) {
      this.paginationInit.currentPage = 1;
      this.getList();
    },
    changePage(page) {
      this.paginationInit.currentPage = page;
      this.getList();
    },
    operationEvent(row, btnRole) {
      switch (btnRole) {
        case 'edit':
          this.isShowDialog = true;
          this.argId = row.id;
          this.type = 'edit';
          break;
      }
    },
    // 查询列表数据
    handleSearch() {
      this.paginationInit.currentPage = 1;
      this.getList();
    },
    getList() {
      let params = {
        pn: this.paginationInit.currentPage, // 第几页
        rn: this.paginationInit.pageSize,
        notall: 1
      };
      this.$service
        .get('FEATUREAPPLIST', { ...params }, {needLoading: true})
        .then((res) => {
          this.tableList = res.list || [];
          this.paginationInit.total = res.total;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 重置
    handleReset() {
      this.paginationInit.currentPage = 1;
      this.formData = {
        appKey: '',
        keyword: ''
      };
      this.getList();
    }
  }
};
</script>

<style lang="less" scoped>

.description {
  overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.search {
  position: relative;
  // display: flex;
  // justify-content: space-between;

  .el-select {
    width: 175px;
  }
}

.keyword_input {
  width: 270px;
  margin-left: -1px;

  /deep/ .el-input__suffix {
    right: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    cursor: pointer;
  }
}
/deep/ .el-table {
  font-size: 14px;
}
.btn_add {
  // position: absolute;
  // top: -2px;
  // right: 0;
  float: right;
}


.table-pagination {
  margin-top: 20px;
  text-align: right;
}

.table_description {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.related_flights_text {
  color: rgb(70, 96, 239);
  cursor: pointer;
}

.table_tag {
  display: inline-block;
  border-radius: 4px;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  color: #2f2f3f;
  background: #f8f8fc;
  border: 1px solid #e7e9f5;
}

.table_tag_required {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
}

.action_edit {
  margin-right: 10px;
}
/deep/ .op-switch.is-disabled .el-switch__core,
/deep/ .op-switch.is-disabled .el-switch__label {
  background-color: rgb(155, 156, 158);
  border-color: rgb(155, 156, 158);
  cursor: pointer;
}
/deep/ .el-switch.is-disabled.is-checked .el-switch__core,
/deep/ .op-switch.is-disabled.is-checked .el-switch__label {
  border-color: #42c57a;
  background-color: #42c57a;
}
/deep/ .el-button--text-primary > span {
  font-size: 14px;
}
</style>