<template>
  <div class="popoverContent" style="font-size: 14px">
    <template v-if="typeof rowData[`no${n}`] == 'object'">
      <div class="pb10">
        {{ rowData.versionName }}：
        {{ rowData[`no${n}`]['value'] + '%' }}
      </div>
      <div class="pb10">
        差异绝对值：
        <template v-if="rowData[`no${n}`]['diffAbsVal']">
          {{ rowData[`no${n}`]['diffAbsVal'] == '-' ? '-' : rowData[`no${n}`]['diffAbsVal'] + '%' }}
        </template>
        <template v-else>
          <span>-</span>
        </template>
      </div>
      <div class="pb10">
        差异相对值：
        <template v-if="rowData[`no${n}`]['diffRelaVal']">
          {{
            rowData[`no${n}`]['diffRelaVal'] == '-' ? '-' : rowData[`no${n}`]['diffRelaVal'] + '%'
          }}
        </template>
        <template v-else>
          <span>-</span>
        </template>
      </div>
      <div class="pb10" style="display: flex;align-items:center;">
        置信区间：
        <template
          v-if="rowData[`no${n}`]['confidInterval'] && rowData[`no${n}`]['confidInterval'].length"
        >
          <!-- <span>[{{ rowData[`no${n}`]['confidInterval'][0] }}%</span>
          ,
          <span>{{ rowData[`no${n}`]['confidInterval'][1] }}%]</span> -->
           <template v-if="rowData[`no${n}`]['isShowCon']">
              <confidence
                :min="rowData[`no${n}`]['confidInterval'][0]"
                :max="rowData[`no${n}`]['confidInterval'][1]"
                :pValue="rowData[`no${n}`]['pValue']"
                :diffRelaVal="rowData[`no${n}`]['diffRelaVal']+'%'"
                :isConfidence="rowData[`no${n}`]['isConfidence']"
                :color="rowData[`no${n}`]['color']"
                :customWidth="200+'px'"
                :customHeight="55+'px'"
              ></confidence>
            </template>
        </template>
        <template v-else>-</template>
      </div>
      <div class="pb10">
        p-value：
        <template v-if="rowData[`no${n}`]['pValue'] !== undefined">
          <!-- {{ rowData[`no${n}`]['pValue'] }} -->
          {{ rowData[`no${n}`]['pValue'] == '-' ? '-' : rowData[`no${n}`]['pValue'] }}
        </template>
        <template v-else>
          <span>-</span>
        </template>
      </div>
      <div class="pb10">
        MDE：
        <template v-if="rowData[`no${n}`]['MDE']">
          {{ rowData[`no${n}`]['MDE'] == '-' ? '-' : rowData[`no${n}`]['MDE'] + '%' }}
        </template>
        <template v-else>
          <span>-</span>
        </template>
      </div>
    </template>
  </div>
</template>
<script>
import confidence from './confidence.vue';
export default {
  name: 'popover-content',
  components:{
    confidence
  },
  props: {
    rowData: {
      type: Object,
      default: () => {
        {
        }
      }
    },
    n: Number
  }
  //   data(){
  //       return {
  //           loading: true
  //       }
  //   },

  // watch:{
  //   rowData(newVal,oldVal){
  //       if(newVal){
  //           this.loading = false;
  //       }
  //   }
  // }
};
</script>
<style scoped lang="less">
.pb10 {
  padding-bottom: 10px;
}
</style>

