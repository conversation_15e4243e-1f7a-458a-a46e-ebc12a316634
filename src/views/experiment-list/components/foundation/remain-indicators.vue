<template>
  <div class="indicator-container">
    <div v-if="isSameTerm">详细数据</div>
    <!-- 下载数据 -->
    <div class="indicator-download" @click="downloadData" v-if="isSameTerm">
      <i class="el-icon-download"></i>
      <span>下载数据</span>
    </div>
    <!-- table -->
    <foundation-remain-table
      v-if="isSameTerm"
      :searchResult="searchResultData"
    ></foundation-remain-table>
    <!-- 同期群留存趋势 -->
    <same-term-remain v-if="isSameTerm"></same-term-remain>
    <!-- N日留存日趋图 -->
    <n-day-remain
      v-if="isNDay"
      :experimentData="experiment"
      :nDayTimeData="nDayTimeData"
      @getNDayChange="getNDayChange"
    ></n-day-remain>

    <!-- 趋势切换 -->
    <div class="statistic-type-change">
      <div
        :class="['statistic-type-change-item', {statistic_choosed: isSameTerm}]"
        @click="sameTerm"
      >
        <div class="statistic-type-change-item-content">
          <i class="el-icon-s-data"></i>
          <span>同期群留存趋势</span>
        </div>
      </div>

      <div :class="['statistic-type-change-item', {statistic_choosed: isNDay}]" @click="nDay">
        <div class="statistic-type-change-item-content">
          <i class="el-icon-s-marketing"></i>
          <span>N日留存日趋图</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import foundationRemainTable from './foundation-remain-table.vue';
import sameTermRemain from './same-term-remain.vue';
import nDayRemain from './n-day-remain.vue';
export default {
  components: {foundationRemainTable, sameTermRemain, nDayRemain},
  name: 'remain-indicators',
  props: {
    searchResultData: {
      type: Array,
      default: () => {
        [];
      },
    },
    experiment: {
      type: Object,
      default: () => ({}),
    },
    parTimeForm: {
      type: Object,
      default: () => ({}),
    },
    // N日留存日趋数据
    nDayTimeData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isSameTerm: true,
      isNDay: false,
    };
  },
  watch: {
    // stopTimeForRemain: {
    //   handler(newVal,oldVal){
    //     debugger;
    //   }
    // }
  },
  methods: {
    downloadData() {
      const startTime = this.formatTime(this.parTimeForm.startTime.toString()),
        stopTime = this.formatTime(this.parTimeForm.stopTime.toString());
      //debugger;
      let url =
        process.env.VUE_APP_PROXY +
        `/earthworm/mis/report/expretdown?experimentId=${this.experiment.id}&startTime=${startTime}&stopTime=${stopTime}`;
      //debugger;
      this.$downloadUrl(url);
    },
    formatTime(time) {
      // 将毫秒级别时间戳转换为秒级别
      return +time.substr(0, 10);
    },
    sameTerm() {
      this.$emit('getSaveData', 'sameTerm');
      this.isSameTerm = true;
      this.isNDay = false;
    },
    nDay() {
      this.$emit('getSaveData', 'nDay');
      this.isSameTerm = false;
      this.isNDay = true;
    },
    getNDayChange(val) {
      this.$emit('getNDayChange', val);
    },
  },
};
</script>
<style scoped>
.indicator-container {
  font-size: 14px;
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #f0f1f5;
}
.indicator-download {
  color: #42c57a;
  float: right;
  cursor: pointer;
}
.statistic-type-change {
  margin-top: 30px;
  display: flex;
  justify-content: space-between;
}

.statistic-type-change-item {
  height: 55px;
  width: 50%;
  text-align: center;
  background: linear-gradient(rgba(118, 122, 138, 0.08) 1%, rgb(251, 251, 251));
  cursor: pointer;
}
.statistic_choosed {
  border-top: 4px solid #42c57a;
}

.statistic-type-change-item-content {
  padding-top: 20px;
}
</style>
