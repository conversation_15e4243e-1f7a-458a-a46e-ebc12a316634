<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-27 17:12:19
 * @LastEditors: huy<PERSON><PERSON>
 * @LastEditTime: 
 * @Description: 实验报告-实验关注指标-置信区间
-->

<template>
  <el-popover placement="right" width="400" trigger="hover">
    <div class="p-wrap">
      <p class="p-con">
        置信区间为: [
        <span>{{ min }}%</span>
        ,
        <span>{{ max }}%</span>
        ]
      </p>
      <p class="p-con">p-value: {{ pValue }}</p>
      <p class="p-con">
        实验版本样本均值对比对照版本的变化率为{{ diffRelaVal }}。在{{
          confidenceVal
        }}置信度下，置信区间为[
        <span>{{ min }}</span>
        % ,
        <span>{{ max }}</span>
        %]，
        <span v-if="isConfidence === 1">统计显著正向说明当前的样本容量条件下已经检测出实验版本优于对照版本。</span>
        <span v-if="isConfidence === 0">统计显著负向说明当前的样本容量条件下已经检测出实验版本在核心指标上劣于对照版本。</span>
        <span v-if="isConfidence === 2">置信区间一负一正，实验结果是非统计显著的。</span>
        
      </p>
    </div>
    <div slot="reference" class="p-wrap-con" :style="[{width: (customWidth ? customWidth : '100%') },{height: (customHeight ? customHeight : '80px') }]">
      <div class="confidence-retain-content">
        <span v-if="isConfidence === 2" class="confidence" style="color: #8b8ba6">{{ diffRelaVal }}</span>
        <span v-else class="confidence" :style="`color: ${color}`">{{ diffRelaVal }}</span>
        <div class="color-box" :style="`background-color: ${color}`">
          <span class="min-span">{{min}}%</span>
          <div class="left-border" :style="`background-color: ${color}`"></div>
          <div class="center-border" ></div>
          <div class="right-border" :style="`background-color: ${color}`"></div>
          <span class="max-span">{{max}}%</span>
        </div>
      </div>
    </div>
  </el-popover>
</template>

<script>
export default {
  name: 'confidence',
  components: {},
  props: {
    min: {
      type: Number | String,
      default: function () {
        return -1.382;
      }
    },
    max: {
      type: Number | String,
      default: function () {
        return -0.137;
      }
    },
    pValue: {
      type: Number | String,
      default: function () {
        return -1.382;
      }
    },
    diffRelaVal: {
      type: Number | String,
      default: function () {
        return -1.382;
      }
    },
    confidenceVal: {
      type: String,
      default: function () {
        return '95%';
      }
    },
    isConfidence: {
      type: Number,
      default: function () {
        return 1; // 1是正向显著  0:负向显著 2: 不显著
      }
    },
    color: {
      type: String,
      default: function () {
        return '';
      }
    },
    customWidth: {
      type: String
    },
    customHeight: {
      type: String
    }
  },
  data() {
    return {};
  },

  computed: {},
  watch: {},
  created() {},
  methods: {},
  beforeDestroy() {}
};
</script>

<style lang="less" scoped>
/deep/ .p-wrap {
  font-size: 14px;
}
/deep/ .p-con {
  //padding-bottom: 20px;
  font-size: 14px;
  line-height: 24px;
}
/deep/ .p-wrap-con {
  height: 80px;
  display: flex;
  align-items: center;
}
/deep/ .confidence-retain-content {
  width: 70%;
  height: 4px;
  margin: 10px 1px 20px 20px;
  background-color: #d9d9e9;
  color: #8b8ba6;
  position: relative;
  text-align: center;
  font-size: 10px;
  //margin-top: 33px;
  margin: auto ;
  .confidence {
    position: absolute;
    left: 25%;
    top: -20px;
    color: rgb(31, 212, 181);
    display: inline-block;
    width: 50%;
    margin: 0 auto;
  }
  .color-box {
    width: 33%;
    height: 4px;
    position: absolute;
    left: 33%;
    background-color: #5acebb;
    .min-span {
      position: absolute;
      left: -130%;
      bottom: -25px;
    }
    .left-border {
      position: absolute;
      left: 0;
      width: 1px;
      height: 15px;
      background-color: inherit;
    }
    .center-border {
      position: absolute;
      left: 50%;
      width: 1px;
      height: 100%;
      background-color: rgb(0, 0, 0);
    }
    .right-border {
      position: absolute;
      right: 0;
      width: 1px;
      height: 15px;
      background-color: inherit;
    }
    .max-span {
      position: absolute;
      right: -130%;
      bottom: -25px;
    }
  }
}
</style>
