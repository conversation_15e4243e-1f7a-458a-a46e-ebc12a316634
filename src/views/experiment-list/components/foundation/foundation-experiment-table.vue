<template>
  <div class="foundation-table">
    <el-table
      :data="expindsumData"
      row-key="versionId"
      :tree-props="{ children: 'detailList', hasChildren: 'hasChildren' }"
    >
      <!-- <el-table-column type="expand" align="center" >
        <template slot-scope="props">
          <template v-if="props.row.detailList">
            <expindDetail :tableList="props.row.detailList"></expindDetail>
          </template>
        </template>
      </el-table-column> -->
      <el-table-column label="实验分组" align="center" width="180" fixed>
        <template slot-scope="scope">
          <!-- <div class="table-cell">=
            <div
              class="table-version-name-bar"
              :style="{ background: colorList[scope.$index] }"
            ></div>
            <div class="version-box-container">
              <div>{{ scope.row.versionName ? scope.row.versionName : '' }}</div>
              <div class="version-box">
                <label>版本ID:</label>
                <span>{{ scope.row.versionId }}</span>
                <span
                  v-clipboard:copy="scope.row.versionId"
                  v-clipboard:success="onCopySuccess"
                  v-clipboard:error="onCopyError"
                  role="img"
                  aria-label="copy"
                  tabindex="-1"
                  class="anticon-copy copy-icon"
                >
                  <svg
                    viewBox="64 64 896 896"
                    focusable="false"
                    class=""
                    data-icon="copy"
                    width="1em"
                    height="1em"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"
                    ></path>
                  </svg>
                </span>
              </div>
            </div>
          </div> -->
          {{ scope.row.versionName }}
        </template>
      </el-table-column>
      <el-table-column label="进组人数" align="center" prop="num" width="200" fixed>
        <template slot-scope="scope">
          <span>{{ scope.row.num }}</span>
          <span class="group-number-percent">{{ scope.row.numPersent }}%</span>
        </template>
      </el-table-column>
      <el-table-column align="center" width="230">
        <template slot="header" slot-scope="scope">
          <el-popover placement="top" trigger="hover" width="500" :open-delay="500">
            <div class="atomic-index-definition-hover">
              <p class="atomic-index-definition-title">{{ choosedIndicator.indName }}</p>
              <el-divider></el-divider>
              <div class="atomic-index-definition-form">
                <el-form size="mini" label-position="right" label-width="100px">
                  <el-form-item label="指标类型">
                    <p>{{ choosedIndicator.indType }}</p>
                  </el-form-item>
                  <el-form-item label="原子指标定义">
                    <div
                      class="atomic-index-definition"
                      v-for="(item, index) in choosedIndicator.evtList"
                      :key="`evtList${index}`"
                    >
                      <p class="tomic-index-definition-sortCode">{{ item.sortCode }}</p>
                      <el-input
                        disabled
                        v-model="item.eventName"
                        class="tomic-index-definition-input"
                      ></el-input>
                      <el-select
                        disabled
                        v-model="item.eventType"
                        class="tomic-index-definition-select"
                      ></el-select>
                    </div>
                  </el-form-item>
                  <el-form-item label="原子指标关系">
                    <p>{{ choosedIndicator.relation }}</p>
                  </el-form-item>
                  <el-form-item label="指标计算方式">
                    <p class="tomic-index-definition-calType">
                      {{ choosedIndicator.calcIntro }}
                    </p>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            <p slot="reference" slot-scope="scope">{{ choosedIndicator.indName }}</p>
          </el-popover>
        </template>
        <template slot-scope="scope">
          {{ scope.row.indValue }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="diffAbsVal" width="200">
        <template slot="header" slot-scope="scope">
          <el-popover placement="top" trigger="hover">
            <p>当前实验版本相对基准版本（对照组）的绝对差异</p>
            <p slot="reference">差异绝对值</p>
          </el-popover>
        </template>
        <template slot-scope="scope">
          {{ scope.row.diffAbsVal }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="diffRelaVal" width="200">
        <template slot="header" slot-scope="scope">
          <el-popover placement="top" trigger="hover">
            <p>当前实验版本相对基准版本（对照组）的绝对差异/基准版本值</p>
            <p slot="reference">差异相对值</p>
          </el-popover>
        </template>
        <template slot-scope="scope">
          {{ scope.row.diffRelaVal }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="confidInterval" width="240">
        <template slot="header" slot-scope="scope">
          <el-popover placement="top" trigger="hover">
            <p>由样本统计量构成的总体参数的估计区间</p>
            <p slot="reference">置信区间</p>
          </el-popover>
        </template>
        <template slot-scope="scope">
          <!-- 如果index不等0（即不是对照组）的话才用显示置信区间 -->
          <template v-if="scope.row.index !== 0">
            <template v-if="scope.row.isShowCon">
              <confidence
                :min="scope.row.confidInterval.min"
                :max="scope.row.confidInterval.max"
                :pValue="scope.row.pValue"
                :diffRelaVal="scope.row.diffRelaVal"
                :isConfidence="scope.row.isConfidence"
                :color="scope.row.color"
              ></confidence>
            </template>
            <template v-else>-</template>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="pValue" width="200">
        <template slot="header" slot-scope="scope" width="200">
          <el-popover placement="top" trigger="hover">
            <p>
              在原假设为真的前提下随机抽取样本出现极端情况的概率。当p-value&lt;1-置信度水平，认为统计显著
            </p>
            <p slot="reference">p-value</p>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="MDE">
        <template slot="header" slot-scope="scope">
          <el-popover placement="top" trigger="hover">
            <p>
              Minimum Detectable
              Effect最小可检测单位（检验灵敏度），在当前条件下能有效检出置信度的diff幅度。
            </p>
            <p slot="reference">MDE</p>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import confidence from './confidence.vue';
import expindDetail from './expind-detail.vue';
export default {
  name: 'foundation-experiment-table',
  components: {
    confidence,
    expindDetail
  },
  props: {
    // table 数据
    expindsumData: {
      type: Array,
      default: () => {
        [];
      }
    },
    // 所选指标
    choosedIndicator: {
      type: Object,
      default: () => ({})
    },
    colorList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    // 置信度
    confidenceVal: {
      type: String,
      default: function () {
        return '95%';
      }
    }
  },
  data() {
    return {};
  },
  watch: {},
  methods: {
    // copy 失败
    onCopyError() {
      this.$message.success('复制失败');
    },
    // copy 成功
    onCopySuccess(e) {
      this.$message.success(`版本ID：${e.text} 复制成功`);
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .el-table td,
.el-table th {
  padding: 0;
  height: 80px;
}
/deep/ .el-table .cell {
  //height: 80px;
  padding: 0;
  // line-height: 80px;
  font-size: 14px;
  // display: flex;
  // align-items: center;
  // justify-content: center;
}
.foundation-table {
  margin-top: 30px;
}
.atomic-index-definition {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.atomic-index-definition-title {
  padding: 10px 0;
}

.atomic-index-definition-form {
  padding: 10px;
}
.tomic-index-definition-sortCode {
  margin: 0 6px;
}

.tomic-index-definition-input {
  width: 175px;
}

.tomic-index-definition-select {
  width: 185px;
  margin-left: 8px;
}

.anticon-copy {
  margin-left: 4px;
  cursor: pointer;
}
.version-box {
  margin-left: 12px;
}

.table-version-name-bar {
  width: 4px;
  height: 80px;
  margin-right: 10px;
}

.group-number-percent {
  margin-left: 8px;
  color: rgb(139, 139, 166);
  font-weight: bold;
}

.table-cell {
  display: flex;
  justify-content: flex-start;
  height: 80px;
}

.version-box-container {
  margin-top: -10px;
  display: inline-block;
  div {
    height: 22px;
  }
}
// /deep/ .el-table td:first-child .cell {
//   padding-left: 0;
//   display: flex;
//   justify-content: center;
//   align-items: center;
// }
// /deep/ .childTable .el-table__body {
//   padding-left: 47px;
// }
// /deep/ .childTable .el-table__body td {
//   height: 50px;
// }
// /deep/ .childTable .el-table__body .cell {
//   height: 50px;
// }
// /deep/ .el-table__expand-icon {
//   height: 50px;
//   width: 50px;
// }
</style>
