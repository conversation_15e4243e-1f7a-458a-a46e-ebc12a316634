<template>
  <div class="day-trend-remain">
    <!-- echart图 -->
    <div class="day-trend-echarts">
      <div ref="myChart" class="day-trend-chart"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'day-trend',
  props: {
    timeLevel: {
      type: Number,
      default: 0,
    },
    experimentData: {
      type: Object,
      default: () => ({}),
    },
    trendResultData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      xAxisData: [],
      dataTitleList: [], // 统计具体类型list
      yAxisData: [], // y轴
    };
  },
  created() {
    // 天级
    if (this.timeLevel === 1) {
      this.trendResultData.versionList.forEach((item) => {
        // 实验版本名字
        this.dataTitleList.push(item.versionName);
      });
      // 横坐标
      this.xAxisData = Object.keys(this.trendResultData.timeList);

      // y轴数据
      const yData = [];
      this.xAxisData.forEach((item) => {
        this.trendResultData.timeList[item].forEach((ite) => {
          const jsonData = {};
          jsonData.time = item;
          jsonData.name = ite.versionName;
          jsonData.value = `${ite.persent}%`;
          jsonData.change = ite.change;
          yData.push(jsonData);
        });
      });
      this.yAxisData = [...yData];
    }
    // 时级
    if (this.timeLevel === 2) {
    }
  },
  mounted() {
    this.drawEchart();
  },
  methods: {
    drawEchart() {
      let myChart = this.$echarts.init(this.$refs.myChart);
      const legendData = {data: [...this.dataTitleList], show: true};
      let option = {
        tooltip: {
          trigger: 'axis',
          // formatter: (param) => {
          // return `${param[0].data.time}`;
          // console.log('param==== ', param[0].data);
          // },
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [...this.xAxisData],
        },
        yAxis: {
          type: 'value',
          show: true,
        },
        // series: this.yAxisData,
        series: [
          {
            name: '版本名称',
            type: 'line',
            data: [231, 234],
          },
          {
            name: '实验版本',
            type: 'line',
            data: [111, 6666],
          },
        ],
      };
      option.legend = legendData;
      myChart.setOption(option);
    },
  },
};
</script>
<style scoped>
.day-trend-echarts {
  width: 100%;
}

.day-trend-chart {
  width: 100%;
  height: 400px;
  margin: 20px auto;
}
</style>

