<!--
 * @Description: 
 * @Author: huy<PERSON><PERSON>
 * @LastEditors  : huyew<PERSON>
 * @Date: 2021-07-20 10:56:19
 * @LastEditTime : Please set LastEditTime
 -->
<template>
  <div id="line-chart5"></div>
</template>

<script>
import G2 from '@antv/g2';
import {Chart} from '@antv/g2';
import DataSet from '@antv/data-set';
export default {
  name: 'pvalueLineChart',
  data() {
    return {
      chart: null,
      indDataFormat: 1 // 指标数值类型 1:数字 2:百分比
    };
  },
  props: {
    charData: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  created() {
    // // 指标数值类型 1:数字 2:百分比
    this.indDataFormat = this.charData[0].indDataFormat;
    //debugger;
  },
  mounted() {
    this.drawChart(this.charData);
  },
  watch: {
    charData(val, oldVal) {
      this.indDataFormat = val[0].indDataFormat;
      //debugger;
      this.$nextTick(() => {
        this.drawChart(val);
      });
    }
  },
  methods: {
    drawChart(data) {
      //debugger;
      this.chart && this.chart.destroy();
      this.chart = new Chart({
        container: 'line-chart5',
        forceFit: true,
        autoFit: true,
        height: 400,
        padding: [40, 60, 100, 100]
      });

      this.chart.source(data);
      this.chart.scale({
        time: {
          range: [0.02, 1]
        },
        value: {
          min: 0,
          nice: true
        }
      });
      this.chart.scale('time', {
        range: [0.01, 1],
        tickCount: 12
      });
      this.chart.tooltip({
        showCrosshairs: true, // 展示 Tooltip 辅助线
        shared: true,
        // itemTpl: `
        // <div style="margin-bottom: 10px;list-style:none;">
        // <span style="background-color:{color};" class="g2-tooltip-marker"></span>
        // {name}: &nbsp;&nbsp;{value1}&nbsp;&nbsp;&nbsp;<span style="color:{currentColor};">{value2}</span>
        // </div>
        // `
      });
      this.chart.axis('value', {
        label: {
          formatter: val => {
            //debugger;
            return val;
          }
        }
      });
      this.chart
        .line()
        .position('time*value')
        .color('versionName')
        .shape('smooth');
      this.chart
        .point()
        .position('time*value')
        .color('versionName')
        .shape('circle');

      this.chart.render();
    }
  }
};
</script>
