<template>
  <div class="total-trend-remain">
    <!-- echart图 -->
    <div class="total-trend-echarts">
      <div ref="myChart" class="total-trend-chart"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'total-trend',
  props: {
    trendResultData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      xAxisData: [],
      dataTitleList: [],
    };
  },
  created() {
    // 只有天级累计
    this.trendResultData.versionList.forEach((item) => {
      this.dataTitleList.push(item.versionName);
    });
    this.xAxisData = Object.keys(this.trendResultData.timeList);
  },
  mounted() {
    this.drawEchart();
  },
  methods: {
    drawEchart() {
      let myChart = this.$echarts.init(this.$refs.myChart);
      myChart.setOption({
        tooltip: {trigger: 'axis'},
        legend: {
          data: [...this.dataTitleList],
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [...this.xAxisData],
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100,
          axisLabel: {
            show: true,
            interval: 'auto',
            formatter: '{value} %',
          },
          show: true,
        },
        series: [],
      });
    },
  },
};
</script>
<style scoped>
.total-trend-echarts {
  width: 100%;
}

.total-trend-chart {
  width: 100%;
  height: 400px;
  margin: 20px auto;
}
</style>

