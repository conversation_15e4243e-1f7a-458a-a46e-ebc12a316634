<template>
  <!-- <div class="n-day-remain">
    <div class="n-day-remain-title">N日留存日趋图</div>
    <label class="n-day-remain-label">留存指标</label>
    <el-select v-model="day" size="mini" @change="nDayChange">
      <el-option
        v-for="item in daysList"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      ></el-option>
    </el-select>
    <div class="n-day-echarts">
      <div id="line-chart4"></div>
    </div>
  </div> -->
  <div class="n-day-echarts">
    <div id="line-chart4"></div>
  </div>
</template>
<script>
import G2 from '@antv/g2';
import {Chart} from '@antv/g2';
import DataSet from '@antv/data-set';
export default {
  name: 'n-day-remain',
  props: {
    charData: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  data() {
    return {
      day: 1,
      days: 0, //实验天数
      daysList: [],
      chart: null
    };
  },
  created() {},
  mounted() {
    this.drawChart(this.charData);
  },
  watch: {
    charData(val, oldVal) {
      this.drawChart(val);
    }
  },
  methods: {
    drawChart(data) {
      this.chart && this.chart.destroy();
      this.chart = new Chart({
        container: 'line-chart4',
        forceFit: true,
        autoFit: true,
        height: 400,
        padding: [40, 40, 100, 100]
      });

      this.chart.source(data);
      this.chart.scale({
        time: {
          range: [0.02, 1]
        },
        persent: {
          min: 0,
          nice: true
        }
      });
      this.chart.scale('time', {
        range: [0.01, 1],
        tickCount: 12
      });
      this.chart.tooltip({
        showCrosshairs: true, // 展示 Tooltip 辅助线
        shared: true,
        // containerTpl: `<div class="g2-tooltip" ><p class="g2-tooltip-title"></p><ul class="g2-tooltip-list"></ul></div>`,
        itemTpl: `
        <div style="margin-bottom: 10px;list-style:none;">
        <span style="background-color:{color};" class="g2-tooltip-marker"></span>
        {name}: &nbsp;&nbsp;{value1}&nbsp;&nbsp;&nbsp;<span>{value2}</span>
        </div>
        `
        // containerTpl:
        //   '<div class="g2-tooltip">' +
        //   `<div class="g2-tooltip-title">{time}</div>` +
        //   `<div class="g2-tooltip-title"><span class="tooltip-cat"></span><span>进组人数</span><span>占比（%）</span></div>` +
        //   `<div class="g2-tooltip-list"></div>` +
        //   '</div>',
        // itemTpl: `<li class="g2-tooltip-list-item tooltip-line"><span class="g2-tooltip-marker" style="background-color:{color};"></span><span class="tooltip-cat">{versionName}:</span><span class="tooltip-number">{num}</span><span class="tooltip-number">{percent}</span></li>`
      });
      this.chart.axis('persent', {
        label: {
          formatter: val => {
            return val + ' %';
          }
        }
      });
      this.chart
        .line()
        .position('time*persent')
        .color('versionName')
        .shape('smooth')
        .tooltip('time*persent*versionName*num', function (time, persent, versionName, num) {
          // tooltip的第一个参数写上需要显示的字段'item1*item2*...'；第二个参数为一个函数，该函数的参数为需要显示的字段。
          return {
            name: versionName,
            value1: num, // 这里也可以通过调用其他自定义函数的方式，去对数据进行更深层次的变换。但要注意this的指向问题！
            value2: persent + '%' // 这里也可以通过调用其他自定义函数的方式，去对数据进行更深层次的变换。但要注意this的指向问题！
          };
        });
      this.chart
        .point()
        .position('time*persent')
        .color('versionName')
        .shape('circle');

      this.chart.render();
    },
    // N日留存日趋图
    // nDayChange(val) {
    //   this.$emit('getNDayChange', val);
    // },
    // 获取实验天数
    getDaysList(param) {
      this.$service.get('REPORT_FOUND_EXPRETDAYNUM', {...param}, {needLoading: false}).then(res => {
        this.days = res.dayNum;
        for (let i = 1; i <= res.dayNum; i++) {
          const day = {
            id: i,
            name: `第${i}天留存`
          };
          this.daysList.push(day);
        }
      });
    }
  }
};
</script>
<style scoped>
.n-day-remain-title {
  margin: 10px 0 20px 0;
}
.n-day-remain-label {
  margin-right: 10px;
}

.n-day-echarts {
  width: 100%;
}

.n-day-chart {
  width: 100%;
  height: 400px;
  margin: 20px auto;
}
</style>
