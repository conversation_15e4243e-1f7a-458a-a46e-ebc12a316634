<!--
 * @Description: 
 * @Author: huy<PERSON><PERSON>
 * @LastEditors  : huyew<PERSON>
 * @Date: 2021-07-20 10:56:19
 * @LastEditTime : Please set LastEditTime
 -->
<template>
  <div id="line-chart2"></div>
</template>

<script>
import G2 from '@antv/g2';
import {Chart} from '@antv/g2';
import DataSet from '@antv/data-set';
export default {
  name: 'cumLineChart',
  data() {
    return {
      chart: null
    };
  },
  props: {
    charData: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  created() {
    // // 指标数值类型 1:数字 2:百分比
    this.indDataFormat = this.charData[0].indDataFormat;
    //debugger;
  },
  mounted() {
    this.drawChart(this.charData);
  },

  watch: {
    charData(val, oldVal) {
      this.indDataFormat = val[0].indDataFormat;
      this.$nextTick(() => {
        this.drawChart(val);
      });
      //this.drawChart(val);
    }
  },
  methods: {
    drawChart(data) {
      this.chart && this.chart.destroy();
      this.chart = new Chart({
        container: 'line-chart2',
        forceFit: true,
        autoFit: true,
        height: 400,
        padding: [40, 60, 100, 100]
      });

      this.chart.source(data);
      this.chart.scale({
        time: {
          range: [0.02, 1]
        },
        persent: {
          min: 0,
          nice: true
        }
      });
      this.chart.scale('time', {
        range: [0.01, 1],
        tickCount: 12
      });
      this.chart.tooltip({
        showCrosshairs: true, // 展示 Tooltip 辅助线
        shared: true,
        itemTpl: `
        <div style="margin-bottom: 10px;list-style:none;">
        <span style="background-color:{color};" class="g2-tooltip-marker"></span>
        {name}: &nbsp;&nbsp;{value1}&nbsp;&nbsp;&nbsp;<span style="color:{currentColor};">{value2}</span>
        </div>
        `
      });
      this.chart.axis('persent', {
        label: {
          formatter: val => {
            return this.indDataFormat == 1 ? val : val + ' %';
          }
        }
      });
      this.chart
        .line()
        .position('time*persent')
        .color('versionName')
        .shape('smooth')
        .tooltip('time*persent*versionName*change*indDataFormat', function (time, persent, versionName, change,indDataFormat) {
          // tooltip的第一个参数写上需要显示的字段'item1*item2*...'；第二个参数为一个函数，该函数的参数为需要显示的字段。
          let currentVal = '';
          if(change !== ''){
            if(change > 0){
               currentVal = change + '% ↑';
            }else {
               currentVal = change + '% ↓';
            }
          }
          
          return {
            name: versionName,
            value1: indDataFormat == 1 ? persent: persent + '%', // 这里也可以通过调用其他自定义函数的方式，去对数据进行更深层次的变换。但要注意this的指向问题！
            //value2: change // 这里也可以通过调用其他自定义函数的方式，去对数据进行更深层次的变换。但要注意this的指向问题！
            value2: currentVal, // 这里也可以通过调用其他自定义函数的方式，去对数据进行更深层次的变换。但要注意this的指向问题！
            currentColor: change > 0 ? '#00BFFF' : '#DC143C'
          };
        });
      this.chart
        .point()
        .position('time*persent')
        .color('versionName')
        .shape('circle');

      this.chart.render();
    }
  }
};
</script>
