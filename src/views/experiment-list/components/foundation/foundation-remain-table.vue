<template>
  <div class="foundation-table">
    <el-table
      :data="remainTableData"
      class="remian-table"
      row-key="versionId"
      size="mini"
      border
      @cell-mouse-enter="handleEnter"
      :tree-props="{ children: 'detailList', hasChildren: 'hasChildren' }"
    >
      <!-- 
        由于zyb-pc-ui 不支持，只能再重新安装element-ui
        children 和 hasChildren 不能同时出现
      -->
      >
      <el-table-column
        label="实验分组"
        align="center"
        prop="versionName"
        fixed
        width="120"
      ></el-table-column>
      <el-table-column
        label="新进组用户"
        align="center"
        prop="newNum"
        width="120"
        fixed
      ></el-table-column>
      <!-- 近七天用户留存 -->
      <el-table-column label="近七天用户留存" align="center">
        <el-table-column v-for="n in 7" :label="`${n}天后`" :key="n" width="110" align="center">
          <template slot="header" slot-scope="scope">
            <el-popover placement="top" trigger="hover" width="220">
              <p>
                {{ `${n}` }} 日留存率，取基准用户中第 {{ `${n + 1}` }}
                天仍然回访使用应用的用户数 / 基准用户数
              </p>
              <span slot="reference">
                {{ `${n}天后` }}
                <i class="el-icon-question" ></i>
              </span>
            </el-popover>
          </template>
          <template slot-scope="scope">
            <!-- 不是对照组，才需要popover-->
            <template v-if="scope.row.isCompare !== 1">
              <el-popover trigger="hover" placement="top" width="300" :open-delay="500">
                <!-- <div class="popoverContent" style="font-size: 14px">
                <template v-if="typeof scope.row[`no${n}`] == 'object'">
                  <div class="pb10">
                    {{ scope.row.versionName }}：
                    {{ scope.row[`no${n}`]['value'] + '%' }}
                  </div>
                  <div class="pb10">
                    差异绝对值：{{
                      scope.row[`no${n}`]['diffAbsVal'] == '-'
                        ? '-'
                        : scope.row[`no${n}`]['diffAbsVal'] + '%'
                    }}
                  </div>
                  <div class="pb10">
                    差异相对值：{{
                      scope.row[`no${n}`]['diffRelaVal'] == '-'
                        ? '-'
                        : scope.row[`no${n}`]['diffRelaVal'] + '%'
                    }}
                  </div>
                  <div class="pb10">
                    置信区间：
                    <template v-if="scope.row[`no${n}`]['confidInterval'].length">
                      <span>[{{ scope.row[`no${n}`]['confidInterval'][0] }}%</span>
                      ,
                      <span>{{ scope.row[`no${n}`]['confidInterval'][1] }}%]</span>
                    </template>
                    <template v-else>-</template>
                  </div>
                  <div class="pb10">p-value：{{ scope.row[`no${n}`]['pValue'] }}</div>
                  <div class="pb10">
                    MDE：{{
                      scope.row[`no${n}`]['MDE'] == '-' ? '-' : scope.row[`no${n}`]['MDE'] + '%'
                    }}
                  </div>
                </template>
              </div> -->
                <popoverContent :rowData="scope.row" :n="n"></popoverContent>
                <template v-if="scope.row[`no${n}`] !== undefined">
                  <span slot="reference" class="">
                    {{
                      typeof scope.row[`no${n}`] == 'object'
                        ? scope.row[`no${n}`]['value'] + '%'
                        : ''
                    }}
                  </span>
                </template>
                <template v-else>
                  <span slot="reference" class=""></span>
                </template>
              </el-popover>
            </template>
            <template v-else>
              <span class="">
                {{
                  typeof scope.row[`no${n}`] == 'object' ? scope.row[`no${n}`]['value'] + '%' : ''
                }}
              </span>
            </template>
          </template>
        </el-table-column>
      </el-table-column>
      <!-- 其他用户留存 -->
      <el-table-column label="其他用户留存" align="center" prop="address">
        <el-table-column
          v-for="n in 30"
          v-if="n > 7"
          :label="`${n}天后`"
          :key="n"
          width="110"
          align="center"
        >
          <template slot="header" slot-scope="scope">
            <el-popover placement="top" trigger="hover" width="220">
              <p>
                {{ `${n}` }} 日留存率，取基准用户中第 {{ `${n + 1}` }}
                天仍然回访使用应用的用户数 / 基准用户数
              </p>
              <span slot="reference">
                {{ `${n}天后` }}
                <i class="el-icon-question" ></i>
              </span>
            </el-popover>
          </template>
          <template slot-scope="scope">
            <!-- 不是对照组，才需要popover-->
            <template v-if="scope.row.isCompare !== 1">
              <el-popover trigger="hover" placement="top" width="300" :open-delay="500">
                <!-- <div class="popoverContent" style="font-size: 14px">
                <template v-if="typeof scope.row[`no${n}`] == 'object'">
                  <div class="pb10">
                    {{ scope.row.versionName }}：
                    {{ scope.row[`no${n}`]['value'] + '%' }}
                  </div>
                  <div class="pb10">
                    差异绝对值：{{
                      scope.row[`no${n}`]['diffAbsVal'] == '-'
                        ? '-'
                        : scope.row[`no${n}`]['diffAbsVal'] + '%'
                    }}
                  </div>
                  <div class="pb10">
                    差异相对值：{{
                      scope.row[`no${n}`]['diffRelaVal'] == '-'
                        ? '-'
                        : scope.row[`no${n}`]['diffRelaVal'] + '%'
                    }}
                  </div>
                  <div class="pb10">
                    置信区间：
                    <template v-if="scope.row[`no${n}`]['confidInterval'].length">
                      <span>[{{ scope.row[`no${n}`]['confidInterval'][0] }}%</span>
                      ,
                      <span>{{ scope.row[`no${n}`]['confidInterval'][1] }}%]</span>
                    </template>
                    <template v-else>-</template>
                  </div>
                  <div class="pb10">p-value：{{ scope.row[`no${n}`]['pValue'] }}</div>
                  <div class="pb10">
                    MDE：{{
                      scope.row[`no${n}`]['MDE'] == '-' ? '-' : scope.row[`no${n}`]['MDE'] + '%'
                    }}
                  </div>
                </template>
              </div> -->
                <popoverContent :rowData="scope.row" :n="n"></popoverContent>
                <template v-if="scope.row[`no${n}`] !== undefined">
                  <span slot="reference" class="">
                    {{
                      typeof scope.row[`no${n}`] == 'object'
                        ? scope.row[`no${n}`]['value'] + '%'
                        : ''
                    }}
                  </span>
                </template>
                <template v-else>
                  <span slot="reference" class=""></span>
                </template>
              </el-popover>
            </template>
            <template v-else>
              <span class="">
                {{
                  typeof scope.row[`no${n}`] == 'object' ? scope.row[`no${n}`]['value'] + '%' : ''
                }}
              </span>
            </template>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import popoverContent from './popover-content';
export default {
  name: 'foundation-remain-table',
  components: {
    popoverContent
  },
  props: {
    remainTableData: {
      type: Array,
      default: () => {
        [];
      }
    }
  },
  watch: {
    remainTableData(val) {
      //debugger
      // val.forEach((item) => {
      //   if (item.detailList.length > 0) {
      //     item.detailList.forEach((ite, index) => {
      //       ite.versionId = `${item.versionId}${index}`;
      //     });
      //   }
      // });
      // console.log('val ', val);
    }
  },
  methods: {
    handleEnter(row, column, cell, event) {
      //debugger;
      this.$emit('handleTableEnter', row, column, cell, event);
      //console.log(row, column, cell, event);
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .el-table .cell {
  //padding: 0;
  font-size: 14px;
}
.remian-table {
  // margin-top: 20px;
  margin-top: 10px;
}
/deep/ .el-table__expand-icon {
  display: inline-block;
  width: 20px;
  line-height: 20px;
  height: 20px;
  text-align: center;
  margin-right: 3px;
}
.pb10 {
  padding-bottom: 10px;
}
</style>

