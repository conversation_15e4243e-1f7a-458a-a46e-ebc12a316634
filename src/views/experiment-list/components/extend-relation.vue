<template>
  <div class="feature-container">
    <h2 class="title">继承实验历史版本</h2>
    <el-timeline class="all-highlight history-timeline">
      <template v-for="(item, index) in extendData">
        <el-timeline-item :key="index">
          <el-card>
            <div class="left-container">
              <p class="time" v-if="item.startTime && !item.stopTime">
                {{ formatDateN(item.startTime) }} ~ 至今
              </p>
              <p class="time" v-if="item.startTime && item.stopTime">
                {{ formatDateN(item.startTime) }} ~ {{ formatDateN(item.stopTime) }}
              </p>
              <p class="time">
                {{ item.id }}：
                <!-- <span v-if="item.id == currentId" type="text" class="pri-btn">
                  {{ item.displayName }}
                </span> -->
                <!-- <el-link v-else type="primary" @click="check(item)">{{ item.displayName }}</el-link> -->
                <el-link type="primary" @click="check(item)" :disabled="item.id == currentId">{{ item.displayName }}</el-link>
                <!-- <el-button v-else type="text" class="pri-btn" @click="check(item)">
                  {{ item.displayName }}
                </el-button> -->
                <span class="onEffect" v-if="item.status === 1">草稿</span>
                <span class="onEffect" v-if="item.status === 2">调试中</span>
                <span class="onEffect" v-if="item.status === 3">发布中</span>
                <span class="onEffect" v-if="item.status === 4" style="color: rgb(171 171 173)">结束</span>
                <span class="onEffect" v-if="item.status === 5">待发布</span>
              </p>
            </div>
          </el-card>
        </el-timeline-item>
      </template>
    </el-timeline>
  </div>
</template>
<script>
import { formatDate, localStorage } from '@/common/utils';
import { mapGetters } from 'vuex';

export default {
  name: 'extend-relation',
  components: {},
  props: {
    extendData: Array,
    expType: Number
  },
  computed: {
    ...mapGetters(['statusMap'], 'message')
  },
  inject: ['reload'],
  data() {
    return {
      currentId: this.$route.query.id
    };
  },
  created() {
    // const id = this.$route.query.id; // 固化ID
    // const param = {
    //   id
    // };
    // this.getFeatureHistory(param);
  },
  methods: {
    handleStatus(status) {
      //const statusData = statusList;
      return this.statusMap[status];
    },
    // 组装实验历史需要的部分数据
    assembleHistoryDay(row) {
      localStorage.setItem('displayName', row.displayName);
      const statusVal = this.handleStatus(row.status);
      localStorage.setItem('status', statusVal);
    },
    check(item) {
      if (item.id == this.$route.query.id) return;
      this.assembleHistoryDay(item);
      const path = {
        path: '/exp-manage/list/edit',
        query: {
          id: item.id,
          type: 'check',
          ex: this.expType,
          random: Math.random()
        }
      };
      //debugger;

      this.$router.push(path);
      this.reload();
    },
    formatDateN(date) {
      return formatDate(date * 1000, 'yyyy-MM-dd HH:mm:ss');
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .el-timespot__right.is-small {
  width: 80%;
}

.title {
  font-size: 14px;
  font-weight: 700;
}
.feature-container {
  padding: 15px;
  background: #ffffff;
  font-size: 14px;
}
.left-container {
  display: flex;
  flex-direction: column;
  justify-content: left;
  font-size: 14px;
  // margin-left: 30px;
}
.pri-btn {
  color: #333;
  font-size: 14px;
}
.pri-btn:hover {
  color: rgb(51, 112, 255);
}
.right-container {
  display: flex;
  // margin-left: 30px;
  margin-bottom: 12px;
  border: 1px solid #e2e4e8;
  border-radius: 7px;
  padding: 18px 40px 18px 15px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 14px;
  box-sizing: border-box;
}
.history-timeline {
  margin: 24px 20px 0 12px;
}

.icon {
  margin-left: 15px;
}
.icon-right {
  padding: 0 5px;
}
.icon-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f4f4f4;
  color: #424242;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.feature-version-item-content-start-tag {
  background: #f4f6f9;
  border: 1px solid #e7e9f5;
  box-sizing: border-box;
  border-radius: 12px;
  padding: 4px 8px;
  max-width: 80px;
  margin-left: 10px;
}
.feature-version-item-content-start-circle {
  margin-left: 20px;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.feature-version-status-color {
  color: #ffffff;
  font-weight: 700;
}
.onEffect {
  color: rgb(51, 112, 255);
  margin-left: 15px;
  font-size: 13px;
}

.disabled {
  cursor: pointer;
  pointer-events: none;
}

.time {
  margin-bottom: 12px;
}

/deep/ .el-timeline-item__content {
  padding-top: 3px;
}
</style>

