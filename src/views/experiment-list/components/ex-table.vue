<template>
  <div class="ex-table">
    <el-popover placement="left-start" trigger="click">
      <el-checkbox-group @change="handleCheckedChange" v-model="checkedColumns">
        <el-checkbox class="checkbox" v-for="item in checkedColumnsOptions" :label="item" :key="item" />
      </el-checkbox-group>
      <svg-icon icon-class="op" slot="reference"></svg-icon>
    </el-popover>
    <zyb-table :table-data="tableList" :table-columns="_tableColumns" :showOverflowTooltip="false">
      <template slot="action">
        <p>操作</p>
      </template>
      <template slot="pause">
        <div class="operateIcon">
          <p>冻结/暂停</p>
          <el-popover placement="top" trigger="hover" width="200">
            <span>
              客户端实验冻结后，将不再有用户进组，维持原对照组和实验组内用户数量不变，观察留存和转化。
              服务端实验暂停后，所有进组用户将会出组，实验报告数据将会按照暂停前的上报数据正常计算。
            </span>
            <i slot="reference" class="el-icon-warning-outline tip-btn"></i>
          </el-popover>
        </div>
      </template>

      <template v-slot:custom="{ data: { item, scope } }">
        <template v-if="item.prop === 'displayName'">
          <el-popover v-if="[1, 5].includes(scope.row.type - 0)" placement="left" width="750"
            popper-class="overhidden-auto-popover" trigger="hover">
            <h1 class="ex-tit">
              「{{ scope.row.displayName }}」
              <span v-if="scope.row.type" class="ex-tit-type">
                {{ scope.row.type | filterExpType }}
              </span>
              <span v-if="scope.row.extendsGroupId" class="ex-tit-type ex-tit-type1">继承</span>
              <span v-if="Array.from(scope.row.childExperimentList).length > 0" class="ex-tit-type ex-tit-type1">
                父实验
              </span>
              <span v-if="scope.row.parentId" class="ex-tit-type ex-tit-type1">子实验</span>
              <span v-if="scope.row.hasCorrect" class="ex-tit-type ex-tit-type1">已固化</span>
            </h1>
            <section style="margin: 0 20px;">
              <section style="align-items: center;display: flex;justify-content: space-between;">
                <p class="form-title">版本信息</p>
                <span v-if="scope.row.exclusiveDisplayName">
                  流量层：{{ scope.row.exclusiveDisplayName }}
                </span>
              </section>
              <znzt-table :table-data="scope.row.versionInfoList" :table-columns="versionColumns"
                :flow="scope.row.flow"></znzt-table>
              <template v-if="scope.row.pushInfo.pushId">
                <p class="form-title" style="margin-top: 32px;">发布信息</p>
                <el-table :data="[scope.row.pushInfo]">
                  <el-table-column v-for="item in pubColumns" :key="item.name" :prop="item.prop" :label="item.label">
                    <template #default="scope" v-if="item.render">
                      {{ item.render(scope) }}
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </section>
            <a slot="reference" @click="checkExp(scope.row)">{{ scope.row.displayName }}</a>
          </el-popover>
          <a v-else @click="checkExp(scope.row)">{{ scope.row.displayName }}</a>
          <i class="el-icon-star-on" v-if="scope.row.favorite" style="color: #FFC845; margin-left: 10px;"></i>
        </template>
        <template v-else-if="item.prop === 'timeStr'">
          <template v-if="scope.row.timeStr !== '-'">
            <el-popover placement="top" trigger="hover" :visible-arrow="true">
              <p style="text-align: center">{{ scope.row.timeStr }}</p>
              <span slot="reference">{{ scope.row.timeStr }}</span>
            </el-popover>
          </template>
        </template>
        <template v-else-if="item.prop === 'createTime'">
          <template v-if="scope.row.createTime !== '-'">
            <el-popover placement="top" trigger="hover">
              <p style="text-align: center">{{ scope.row.createTime }}</p>
              <span slot="reference">{{ scope.row.createTime }}</span>
            </el-popover>
          </template>
        </template>
        <template v-else-if="item.prop === 'updateTime'">
          <template v-if="scope.row.updateTime !== '-'">
            <el-popover placement="top" trigger="hover">
              <p style="text-align: center">{{ scope.row.updateTime }}</p>
              <span slot="reference">{{ scope.row.updateTime }}</span>
            </el-popover>
          </template>
        </template>
        <template v-else-if="item.prop === 'correctDisplayName'">
          <a v-if="scope.row.correctId > 0" @click="turnFeatureManage(scope)">
            {{ scope.row.correctDisplayName }}
          </a>
          <a v-if="scope.row.correctId == 0 && scope.row.status != 1" @click="turnFeature(scope)">
            去关联
          </a>
          <span v-if="scope.row.correctId == 0 && scope.row.status == 1">-</span>
          <span v-if="scope.row.correctId == -1">-</span>
        </template>
        <!-- 暂停/冻结 -->
        <template v-else-if="item.prop === 'pause'">
          <!-- 非运行中的实验不能暂停也不能冻结 -->
          <template v-if="scope.row.status != 3">
            <el-popover placement="top" trigger="hover">
              <span>非运行中的实验不支持冻结</span>
              <el-switch slot="reference" v-if="scope.row.status != 3 && scope.row.clientType == 1"
                class="tableScopeSwitch" :disabled="scope.row.clientType == 1" active-text="禁用" inactive-text="禁用" />
            </el-popover>
            <el-popover placement="top" trigger="hover">
              <span>非运行中的实验不支持暂停</span>
              <el-switch slot="reference" class="tableScopeSwitch"
                v-if="scope.row.status != 3 && scope.row.clientType == 2" :disabled="scope.row.clientType == 2"
                active-text="禁用" inactive-text="禁用" />
            </el-popover>
          </template>
          <template v-else>
            <!-- 客户端实验可以冻结/解冻 -->
            <template v-if="scope.row.isResetUser === 0 && isAuthLevel2(scope.row)">
              <el-switch class="tableScopeSwitch op-switch" v-model="scope.row.copySwitch" active-text="开启"
                inactive-text="关闭" disabled inactive-color="#c0c4cc"
                @click.native.prevent="val => handleFreeze(scope.row, val)" />
            </template>
            <!-- 服务端实验可以暂停或开始 -->
            <template v-if="scope.row.clientType == 2">
              <el-switch class="tableScopeSwitch op-switch" v-model="scope.row.copySwitch" active-text="开启"
                inactive-text="关闭" disabled inactive-color="#c0c4cc"
                @click.native.prevent="val => handleSwitch(scope.row, val)" />
            </template>
          </template>
        </template>
        <template v-else-if="item.prop === 'action'">
          <template v-if="isAuthLevel2(scope.row)">
            <zyb-text v-if="[4].includes(scope.row.status * 1)" @click.native="handleCopy(scope.row, 4)">
              复制
            </zyb-text>
            <zyb-text v-if="scope.row.status !== 4 && scope.row.status !== 8 && scope.row.status !== 9"
              @click="editExp(scope.row)">
              编辑
            </zyb-text>
            <zyb-text v-if="(scope.row.status == 2 || scope.row.status == 5) && !scope.row.hasStartCron"
              @click="handleComfirm(scope.row, 2)">
              开始
            </zyb-text>
            <!-- 暂停状态下的开始 -->
            <zyb-text v-if="scope.row.status == 6" @click="handleComfirm(scope.row, 3)">
              继续
            </zyb-text>
            <zyb-text v-if="scope.row.status == 3" @click.native="handleComfirm(scope.row, 1)">
              暂停
            </zyb-text>
            <!-- 解冻 -->
            <zyb-text v-if="scope.row.status == 7" @click="handleComfirm(scope.row, 7)">
              解冻
            </zyb-text>
            <zyb-text v-if="[1, 4].includes(scope.row.status * 1)" @click.native="handleComfirm(scope.row, 4)"
              class="warn-tip">
              删除
            </zyb-text>
            <template v-if="[8].includes(scope.row.status * 1)">
              <zyb-text @click="continuePublish(scope.row, 7)" v-if="scope.row.flow < 1000">
                继续发布
              </zyb-text>
              <zyb-text @click="handleRollBack(scope.row)" class="warn-tip">
                回滚
              </zyb-text>
            </template>
            <template v-if="[9].includes(scope.row.status * 1)">
              <zyb-text @click="handlePublish(scope.row)">发布</zyb-text>
              <zyb-text @click.native="handleCopy(scope.row, 4)">
                复制
              </zyb-text>
            </template>
            <el-dropdown placement="bottom-start" v-if="[1, 2, 3, 4, 5, 6, 7, 8, 9].includes(scope.row.status * 1)">
              <zyb-text class="action_more">
                更多
                <i class="el-icon-arrow-down"></i>
              </zyb-text>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="scope.row.canPush" @click.native="handlePublish(scope.row)">
                  发布
                </el-dropdown-item>
                <el-dropdown-item v-if="scope.row.hasStopCron || scope.row.hasStartCron"
                  @click.native="controlCronAction(scope.row)">
                  定时管理
                </el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status == 3 && scope.row.isResetUser === 0"
                  @click.native="handleComfirm(scope.row, 6)">
                  冻结
                </el-dropdown-item>
                <el-dropdown-item v-if="[2, 3, 5, 6, 7, 8, 9].includes(scope.row.status * 1) && !scope.row.hasStopCron"
                  @click.native="handleComfirm(scope.row, 5)">
                  结束
                </el-dropdown-item>
                <el-dropdown-item v-if="[2, 3, 5, 6, 7, 8].includes(scope.row.status * 1)"
                  @click.native="handleCopy(scope.row, 4)">
                  复制
                </el-dropdown-item>
                <el-dropdown-item class="warn-tip" v-if="[2, 5].includes(scope.row.status * 1)"
                  @click.native="handleComfirm(scope.row, 4)">
                  删除
                </el-dropdown-item>
                <el-dropdown-item v-if="[3, 6, 7].includes(scope.row.status * 1)"
                  @click.native="createExtendExp(scope.row)">
                  实验继承
                </el-dropdown-item>
                <el-dropdown-item v-if="[3, 6, 7].includes(scope.row.status * 1) && !scope.row.parentId"
                  @click.native="createChildExp(scope.row)">
                  创建子实验
                </el-dropdown-item>
                <el-dropdown-item @click.native="managePermission(scope.row)">
                  权限管理
                </el-dropdown-item>
                <!-- 新增收藏按钮 -->
                <el-dropdown-item @click.native="handleStar(scope.row)">
                  {{ scope.row.favorite ? '取消收藏' : '收藏' }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
          <template v-else>
            <zyb-text v-if="scope.row.status !== 1" @click.native="handleCopy(scope.row, 4)">
              复制
            </zyb-text>
          </template>
        </template>
      </template>
    </zyb-table>
    <el-dialog :visible.sync="permissionDialog.show" width="720px" title="实验权限" :close-on-click-modal="false"
      :close-on-press-escape="false" top="12vh">
      <ex-permission v-if="permissionDialog.show" :id="permissionDialog.data"
        @close="closePermissionDialog"></ex-permission>
    </el-dialog>
    <el-dialog :visible.sync="continuePublishShow" width="720px" title="继续发布" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <section class="slider-container" style="padding-left: 24px;">
        <span>实验流量</span>
        <el-slider v-model="publishInfo.flow" :show-tooltip="false" :min="0" :max="100" :step="0.1"
          style="display: inline-block;width: 275px;vertical-align: middle;margin: 0 12px;position: relative; top: -2px"></el-slider>
        <el-input-number controls-position="right" v-model="publishInfo.flow" :min="publishInfo.minFlow" :max="100"
          :precision="1" :step="0.1" />
        <span style="margin: 0 6px">%</span>
        <section style="width: 100%;font-size: 12px;margin-left: 68px;">
          比例设置生效后，将不支持再下调！
        </section>
      </section>
      <span slot="footer" class="dialog-footer">
        <el-button @click="continuePublishShow = false">取 消</el-button>
        <el-button type="primary" @click="continuePublishAction">确 定</el-button>
      </span>
    </el-dialog>
    <ExCron :title="cron.title" :tips="cron.tips" :visible.sync="cron.visible" :text="cron.text"
      @confirm="confirmTime" />
    <ControlCron :visible.sync="controlCron.visible" :id="controlCron.id" @refresh="refreshTable" />
  </div>
</template>
<script>
import { formatDate, localStorage } from '@/common/utils';
//import { statusList } from '../status.js';
import ZybTable from '@/components/zyb-table/index.vue';
import ZybText from '@/components/zyb-text/index.vue';
import { mapGetters } from 'vuex';
import { getUrlByLocation } from '../../../utils/util';
import ZnztTable from './znzt-table.vue';
import ExPermission from './ex-permission.vue';
import { formatFormData } from '@/utils/util';
import { canStartExp, editExp } from '../utils';
import ExCron from './ex-cron.vue';
import ControlCron from './control-cron.vue';
const exTypes = {
  1: '编程实验',
  2: '可视化实验',
  3: '多链接实验',
  4: '推送实验',
  5: '云控配置实验'
};

export default {
  name: 'ex-table',
  components: { ZybTable, ZybText, ZnztTable, ExPermission, ExCron, ControlCron },
  data() {
    let columns = [
      'ID',
      '业务线',
      'AppID',
      '实验类型',
      '实验名称',
      //'实验模式',
      '运行时间',
      '流量占比',
      '关联固化',
      '状态',
      //'冻结',
      '冻结/暂停',
      '操作'
    ];
    const columnsStorage = localStorage.getItem('checked-column');
    if (columnsStorage) {
      columns = JSON.parse(columnsStorage);
    }

    return {
      value1: true,
      // 操作对象
      ops: {
        1: {
          opName: '暂停',
          opTip: '暂停成功',
          data: {
            status: 6
          },
          api: 'SET_STATUS',
          warningTip: '暂停实验后会没有用户命中实验，确定暂停实验吗？'
        },
        2: {
          opName: '开始',
          opTip: '开始成功',
          warningTip: '开启实验后，进组用户可实时查看。确定开始吗？',
          api: 'START_TEST'
        },
        3: {
          opName: '继续',
          opTip: '继续成功',
          warningTip: '确定继续实验吗？',
          data: {
            status: 3
          },
          api: 'SET_STATUS'
        },
        4: {
          opName: '删除',
          opTip: '删除成功',
          warningTip: '删除的实验将从系统移除，删除后无法查看相关信息，确定删除吗？',
          api: 'DELETE_TEST'
        },
        5: {
          opName: '结束',
          opTip: '结束成功',
          warningTip: '结束实验后，当前实验结果将为最终结果，而且无法重新启动实验，确定结束吗？',
          api: 'OVER_TEST'
        },
        6: {
          opName: '冻结',
          opTip: '冻结成功',
          warningTip: '冻结实验后，除已命中实验用户外，不再有新增用户命中实验。确定冻结实验吗？',
          api: 'SET_STATUS',
          data: {
            status: 7
          }
        },
        7: {
          opName: '解冻',
          opTip: '解冻成功',
          warningTip: '确定解冻实验吗？',
          api: 'SET_STATUS',
          data: {
            status: 3
          }
        }
      },
      checkedColumns: columns, // 筛选出来的表头
      checkedColumnsOptions: [
        'ID',
        '实验名称',
        '状态',
        '运行时间',
        '实验类型',
        '实验模式',
        '账号体系',
        '业务线',
        'AppID',
        '流量占比',
        // '关联Feature',
        '创建人',
        '创建时间',
        '更新人',
        '更新时间'
        // '冻结/暂停',
      ], // 所有表头数据

      isIndeterminate: true,
      checkAll: false, // 全选
      tableColumns: [
        {
          label: 'ID',
          prop: 'id'
        },
        {
          label: '实验名称',
          prop: 'displayName',
          'min-width': 120,
          //title: true,
          custom: true
        },
        {
          label: '状态',
          prop: 'status',
          render: scope => {
            return this.handleStatus(scope.row);
          }
        },
        {
          label: '运行时间',
          prop: 'timeStr',
          'min-width': 260,
          custom: true
        },
        {
          label: '实验类型',
          prop: 'clientType',
          render: scope => {
            return scope.row.clientType == 1 ? '客户端' : '服务端';
          }
        },
        {
          label: '实验模式',
          'min-width': 100,
          prop: 'type',
          render: scope => {
            return exTypes[scope.row.type];
          }
        },
        {
          label: '账号体系',
          prop: 'accountSystem',
          render: scope => {
            return scope.row.accountSystem == 1 ? 'cuid' : 'uid';
          }
        },
        {
          label: '业务线',
          prop: 'appDisplayName',
          //title: true,
          line: 2,
          'min-width': 120
        },
        {
          label: 'AppID',
          prop: 'appKey',
          'min-width': 150
        },
        {
          label: '流量占比',
          prop: 'flow',
          render: scope => {
            return scope.row.flow ? scope.row.flow / 10 + '%' : '0%';
          }
        },
        {
          label: '创建人',
          prop: 'createUser',
          width: 110,
          showOverflowTooltip: true
        },
        {
          label: '创建时间',
          prop: 'createTime',
          'min-width': 170,
          custom: true
          // render: (scope) => {
          //   return this.handleFormatDate(scope.row.createTime);
          // }
        },
        {
          label: '更新人',
          prop: 'updateUser',
          'min-width': 120
        },
        {
          label: '更新时间',
          prop: 'updateTime',
          'min-width': 120,
          custom: true
          // render: (scope) => {
          //   return this.handleFormatDate(scope.row.updateTime);
          // }
        },
        // {
        //   label: '关联Feature',
        //   prop: 'correctDisplayName',
        //   //title: true,
        //   'min-width': 120,
        //   custom: true
        // },
        // {
        //   label: '冻结/暂停',
        //   prop: 'pause',
        //   'min-width': 100,
        //   custom: true,
        //   header: true
        // },
        {
          label: '操作',
          prop: 'action',
          fixed: 'right',
          'min-width': 190,
          custom: true,
          header: true
        }
      ],
      versionColumns: [
        {
          label: '版本名',
          prop: 'displayName'
        },
        {
          label: '版本ID',
          prop: 'versionId'
        },
        {
          label: '参数名',
          prop: 'keyName'
        },
        {
          label: '参数值',
          prop: 'keyValue'
        },
        {
          label: '流量占比',
          prop: 'rate'
        },
        {
          label: '实际流量',
          prop: 'flow'
        }
      ],
      pubColumns: [
        {
          label: '发布比例',
          prop: 'flow',
          render: scope => {
            const flow = scope.row.flow;
            return (flow / 10).toFixed(2) + '%';
          }
        },
        {
          label: '发布组别',
          prop: 'displayName'
        },
        {
          label: '参数值',
          prop: 'keyValue',
          render: scope => {
            const list = scope.row.featureList || [];
            return list.map(item => item.keyValue).join('\n');
          }
        },
        {
          label: '剩余天数',
          prop: 'remainDay'
        },
        {
          label: '发布时间',
          prop: 'createshijian',
          render: scope => {
            const time = scope.row.createTime;
            if (!time) {
              return '';
            }
            return this.handleFormatDate(time);
          }
        }
      ],
      permissionDialog: {
        show: false,
        data: null
      },
      publishInfo: {},
      continuePublishShow: false,
      cron: {
        visible: false,
        title: "",
        tips: '',
        text: '开始'
      },
      controlCron: {
        id: null,
        visible: false
      }
    };
  },
  computed: {
    _tableColumns() {
      const str = this.checkedColumns.toString();
      return this.tableColumns.filter(f => this.checkedColumns.some(sf => sf === f.label));
    },
    ...mapGetters(['statusMap'], 'message')
  },
  props: {
    tableList: {
      type: Array
    },
    appKey: {
      type: String
    }
  },
  mounted() {
    console.log(this.appKey);
  },
  methods: {
    controlCronAction(row) {
      this.controlCron = {
        visible: true,
        id: row.id
      };
    },
    async handleRollBack(row) {
      await this.$confirm(
        '回滚后，实验将作废发布计划，实验进入已回滚状态，在已回滚状态下实验执行发布前的实验策略，在已回滚状态下仅支持再次发布实验，请谨慎选择。',
        '实验回滚提示'
      );
      await this.$service.get('ROLLBACKPUSHEXP', {
        experimentId: row.id,
        pushId: row.pushId
      });
      this.$emit('handleSearch');
    },
    handlePublish(row) {
      this.assembleHistoryDay(row);
      this.$router.push({
        path: '/exp-manage/list/publish',
        query: {
          id: row.id,
          pushId: row.pushInfo ? row.pushInfo.pushId : 0
        }
      });
    },
    continuePublish(row) {
      const { pushInfo = {} } = row;
      this.publishInfo = {
        ...pushInfo,
        experimentId: row.id,
        flow: pushInfo.flow / 10,
        minFlow: pushInfo.flow / 10
      };
      this.continuePublishShow = true;
    },
    async continuePublishAction() {
      const data = formatFormData({
        ...this.publishInfo,
        flow: this.publishInfo.flow * 10
      });
      await this.$service.post('EDITPUSHEXP', data);
      this.continuePublishShow = false;
      this.$emit('handleSearch');
    },
    closePermissionDialog(refresh) {
      this.permissionDialog.show = false;
      if (refresh) {
        this.$emit('handleSearch');
      }
    },
    isAuthLevel2(row) {
      return row.authLevel === 2;
    },
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'yyyy-MM-dd');
      }
      return '-';
    },
    handleStatus(row) {
      const statusVal = this.statusMap[row.status];
      return statusVal;
    },
    // 诊断实验
    goDiagnosis(row) {
      const hash = '/exp-manage/diagnosis';
      const params = {
        accSystem: row.accountSystem,
        id: row.id,
        name: encodeURIComponent(row.displayName),
        ex: row.type,
        filterLock: row.filterLock,
        status: row.status
      };
      const url = getUrlByLocation(hash, params);
      window.open(url);
    },
    // 组装实验历史需要的部分数据
    assembleHistoryDay(row) {
      localStorage.setItem('displayName', row.displayName);
      const statusVal = this.handleStatus(row);
      localStorage.setItem('status', statusVal);
    },
    // 编辑实验
    editExp(row) {
      this.assembleHistoryDay(row);
      editExp(row, this.$router, this.$service, this.$message);
    },
    // 复制实验
    handleCopy(row) {
      this.assembleHistoryDay(row);
      console.log('row.parentId', row.parentId);
      console.log('row.parentStatus', row.parentStatus);
      // 如果当前实验parentId存在，则弹出弹窗
      if (row.parentId) {
        // 父实验已结束
        if (row.parentStatus === 4) {
          this.doCopyExp(row, false);
          return;
        }
        this.$confirm('当前实验为子实验，是否复制父子关系', '复制提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning',
          closeOnClickModal: false,
          distinguishCancelAndClose: true,
          customClass: 'ex-table-message'
        })
          .then(() => {
            // 用户点击"是"，继续复制操作
            this.doCopyExp(row, true);
          })
          .catch(action => {
            if (action === 'cancel') {
              // 用户点击"否"，不复制父子关系
              this.doCopyExp(row, false);
            } else {
              // 用户点击X关闭，取消复制操作
              this.$message({
                type: 'info',
                message: '已取消复制'
              });
            }
          });
      } else {
        // 不是子实验，直接复制
        this.doCopyExp(row, false);
      }
    },

    // 执行复制实验操作
    doCopyExp(row, keepParentRelation) {
      const hash = '/exp-manage/list/edit';
      const params = {
        id: row.id,
        type: 'edit',
        subType: 'copy',
        ex: row.type
      };

      // 如果需要保持父子关系，则添加parentId参数
      if (keepParentRelation && row.parentId) {
        params.parentId = row.parentId;
      }

      const url = getUrlByLocation(hash, params);
      // window.open(url);
      location.href = url;
    },
    // 创建继承实验
    createExtendExp(row) {
      if (row.hasCorrect) {
        this.$message.warning('当前实验已固化，不支持继承！');
        return;
      }
      if (!!row.hasRunningChild) {
        this.$message.warning('当前实验存在未结束的子实验，不支持继承');
        return;
      }
      this.assembleHistoryDay(row);
      const hash = '/exp-manage/list/edit';
      const params = {
        id: row.id,
        type: 'edit',
        ex: row.type,
        isExtend: 1
      };
      if (row.parentId / 1) {
        params.parentId = row.parentId;
      }
      this.$router.push({ path: hash, query: params });
    },
    // 查看实验
    checkExp(row) {
      this.assembleHistoryDay(row);
      const hash = '/exp-manage/list/edit';
      const params = {
        id: row.id,
        type: 'check',
        ex: row.type,
        pushId: row.pushInfo && row.pushInfo.pushId
      };
      if (row.parentId / 1) {
        params.parentId = row.parentId;
      }
      this.$router.push({ path: hash, query: params });
      // const url = getUrlByLocation(hash, params);
      // window.open(url);
    },
    // 组装为formData格式
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    // 处理冻结
    handleFreeze(row, val) {
      console.log('wwwww:' + row.switch);
      //debugger;
      if (row.switch == 1) {
        // 去冻结
        this.handleComfirm(row, 6);
      } else {
        // 去解冻
        this.handleComfirm(row, 7);
      }
    },
    handleSwitch(row, val) {
      console.log('wwwww:' + row.switch);
      //debugger;
      if (row.switch == 1) {
        // 去暂停
        // debugger;
        this.handleComfirm(row, 1);
      } else {
        // 去继续
        this.handleComfirm(row, 3);
      }
    },
    confirmTime(data) {
      const { taskTime } = data;
      const { row, info, op, reason } = this.cron;
      this.handleAction(row, op, info, {
        reason,
        taskTime: taskTime
      });
    },
    // 确认操作
    async handleComfirm(row, type) {
      const op = this.ops[type];
      let info = '';
      const childExpName =
        row.childExperimentList && row.childExperimentList.length
          ? row.childExperimentList.map(v => v.displayName)
          : '';
      if (type == 1) {
        info = childExpName
          ? `当前实验关联了子实验：${childExpName};实验暂停后，暂停期间父实验无进组用户，关联父实验版本的子实验版本也将没有进组用户，确定暂停吗？`
          : '暂停后不再有正常用户命中实验，仅对白名单用户生效；暂停期间该实验无进组用户，确定暂停吗？';
      } else if (type == 6) {
        info = childExpName
          ? `当前实验关联了子实验：${childExpName};实验冻结后，父实验不再有新用户进组，已经命中父实验用户，仍作为关联父实验版本的子实验受众继续命中子实验，确定冻结吗？`
          : '冻结实验后，保持当前进组数据，不再有新用户进组，确定冻结实验吗？';
      } else if (type == 5) {
        info = childExpName
          ? `当前的实验关联了子实验：${childExpName};结束实验后，子实验也将同时结束，父实验和子实验结果将为最终结果，结束的实验无法重新启动，确定结束吗？`
          : '结束实验后，当前实验结果将为最终结果，而且无法重新启动实验，确定结束吗？';
      } else if (type == 2 && row.status == 5) {
        info = row.extendsNotice || '开启后原实验自动结束，确定开启继承实验吗？';
      } else {
        info = op.warningTip;
      }
      // 删除逻辑
      if (type === 4) {
        const isExtended = !!row.isExtended;
        if (isExtended) {
          this.$message.warning('当前实验已被继承，不支持删除！');
          return;
        }
      }
      let reason = '';
      // 调试下的开启实验才需要校验
      if (type === 2 && row.status === 2) {
        const res = await canStartExp(row, {
          alert: this.$alert,
          prompt: this.$prompt,
          service: this.$service
        });
        if (!res || !res.valid) {
          return;
        }
        reason = res.reason;
      }
      // 在这里增加设置开始时间和结束时间的逻辑
      if (type === 2) {
        this.cron = {
          visible: true,
          title: '实验开启',
          row,
          info,
          op,
          reason
        };
        return;
      }
      if (type === 5) {
        this.cron = {
          visible: true,
          title: '实验结束',
          text: '结束',
          tips: row.isExtended ? '当前的实验关联了子实验，结束实验后，子实验也将同时结束，父实验和子实验结果将为最终结果，结束的实验无法重新启动！' : '结束实验后，当前实验结果将为最终结果，而且无法重新启动实验！',
          row,
          info,
          op,
          reason
        };
        return;
      }
      this.handleAction(row, op, info, {
        reason
      });
    },
    handleAction(row, op, info, ext = {}) {
      this.$confirm(`【${row.displayName}】 ${info}`, '提示', {
        customClass: 'ex-table-message',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleOperate(row, op, ext);
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    //具体动作
    handleOperate(row, op, ext) {
      const opData = op.data ? op.data : {};
      const data = {
        experimentId: row.id,
        appKey: row.appKey,
        ...opData,
        ...ext
      };
      const formData = this.formatFormData(data);
      this.$service.post(op.api, formData, { allback: 0, needLoading: true }).then(res => {
        this.$message.success(op.opTip);
        this.$emit('handleSearch');
      });
    },
    // 实验报告
    getExpReport(row) {
      this.assembleHistoryDay(row);
      const hash = '/exp-manage/list/report';
      const params = {
        id: row.id
      };
      if (row.parentId / 1) {
        params.parentId = row.parentId;
      }
      const url = getUrlByLocation(hash, params);
      window.open(url);
    },
    // 去关联或者跳转到固化详情页
    turnFeature(val) {
      this.$router.push({
        path: '/feat/list/detail',
        query: {
          id: val.row.correctId > 0 ? val.row.correctId : '', // 固化id
          experimentId: val.row.correctId == 0 ? val.row.id : '', //实验id
          type: val.row.correctId > 0 ? 'detail' : ''
        }
      });
    },
    // 已经关联过后跳转到版本管理页面
    turnFeatureManage(val) {
      this.$router.push({
        path: '/feat/list/manage',
        query: {
          id: val.row.correctId > 0 ? val.row.correctId : '' // 固化id
        }
      });
    },
    // 点击checkbox
    handleCheckedChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.checkedColumnsOptions.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.checkedColumnsOptions.length;
      localStorage.setItem('checked-column', JSON.stringify(value));
    },
    createChildExp(row) {
      const hash = '/exp-manage/list/add';
      const params = {
        type: 'add',
        parentId: row.id,
        ex: row.type
      };
      this.$router.push({ path: hash, query: params });
    },
    managePermission(row) {
      this.permissionDialog = {
        show: true,
        data: row.id
      };
    },
    async handleStar(row) {
      // 调用收藏接口
      try {
        // formdataformat
        const data = formatFormData({
          experimentId: row.id,
          favorite: !row.favorite
        });
        const res = await this.$service.post('FAVORITE', data);
        this.$message.success(!row.favorite ? '已收藏' : '已取消收藏');
        this.$emit('handleSearch');
      } catch (e) {
        this.$message.error('操作失败，请重试');
      }
    },
    refreshTable() {
      this.$emit('handleSearch');
    }
  },
  filters: {
    filterExpType: function (val) {
      if (!val) return '';
      return exTypes[val];
    }
  }
};
</script>

<style lang="less" scoped>
.ex-table {
  position: relative;
  padding-top: 16px;

  i.el-icon-s-fold {
    position: absolute;
    right: 0;
    top: 0px;
    cursor: pointer;
    color: #42c57a;
  }
}

.ex-tit {
  padding: 10px;
  font-size: 16px;
  line-height: 20px;
  font-weight: 600;
}

.action_more {
  color: #444;
}

.slider-container {}

/deep/ .zyb_text.warn-tip {
  color: #fa574b !important;
}

.action_more i {
  font-size: 12px;
}

/deep/ .el-button--small {
  font-size: 14px;
}

/deep/ .el-dropdown-menu__item {
  // color: #42c57a !important;
  color: #444 !important;
}

/deep/ .el-dropdown-menu__item.warn-tip {
  color: #fa574b !important;
}

/deep/ .el-dropdown-menu__item.warn-tip:focus,
/deep/ .el-dropdown-menu__item.warn-tip:hover {
  color: #fa574b !important;
}

/deep/ .el-dropdown-menu__item.is-disabled {
  cursor: not-allowed !important;
  color: #acb1bf !important;
}

.operateIcon {
  display: flex;
  justify-content: center;

  i {
    font-size: 14px;
    margin-left: 12px;
    cursor: pointer;
  }

  i.tip-btn {
    margin-left: 6px;
  }
}

.checkbox {
  display: block;
}

.el-checkbox+.el-checkbox {
  margin-left: 0;
}

/deep/ .tableScopeSwitch .el-switch__label {
  position: absolute;
  display: none;
  color: #fff;
}

/*打开时文字位置设置*/
/deep/ .tableScopeSwitch .el-switch__label--right {
  z-index: 1;
  right: 6px;
  /*不同场景下可能不同，自行调整*/
}

/*关闭时文字位置设置*/
/deep/ .tableScopeSwitch .el-switch__label--left {
  z-index: 1;
  left: 6px;
  /*不同场景下可能不同，自行调整*/
}

/*显示文字*/
/deep/ .tableScopeSwitch .el-switch__label.is-active {
  display: block;
}

/deep/ .tableScopeSwitch.el-switch .el-switch__core,
/deep/ .el-switch .el-switch__label {
  width: 55px !important;
  /*开关按钮的宽度大小*/
}

/deep/ .op-switch.is-disabled {
  opacity: 1;
}

/deep/ .op-switch.is-disabled .el-switch__core,
/deep/ .op-switch.is-disabled .el-switch__label {
  cursor: pointer;
}

.ex-tit-type {
  background-color: rgba(66, 197, 122, 0.9);
  color: #fff;
  padding: 3px 5px;
  margin-left: 7px;
  font-size: 12px;
  border-radius: 2px;
}

.ex-tit-type1 {
  background-color: rgba(51, 112, 255, 0.2);
  color: rgb(51, 112, 255);
}
</style>
