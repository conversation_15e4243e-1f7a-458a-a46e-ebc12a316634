<template>
  <div class="filter-container">
    <!-- 高级分析 -->
    <div class="filter-btns">
      <div
        @click="sameTerm"
        :class="['basicBtn', {selectedAnalysis: isSameTerm}]"
      >
        同期群分析
      </div>
      <div
        @click="comingSoon"
        :class="['basicBtn', {selectedAnalysis: isComingSoon}]"
      >
        敬请期待
      </div>
    </div>
    <!-- 筛选维度 -->
  </div>
</template>
<script>
export default {
  name: 'filter-advance',
  props: {},
  data() {
    return {
      isSameTerm: true,
      isComingSoon: false,
    };
  },
  methods: {
    // 同期群分析
    sameTerm() {
      this.isSameTerm = true;
      this.isComingSoon = false;
    },
    // 敬请期待
    comingSoon() {
      this.isSameTerm = false;
      this.isComingSoon = true;
    },
  },
};
</script>
<style scoped lang="less">
.filter-btns {
  display: flex;
  justify-content: flex-start;
  margin-top: 30px;
  padding: 20px;
  border: 1px solid gainsboro;
}
.basicBtn {
  color: #25292e;
  background: #fafbfc;
  border: 1px solid #e1e4f0;
  box-shadow: 0 1px 1px rgb(0 0 0 / 5%);
  border-radius: 4px;
  font-size: 13px;
  padding: 6px 30px;
  cursor: pointer;
}
.selectedAnalysis {
  color: #ffffff;
  font-size: 13px;
  background: #5d78f2;
  border: 1px solid #5d78f2;
  box-sizing: border-box;
  border-radius: 2px;
  transform: matrix(-1, 0, 0, 1, 0, 0);
}

</style>

