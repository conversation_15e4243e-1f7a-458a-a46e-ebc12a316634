<template>
  <section>
    <section v-if="!filterRule.length && isView" class="no-data">
      无
    </section>
    <section class="rule-container" v-else>
      <div class="line-con" v-if="filterRule.length > 1">
        <span>或</span>
      </div>
      <div v-for="(frule, index) in filterRule" :key="index" class="rule-item">
        <section class="top">
          <p class="and-sign" v-show="frule.filterList.length > 1"><span>且</span></p>
          <div v-if="frule.filterList.length" class="rule-con">
            <div class="detail-set" :class="[frule.filterList.length > 1 ? 'detail-set-b' : '']"
              v-for="(flist, idx) in frule.filterList" :key="idx">
              <!-- 受众规则名称(最右边) -->
              <el-select v-model="flist.filterConfId" size="small" filterable
                :disabled="isDisabledStatus || flist.filterConfId === 0" class="s-w filter-select" placeholder="请选择"
                :popper-append-to-body="false" @change="(val) => handleChangeFilterKeyName(val, flist)">
                <el-option v-for="(fitem, findex) in getFilterList(flist)" :key="findex" :label="fitem.displayName"
                  :value="fitem.id" :disabled="fitem.disabled">
                  <el-popover :open-delay="300" placement="right" trigger="hover">
                    <div>
                      <div style="min-width: 200px">
                        <h3 style="font-size: 14px; font-weight: 600">参数名: {{ fitem.displayName }}</h3>
                        <p style="padding: 20px 0">属性名：{{ fitem.keyName }}</p>
                        <p style="font-size: 14px" v-if="fitem.keyType === 1">值类型: String</p>
                        <p style="font-size: 14px" v-if="fitem.keyType === 2">值类型: Boolean</p>
                        <p style="font-size: 14px" v-if="fitem.keyType === 4">值类型: Float</p>
                      </div>
                    </div>
                    <p slot="reference">{{ fitem.displayName }}</p>
                  </el-popover>
                </el-option>
              </el-select>
              <!-- 受众规则比较逻辑(中间) -->
              <el-select :disabled="isDisabledStatus" v-model="flist.keyType" collapse-tags class="s-w"
                placeholder="请选择" @change="(val) => handleChangeKeyType(val, flist)">
                <el-option v-for="(sItem, sIndex) in flist.symbolList" :key="sIndex" :label="sItem.displayName"
                  :value="sItem.type"></el-option>
              </el-select>
              <!-- 受众规则输入值(最左边) -->
              <!-- 当keyType选择了等于或不等于的时候 -->
              <template v-if="flist.keyType === 1 || flist.keyType === 2">
                <!-- 人群包 -->
                <template v-if="flist.useEnum && flist.filterType === 2">
                  <el-select :disabled="isDisabledStatus || flist.filterConfId === '' || !flist.keyType"
                    v-model="flist.keyValue" multiple filterable class="s-w s-slect" placeholder="">
                    <el-option v-for="(item, kIndex) in flist.keyValueEnums" :key="kIndex" :label="item.label"
                      :value="item.value"></el-option>
                  </el-select>
                </template>
                <template v-else>
                  <el-select v-model="flist.keyValue" v-if="isIpType(flist.filterConfId)" multiple filterable remote
                    :remote-method="getIpListOptions" :disabled="isDisabledStatus">
                    <el-option v-for="item in ipList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                  <el-select v-else :disabled="isDisabledStatus || flist.filterConfId === '' || !flist.keyType"
                    v-model="flist.keyValue" :multiple="flist.keyValueType !== 2" filterable class="s-w s-slect"
                    :allow-create="flist.keyValueType !== 2" default-first-option placeholder="请选择或创建后选择"
                    @change="(val) => handleKeyValueChange(val, flist, idx)">
                    <el-option v-for="item in getKeyValueEnums(flist)" :key="item.value" :label="item.label"
                      :value="item.value"></el-option>
                  </el-select>
                </template>
              </template>
              <template v-else>
                <el-input-number v-if="flist.keyValueType === 4"
                  :disabled="isDisabledStatus || flist.filterConfId === '' || !flist.keyType" controls-position="right"
                  v-model="flist.keyValue" placeholder="请输入"
                  @change="(val) => handleKeyValueChange(val, flist, idx)"></el-input-number>
                <el-input v-else :disabled="isDisabledStatus || flist.filterConfId === '' || !flist.keyType"
                  class="s-w s-slect el-select u-input" v-model="flist.keyValue" placeholder="请输入"
                  @change="(val) => handleKeyValueChange(val, flist, idx)" type="text"></el-input>
              </template>
              <span v-if="!isDisabledStatus" class="sigle-de el-icon-delete"
                @click="handleDelFilterRule(index, idx)"></span>
            </div>
          </div>
        </section>
        <div class="op-con">
          <p class="op-add">
            <el-button v-if="!isDisabledStatus" type="text" @click="handleAddFilterRule(index, 1)">
              <i class="e-icon el-icon-plus"></i>
              添加过滤规则
            </el-button>
            <el-button v-if="!isDisabledStatus" type="text" @click="handleAddFilterRule(index, 2)">
              <i class="e-icon el-icon-plus"></i>
              用户分群
            </el-button>
          </p>
          <p class="op-delete">
            <el-button type="text" v-if="!isDisabledStatus" @click="handleDelRule(index)">
              <i class="e-icon el-icon-delete"></i>
              删除
            </el-button>
          </p>
        </div>
      </div>
    </section>
    <el-button v-if="!isDisabledStatus" type="default" size="small" class="add-rule" @click="handleAddRule"
      icon="el-icon-circle-plus">
      添加受众规则
    </el-button>
  </section>
</template>

<script>
import { mapGetters } from 'vuex';
const clearDuplicate = arr => {
  // debugger;
  let map = new Map();
  for (let item of arr) {
    if (!map.has(item.label)) {
      map.set(item.label, item);
    }
  }
  return [...map.values()];
};
export default {
  name: 'ShouzhongRule',
  components: {
  },
  props: {
    isView: Boolean,
    value: Array
  },
  data() {
    return {
      filterRule: this.value || [],
      ipList: []
    };
  },
  computed: {
    ...mapGetters(['filerList'], 'message'),
    isDisabledStatus() {
      return this.isView;
    },
  },
  watch:{
    filterRule:{
      handler(val){
        this.$emit('input', val);
      },
      immediate: true,
      deep: true
    },
    value:{
      handler(val){
        if(val !== this.filterRule){
          this.filterRule = [
            ...val
          ];
        }
      },
      deep: true
    },
    filerList:{
      handler(data){
        this.filterRule.forEach(rule => {
          rule.filterList.forEach(list => {
            for (let i = 0, len = data.filterList.length; i < len; i++) {
              const current = data.filterList[i];
              if ((list.filterConfId === current.id) || (list.filterType === 2 && current.keyName === '__crowd__')) {
                list.keyValueType = current.keyType;
                list.symbolList = current.symbols !== '' ? current.symbols : [{ type: 1, displayName: '等于' }, { type: 2, displayName: '不等于' }];
                list.filterConfId = current.id;
                list.useEnum = current.useEnum;
                // 受众规则是人群包
                const isCrowd = (list.filterConfId === 0 && current.useEnum === 1) || (list.filterType === 2 && current.keyName === '__crowd__');
                const temp = current.keyType === 2 ? [{ label: 'true', value: 'true' }, { label: 'false', value: 'false' }] : (current.keyValueEnums ? current.keyValueEnums : []);
                const arr = Array.isArray(list.keyValue) ? list.keyValue.map(value => { return { label: value, value }; }): [];
                list.keyValueEnums = clearDuplicate(current.keyType === 2 || isCrowd ? temp : [...temp, ...arr]);
                break;
              }
            }
          });
        });
      },
    }
  },
  created() {
    // this.$store.dispatch('getFilterList');
  },
  mounted() {
  },
  methods: {
    handleAddRule() {
      this.filterRule.push({
        filterList: [
          {
            keyType: '',
            keyValue: [],
            filterConfId: '',
            symbolList: []
          }
        ]
      });
    },
    handleKeyValueChange(val, flist, idx) {
      //this.$message.warning(Object.prototype.toString.call(val) + " --> " + JSON.stringify(val));
      if (typeof val === 'object') {
        flist.keyValue = val;
      } else {
        flist.keyValue = [val + ''];
      }
    },
    getFilterList(item) {
      const index = item.filterConfId === 0 ? 0 : 1;
      const list = (this.filerList.filterList || []).map(item => {
        return {
          ...item,
          disabled: item.id === 0
        };
      });
      return list || [];
    },
    // 当改变受众规则中的keyType
    handleChangeKeyType(val, flist) {
      flist.keyValue = [];
    },
    // 当改变受众规则的keyName时候
    handleChangeFilterKeyName(val, flist) {
      this.reset(flist);
      let filterList = this.filerList.filterList;
      for (let i = 0, len = filterList.length; i < len; i++) {
        const curr = filterList[i];
        if (curr.id === val) {
          flist.keyValueType = curr.keyType;
          flist.filterType = curr.filterType;
          flist.useEnum = curr.useEnum;
          flist.keyValueEnums = curr.keyName === 'ip' ? this.ipList : (curr.keyType === 2 ? [{ label: 'true', value: 'true' }, { label: 'false', value: 'false' }] : (curr.keyValueEnums ? curr.keyValueEnums : []));
          flist.symbolList = curr.symbols && curr.keyType !== 2 ? curr.symbols : [{ type: 1, displayName: '等于' }, { type: 2, displayName: '不等于' }];
          break;
        }
      }
    },
    reset(flist) {
      flist.keyType = '';
      flist.keyValue = [];
    },
    getChangeFilterKeyName(val, flist) {
      let filterList = this.filerList.filterList;
      for (let i = 0, len = filterList.length; i < len; i++) {
        const curr = filterList[i];
        if (curr.id === val) {
          flist.keyValueType = curr.keyType;
          flist.filterType = curr.filterType;
          flist.useEnum = curr.useEnum;
          flist.keyValueEnums = curr.keyType === 2 ? [{ label: 'true', value: 'true' }, { label: 'false', value: 'false' }] : (curr.keyValueEnums ? curr.keyValueEnums : []);
          flist.symbolList = curr.symbols && curr.keyType !== 2 ? curr.symbols : [{ type: 1, displayName: '等于' }, { type: 2, displayName: '不等于' }];
          break;
        }
      }
    },
    handleDelFilterRule(index, idx) {
      const filterArr = this.filterRule[index].filterList;
      if (filterArr.length === 1) {
        this.filterRule.splice(index, 1);
      } else {
        filterArr.splice(idx, 1);
      }
    },
    handleDelRule(index) {
      this.filterRule.splice(index, 1);
    },
    handleAddFilterRule(index, type) {
      let item = {};
      if (type === 2) {
        this.getChangeFilterKeyName(0, item);
        item.filterConfId = 0;
      }
      const data = Object.assign({
        //keyName: '',
        keyType: '',
        keyValue: [],
        filterConfId: '',
        symbolList: [],
        //keyValueType: '',
        //keyDisplayName: ''
      }, item);
      this.filterRule[index].filterList.push(data);
    },
    getKeyValueEnums(flist) {
      const { filterConfId } = flist;
      const list = this.filerList.filterList || [];
      const target = list.find(item => item.id === filterConfId) || {};
      return flist.keyValueEnums || [];
    },
    async getIpListOptions(keyword) {
      const list = await this.$service
        .get('FILTERIPLIST', {
          keyword
        });
      this.ipList = list.map(item => {
        return {
          label: item.name,
          value: item.name
        };
      });
    },
    isIpType(id) {
      const list = this.filerList.filterList || [];
      const target = list.find(item => item.id === id) || {};
      return target.keyName === 'ip';
    }
  }
};
</script>

<style lang="less" scoped>
.no-data {
  padding: 0 8px 12px 8px;
  font-size: 16px;
  color: black;
  opacity: 0.8;
}

.rule-container {
  position: relative;
  margin-left: 45px;

  .el-input,
  .el-input-number {
    width: 192px;
  }

  .line-con {
    position: absolute;
    top: 12px;
    bottom: 12px;
    width: 12px;
    // background-color: #42c57a;
    border: 2px solid #42c57a;
    border-right: none;

    span {
      position: absolute;
      left: -13px;
      background: #42c57a;
      color: white;
      padding: 5px;
      top: 50%;
      font-size: 14px;
      line-height: 14px;
      transform: translateY(-50%);
      border-radius: 4px;
      font-weight: bold;
    }
  }

  .rule-item {
    margin-left: 32px;
    margin-bottom: 8px;

    .top {
      display: flex;
      flex-direction: row;

      .rule-con {
        display: flex;
        width: 100%;
        padding: 0px;
        position: relative;
        flex-direction: column;
      }

      .and-sign {
        width: 12px;
        border: 2px solid #42c57a;
        border-right: none;
        position: relative;
        margin-top: 12px;
        margin-bottom: 12px;
        margin-right: 8px;

        span {
          position: absolute;
          left: -13px;
          background: #42c57a;
          color: white;
          padding: 5px;
          top: 50%;
          font-size: 14px;
          line-height: 14px;
          transform: translateY(-50%);
          border-radius: 4px;
          font-weight: bold;
        }
      }
    }

    .sigle-de {
      margin-left: 12px;
    }


    .detail-set {
      margin: 5px 0;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .detail-set:hover .sigle-de {
      display: inline-block;
    }
  }
}
</style>
