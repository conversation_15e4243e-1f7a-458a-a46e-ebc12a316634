<template>
  <el-dialog :title="title" :visible="dialogVisible" :before-close="dialogClose">
    <h3 class="tip">
      在互斥组的实验，相互之间是互斥的，这意味着应用的用户只能命中该互斥组中的一个实验。
    </h3>
    <el-form ref="mutuformBase" :model="formData" label-width="100px" :rules="rules">
      <el-form-item label="名称：" required prop="displayName">
        <el-input class="f-width-c" type="text" v-model="formData.displayName" maxlength="50" show-word-limit placeholder="请输入名称，支持50个以内字符"></el-input>
      </el-form-item>
      <el-form-item label="描述：" prop="description">
        <el-input type="textarea" v-model="formData.description" class="f-width-c" rows="3" resize="vertical" maxlength="1000" show-word-limit placeholder="请输入描述信息，支持1000个以内字符"></el-input>
      </el-form-item>
      <el-form-item label="业务线：" required>
        <el-select :disabled="true" v-model="addData.appKey" placeholder="请选择业务线" @change="handleChangAppKey">
          <el-option v-for="item in appList" :key="item.appKey" :label="item.displayName" :value="item.appKey"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="实验类型：" required>
        <el-radio-group v-model="addData.clientType" :disabled="true">
          <el-radio :label="1">客户端</el-radio>
          <el-radio :label="2">服务端</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="账号体系：" required>
        <!-- 账号体系，cuid：1，uid: 2-->
        <el-radio-group v-model="addData.accountSystem" :disabled="true">
          <el-radio :label="1">cuid</el-radio>
          <el-radio :label="2">uid</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <!-- 确定取消按钮组 -->
    <section slot="footer" class="dialog-footer">
      <el-button @click="dialogClose">取 消</el-button>
      <el-button type="primary" @click="validateCreate">确定</el-button>
    </section>
  </el-dialog>
</template>
<script>
import * as _ from 'lodash';
import { mapGetters } from 'vuex';
export default {
  name: 'create-dialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    editId: {
      type: Number
    }
  },
  computed: {
    disableEdit() {
      return this.editId ? true : false;
    },
    ...mapGetters(['appList', 'addData', 'mutuList'], 'message')
  },
  data() {
    return {
      isCheck: this.$route.query.type === 'check' ? true : false,
      isAdd: this.$route.query.type === 'add' ? true : false,
      isEdit: this.$route.query.type === 'edit' ? true : false,
      createMutuId: '',
      formData: {
        // clientType: this.addData.clientType,
        // appKey: this.addData.appKey,
        displayName: '',
        description: ''
      },
      title: '新建互斥组',
      rules: {
        appKey: [{ required: true, message: '请选择业务线', trigger: 'blur' }],
        displayName: [
          { required: true, message: '请输入名称，支持50个以内字符', trigger: 'blur' },
          { required: true, message: '请输入名称，支持50个以内字符', trigger: 'change' }
        ]
      }
    };
  },
  created() {
    if (this.editId) {
      // 获取详情
      this.getMutuDetai();
      this.title = '编辑互斥组';
    }
  },
  mounted() { },
  methods: {
    handleChangAppKey(val) {
      //   this.$emit("changeAppKey",val)
    },
    validateCreate() {
      this.$refs['mutuformBase'].validate((valid) => {
        if (valid) {
          this.handleCreateMutu();
        } else {
          console.log('提交失败');
          return false;
        }
      });
    },
    handleCreateMutu() {
      let that = this;
      let params = {
        ...this.formData,
        appKey: this.addData.appKey,
        clientType: this.addData.clientType,
        accountSystem: this.addData.accountSystem
      };
      const formData = this.formatFormData(params);
      let api = this.editId ? 'AB_EXCLUSIVE_UPDATE' : 'AB_EXCLUSIVE_ADD';
      let tip = this.editId ? '编辑成功' : '新建成功';
      //debugger
      this.$service
        .post(api, formData, { needLoading: true })
        .then((data) => {
          // if (this.editId) {
          //   this.$emit('resetEditId');
          // }
          this.createMutuId = data;
          const api = 'AB_EXCLUSIVE_SHOW';
          const formData = this.formatFormData({
            id: data
          });
          this.$service.post(api, formData, { allback: 0, needLoading: true }).then((res) => {
            this.$nextTick(() => {
              that.$emit('afterCreateMutu', data);
            });
            this.$store.dispatch('getMutuListForExp');
            this.dialogClose();
          });
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getMutuListForExp(data) {
      const params = {
        pn: 0,
        rn: 100000,
        appKey: this.addData.appKey,
        clientType: this.addData.clientType,
        isDisplay: 1,
        experimentStatus: this.addData.status == 3 ? 3 : '', // 当实验处于发布中的时候需要传递3
        accountSystem: this.addData.accountSystem,
        parentExperimentId: this.addData.parentId
      };
      //debugger
      this.$service
        .get('AB_EXCLUSIVE', { ...params })
        .then((res) => {
          this.$store.commit('setMutuList', res.list);
          //this.mutuList = res.list;
          this.$message.success('创建成功');
          this.$emit('afterCreateMutu', data);
          this.dialogClose();
          // commit('setMutuList', res.list);
          // ;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
      // this.defaultFormat();
    },
    //获取互斥责详情
    getMutuDetai() {
      this.$service
        .get('AB_EXCLUSIVE_VIEW', { id: this.editId }, { needLoading: true })
        .then((data) => {
          //debugger;
          this.formData = data;
        })
        .catch((err) => {
          console.log(err);
        });
    }
  },
  beforeDestroy() {
    this.$emit('resetEditId');
  }
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 760px;
  min-width: 30%;
}

.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}

.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;

  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }

  .detail-value {
    width: 200px;

    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }

  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;

    span {
      padding: 0 11px;
    }
  }

  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }

  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}

/deep/ .divider-con {
  margin: 20px 0;
}

.tip {
  padding: 10px 10px 20px;
}

/deep/ .el-radio__label {
  font-size: 14px;
}
</style>
