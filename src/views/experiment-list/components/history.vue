<template>
  <div>
    <el-drawer title="操作历史" :before-close="handleClose" :visible.sync="showDrawer" :with-header="false" direction="rtl" ref="drawer">
      <div class="history-page">
        <p style="font-size: 16px; font-weight: 500; margin: 20px 0 40px; line-height: 1.5">
          {{ title }}
          <span style="
              background-color: rgba(51, 112, 255, 0.2);
              color: rgb(51, 112, 255);
              padding: 3px 10px;
              margin-left: 7px;
              font-size: 12px;
            ">
            {{ status }}
          </span>
        </p>
        <div class="history">
          <div style="padding: 0px 10px; position: relative" class="history-warp">
            <div v-for="(item, index) in expHistoryData" :key="index" class="history-content">
              <h3 class="head-date">
                <template>
                  <div>
                    {{ item.createTime | handleFormatDate }}
                  </div>
                </template>
              </h3>
              <div class="history-border"></div>
              <div class="history-item">
                <!--图标-->
                <div class="tob_orderLine_item_circle" style="background-color: rgb(96, 106, 120)">
                  <!-- 草稿 -->
                  <span v-if="item.operationType == 1" class="anticon iconfont znzt-caogao"></span>
                  <!-- 创建实验，进入调试状态 -->
                  <span v-else-if="item.operationType == 5" class="anticon iconfont znzt-tiaoshi"></span>
                  <!-- 草稿=>调试 -->
                  <span v-else-if="item.operationType == 7" class="anticon iconfont znzt-tiaoshi"></span>
                  <!-- 修改实验 -->
                  <span v-else-if="item.operationType == 10" class="anticon iconfont znzt-xiugai"></span>
                  <!-- 开始实验 -->
                  <span v-else-if="item.operationType == 15" class="anticon iconfont znzt-kaishi"></span>
                  <!-- 暂停实验 -->
                  <span v-else-if="item.operationType == 20" class="anticon iconfont znzt-zanting"></span>
                  <!-- 继续实验 -->
                  <span v-else-if="item.operationType == 25" class="anticon iconfont znzt-shibaichujixu"></span>
                  <!-- 冻结实验-->
                  <span v-else-if="item.operationType == 30" class="anticon iconfont znzt-dongjie"></span>
                  <!-- 解冻实验 -->
                  <span v-else-if="item.operationType == 35" class="anticon iconfont znzt-jiedong"></span>
                  <!-- 结束实验 -->
                  <span v-else-if="item.operationType == 40" class="anticon iconfont znzt-jieshu"></span>
                  <!-- 固化feature -->
                  <span v-else-if="item.operationType == 45" class="anticon iconfont znzt-guhuapeizhi"></span>
                  <!-- 回滚feature -->
                  <span v-else-if="item.operationType == 50" class="anticon iconfont znzt-huigun"></span>
                  <span v-else role="img" aria-label="close" class="anticon anticon-close">
                    <svg viewBox="64 64 896 896" focusable="false" data-icon="container" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                      <path
                        d="M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-40 824H232V687h97.9c11.6 32.8 32 62.3 59.1 84.7 34.5 28.5 78.2 44.3 123 44.3s88.5-15.7 123-44.3c27.1-22.4 47.5-51.9 59.1-84.7H792v-63H643.6l-5.2 24.7C626.4 708.5 573.2 752 512 752s-114.4-43.5-126.5-103.3l-5.2-24.7H232V136h560v752zM320 341h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zm0 160h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z">
                      </path>
                    </svg>
                  </span>
                </div>
                <!--修改历史-->
                <div style="display: inline-block; margin-left: 40px; margin-top: 3px; width: 94%">
                  <p class="item-top">
                    <span style="color: rgb(93, 120, 242); margin-right: 3px; font-size: 14px">
                      {{ item.createUser }}
                    </span>

                    {{ UPDATETYPE[item.operationType] }}
                    <span v-if="item.operationType === 75 || item.operationType === 76">
                      : 流量从{{ item.operationDetail.flow.old / 10 }}%调整到{{ item.operationDetail.flow.new / 10 }}%
                    </span>
                  </p>
                  <a v-if="item.operationDetail && Object.keys(item.operationDetail).length && item.operationType !== 75 && item.operationType !== 76" @click="showDetail(item)" class="mt-15 show-detail-btn">
                    展示明细
                    <span role="img" aria-label="double-right" class="anticon anticon-double-right">
                      <svg :style="`transform: rotate(${item.open ? '270deg' : '90deg'});`" viewBox="64 64 896 896" focusable="false" data-icon="double-right" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                        <path
                          d="M533.2 492.3L277.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H188c-6.7 0-10.4 7.7-6.3 12.9L447.1 512 181.7 851.1A7.98 7.98 0 00188 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5zm304 0L581.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H492c-6.7 0-10.4 7.7-6.3 12.9L751.1 512 485.7 851.1A7.98 7.98 0 00492 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5z">
                        </path>
                      </svg>
                    </span>
                  </a>

                  <div v-show="item.open">
                    <div class="operationDetail">
                      <!-- 更新前块开始 -->
                      <div class="operationDetail-item">
                        <div class="operationDetail-item-old">更新前</div>
                        <div class="basic-info">
                          <!-- 基本信息块开始 -->
                          <div>
                            <!-- displayName -->
                            <div v-if="(item.operationDetail.displayName &&
        item.operationDetail.displayName.new) ||
      (item.operationDetail.displayName &&
        item.operationDetail.displayName.old)
      ">
                              <p class="updateTitle-white updateTitle-k">实验名称:</p>
                              <p class="updateTitle-v">
                                {{ item.operationDetail.displayName.old }}
                              </p>
                              <div style="clear: both"/>
                            </div>

                            <!--  description  -->
                            <div v-if="(item.operationDetail.description &&
        item.operationDetail.description.new) ||
      (item.operationDetail.description &&
        item.operationDetail.description.old)
      ">
                              <p class="updateTitle-white updateTitle-k">实验描述:</p>
                              <p class="updateTitle-v">{{ item.operationDetail.description.old }}</p>
                              <div style="clear: both"/>
                            </div>

                            <!-- duration -->
                            <div v-if="(item.operationDetail.duration &&
        item.operationDetail.duration.new) ||
      (item.operationDetail.duration && item.operationDetail.duration.old)
      ">
                              <p class="updateTitle-white updateTitle-k">实验时长:</p>
                              <p class="updateTitle-v">{{ item.operationDetail.duration.old }}天</p>
                              <div style="clear: both"/>
                            </div>

                            <!-- flow -->
                            <div v-if="(item.operationDetail.flow && item.operationDetail.flow.old) ||
      (item.operationDetail.flow && item.operationDetail.flow.new)
      ">
                              <p class="updateTitle-white updateTitle-k">实验流量:</p>
                              <p class="updateTitle-v">{{ item.operationDetail.flow.old / 10 }}%</p>
                              <div style="clear: both"/>
                            </div>

                            <div v-if="(item.operationDetail.label && item.operationDetail.label.old) ||
      (item.operationDetail.label && item.operationDetail.label.new)
      ">
                              <p class="updateTitle-white updateTitle-k">实验标签:</p>
                              <p class="updateTitle-v">{{ item.operationDetail.label.old }}</p>
                              <div style="clear: both"/>
                            </div>
                          </div>
                          <!-- 基本信息块结束-->

                          <!-- 版本变更块开始 -->
                          <div>
                            <!-- versionDisplayName -->
                            <div v-if="(item.operationDetail.versionDisplayName &&
        item.operationDetail.versionDisplayName.new) ||
      (item.operationDetail.versionDisplayName &&
        item.operationDetail.versionDisplayName.old)
      ">
                              <p class="updateTitle-white">版本名称:</p>
                              <div v-for="(val1, key1, i1) in item.operationDetail.versionDisplayName
      .old" :key="i1">
                                <span class="updateTitle-version updateTitle-k">{{ key1 }}:</span>
                                <span class="updateTitle-v">{{ val1 }}</span>
                                <div style="clear: both"/>
                              </div>
                            </div>

                            <!-- versionDescription -->
                            <div v-if="(item.operationDetail.versionDescription &&
        item.operationDetail.versionDescription.new) ||
      (item.operationDetail.versionDescription &&
        item.operationDetail.versionDescription.old)
      ">
                              <p class="updateTitle-white">版本描述:</p>
                              <div v-for="(val1, key1, i1) in item.operationDetail.versionDescription.old" :key="i1">
                                <span class="updateTitle-version updateTitle-k">{{ key1 }}:</span>
                                <span class="updateTitle-v">{{ val1 }}</span>
                                <div style="clear: both"/>
                              </div>
                            </div>

                            <!-- versionFlow -->
                            <div v-if="(item.operationDetail.versionFlow &&
        item.operationDetail.versionFlow.new) ||
      (item.operationDetail.versionFlow &&
        item.operationDetail.versionFlow.old)
      ">
                              <p class="updateTitle-white">版本流量:</p>
                              <div v-for="(val2, key2, i2) in item.operationDetail.versionFlow.old" :key="i2">
                                <span class="updateTitle-version updateTitle-k">{{ key2 }}:</span>
                                <span class="updateTitle-v">{{ val2 / 10 }}%</span>
                                <div style="clear: both"/>
                              </div>
                            </div>

                            <!-- isWhitelistRule -->
                            <div v-if="(item.operationDetail.isWhitelistRule &&
        item.operationDetail.isWhitelistRule.new) ||
      (item.operationDetail.isWhitelistRule &&
        item.operationDetail.isWhitelistRule.old)
      ">
                              <p class="updateTitle-white updateTitle-k">白名单是否满足受众规则</p>
                              <!-- <p
                                v-for="(val2, key2, i2) in item.operationDetail.isWhitelistRule.old"
                                :key="i2"
                              > -->
                              <!-- <span>{{ key2 }}:</span> -->
                              <span class="updateTitle-v">
                                {{ item.operationDetail.isWhitelistRule.old ? '需要' : '不需要' }}
                              </span>
                              <div style="clear: both"/>
                              <!-- </p> -->
                            </div>

                            <!-- versionFeatureList -->
                            <div v-if="(item.operationDetail.versionFeatureList &&
        item.operationDetail.versionFeatureList.new) ||
      (item.operationDetail.versionFeatureList &&
        item.operationDetail.versionFeatureList.old)
      ">
                              <p class="updateTitle-white">版本参数:</p>
                              <div v-for="(val3, key3, i3) in item.operationDetail.versionFeatureList
      .old" :key="i3">
                                <div class="updateTitle-version">{{ key3 }}:</div>
                                <div v-for="(val4, key4, i4) in val3" :key="i4">
                                  <span class="updateTitle-k detail-level3">{{ key4 }}:</span>
                                  <el-tooltip class="item" effect="dark" :content="val4" placement="top">
                                    <p class="operate-detail-item updateTitle-v">{{ val4 }}</p>
                                  </el-tooltip>
                                  <div style="clear: both"/>
                                </div>
                              </div>
                            </div>

                            <!-- 版本白名单 -->
                            <div v-if="(item.operationDetail.versionWhiteList &&
        item.operationDetail.versionWhiteList.new) ||
      (item.operationDetail.versionWhiteList &&
        item.operationDetail.versionWhiteList.old)
      ">
                              <p class="updateTitle-white">版本白名单:</p>
                              <div v-for="(val4, key4, i4) in item.operationDetail.versionWhiteList
      .old" :key="i4">
                                <div class="updateTitle-version">{{ key4 }}:</div>
                                <div v-for="(val5, index1) in val4" :key="index1">
                                  <el-tooltip class="item" effect="dark" :content="val5" placement="top">
                                    <p class="operate-detail-item updateTitle-v">
                                      {{ val5 }}
                                    </p>
                                  </el-tooltip>
                                  <div style="clear: both"/>
                                </div>
                              </div>
                            </div>

                            <!-- filterRule -->
                            <div v-if="(item.operationDetail.filterRule &&
        item.operationDetail.filterRule.new) ||
      (item.operationDetail.filterRule &&
        item.operationDetail.filterRule.old)
      ">
                              <p class="updateTitle-white">过滤规则:</p>
                              <div v-for="(val6, index2) in item.operationDetail.filterRule.old" :key="index2">
                                <span v-for="(val13, key13, i13) in val6" :key="i13" style="display: inline-block; margin: 0 0 8px 0">
                                  <span v-if="key13 == 'keyName'" style="margin-right: 3px">
                                    {{ val13 }}
                                  </span>
                                  <span v-if="key13 == 'keyType'" style="margin-right: 3px">
                                    {{ somelist[val13] }}：
                                  </span>
                                  <span v-if="key13 == 'keyValue'" style="margin-right: 3px">
                                    <template v-if="val13.indexOf('[') > -1">
                                      <span v-for="(v15, i14) in JSON.parse(val13)" :key="i14" style="margin-right: 3px">
                                        {{ v15 }},
                                      </span>
                                    </template>
                                    <template v-else>
                                      <span style="margin-right: 3px">{{ val13 }},</span>
                                    </template>
                                  </span>
                                </span>
                              </div>
                            </div>
                          </div>
                          <!-- 版本变更块结束 -->
                        </div>
                      </div>
                      <!-- 更新前块结束 -->
                      <!-- 更新后块开始 -->
                      <div class="operationDetail-item">
                        <div class="operationDetail-item-new">更新后</div>
                        <div class="basic-info">
                          <!-- 基本信息 -->
                          <div>
                            <!-- displayName -->
                            <div v-if="(item.operationDetail.displayName &&
        item.operationDetail.displayName.new) ||
      (item.operationDetail.displayName &&
        item.operationDetail.displayName.old)
      ">
                              <p class="updateTitle-white updateTitle-k">实验名称:</p>
                              <p class="updateTitle-v">
                                {{ item.operationDetail.displayName.new }}
                              </p>
                              <div style="clear: both"/>
                            </div>
                            <!--  description  -->
                            <div v-if="(item.operationDetail.description &&
        item.operationDetail.description.new) ||
      (item.operationDetail.description &&
        item.operationDetail.description.old)
      ">
                              <p class="updateTitle-white updateTitle-k">实验描述:</p>
                              <p class="updateTitle-v">{{ item.operationDetail.description.new }}</p>
                              <div style="clear: both"/>
                            </div>
                            <!-- duration -->
                            <div v-if="(item.operationDetail.duration &&
        item.operationDetail.duration.new) ||
      (item.operationDetail.duration && item.operationDetail.duration.old)
      ">
                              <p class="updateTitle-white updateTitle-k">实验时长:</p>
                              <p class="updateTitle-v">{{ item.operationDetail.duration.new }}天</p>
                              <div style="clear: both"/>
                            </div>
                            <!-- flow -->
                            <div v-if="(item.operationDetail.flow && item.operationDetail.flow.old) ||
      (item.operationDetail.flow && item.operationDetail.flow.new)
      ">
                              <p class="updateTitle-white updateTitle-k">实验流量:</p>
                              <p class="updateTitle-v">{{ item.operationDetail.flow.new / 10 }}%</p>
                              <div style="clear: both"/>
                            </div>
                            <div v-if="(item.operationDetail.label && item.operationDetail.label.old) ||
      (item.operationDetail.label && item.operationDetail.label.new)
      ">
                              <p class="updateTitle-white updateTitle-k">实验标签:</p>
                              <p class="updateTitle-v">{{ item.operationDetail.label.new }}</p>
                              <div style="clear: both"/>
                            </div>
                          </div>
                          <!-- 版本变更 -->
                          <div>
                            <!-- versionDisplayName -->
                            <div v-if="(item.operationDetail.versionDisplayName &&
        item.operationDetail.versionDisplayName.new) ||
      (item.operationDetail.versionDisplayName &&
        item.operationDetail.versionDisplayName.old)
      ">
                              <p class="updateTitle-white">版本名称:</p>
                              <div v-for="(val1, key1, i1) in item.operationDetail.versionDisplayName
      .new" :key="i1">
                                <div class="updateTitle-version updateTitle-k">{{ key1 }}:</div>
                                <span class="updateTitle-v">{{ val1 }}</span>
                                <div style="clear: both"/>
                              </div>
                            </div>
                            <!-- versionDescription -->
                            <div v-if="(item.operationDetail.versionDescription &&
        item.operationDetail.versionDescription.new) ||
      (item.operationDetail.versionDescription &&
        item.operationDetail.versionDescription.old)
      ">
                              <p class="updateTitle-white">版本描述:</p>
                              <div v-for="(val1, key1, i1) in item.operationDetail.versionDescription.new" :key="i1">
                                <span class="updateTitle-version updateTitle-k">{{ key1 }}:</span>
                                <span class="updateTitle-v">{{ val1 }}</span>
                                <div style="clear: both"/>
                              </div>
                            </div>
                            <!-- versionFlow -->
                            <div v-if="(item.operationDetail.versionFlow &&
        item.operationDetail.versionFlow.new) ||
      (item.operationDetail.versionFlow &&
        item.operationDetail.versionFlow.old)
      ">
                              <p class="updateTitle-white">版本流量:</p>
                              <div v-for="(val2, key2, i2) in item.operationDetail.versionFlow.new" :key="i2">
                                <div class="updateTitle-version updateTitle-k">{{ key2 }}:</div>
                                <span class="updateTitle-v">{{ val2 / 10 }} %</span>
                                <div style="clear: both"/>
                              </div>
                            </div>
                            <!-- isWhitelistRule -->
                            <div v-if="(item.operationDetail.isWhitelistRule &&
        item.operationDetail.isWhitelistRule.new) ||
      (item.operationDetail.isWhitelistRule &&
        item.operationDetail.isWhitelistRule.old)
      ">
                              <p class="updateTitle-white updateTitle-k">白名单是否满足受众规则</p>
                              <!-- <p
                                v-for="(val2, key2, i2) in item.operationDetail.isWhitelistRule.new"
                                :key="i2"
                              > -->
                              <!-- <span>{{ key2 }}:</span> -->
                              <span class="updateTitle-v">
                                {{ item.operationDetail.isWhitelistRule.new ? '需要' : '不需要' }}
                              </span>
                              <div style="clear: both"/>
                              <!-- </p> -->
                            </div>
                            <!-- versionFeatureList -->
                            <div v-if="(item.operationDetail.versionFeatureList &&
        item.operationDetail.versionFeatureList.new) ||
      (item.operationDetail.versionFeatureList &&
        item.operationDetail.versionFeatureList.old)
      ">
                              <p class="updateTitle-white">版本参数:</p>
                              <div v-for="(val3, key3, i3) in item.operationDetail.versionFeatureList
      .new" :key="i3">
                                <div class="updateTitle-version">{{ key3 }}:</div>
                                <div v-for="(val4, key4, i4) in val3" :key="i4">
                                  <div class="updateTitle-k detail-level3">{{ key4 }}:</div>
                                  <el-tooltip class="item" effect="dark" :content="val4" placement="top">
                                    <p class="operate-detail-item updateTitle-v">{{ val4 }}</p>
                                  </el-tooltip>
                                  <div style="clear: both"/>
                                </div>
                              </div>
                            </div>

                            <!-- 版本白名单 -->
                            <div v-if="(item.operationDetail.versionWhiteList &&
        item.operationDetail.versionWhiteList.new) ||
      (item.operationDetail.versionWhiteList &&
        item.operationDetail.versionWhiteList.old)
      ">
                              <p class="updateTitle-white">版本白名单:</p>
                              <div v-for="(val4, key4, i4) in item.operationDetail.versionWhiteList
      .new" :key="i4">
                                <div class="updateTitle-version">{{ key4 }}:</div>
                                <div v-for="(val5, index1) in val4" :key="index1">
                                  <el-tooltip class="item" effect="dark" :content="val5" placement="top">
                                    <p class="operate-detail-item updateTitle-v">
                                      {{ val5 }}
                                    </p>
                                  </el-tooltip>
                                  <div style="clear: both"/>
                                </div>
                              </div>
                            </div>

                            <!-- filterRule -->
                            <div v-if="(item.operationDetail.filterRule &&
        item.operationDetail.filterRule.new) ||
      (item.operationDetail.filterRule &&
        item.operationDetail.filterRule.old)
      ">
                              <p class="updateTitle-white">过滤规则:</p>
                              <div v-for="(val6, index2) in item.operationDetail.filterRule.new" :key="index2">
                                <span v-for="(val13, key13, i13) in val6" :key="i13" style="display: inline-block; margin: 0 0 8px 0">
                                  <span v-if="key13 == 'keyName'" style="margin-right: 3px">
                                    {{ val13 }}
                                  </span>
                                  <span v-if="key13 == 'keyType'" style="margin-right: 3px">
                                    {{ somelist[val13] }}：
                                  </span>
                                  <span v-if="key13 == 'keyValue'" style="margin-right: 3px">
                                    <template v-if="val13.indexOf('[') > -1">
                                      <span v-for="(v14, i14) in JSON.parse(val13)" :key="i14" style="margin-right: 3px">
                                        {{ `${v14}` }},
                                      </span>
                                    </template>
                                    <template v-else>{{ val13 }},</template>
                                  </span>
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- 更新后块结束 -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <el-tooltip class="item" effect="dark" content="操作历史" placement="top">
      <i class="icon2-lishi2 iconfont2 history-btn " @click="openDrawer"></i>
    </el-tooltip>
  </div>
</template>

<script>
import { formatDate, localStorage } from '@/common/utils';
import { mapGetters } from 'vuex';
export default {
  name: 'ExpHistory',
  components: {},
  props: {
    expName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      title: '',
      status: '',
      isEdit: this.$route.query.type === 'edit',
      isCheck: this.$route.query.type === 'check',
      somelist: {
        1: '等于',
        2: '不等于',
        3: '小于',
        4: '小于等于',
        5: '大于',
        6: '大于等于',
        7: '小于(版本)',
        8: '小于等于(版本)',
        9: '大于(版本)',
        10: '大于等于(版本)'
      },
      UPDATETYPE: {
        1: '创建实验草稿',
        5: '创建实验，进入调试状态',
        7: '草稿=>调试',
        10: '修改实验',
        15: '开始实验',
        20: '暂停实验',
        25: '继续实验',
        30: '冻结实验',
        35: '解冻实验',
        36: '发布实验',
        37: '回滚发布计划',
        38: '重新发布实验',
        39: '发布周期变更',
        40: '结束实验',
        45: '固化',
        50: '回滚固化',
        75: '放量',
        76: '缩量',
      },
      showDrawer: false,
      width: 480,
      opacity: 0.1,
      mask: true,
      position: 'right',

      flag: true,
      reportData: {},
      breadcrumbList: ['ExperimentList', 'ExpReport']
    };
  },
  computed: {
    ...mapGetters(['expHistoryData', 'expReportData', 'addData', 'statusMap'], 'message')
  },
  filters: {
    handleFormatDate(val) {
      if (val) {
        return formatDate(val * 1000, 'yyyy-MM-dd HH:mm:ss');
      }
      return '-';
    },
    statusFilter(val) {
      //console.log(this.statusMap,val);
      // if (this.statusMap) {
      //   return this.statusMap[val];
      // }
    }
  },
  created() { },
  mounted() {
    const expName = localStorage.getItem('displayName');
    this.title = `「${expName}」实验操作历史`;
    this.status = localStorage.getItem('status');
    // debugger;
  },
  methods: {
    handleClose(done) {
      this.showDrawer = false;
    },
    openDrawer() {
      this.showDrawer = true;
    },
    showDetail(item) {
      item.open = !item.open;
    }
  },
  beforeDestroy() { }
};
</script>

<style lang="less" scoped>
.history-btn {
  cursor: pointer;
}

.history-page {
  overflow-y: auto;
  box-sizing: border-box;
  padding: 20px;
}

.history-warp .history-content:first-child .head-date {
  line-height: 30px;
  background-color: rgb(243, 244, 252);
  width: 100%;
  font-size: 14px;
  color: rgb(147, 154, 163);
  margin-left: 0;
}

.head-date {
  line-height: 30px;
  background-color: rgb(243, 244, 252);
  width: 94.5%;
  font-size: 14px;
  color: rgb(147, 154, 163);
  margin-left: 28px;
}

.item-top {
  margin-bottom: 20px;
}

.mt-15 {
  display: inline-block;
  margin-bottom: 15px;
}

/deep/ .history-border {
  width: 2px;
  height: calc(100% - 92px);
  position: absolute;
  top: 42px;
  left: 24px;
  z-index: 0;
  background: repeating-linear-gradient(0,
      rgba(93, 120, 242, 0.6),
      rgba(93, 120, 242, 0.6) 0.3em,
      #fff 0,
      #fff 0.9em);
}

/deep/ .history {
  width: 100%;
}

/deep/ .history .history-content .history-item {
  margin-bottom: 12px;
  margin-top: 8px;
  font-size: 13px;
  color: #939aa3;
  position: relative;
}

/deep/ .history .history-content .history-item .tob_orderLine_item_circle {
  width: 30px;
  height: 30px;
  border-radius: 50px;
  display: inline-block;
  position: absolute;
  color: #fff;
  top: 0;
  z-index: 2;
}

/deep/ .history .history-content .history-item .tob_orderLine_item_circle .anticon {
  position: relative;
  top: calc(50% - 9px);
  left: calc(50% - 9px);
}

/deep/ .history .history-content .history-item .tob_orderLine_item_circle .anticon svg {
  width: 18px;
  height: 18px;
}

.operationDetail {
  // border-radius: 5px;
  // width: 95%;
  // display: flex;
  // justify-content: center;
  // border: 1px solid rgb(245, 239, 239);
  // border-radius: 4px;
  color: #fff;
  display: flex;
  justify-content: space-between;
}

.operationDetail-item {
  width: 48.5%;
  text-align: center;
  border: 1px solid rgb(225, 225, 225);
  border-radius: 5px;
}

/deep/ .operationDetail-item-old {
  font-size: 16px;
  background: rgb(179, 182, 192);
  color: #fff;
  height: 40px;
  line-height: 40px;
}

.operationDetail-item-new {
  font-size: 16px;
  background-color: #42c57a;
  height: 40px;
  line-height: 40px;
  border-left: 1px solid rgb(245, 239, 239);
}

.operationDetail-item-detail {
  padding-left: 12px;
  font-size: 12px;
  text-align: left;
  color: black;
}

.updateTitle-white {
  font-size: 14px;
  text-align: left;
  color: rgb(147, 154, 163);
  margin-bottom: 6px;
}

.updateTitle-version {
  padding-left: 6px;
  font-size: 12px;
  text-align: left;
  color: rgb(120, 130, 140);
  margin-bottom: 6px;
}

.updateTitle-k {
  float: left;
}

.detail-level3 {
  padding-left: 12px;
}

.updateTitle-v {
  float: right;
}

.operate-detail-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80%;         /* 限制宽度 */
}

.basic-info {
  text-align: left;
  padding: 15px;
  font-size: 10px;
  color: #2f2f3f;

  div p {
    padding: 5px 0;
  }
}

.whiteList-item {
  display: flex;
  justify-content: flex-start;
}

.show-detail-btn {
  color: rgb(51, 112, 255);
}

.iconfont {
  font-size: 18px;
}

.show-detail-btn .anticon-double-right {
  vertical-align: middle !important;
}
</style>
