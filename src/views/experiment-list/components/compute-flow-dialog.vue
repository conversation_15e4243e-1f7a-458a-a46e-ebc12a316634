<template>
  <el-dialog :title="title" :visible="dialogVisible" :before-close="dialogClose">
    <p class="exp-detail">
      当前实验您有 {{ addData.versionList.length - 1 }}
      个实验版本(含对照版本)
    </p>
    <div class="utils-detail">
      <div class="detail">
        <label>
          每周新到访的活跃用户数WAU
          <br />
          <div class="label-small">每周打开您的应用的新到访用户数</div>
        </label>
      </div>
      <div class="detail-value">
        <el-input-number
          class="computed-input"
          size="small"
          controls-position="right"
          v-model="personNum"
          :min="0"
          :step="1"
          :disabled="isCheck"
          @change="personNumChange"
        />
        <span>个用户</span>
      </div>
    </div>
    <div class="utils-detail">
      <div class="detail">
        <label>
          核心指标基线
          <br />
          <div class="label-small">
            实验核心指标近期历史均值。若未能自动依据最近数据产出，请输入
          </div>
        </label>
      </div>
      <div class="detail-value">
        <el-input-number
          class="computed-input"
          controls-position="right"
          size="small"
          v-model="flowNum.p1"
          :min="0"
          :max="100"
          :step="1"
          :disabled="isCheck"
        />
        <span></span>
      </div>
    </div>
    <div class="utils-detail">
      <div class="detail">
        <label>
          校验灵敏度MDE
          <br />
          <div class="label-small">需要检验出显著的最小指标变化幅度(相对值)</div>
        </label>
      </div>
      <div class="detail-value">
        <el-input-number
          class="computed-input"
          size="small"
          controls-position="right"
          v-model="flowNum.mde"
          :min="0"
          :max="100"
          :step="1"
          :disabled="isCheck"
        />
        <span>%</span>
      </div>
    </div>
    <div class="utils-detail">
      <div class="detail">
        <label>
          指标方差
          <br />
          <div class="label-small">实验版本内用户粒度层面的近期指标方差。若未自动产出，请计算</div>
        </label>
      </div>
      <div class="detail-value">
        <el-input-number
          class="computed-input"
          size="small"
          controls-position="right"
          v-model="flowNum.n1"
          :min="0"
          :max="100"
          :step="1"
          :disabled="isCheck"
        />
        <span></span>
      </div>
    </div>
    <div class="utils-detail">
      <div class="detail">
        <label>
          统计功效power(1-β)
          <br />
          <div class="label-small">多大概率能检查出版本差异</div>
        </label>
      </div>
      <div class="detail-value">
        <el-select class="computed-input" v-model="flowNum.power" size="small" :disabled="isCheck">
          <el-option
            v-for="(item, index) in powerOption"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <span></span>
      </div>
    </div>
    <div class="utils-detail">
      <div class="detail">
        <label>
          统计显著性(1-α)
          <br />
          <div class="label-small">无差异的版本多大概率判定无显著结论</div>
        </label>
      </div>
      <div class="detail-value">
        <el-select value="95%" class="computed-input" disabled size="small"></el-select>
        <span></span>
      </div>
    </div>
    <div class="utils-detail">
      <div class="detail"></div>
      <div class="detail-value">
        <el-button type="Default" size="small" :disabled="isCheck" @click="resetFlowNum">
          重置
        </el-button>
        <el-button
          plain
          size="small"
          class="com-btn"
          :loading="isComputed"
          @click="flowSuggest"
          :disabled="isCheck"
        >
          开始计算
        </el-button>
      </div>
    </div>
    <el-divider class="divider-con"></el-divider>
    <div class="utils-detail">
      <div class="detail">
        <label>每个版本所需样本量</label>
      </div>
      <div class="lang-content">
        {{ versionSamleNum }}
      </div>
    </div>
    <div class="utils-detail">
      <div class="slider-con">
        <label>
          总流量分配
          <br />
          <div class="label-small">建议的线上总流量</div>
        </label>
      </div>
      <div class="lang-content">
        <el-slider
          :disabled="isCheck"
          v-model="progress"
          :marks="marks"
          :min="0"
          :max="100"
          :step="0.1"
          @change="progressChange"
        />
        <span></span>
      </div>
    </div>
    <div class="utils-detail">
      <div class="detail">
        <label>实验至少开启多久可见显著性效果</label>
      </div>
      <div class="lang-content">
        <el-input-number
          class="computed-input"
          size="small"
          controls-position="right"
          v-model="flowDays"
          :min="1"
          :max="365"
          :step="1"
          :disabled="isCheck"
          @change="flowDaysChange"
        />
        <span>天</span>
      </div>
    </div>
    <!-- 确定取消按钮组 -->
    <section slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogClose">取 消</el-button>
      <el-button size="small" type="primary" @click="handleSetFlowNum" :disabled="isCheck">
        设置为实验流量
      </el-button>
    </section>
  </el-dialog>
</template>
<script>
import * as _ from 'lodash';
import {mapGetters} from 'vuex';
export default {
  name: 'compute-flow-dialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    ...mapGetters(['addData', 'appKey', 'mutuList', 'whiteList'], 'message'),
  },
  data() {
    return {
      isCheck: this.$route.query.type === 'check' ? true : false,
      title: '实验流量建议工具',
      personNum: 1,
      versionSamleNum: 0,
      isComputed: false,
      flowDays: 14,
      flowNum: {
        p1: 0.5958321104, // 核心指标基线
        mde: 5,
        n1: 0.8144209832,
        power: 0.8,
      },
      marks: {
        0: '0%',
        100: '100%',
      },
      progress: 80,
      powerOption: [
        {
          value: 0.5,
          label: '50%',
        },
        {
          value: 0.8,
          label: '80%',
        },
        {
          value: 0.9,
          label: '90%',
        },
        {
          value: 0.99,
          label: '99%',
        },
        {
          value: 0.9999,
          label: '99.99%',
        },
      ],
    };
  },
  mounted() {},
  methods: {
    // 设置为实验流量
    handleSetFlowNum() {
      this.addData.flow = this.progress;
      this.dialogClose();
    },
    // 实验至少开启多少天
    flowDaysChange() {
      this.computeFlow();
    },
    // 拖动流量滑块的时候
    progressChange() {
      this.computedDays();
    },
    // 点击开始计算，计算流量值
    flowSuggest() {
      const params = {
        p1: this.flowNum.p1, // 核心指标基线
        mde: Number((this.flowNum.mde / 100).toFixed(2)),
        n1: this.flowNum.n1,
        power: this.flowNum.power,
      };
      for (let key in this.flowNum) {
        if (this.flowNum[key] == 0) {
          this.$message.warning(
            '您输入的WAU，指标基线，MDE等参数有误，无法计算样本量，流量和实验周期，请重新输入'
          );
          return;
        }
      }

      this.isComputed = true;
      this.$service
        .get('FLOW_SUGGEST', {...params})
        .then((res) => {
          this.versionSamleNum = res ? res.n : 0; // 样本量
          // this.progress =
          //   ((this.versionSamleNum * versionLen) / this.personNum / this.flowDays) * 7 * 100;
          this.computeFlow();
          this.isComputed = false;
        })
        .catch((err) => {
          this.isComputed = false;
          console.log(err);
        });
    },
    // 计算流量
    computeFlow() {
      const versionLen = this.addData.versionList.length - 1; // 版本数
      const molecular = this.versionSamleNum * versionLen; //分子(每个版本的样本量*版本数)
      const denominator = this.personNum * (this.flowDays / 7); //分母（每周的WAU * （实验至少开启的天数/7 ））
      //我们用slider是不是v-model的时候用的是80啊60啊这样的数据,再在html部分加上一个%,但是我们计算数据的时候用的就是80%这个数也就是0.8,所以需要乘以100
      const result = ((molecular / denominator) * 100).toFixed(1);
      this.progress = parseFloat(result) > 100 ? 100 : parseFloat(result);
    },
    // 计算天数
    computedDays() {
      const versionLen = this.addData.versionList.length - 1; // 版本数
      const molecular = this.versionSamleNum * versionLen; //分子(每个版本的样本量*版本数)
      const denominator = this.personNum * (this.progress / 100); //分母（每周的WAU * （实验至少开启的天数/7 ））
      //我们用slider是不是v-model的时候用的是80啊60啊这样的数据,再在html部分加上一个%,但是我们计算数据的时候用的就是80%这个数也就是0.8,所以需要乘以100
      const result = (molecular / denominator) * 7;
      this.flowDays = parseInt(result) > 365 ? 365 : parseInt(result);
    },
    // 重置查询值
    resetFlowNum() {
      this.progress = 80;
      this.versionSamleNum = 0;
      this.flowNum = {
        p1: 0.5958321104, // 核心指标基线
        mde: 5,
        n1: 0.8144209832,
        power: 0.8,
      };
    },
    // 改变用户值的时候
    personNumChange() {
      this.computeFlow();
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .zyb-dialog--default {
  max-height: 90% !important;
  width: 760px;
  min-width: 30%;
}
.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}
.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  .detail {
    display: block;
    //color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }
  .detail-value {
    width: 200px;
    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }
  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;
    span {
      padding: 0 11px;
    }
  }
  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }
  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}
.slider-con {
  width: 43%;
  margin-bottom: 41px;
}
/deep/ .computed-input {
  width: 130px;
}
/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}
/deep/ .divider-con {
  margin: 20px 0;
}
/deep/ .utils-detail .el-select {
  max-width: 200px;
  min-width: 150px;
}
/deep/ .utils-detail .el-slider__marks .el-slider__marks-text {
  width: 40px;
}
/deep/ .utils-detail .el-slider__bar {
  position: static;
}
</style>
