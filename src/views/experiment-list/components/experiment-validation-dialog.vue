<template>
  <el-dialog
    :visible.sync="visible"
    :title="title"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="validation-content">
      <!-- 实验缩量警告 -->
      <div v-if="validations.trafficReduction" class="validation-item">
        <div class="validation-header">
          <i class="el-icon-warning" style="color: #e6a23c;"></i>
          <span class="validation-title">实验缩量提示</span>
        </div>
        <div class="validation-body">
          <p>
            当前操作存在缩量操作，实验流量将从{{ validations.trafficReduction.preFlow }}%缩量到{{
              validations.trafficReduction.flow
            }}%。
          </p>
          <p>实验缩量将影响实验报告科学性，导致实验报告数据不可用，请谨慎操作。</p>
          <p v-if="validations.trafficReduction.needApproval">
            提交后，将发起缩量申请，需由业务线负责人审批通过后，即可实现缩量。确定发起缩量申请吗？
          </p>
        </div>
        <div v-if="validations.trafficReduction.needApproval" class="validation-input">
          <el-input
            type="textarea"
            v-model="trafficReductionReason"
            placeholder="请输入实验缩量原因"
            :rows="3"
          ></el-input>
        </div>
      </div>

      <!-- 分组流量变更警告 -->
      <div v-if="validations.groupFlowChange" class="validation-item">
        <div class="validation-header">
          <i class="el-icon-warning" style="color: #e6a23c;"></i>
          <span class="validation-title">分组流量变更提示</span>
        </div>
        <div class="validation-body">
          <p>{{ validations.groupFlowChange.message }}</p>
          <p v-if="validations.groupFlowChange.needApproval">
            提交修改后，将发起调整分组流量占比申请，需由上级进行审批，审批通过后，即可实现调整分组流量占比。
          </p>
        </div>
      </div>

      <!-- 实验时长缩短警告 -->
      <div v-if="validations.durationShorten" class="validation-item">
        <div class="validation-header">
          <i class="el-icon-warning" style="color: #e6a23c;"></i>
          <span class="validation-title">实验时长缩短提示</span>
        </div>
        <div class="validation-body">
          <p>{{ validations.durationShorten.message }}</p>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :disabled="!isFormValid">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ExperimentValidationDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    validations: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      trafficReductionReason: ''
    };
  },
  computed: {
    title() {
      const validationCount = Object.keys(this.validations).length;
      return validationCount > 1 ? '实验修改确认' : '实验修改提示';
    },
    isFormValid() {
      // 如果需要输入缩量原因，检查是否已填写
      if (this.validations.trafficReduction?.needApproval) {
        return this.trafficReductionReason.trim().length > 0;
      }
      return true;
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.trafficReductionReason = '';
      }
    }
  },
  methods: {
    handleConfirm() {
      if (!this.isFormValid) {
        this.$message.warning('请输入实验缩量原因');
        return;
      }

      const result = {
        confirmed: true,
        reason: this.trafficReductionReason.trim()
      };

      this.$emit('confirm', result);
      this.handleClose();
    },
    handleCancel() {
      this.$emit('cancel');
      this.handleClose();
    },
    handleClose() {
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="less" scoped>
.validation-content {
  .validation-item {
    margin-bottom: 20px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fdf6ec;

    &:last-child {
      margin-bottom: 0;
    }

    .validation-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .validation-title {
        margin-left: 8px;
        font-weight: 600;
        color: #303133;
      }
    }

    .validation-body {
      color: #606266;
      line-height: 1.6;

      p {
        margin: 0 0 8px 0;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .validation-input {
      margin-top: 12px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
