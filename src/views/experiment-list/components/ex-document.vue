<template>
    <section class="exp-document">
        <section v-for="item in list" class="document-item" :data-key="item.key">
            <section class="document-content" :class="{ 'active': isActive(item.key) }">
                <p class="document-title">{{ item.title }}</p>
                <p class="document-text" v-for="(text, index) in item.content"
                    v-html="formatTextWithLinks(text, index)">
                </p>
            </section>
        </section>
    </section>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
    data() {
        return {
            activeKey: '',
        };
    },
    computed: {
        ...mapGetters(['addData',], 'message'),
        isChild() {
            return !!this.$route.query.parentId || !!this.addData.parentId;
        },
        isExtend() {
            return this.$route.query.isExtend === '1';
        },
        list() {
            const list = [
                {
                    key: 'name',
                    title: '实验名称',
                    content: [
                        '实验名称建议取与实验内容相关的名称，如有实验版本迭代可以增加版本号后缀，让你的伙伴能够快速了解到实验是做什么的、是在哪个迭代版本的。',
                        '规范：最长不超过50个字符。'
                    ]
                },
                {
                    key: 'description',
                    title: '实验描述',
                    content: [
                        '实验内容简述，可以让项目相关人员更清晰地知道到这个实验是如何做的，解决什么问题，同时也便于后期查看和管理历史实验时一目了然。',
                        '规范：最长不超过200个字符。'
                    ]
                },
                {
                    key: 'appKey',
                    title: '业务线',
                    content: ['当前实验所属业务线；传参时appid需要传对应业务线的appid。']
                },
                {
                    key: 'type',
                    title: '实验类型',
                    content: [
                        '根据实验规划选择创建的实验为客户端实验还是服务端实验。',
                        '客户端，指通过客户端获取实验分组信息并控制配置生效的实验，如客户端交互功能、UI样式等都建议创建客户端实验。',
                        '服务端实验，指通过服务端获取实验分组信息并控制配置生效或下发的实验，如内容分发算法&策略、由服务端逻辑控制产品功能的实验都是服务端实验。'
                    ]
                },
                {
                    key: 'accountSystem',
                    title: '账号体系',
                    content: ['实验中流量对应的账号体系，支持选择cuid和uid。']
                },
                {
                    key: 'duration',
                    title: '实验时长',
                    content: [
                        '指实验开启的时长，一般为了避免不同时间段（工作日与周末）的用户行为差异，建议至少观察 2 个完整的实验周期。例如，考虑工作日与周末影响时，实验周期至少需要一周，那实验开启时长建议为14天。',
                        this.isChild ? '子实验：子实验生命周期须囊括在父实验生命周期之内。' : '规范：仅支持填写正整数，最大不超过90天。',
                    ]
                },
                {
                    key: 'label',
                    title: '实验标签',
                    content: [
                        '可以自定义标签或选择已有标签，主要用于分类和搜索查询，方便实验的管理。',
                        '支持添加多个标签。'
                    ]
                },
                {
                    key: 'dingdingGroup',
                    title: '告警钉钉群',
                    content: [
                        '可以创建或选择已有钉钉群，主要用于实验平台信息的接收，方便实验的管理。',
                        '支持添加多个钉钉群。'
                    ]
                },
                {
                    key: 'owner',
                    title: '负责人',
                    content: [
                        '默认为实验创建者。',
                        '支持添加其他人为负责人，方便协同操作。'
                    ]
                },
                {
                    key: 'exclusiveGroup',
                    title: '是否互斥实验',
                    content: this.isChild ? ['子实验不能和父实验选择同一个互斥组。'] : [
                        '当您需要开展多个AB实验时，您需要根据实验情况配置当前创建的实验是否需要与其他实验互斥，来避免同一个用户被分配进入了两个会相互影响的实验，从而影响AB实验结果。例如，您要同时做按钮颜色和按钮形状的实验，这两个实验彼此影响，就需要将这两个实验加入到一个互斥组中。',
                        '了解更多流量层相关内容及如何创建流量层可查看：互斥组管理（https://docs.zuoyebang.cc/doc?fileId=1820743216977223681）',
                    ]
                },
                {
                    key: 'experimentFlow',
                    title: '实验流量',
                    content:
                        this.isExtend ? ['实验继承时，实验流量不支持调小，仅支持在原实验基础上调大。'] : [
                            '系统默认选择0%的流量，可拖动流量进度条或者输入数值，来控制命中实验的用户比例。',
                            '您可以通过建议工具（点击流量计算器）来看设置多少流量合适。详见：流量建议工具（https://docs.zuoyebang.cc/doc?fileId=1821031294610132993）',
                            '实验继承时，实验流量不支持调小，仅支持在原实验基础上调大。'
                        ]
                },
                {
                    key: 'filterRule',
                    title: '选取受众',
                    content: [
                        '您可以创建多个筛选组，筛选组间为"或"的逻辑关系；每个筛选组内可设置多个过滤条件，过滤条件间为"且"的逻辑关系。',
                        '支持创建过滤规则，详见：过滤规则（https://docs.zuoyebang.cc/doc?fileId=1821439136515518466）',
                        '用户分群来源于用户创建的用户分群。详见：用户分群（https://docs.zuoyebang.cc/doc?fileId=1821436633540587521）',
                        '选用过滤参数前，请和所属业务线研发沟通确认该参数是否可用，同时确认好对应的参数值格式，避免配置错误。',
                        '错误案例：（1）选错过滤参数，导致受众没有生效。查看详情 >（https://docs.zuoyebang.cc/doc?fileId=1938077246425604098）'
                    ]
                },
                {
                    key: 'userAttributeChange',
                    title: '用户属性变更',
                    content: [
                        '进组不出组：用户在命中试验后，即使属性或分群发生变化，也将始终保持之前的命中结果(除非暂停，关闭实验)。比如：受众选择只有北京的用户可以看到实验，选择进组不出组后，一开始用户在北京命中实验，后续用户离开北京，依然可以看到实验。',
                        '进组出组：根据用户属性和分群变化，实时判断用户能否命中实验。还是上面那个例子，如果用户离开北京之后，就无法看到实验。'
                    ]
                },
                {
                    key: 'hitCondition',
                    title: '命中实验前提条件',
                    content: [
                        '选择测试用户命中对应的实验版本时，是否需满足上一个步骤中设置的受众规则。由于测试用户主要用于实验正式开启前的调试验证，测试用户的选择可以与最终实验命中用户不一致，您可以根据实际需要进行配置。'
                    ]
                },
                {
                    key: 'parameter',
                    title: '实验参数',
                    content: [
                        '是对实验版本的补充，一般是一个功能控制配置项，用来区分对照组和实验组。您可在对应实验版本界面配置实验参数名称、格式、和参数取值。',
                        '参数数据格式支持选择string、boolean、float、json：',
                        '（1）string：允许输入字符串，字母、数字、下划线。',
                        '（2）float：只允许输入数字。',
                        '（3）boolean：默认包含两个变体值True和False。',
                        '（4）json：json类型支持多层嵌套，请按json规范填写。',
                        '同一个参数只允许存在一个运行中的实验。'
                    ]
                },
                {
                    key: 'version',
                    title: '实验版本',
                    content: [
                        'AB实验通常为一组对照实验：实验版本、对照版本。通过对实验版本和对照版本分别设置不同的逻辑策略，保持其他条件稳定的情况下验证哪种策略更优。对照版本一般是当前的策略，实验版本一般是我们想要尝试的新策略。',
                        '您可以在版本描述中写明当前的版本策略是什么，可以单击左上角的版本1/版本2修改版本名称，便于后续快速了解当前是哪个实验版本。',
                        '可以点击图片位置，上传实验图片或页面截图，便于快速辨识实验版本的差异。',
                        '如果您有多个新策略需要进行实验，可单击下方的添加实验版本，继续添加新的实验组版本。'
                    ]
                },
                {
                    key: 'whiteList',
                    title: '实验白名单',
                    content:
                        this.isChild ?
                            ['父实验中添加的白名单不一定能命中子实验。详见：父子实验白名单错误案例。（https://docs.zuoyebang.cc/doc?fileId=1938072472144519170）']
                            : [
                                '为对应的实验版本添加测试用户，后续实验创建完成后，添加的测试用户将会进入对应的实验版本，可测试实验逻辑是否生效、功能是否有bug等。',
                                '如果一个用户是某个版本的白名单用户，那么实验分流对该用户是没有影响的，会固定在该版本。白名单的用户，实验开启后的数据也会被计算到实验报告里。',
                                '更多关于白名单的说明，详见：白名单（https://docs.zuoyebang.cc/doc?fileId=1821069595906117634）',
                            ]
                },
                {
                    key: 'versionFlowRatio',
                    title: '各版本流量占比',
                    content: [
                        '后续实验开启后，所有参与AB实验的实验流量中，不同实验版本之间分配的实验流量比例。',
                        '（1）流量均匀分配-开启：默认为开启状态，开启状态下为强制各实验版本流量均匀分配。',
                        '（2）流量均匀分配-关闭：关闭后可调配对照版本和实验版本的流量相对占比。可通过输入数值，来控制每个实验版本命中的用户比例。例如，当关闭均匀分配后，三个实验版本，可配置对照版本占比20%，实验版本1占比为30%，实验版本2占比为30%，整体总和为100%，实验版本3即自动分20%流量。',
                        '注：调整分组流量后（1）原实验是进组不出组：各实验版本流量权重变更后，历史进组用户不会受到影响，只会影响进组的用户。（2）原实验是进组出组：会影响所有的用户，已进组用户可能会发生跳组。详见：实验流量调整（https://docs.zuoyebang.cc/doc?fileId=1902995978627801090）'
                    ]
                },
                {
                    key: 'parentVersion',
                    title: '关联父实验版本',
                    content: [
                        '子实验的每个实验组必须关联一个父实验的版本，因为子实验的流量来自父实验。',
                        '每个子实验的实验版本都可以关联多个父实验的具体版本。',
                        '父实验版本相当于受众规则，子实验设定自己的样本总量和受众之后，会筛选出一部分用户，然后再看这些用户是否能够命中父实验，能命中父实验就接着往子实验走，如果不能命中父实验，则无法命中子实验。',
                        '想要实现父实验版本流量全部进入子实验，子实验样本总量需要设置为100%，且无受众规则。',
                        '想要实现父实验分流效果，需要子实验两个版本均关联父实验同一个版本。',
                        '错误案例：（1）每个子实验的实验版本分别关联父实验的每个实验版本，会导致父实验的流量无法全部进入子实验。详见：父子实验分流。（https://docs.zuoyebang.cc/doc?fileId=1937794306725871617）'
                    ]
                },
                {
                    key: 'metrics',
                    title: '关注指标',
                    content: [
                        '对需要关注的指标进行配置。您可以在页面的下拉列表中选择需要关注的指标，也可以在页面移除指标按钮，快速调整需要关注的指标。'
                    ]
                }
            ];
            return list;
        }
    },
    mounted() {
        this.$eventbus.$on('exp-document', this.setActiveKey);
    },
    beforeDestroy() {
        this.$eventbus.$off('exp-document', this.setActiveKey);
    },
    methods: {
        isActive(key) {
            return this.activeKey === key;
        },
        setActiveKey(key) {
            this.activeKey = key;
            // 增加滚动到可视窗口
            const el = document.querySelector(`[data-key="${key}"]`);
            if (el) {
                el.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        },
        formatTextWithLinks(text, index) {
            // 匹配HTTP和HTTPS链接的正则表达式，排除常见的结束符号
            const urlRegex = /(https?:\/\/[^\s)）。，]+)/g;
            const lastText = `${index + 1}. ${text}`;
            return lastText.replace(urlRegex, '<a href="$1" target="_blank" class="document-link">$1</a>');
        }
    }
};
</script>
<style lang="less" scoped>
.exp-document {
    overflow: auto;
    width: 100%;
    max-width: 300px;
    padding-left: 12px;
    padding-right: 6px;
    background-color: white;
    box-sizing: border-box;
    margin-top: 24px;
    flex-shrink: 0;
    min-width: 220px;

    .document-item {
        background-color: white;
        padding-bottom: 8px;

        &:last-child {
            padding-bottom: 0;
        }

        .document-content {
            background-color: @zyb-grey-2;
            border-radius: 4px;
            padding: 12px;
            padding-bottom: 6px;

            &.active {
                background-color: #dbeafe;
            }

            .document-title {
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 4px;
            }

            .document-text {
                color: rgba(0, 0, 0, 0.7);
                font-size: 12px;
                line-height: 16px;
                margin: 4px 0;

                // HTTP链接样式
                :deep(.document-link) {
                    color: #22c55e;
                    text-decoration: none;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}
</style>
