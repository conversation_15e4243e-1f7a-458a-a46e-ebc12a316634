<template>
  <div class="base-form">
    <h3>实验基本信息</h3>
    <el-form ref="formBase" :model="addData" label-width="130px" v-if="show">
      <el-form-item label="实验名称：" required @click.native="handleDocumentClick('name')">
        <el-input type="text" v-model="addData.displayName" placeholder="请输入实验名称" :disabled="isCheck" maxlength="50"
          show-word-limit></el-input>
      </el-form-item>
      <el-form-item label="实验描述：" @click.native="handleDocumentClick('description')">
        <el-input type="textarea" v-model="addData.description" rows="3" resize="vertical" placeholder="请输入描述信息"
          :disabled="isCheck" maxlength="200" show-word-limit></el-input>
      </el-form-item>
      <el-form-item v-if="ex != 4" label="业务线：" required @click="handleDocumentClick('appKey')">
        <el-select :disabled="isCheck || isCopy || statusIsDisable() || isChild || hasCorrectId"
          @focus="handleDocumentClick('appKey')" v-model="addData.appKey" placeholder="请选择业务线"
          @change="handleAppKeyChange">
          <el-option v-for="item in appList" :key="item.appKey" :label="item.displayName"
            :value="item.appKey"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="ex != 4" label="实验类型：" @click.native="handleDocumentClick('type')">
        <!-- 在草稿，调试中，发布中 的时候都不能选择-->
        <el-radio-group v-model="addData.clientType"
          :disabled="isCheck || statusIsDisable() || isChild || isCopy || ex == 2 || hasCorrectId"
          @change="changeClientType">
          <el-radio :label="1">客户端</el-radio>
          <el-radio :label="2">服务端</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="账号体系：" @click.native="handleDocumentClick('accountSystem')">
        <!-- 账号体系，cuid：1，uid: 2-->
        <el-radio-group v-model="addData.accountSystem"
          :disabled="isCheck || isCopy || statusIsDisable() || isChild || hasCorrectId" @change="changeAccountSystem">
          <el-radio :label="1">cuid</el-radio>
          <el-radio :label="2">uid</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="ex != 4 && ex != 5" label="实验时长：" class="ex-item-duration" required @click.native="handleDocumentClick('duration')">
        <el-input-number @blur="handleDurationBlur" :disabled="isCheck || addData.togetherEnd"
          v-if="!isChild || (isChild && fatherData.endTime)" v-model="addData.duration" @input.native="durationChange"
          controls-position="right" :min="mixDuration" :precision="0"
          :max="isChild ? maxDuration : defaultMaxDuration"></el-input-number>
        天
        <el-checkbox v-if="isChild" class="together-end" v-model="addData.togetherEnd" :disabled="isCheck || isEdit"
          @change="addData.duration = fatherData.duration">
          与父实验同时结束
        </el-checkbox>
        <el-alert v-if="durationMindaysAlert" @close="durationMindaysAlert = false" class="duration-alert"
          title="您设置的实验时长过短，建议至少设置为 14天，以确保有足够的时间收集数据并得出可靠的实验结论。较短的实验时间可能会受到短期数据波动的影响，导致结果不准确。" type="warning"
          effect="dark" />
      </el-form-item>
      <el-form-item v-if="showDurationAlert">
        <el-alert :title="maxDurationMsg" type="warning" show-icon></el-alert>
      </el-form-item>
      <el-form-item v-if="isChild && ex != 4 && ex != 5">
        <el-alert class="baseinfo-alter" :title="`父实验运行时子实验才能开启运行。您的父实验预计在${maxDuration}天后停止，您最多可为子实验设置${maxDuration}天的运行时长`
          " type="warning" show-icon></el-alert>
      </el-form-item>
      <el-form-item label="实验标签：" @click.native="handleDocumentClick('label')">
        <el-select @focus="handleDocumentClick('label')" :disabled="isCheck" v-model="addData.labelList"
          placeholder="请选择或者输入实验标签" allow-create filterable multiple>
          <el-option v-for="item in labelList" :key="item.displayName" :label="item.displayName"
            :value="item.displayName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="!isGlobal" label="告警钉钉群：" @click.native="handleDocumentClick('dingdingGroup')">
        <el-select @focus="handleDocumentClick('dingdingGroup')" :disabled="isCheck" v-model="addData.dingGroupIds"
          multiple placeholder="请选择">
          <el-option v-for="item in dingGroupList" :key="item.id" :label="item.dingGroupName"
            :value="item.id"></el-option>
        </el-select>
        <span class="flow-set-btn" @click="goCreateDingGroup">去创建</span>
      </el-form-item>
      <el-form-item label="负责人：" required @click.native="handleDocumentClick('owner')">
        <search-user v-model="addData.createUser" :usedefault="isAdd" :disabled="isCheck"
          @click.native="handleDocumentClick('owner')"></search-user>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { localStorage } from '@common/utils';
import { mapGetters } from 'vuex';
import SearchUser from '@/components/search-user/index.vue';
export default {
  name: 'ExbaseInfo',
  components: {
    SearchUser
  },
  props: {},
  data() {
    return {
      isAdanced: false,
      showDurationAlert: false,
      durationMindaysAlert: false,
      isCheck: this.$route.query.type === 'check' || false,
      isEdit: this.$route.query.type === 'edit' || false,
      isAdd: this.$route.query.type === 'add' || false,
      isCopy: this.$route.query.subType === 'copy' || false,
      ex: this.$route.query.ex,
      hasCorrectId: !!this.$route.query.correctId, // 从固化新建实验
      labelList: [],
      dingGroupList: [],
      show: false,
    };
  },
  computed: {
    ...mapGetters(['fatherData', 'addData', 'appList', 'userInfo', 'locationList'], 'message'),
    isAdmin() {
      return this.userInfo.isAdmin;
    },
    isChild() {
      return !!this.$route.query.parentId;
    },
    isCloudControlType() {
      const { query = {} } = this.$route;
      return +query.ex === 5;
    },
    maxDuration() {
      const date = this.fatherData.endTime * 1000 - new Date().getTime();
      const count = date / (24 * 60 * 60 * 1000);
      return Math.ceil(count || 0);
    },
    defaultMaxDuration() {
      if (this.isAdmin || this.isCloudControlType || this.isCheck) {
        return 999999999;
      }
      if (this.isEdit) {
        return Math.max(this.addData.maxDuration || 90, this.addData.originDuration);
      }
      return 90;
    },
    maxDurationMsg() {
      return `编程实验最多支持${this.defaultMaxDuration}天。`;
    },
    mixDuration() {
      return this.addData.mixDuration || 1;
    },
    isGlobal() {
      return location.href.includes('global-abtest');
    }
  },
  created() {
    if (this.isAdd) {
      const uname = localStorage.getItem('uname');
      if (this.addData.myEmails.length) {
        this.addData.myEmails[0].value = uname;
      }
    }
    setTimeout(() => {
      this.show = true;
    }, 100);
  },
  mounted() {
    this.getLabelList();
    !this.isGlobal && this.getdingGroupList();
  },
  methods: {
    handleDurationBlur() {
      this.durationMindaysAlert = this.addData.duration < 14;
    },
    handleDocumentClick(key) {
      this.$eventbus.$emit('exp-document', key);
    },
    getdingGroupList() {
      this.$service
        .get(
          'DINGGROUPLIST',
          {
            pn: 1,
            rn: 10000
          },
          { allback: 0, needLoading: true }
        )
        .then(res => {
          this.dingGroupList = res.list || [];
        })
        .catch(err => {
          console.log(err);
        });
    },
    getLabelList() {
      this.$service
        .get('LABLELIST', {}, { allback: 0, needLoading: true })
        .then(res => {
          this.labelList = res.list || [];
        })
        .catch(err => {
          console.log(err);
        });
    },
    statusIsDisable() {
      return [1, 2, 3, 4, 5, 6, 7, 8, 9].includes(this.addData.status);
    },
    statusIsDraft() {
      // 草稿状态
      return this.addData.status === 1;
    },
    statusIsDebug() {
      // 调试状态
      return this.addData.status === 2;
    },
    statusIsPub() {
      // 发布状态
      return this.addData.status === 3;
    },
    statusIsStop() {
      // 结束状态
      return this.addData.status === 4;
    },
    statusIsAwaitPub() {
      // 待发布状态
      return this.addData.status === 5;
    },
    statusIsPause() {
      // 暂停状态
      return this.addData.status === 6;
    },
    statusIsDongJie() {
      // 冻结状态
      return this.addData.status === 7;
    },
    //
    changeClientType(val) {
      // if (!this.addData.appKey) {
      //   this.$message.warning('请先选择业务线');
      //   return;
      // }
      this.$store.dispatch('getMutuList');
      // 重置互斥组
      this.$store.commit('resetNewExclusiveId');
    },
    // 改变账号体系的时候
    changeAccountSystem() {
      // if (!this.addData.appKey) {
      //   this.$message.warning('请先选择业务线');
      //   return;
      // }
      this.$store.dispatch('getFilterList');
      this.$store.dispatch('getMetricList');
      this.$store.dispatch('getMutuList');
      this.$store.dispatch('getWhiteList');
      //需要重置受众规则
      this.$store.commit('resetFilterList');
      // 重置白名单
      this.$store.commit('resetMyWhiteList');
      // 重置互斥组
      this.$store.commit('resetNewExclusiveId');
    },
    // 当appKey改变的时候
    handleAppKeyChange(val) {
      this.$store.dispatch('getFilterList', val);
      this.$store.dispatch('getMutuList', val);
      this.$store.dispatch('getWhiteList', val);
      this.$store.dispatch('getMetricList', val);
      //需要重置受众规则
      this.$store.commit('resetFilterList');
      // 重置白名单
      this.$store.commit('resetMyWhiteList');
      // 重置互斥组
      this.$store.commit('resetNewExclusiveId');
    },
    // 新增邮件接收人
    handleAddEmail() {
      if (this.addData.myEmails.length < 6) {
        this.addData.myEmails.push({
          value: ''
        });
      } else {
        this.$message.warning('最多添加6个邮件接受人');
        return;
      }
    },
    // 删除邮件接收人
    handleDelEmail(index) {
      this.addData.myEmails.splice(index, 1);
    },
    // 高级设置切换
    openAdancedSet() {
      this.isAdanced = !this.isAdanced;
    },
    durationChange(event) {
      if (+event.target.value > this.defaultMaxDuration) {
        this.showDurationAlert = true;
      } else {
        this.showDurationAlert = false;
      }
    },
    testToken() {
      console.log('this token', this.addData.dingToken);
      if (!this.addData.dingToken) {
        this.$message.warning('请输入钉钉TOKEN');
        return;
      }
      this.$service
        .get(
          'DINGTEST',
          {
            token: this.addData.dingToken
          },
          { allback: 0, needLoading: true }
        )
        .then(res => {
          this.$message.success('调试成功');
        })
        .catch(err => {
          console.log(err);
        });
    },
    goCreateDingGroup() {
      const origin = window.location.origin;
      const path = '/static/fe-ugc-message/#/dinggroup';
      const url = `${origin}${path}`;
      window.open(url);
    }
  }
};
</script>

<style lang="less" scoped>
.base-form {
  margin-top: 36px;
  padding: 15px 20px 0 20px;
  border: 1px solid #e1e2e6;
  border-radius: 4px;

  .el-alert {
    &.duration-alert {
      margin-top: 5px;
    }

    /deep/ .el-alert__content {
      line-height: 18px;

      .el-alert__closebtn {
        right: 8px;
      }
    }
  }

  .el-form {

    /deep/ .el-form-item {
      .el-input,
      .el-select,
      .el-textarea {
        max-width: 350px;
      }
    }

    .el-form-item {
      .el-select {
        width: 350px;
      }
    }

    //:deep(.el-form-item__content) {
    //  & > * {
    //    width: 100% !important;
    //  }
    //}
  }

  .flow-set-btn {
    position: absolute;
    margin-left: 10px;
    cursor: pointer;
    color: #42c57a;
    width: 150px;
    font-size: 12px;
  }

  .baseinfo-alter {
    width: 700px;
  }

  // width: 70%;
  // margin: 40px auto 0;
  >h3 {
    padding: 0 0 20px 0;
    font-size: 16px;
    font-weight: 700;
  }

  >div {
    margin-top: 30px;
  }

  .f-width-c {
    width: 40%;
  }

  .adv-set {
    width: 100px;
    padding: 10px 16px;
    text-align: center;

    /deep/ span {
      font-size: 14px;
    }
  }

  .op-con {
    padding: 10px 20px;
    margin-top: 20px;
  }

  /deep/ .s-w {
    width: 270px;
  }

  /deep/ .el-tag.is-disabled {
    background-color: transparent;
    border-color: #f0f1f5;
    color: #999;
    cursor: not-allowed;
  }

  /deep/ .el-textarea.is-disabled .el-textarea__inner,
  /deep/ .el-input.is-disabled .el-input__inner {
    //background-color: transparent;
    border-color: #e1e2e6;
    color: #999;
    cursor: not-allowed;
  }

  /deep/ .el-button--text-primary.is-disabled {
    background-color: transparent;
  }

  .email-con {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .email-input {
      width: 400px;
      margin-right: 15px;

      .email-sign-con {
        display: flex;
        align-items: center;
      }

      .email-sign-con .email-sign {
        margin-top: 4px;
      }
    }
  }
}

/deep/ .el-radio__label {
  font-size: 14px;
}

.together-end {
  margin-left: 10px;
}

.warning-tips {
  color: red;
}
</style>
