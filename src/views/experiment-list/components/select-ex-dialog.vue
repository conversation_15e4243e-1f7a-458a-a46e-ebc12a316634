<template>
  <el-dialog title="选择实验模式" :visible="dialogVisible" :before-close="closeDialog" :width="width">
    <template>
      <div class="ex_list">
        <div class="ex_box" v-for="(item, index) in list" :key="index" @click="handleSelect(item.type)">
          <el-card class="ex_card" shadow="hover">
            <img class="ex_img" :src="item.img" />
            <div class="ex_title">{{ item.title }}</div>
            <div class="ex_desc">{{ item.desc }}</div>
          </el-card>
        </div>
      </div>
    </template>

    <template slot="footer">
      <el-button @click="closeDialog">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script>
import code from '@/static/images/ex_code.png';
import visual from '@/static/images/ex_visual.png';
import links from '@/static/images/ex_links.png';
import push from '@/static/images/ex_push.png';
import { getUrlByLocation } from '../../../utils/util';

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [
        {
          type: 1,
          img: code,
          title: '编程实验',
          desc:
            '通过代码编程的AB实验，广泛使用于前端优化、策略优化和后端算法优化等多种实验场景，包含客户端实验、服务端实验。'
        },
        // {
        //   type: 2,
        //   img: visual,
        //   title: '可视化实验',
        //   desc:
        //     '支持编辑页面元素，如图片、文本替换和编辑、元素位置移动等，多用于UI多变量的场景。目前支持在“Web端网页、H5页面”创建实验，属于客户端实验。'
        // },
        // {
        //   type: 3,
        //   img: links,
        //   title: '多链接实验',
        //   desc:
        //     '亦称为Split URL实验，不同实验版本访问不同URL、划分线上流量，适用于活动着陆页效果比对、流程优化等场景。目前支持在“Web端网页、H5页面”创建实验，属于客户端实验。'
        // },
        // {
        //   type: 4,
        //   img: push,
        //   title: '推送实验',
        //   desc:
        //     '运营推送活动，针对活动落地页、推送文案、推送策略、后续动作、推送时机等都可以进行实验，提升活动收益转化率。目前只支持iOS、Android应用，属于「服务端实验」。'
        // }
        {
          type: 5,
          img: links,
          title: '云控配置实验',
          desc:
            '不同的实验版本设置不同的配置，支持实验发布后修改参数值，适用于获取不同的配置场景。'
        }
      ]
    };
  },
  computed: {
    width() {
      return 220 + (this.list.length - 1) * 244 + 40 + 'px';
    }
  },
  methods: {
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    handleSelect(val) {
      this.$store.commit('resetAddData');
      this.closeDialog();
      const hash = '/exp-manage/list/add';
      const params = {
        type: 'add',
        ex: val
      };
      this.$router.push({ path: hash, query: params });
    }
  }
};
</script>

<style lang="less" scoped>
.ex_list {
  display: flex;
}

.ex_box {
  margin-left: 24px;
  cursor: pointer;

  &:first-child {
    margin-left: 0;
  }
}

.ex_card {
  width: 220px;
  height: 100%;
  box-sizing: border-box;

  /deep/ .el-card__body {
    padding-top: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

.ex_img {
  width: 46px;
  height: 46px;
}

.ex_title {
  color: #606266;
  font-size: 14px;
  text-align: center;
  margin-top: 14px;
  font-weight: 700;
}

.ex_desc {
  line-height: 1.65;
  margin-top: 14px;
  color: #9f9fab;
  font-size: 12px;
}
</style>