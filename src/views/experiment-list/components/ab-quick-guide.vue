<template>
  <div class="ab-quick-guide">
    <div class="guide-content">
      <!-- 关闭按钮 -->
      <div class="close-btn" @click="handleClose">
        <i class="el-icon-close"></i>
      </div>

      <!-- 标题区域 -->
      <div class="title-section">
        <h2 class="main-title">A/B测试，助力产品增长</h2>
        <p class="sub-title">摆脱猜测，用科学的实验衡量决策，让产品增长更简单！</p>
      </div>

      <!-- 功能介绍区域 -->
      <div class="features-section">
        <div class="feature-item">
          <div class="feature-icon">
            <img :src="AbShiYan" alt="" />
          </div>
          <span class="feature-text">A/B实验怎么开</span>
        </div>

        <div class="feature-item">
          <div class="feature-icon">
            <img :src="AbGongKaiKe" alt="" />
          </div>
          <span class="feature-text">A/B实验产品公开课</span>
        </div>

        <div class="feature-item">
          <div class="feature-icon">
            <img :src="AbZuiJiaShiJian" alt="" />
          </div>
          <span class="feature-text">A/B实验最佳实践</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AbGongKaiKe from '@/static/images/ab-gongkaike.png';
import AbShiYan from '@/static/images/ab-shiyan.png';
import AbZuiJiaShiJian from '@/static/images/ab-zuijiashijian.png';

export default {
  name: 'AbQuickGuide',
  data(){
    return {
      AbGongKaiKe,
      AbShiYan,
      AbZuiJiaShiJian
    };
  },
  methods: {
    handleClose() {
      this.$emit('close');
    },
    openLink(type) {
      const links = {
        manual: 'https://docs.zuoyebang.cc/doc?fileId=1821023390622781441',
        course: 'https://baifenxuetang.radnova.cn/kng/#/video/play?kngId=e31f60d3-eed2-4f63-b248-15eec38e828c&projectId=&btid=&gwnlUrl=',
        practice: 'https://docs.zuoyebang.cc/doc?fileId=1938533725139501057'
      };

      if (links[type]) {
        window.open(links[type], '_blank');
      }
    }
  }
};
</script>

<style lang="less" scoped>
.ab-quick-guide {
  margin: 16px 0;
  background: rgba(66, 197, 122, 0.1);
  border-radius: 4px;
  position: relative;

  .guide-content {
    padding: 20px 24px;
    position: relative;
  }

  .close-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s;

    &:hover {
      background: rgba(66, 197, 122, 0.2);
    }

    .el-icon-close {
      color: #42C57A;
      font-size: 16px;
    }
  }

  .title-section {
    margin-bottom: 12px;

    .main-title {
      font-size: 16px;
      color: #42C57A;
      margin: 0 0 8px 0;
      font-weight: 500;
    }

    .sub-title {
      font-size: 13px;
      color: #42C57A;
      margin: 0;
      line-height: 1.5;
    }
  }

  .features-section {
    display: flex;
    gap: 16px;

    .feature-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: all 0.3s;
      background-color: #fafbfc;
      color: #333;
      min-width: 150px;

      &:hover {
        // background: rgba(66, 197, 122, 0.15);
        background: rgba(66, 197, 122, 0.1);
      }

      .feature-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;

        img {
          width: 18px;
          height: 18px;
        }
      }

      .feature-text {
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
}
</style>
