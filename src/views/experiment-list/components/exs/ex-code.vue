<template>
  <div class="ex-code">
    <span>参数设置：</span>
    <div class="version-create">
      <!-- 参数部分 -->
      <div class="version-argvs" @click="handleDocumentClick('parameter')">
        <section class="argv-name-item" v-for="(feat, idx) in addData.versionList[0].featureList" :key="idx">
          <el-input slot="reference" :disabled="isCheck || statusIsRun() || statusIsAwaitPub()" v-model="feat.keyName"
            type="text" placeholder="请输入参数" @change="handleChangeKeyName(idx, feat.keyName)"></el-input>
          <el-select :disabled="isCheck || statusIsRun() || statusIsAwaitPub()" placeholder="请选择" v-model="feat.keyType"
            @change="handleChangeParamType(idx, feat.keyType)">
            <el-option v-for="item in paramTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <section class="right">
            <el-button type="text" icon="el-icon-circle-plus-outline"
              v-if="!isCheck && !statusIsRun() && !statusAwaitRun()"
              :disabled="isCheck || statusIsRun() || statusAwaitRun()" @click="handleAddParams"></el-button>
            <el-button type="text" icon="el-icon-delete"
              v-if="addData.versionList[0].featureList.length > 1 && !isCheck && !statusIsRun() && !statusIsDebug() && !statusIsAwaitPub()"
              @click="handleDeleteParms(idx)"></el-button>
          </section>
        </section>
      </div>
      <!-- 版本部分 -->
      <el-collapse class="t-version-con" :value="expandVersions" @click.native="handleDocumentClick('version')">
        <ex-version v-for="(version, index) in addData.versionList" :key="index" v-if="index !== 0"
          :length="addData.versionList.length" :version="version" :index="index" :allSelectedIds="selectedIdList"
          :handleEditJson="handleEditJson"></ex-version>
      </el-collapse>
      <div class="add-con" v-if="!isCheck" @click="handleDocumentClick('version')">
        <template v-if="statusIsAwaitPub() || statusIsDraft() || isExtend || isAdd || isCopy">
          <el-button class="add-btn" type="default" @click="addversion" icon="el-icon-circle-plus">
            添加实验版本
          </el-button>
        </template>
      </div>
    </div>
    <edit-json-dialog v-if="isShowDialog" :dialogVisible.sync="isShowDialog" @handleJsonSucceed="handleJsonSucceed"
      @handleJsonClose="handleJsonClose" :jsonValue="jsonValue"></edit-json-dialog>
  </div>
</template>

<script>
import { isEqual } from 'lodash';
import { mapGetters } from 'vuex';
import EditJsonDialog from '../edit-json-dialog.vue';
import ExVersion from './ex-version.vue';
const colorMap = ['#42c57a', 'rgba(30, 144, 255, 1)', 'rgba(0, 206, 209, 1)', 'rgba(255, 69, 0, 1)', 'rgba(255, 140, 0, 1)', 'rgba(255, 215, 0, 1)', 'rgba(144, 238, 144, 1)', 'rgba(0, 206, 209, 1)', 'rgba(199, 21, 133, 1)', 'rgba(255, 69, 0, 0.68)', 'rgba(199, 21, 133, 0.46)', 'rgba(255, 120, 0, 1)'];
const actions = {
  local: '/testAddress/earthworm/mis/upload/uploadimg',
  test: '/earthworm/mis/upload/uploadimg',
  server: '/earthworm/mis/upload/uploadimg'
};

export default {
  data() {
    return {
      jsonValue: '',
      indexs: [],
      isShowDialog: false,
      isYunTest: this.$route.query.ex === '5' || false,
      isCodeTest: this.$route.query.ex === '1' || false,
      hasCorrectId: !!this.$route.query.correctId, // 从固化新建实验
      isExtend: this.$route.query.isExtend === '1',
      isConfirm: false,
      selectedIdList: []
    };
  },
  components: {
    EditJsonDialog,
    ExVersion
  },
  watch: {

    allSelectedIds: {
      handler(newList, oldList) {
        if (isEqual(newList, this.selectedIdList)) {
          return;
        }
        this.selectedIdList = [...newList];
      },
      immediate: true,
      deep: true
    },
    whiteList: {
      handler(newList, oldList) {
        this.addData.versionList.forEach(version => {
          const { whitelist = [] } = version;
          version.mywhitelist = whitelist.map(({ whitelistId }) => {
            // const group = newList.find(item => item.whitelist.some(data => data.id === whitelistId));
            // console.log('group', group);
            // if (group) {
            //   const { whitelistGroupDisplayName } = group;
            //   return ['group-' + whitelistGroupDisplayName, whitelistId];
            // }
            return whitelistId;
          });
        });
        console.log('ths.adddata', this.addData.versionList);
        console.log('newListnewListnewListnewListnewList', newList);
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    ...mapGetters(['addData', 'paramTypes', 'booleanList', 'whiteList']),
    isCheck() {
      return this.$route.query.type === 'check';
    },
    uploadConfig() {
      return {
        action: this.computedEnv()
      };
    },
    isAdd() {
      return this.$route.query.type === 'add';
    },
    isCopy() {
      return this.$route.query.subType === 'copy';
    },
    expandVersions() {
      const res = [];
      this.addData.versionList.forEach((item, index) => {
        if (!item.hidden && index > 0) {
          res.push(index);
        }
      });
      return res;
    },
    allSelectedIds() {
      const versions = this.addData.versionList.slice(1);
      const ids = [];
      versions.forEach(item => {
        item.mywhitelist.forEach(item => {
          ids.push(item);
        });
      });
      return ids;
    },
  },
  methods: {
    handleDocumentClick(key) {
      this.$eventbus.$emit('exp-document', key);
    },
    getWhiteListOptions(whiteList) {
      console.log('whitelist');
      const allIds = this.allSelectedIds;
      const res = this.whiteList.map(item => {
        return {
          value: 'group-' + item.whitelistGroupDisplayName,
          label: item.whitelistGroupDisplayName,
          children: item.whitelist.map(item => {
            return {
              value: item.id,
              label: item.displayName,
              disabled: !whiteList.includes(item.id) && allIds.includes(item.id)
            };
          }),
        };
      });
      return res;
    },
    // allSelectedIds() {
    //   const versions = this.addData.versionList.slice(1);
    //   const ids = [];
    //   versions.forEach(item => {
    //     item.mywhitelist.forEach(item => {
    //       ids.push(item);
    //     });
    //   });
    //   return ids;
    // },
    changeItemExpand(version) {
      this.$set(version, 'hidden', !version.hidden);
    },
    getBgColorByIndex(index) {
      const remainder = index % 12;
      return colorMap[remainder];
    },
    statusAwaitRun() {
      return this.statusIsAwaitPub() || this.statusIsDebug();
    },
    statusIsRun() {
      return this.statusIsPub() || this.statusIsPause() || this.statusIsDongJie();
    },
    statusIsDraft() { // 草稿状态
      return this.addData.status === 1;
    },
    statusIsDebug() { // 调试状态
      return this.addData.status === 2;
    },
    statusIsPub() { // 发布状态
      return this.addData.status === 3;
    },
    statusIsStop() { // 结束状态
      return this.addData.status === 4;
    },
    statusIsDongJie() {
      return this.addData.status === 7;
    },
    statusIsAwaitPub() { // 待发布状态
      return this.addData.status === 5;
    },
    statusIsPause() { // 暂停状态
      return this.addData.status === 6;
    },
    handleJsonClose() { },
    /**
     * @description: json编辑成功
     * @param {*} e
     * @return {*}
     */
    handleJsonSucceed(e) {
      let verIndex = this.indexs[0];
      let feaIndex = this.indexs[1];
      this.addData.versionList[verIndex].featureList[feaIndex].keyValue = JSON.stringify(e);
    },
    /**
     * @description: 校验输入值合法性
     * @param {*} index
     * @param {*} idx
     * @return {*}
     */
    handleJsonInput(val) {
      if (!val) {
        return;
      } else if (!this.isJson(val)) {
        this.$message.error('请输入正确的JSON字符串');
      }
      // index version 编号 idx feature 编号
      // 数据结构是竖着的
      // console.log("index, idx", index, idx);
    },
    /**
     * @description: icon点击
     * @param {*} index
     * @param {*} idx
     * @return {*}
     */
    handleEditJson(index, idx) {
      this.jsonValue = data;
      this.isShowDialog = true;
    },
    isJson(str) {
      if (typeof str == 'string') {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
            return false;
          }
        } catch (e) {
          console.log('error：' + str + '!!!' + e);
          return false;
        }
      } else {
        this.$message.error('请输入字符串');
      }
    },
    // 适配文件上传环境地址
    computedEnv() {
      let hostName = location.hostname;
      let url = '';
      if (/^localhost$/gi.test(hostName)) {
        url = actions.local;
      } else if (/docker|test/gi.test(hostName)) {
        url = actions.test;
      } else {
        url = actions.server;
      }
      return url;
    },
    versionInputShow(version) {
      console.log('hahahahahahha', version);
      this.$set(version, 'editName', version.displayName || version.fixedName);
      this.$set(version, 'editNameStatus', true);
    },
    versionInputHide(version) {
      this.isConfirm = true;
      setTimeout(() => {
        this.isConfirm = false;
      }, 200);
      if (!version.editName) {
        this.$message({
          type: 'error',
          message: '请输入版本名称'
        });
        return;
      }
      const name = version.editName;
      version.displayName = name;
      version.fixedName = name;
      version.editName = '';
      version.editNameStatus = false;
    },
    blurInput(version) {
      setTimeout(() => {
        if (this.isConfirm) {
          return;
        }
        version.editName = '';
        version.editNameStatus = false;
      }, 150);
    },
    deleteKeyup(e) {
      console.log('hahahahahahah', e);
      setTimeout(() => {
        e.target.focus();
      }, 50);
    },
    // 处理图片上传成功
    handleImageSuccess(version, res, file) {
      version.picUrl = res.data.url;
    },
    // 上传图片之前
    beforeImageUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      const isImg = 'png,jpg,jpeg'.indexOf(file.type.split('/')[1]) !== -1;

      if (!isImg) {
        this.$message.error('上传图片只能是 JPG、JPEG、PNG 格式!');
      }

      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isLt2M && isImg;
    },
    // 添加版本
    addversion() {
      this.$store.commit('addVersion');
    },
    // 增加参数
    handleAddParams() {
      this.$store.commit('addParams');
    },
    // 改变参数值
    handleChangeKeyName(index, value) {
      this.$store.commit('changeKeyName', { index, value });
    },
    // 改变参数类型
    handleChangeParamType(index, type) {
      this.$store.commit('changeParamType', { index, type });
    },
    // 删除版本
    handleDeleteVersion(index) {
      if (this.statusIsDebug() ||
        (this.statusIsRun() && !this.isExtend) ||
        this.isCheck
      ) {
        return;
      } else if (this.statusIsAwaitPub() || this.isExtend) {
        const versions = this.addData.versionList,
          trueVersions = versions.slice(1),
          trueVerLen = trueVersions.length;
        if (trueVerLen == 2) {
          this.$message.warning('至少保留两个版本');
          return;
        }
        this.$store.commit('deleteVersion', { index });
      } else {
        this.$store.commit('deleteVersion', { index });
      }
    },
    // 删除参数
    handleDeleteParms(index) {
      this.$store.commit('deleteParms', { index });
    }
  }
};
</script>

<style lang="less" scoped>
.ex-code {
  display: flex;

  &>span {
    font-size: 14px;
    width: 120px;
    flex-shrink: 0;
    margin-right: 10px;
    text-align: right;
    box-sizing: border-box;
  }

  .version-create {
    display: flex;
    overflow: hidden;
    flex-direction: column;
    width: 100%;

    .version-argvs {
      position: relative;
    }

    .argv-name-item {
      position: relative;
      margin-bottom: 8px;
      display: flex;
      align-items: center;

      &>.el-input {
        margin-right: 12px;
        max-width: 220px;
      }

      .right {
        margin-left: 12px;

        i {
          font-size: 15px;
          margin-right: 10px;
        }

        /deep/ .el-icon-delete {
          color: #f56c6c;
        }
      }
    }

    .t-version-con {
      display: flex;
      flex-direction: column;
      width: 860px;

      &.el-collapse {
        border: none;

        /deep/.el-collapse-item {
          margin-bottom: 16px;

          .el-collapse-item__header {
            border: none;
            position: relative;
            color: white;

            .version-item-head {
              height: 100%;
              padding-left: 32px;
              padding-right: 16px;
              width: 100%;
              border-top-left-radius: 5px;
              border-top-right-radius: 5px;
              display: flex;
              justify-content: space-between;
              align-items: center;

              .version-name {
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 14px;
                font-weight: bold;


                .version-edit-icon {
                  margin-left: 6px;
                  padding: 12px;
                }

                .el-input {
                  width: 200px;
                }

                span {
                  line-height: 16px;
                  display: inline-block;
                  font-weight: bold;
                  max-width: 160px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }

              .close-btn {
                font-size: 14px;
              }
            }

            &>i {
              position: absolute;
              left: 8px;
            }
          }

          .el-collapse-item__wrap {
            border: 1px solid #ebeef5;
            border-top: none;
            padding: 12px 16px !important;
            overflow: auto;
            max-height: 300px;
          }

          .el-collapse-item__content {
            padding-bottom: 0px;
          }

          .version-group {
            position: relative;
            display: flex;
            justify-content: space-between;

            &>ul {
              flex: 1;

              .argv-name-item {
                position: relative;
                margin-bottom: 8px;
                display: flex;
                align-items: center;

                .el-input-number,
                .el-select {
                  width: 100%;
                }

                &>.el-input {
                  margin-right: 12px;
                  max-width: 120px;
                }

                &>div {
                  flex: 1;
                }
              }
            }

            .version-item {
              flex: 1;
              margin-right: 12px;
              display: flex;
              flex-direction: column;
              border-right: 1px solid #e1e3e9;
              padding-right: 12px;

              .top {
                display: flex;
                flex-direction: row;

                .version-desc {
                  flex: 1;
                  margin-right: 12px;
                  // padding: 120px;

                  textarea {
                    // min-height: 82px;
                    height: 82px !important;
                  }
                }

                .img-uploader {
                  height: 82px;
                  line-height: 82px;
                  overflow: hidden;

                  /deep/ .el-upload {
                    border: 1px dashed #d9d9d9;
                    border-radius: 6px;
                    cursor: pointer;
                    position: relative;
                    overflow: hidden;
                  }

                  .img-container {
                    width: 82px;
                    height: 82px;
                    position: relative;

                    .img-uploader-con {
                      width: 100%;
                      height: 100%;
                    }

                    .hover-container {
                      position: absolute;
                      top: 0;
                      left: 0;
                      width: 100%;
                      height: 100%;
                      background: rgba(0, 0, 0, 0.5);
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      // background-color: red;
                      font-size: 14px;
                      cursor: pointer;
                      opacity: 0;
                    }

                    &:hover .hover-container {
                      opacity: 1;
                    }
                  }

                  .img-uploader-icon {
                    border: 1px dashed #d9d9d9;
                    border-radius: 6px;
                    font-size: 14px;
                    color: #8c939d;
                    width: 82px;
                    height: 82px;
                    text-align: center;
                    display: block;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    box-sizing: border-box;
                  }
                }
              }

              .bottom {
                margin-top: 8px;

                .el-select {
                  width: 100%;
                }
              }
            }

            &:hover .version-row-top .version-item-head .close-btn {
              display: block;
            }

            .argv-item {
              /deep/ .el-input__inner {
                width: 264px !important;
                height: 88px !important;
                padding: 12px;
              }
            }
          }

        }
      }
    }
  }
}

.json_edit {
  padding: 10px;
  border-radius: 4px;
  background: aliceblue;
  color: #1a1a1a;
  border: thin solid #3883fa;
  box-sizing: border-box;

  /deep/.el-textarea__inner {
    width: 100%;
    height: 120px;
    padding: 6px;
    border: transparent;
  }
}

.edit_icon {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
}

h3 {
  padding: 0 20px 20px;
  font-size: 14px;
  font-weight: 700;
}

.par-con {
  width: 264px;
  height: 210px;
  padding: 12px;
  display: flex;
  align-items: flex-end;
}

.s-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-param {
  margin-left: 35px;
}

.add-con {
  /deep/ .el-icon-circle-plus {
    color: #42c57a;
    font-size: 14px;
  }

  .el-button {
    font-size: 14px;

    /deep/ span {
      font-weight: bold;
    }
  }
}
</style>
