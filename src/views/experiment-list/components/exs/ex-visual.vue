<template>
  <div class="ex_visual">
    <div class="url_box">
      <div class="url_label">实验URL：</div>
      <el-select class="url_select" v-model="value" placeholder="请选择" disabled>
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-input v-model="addData.url" placeholder="http://"></el-input>
    </div>

    <div class="set_box">
      <div class="set_label">设置参数</div>
      <el-button
        type="primary"
        icon="el-icon-plus"
        :disabled="statusIsRun() || statusIsDebug() || isCheck"
        @click="handleAddversion"
      >
        添加实验版本
      </el-button>
    </div>

    <div class="version_box">
      <div v-for="(item, index) in versionList" :key="index" class="version_card">
        <div class="version_title">
          <div v-if="index === 0 || isCheck" class="version_title_disabled">
            {{ item.displayName }}
          </div>
          <el-input
            v-else
            v-model="item.displayName"
            class="version_title_input"
            size="small"
            type="text"
            placeholder="请输入版本名称"
          />
          <div>
            <a v-if="index === 0" class="version_title_blank" @click="jumpToVisualEditor(item)">
              预览版本
            </a>
            <a v-else class="version_title_blank" @click="jumpToVisualEditor(item)">进入编辑器</a>
          </div>
        </div>
        <div class="version_img"></div>
        <div class="version_info">
          <div v-if="index !== 0">{{ item.changeNum }}处修改</div>
          <a v-if="!isCheck && index !== 0" @click="handleDeleteVersion(index)">删除</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { v4 as uuidv4 } from 'uuid';

export default {
  data() {
    return {
      options: [
        {
          value: 1,
          label: 'URL简单匹配'
        }
      ],
      value: 1,
      id: uuidv4()
    };
  },
  computed: {
    ...mapGetters(['addData']),
    versionList() {
      return this.addData.versionList.slice(1);
    },
    isCheck() {
      return this.$route.query.type === 'check';
    }
  },
  // watch: {
  //   versionList(newVal) {
  //     console.log('\x1b[36m\x1b[0m [LOG]: versionList', newVal);
  //   }
  // },
  methods: {
    statusAwaitRun() {
      return this.statusIsAwaitPub() || this.statusIsDebug();
    },
    statusIsRun() {
      return this.statusIsPub() || this.statusIsPause();
    },
    statusIsDraft() { // 草稿状态
      return this.addData.status === 1;
    },
    statusIsDebug() { // 调试状态
      return this.addData.status === 2;
    },
    statusIsPub() { // 发布状态
      return this.addData.status === 3;
    },
    statusIsStop() { // 结束状态
      return this.addData.status === 4;
    },
    statusIsAwaitPub() { // 待发布状态
      return this.addData.status === 5;
    },
    statusIsPause() { // 暂停状态
      return this.addData.status === 6;
    },
    handleAddversion() {
      this.$store.commit('addVersion');
    },
    handleDeleteVersion(index) {
      if (this.statusIsRun() || this.statusIsDebug() || this.isCheck) {
        return;
      } else {
        this.$store.commit('deleteVersion', { index });
      }
    },
    jumpToVisualEditor(item) {
      if (!this.addData.url) {
        this.$message.warning('实验版本的URL不合法');
      } else {
        if (item.displayName === '对照版本') {
          window.open(this.addData.url);
          return;
        }
        const versions = this.versionList.map((v) => {
          return {
            displayName: v.displayName,
            id: v.id || v.tabName
          };
        });
        const id = this.$route.query.type === 'add' ? this.id : this.$route.query.id;
        const version = item.id || item.tabName;
        localStorage.setItem(`__VERSION__${id}`, JSON.stringify(versions));
        window.open(
          `#/exp-manage/visual-editor?id=${id}&version=${version}&url=${this.addData.url}&type=${this.$route.query.type}`
        );
      }
    }
  },
  mounted() {
    window.addEventListener('message', (e) => {
      if (e.data && e.data.type === 'vcs') {
        // console.log('\x1b[36m\x1b[0m [LOG]: message', e.data);
        e.data.data.forEach((v) => {
          const index = this.addData.versionList.findIndex(
            (f) => f.id == v.id || f.tabName == v.id
          );
          if (index > -1) {
            this.addData.versionList[index].changes = v.changes;
            this.addData.versionList[index].changeNum = v.changeNum;
          }
        });
      }
    });
  }
};
</script>

<style lang="less" scoped>
.url_box {
  display: flex;
  align-items: center;
}

.url_label {
  white-space: nowrap;
}

.url_select {
  margin: 0 12px;
}

.set_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 50px;
}

.set_label {
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  font-weight: 500;
}

.version_box {
  margin-top: 50px;
  display: flex;
  overflow: auto;
}

.version_card {
  border-radius: 4px;
  border: 1px solid #e7e9f5;
  padding: 12px 16px;
  margin-bottom: 20px;
  width: 340px;
  display: inline-block;
  box-sizing: border-box;
  margin-left: 20px;

  &:first-child {
    margin-left: 0;
  }
}

.version_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version_title_disabled {
  color: #606266;
  font-size: 13px;
  height: 32px;
  line-height: 32px;
}

.version_title_blank {
  white-space: nowrap;
  margin-left: 12px;
}

.version_img {
  width: 308px;
  height: 208px;
  border: 1px solid #eee;
  margin-top: 6px;
  background: #eee;
  text-align: center;
}

.version_info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 20px;
  margin-top: 10px;
}

.version_title_input /deep/ .el-input__inner {
  border: none;
  padding-left: 0;
}
</style>
