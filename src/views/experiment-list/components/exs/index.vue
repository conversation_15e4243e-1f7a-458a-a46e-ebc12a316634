<template>
  <div class="exs">
    <component :is="name"></component>
  </div>
</template>

<script>
import ExCode from './ex-code.vue';
import ExLinks from './ex-links';
import ExPush from './ex-push/index.vue';
import ExVisual from './ex-visual';

export default {
  components: { ExCode, ExVisual, ExLinks, ExPush },
  data() {
    return {
      ex: this.$route.query.ex,
      comps: {
        1: 'ExCode',
        2: 'ExVisual',
        3: 'ExLinks',
        4: 'ExPush',
        5: 'ExCode',
      }
    };
  },
  computed: {
    name() {
      return this.comps[this.ex || 1];
    }
  }
};
</script>

<style lang="less" scoped>
</style>