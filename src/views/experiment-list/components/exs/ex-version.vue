<template>
  <el-collapse-item :name="index">
    <template slot="title">
      <div class="version-item-head" :style="{ backgroundColor: getBgColorByIndex(index) }"
        @click.stop.prevent="changeItemExpand(version)">
        <section class="version-name">
          <template v-if="!version.editNameStatus">
            <el-tooltip :content="version.displayName" placement="top">
              <span>{{ version.displayName }}</span>
            </el-tooltip>
            <i slot="suffix" v-if="!isCheck" class="el-icon-edit version-edit-icon"
              @click.stop.prevent="versionInputShow(version)">
            </i>
          </template>
          <template v-else>
            <el-input :disabled="isCheck" v-model="version.editName" size="small" type="text" placeholder="请输入版本名称"
              ref="refVersionInput" @click.native.stop.prevent="() => { }" @blur="blurInput(version)">
            </el-input>
            <el-button @click="versionInputHide(version)" icon="el-icon-check" type="text">
              <!-- <i slot="suffix" class="el-icon-check version-edit-icon"> -->
              <!-- </i> -->
            </el-button>
          </template>
        </section>
        <template v-if="!isCheck">
          <i class="el-icon-delete close-btn" @click.stop.prevent="handleDeleteVersion(index)" v-if="length > 3 && index > 1 && !(statusIsDebug() ||
            (statusIsRun() && !isExtend))"></i>
        </template>
        <template v-else>
          <span>ID: {{ version.versionId }}</span>
        </template>
      </div>
    </template>
    <div class="version-group">
      <div class="version-item">
        <section class="top">
          <p class="version-desc">
            <el-input :disabled="isCheck" v-model="version.description" size="small" type="textarea"
              placeholder="请输入版本描述" :autosize="{ minRows: 3, maxRows: 6 }"></el-input>
          </p>
          <el-upload :disabled="isCheck" class="img-uploader" :action="uploadConfig.action" :show-file-list="false"
            :on-success="(value) => handleImageSuccess(version, value)" :before-upload="beforeImageUpload">
            <section v-if="version.picUrl" class="img-container">
              <img :src="version.picUrl" class="img-uploader-con" />
              <section class="hover-container">
                <el-button type="text" icon="el-icon-view" @click.stop.prevent="previewImage"></el-button>
                <el-button type="text" icon="el-icon-delete" @click.stop.prevent="deleteImage"></el-button>
              </section>
            </section>
            <i v-else class="img-uploader-icon">上传图片</i>
          </el-upload>
        </section>
      </div>
      <ul>
        <li class="argv-name-item s-item" v-for="(feature, idx) in version.featureList" :key="idx">
          <el-input disabled v-model="feature.keyName" readonly style="width: 120px;"></el-input>
          <div v-if="feature.keyType == 1">
            <el-input slot="reference" :disabled="isCheck || (statusIsRun() && !isYunTest && !isExtend)"
              v-model="feature.keyValue" size="small" type="text" maxlength="200" placeholder="请输入字符串"></el-input>
          </div>
          <div v-if="feature.keyType == 2">
            <el-select v-model="feature.keyValue" class="paramInput"
              :disabled="(statusIsRun() && !isYunTest && !isExtend) || isCheck" placeholder="选择取值">
              <el-option v-for="(paramType, paramTypeIndex) in booleanList" :key="paramTypeIndex"
                :value="paramType.name" :label="paramType.name"></el-option>
            </el-select>
          </div>
          <div v-if="feature.keyType == 4">
            <el-input-number slot="reference" :disabled="isCheck || (statusIsRun() && !isYunTest && !isExtend)"
              v-model="feature.keyValue" controls-position="right" :min="-9007199254740991" :max="9007199254740991"
              maxlength="10"></el-input-number>
          </div>
          <div style="position: relative" v-if="feature.keyType == 5">
            <!-- <span v-if="!isCheck" class="edit_icon" @click="handleEditJson(index, idx)"> -->
            <span class="edit_icon" @click="handleEditJson(index, idx)">
              <svg viewBox="64 64 896 896" focusable="false" data-icon="fullscreen" width="1em" height="1em"
                fill="currentColor" aria-hidden="true">
                <path
                  d="M290 236.4l43.9-43.9a8.01 8.01 0 00-4.7-13.6L169 160c-5.1-.6-9.5 3.7-8.9 8.9L179 329.1c.8 6.6 8.9 9.4 13.6 4.7l43.7-43.7L370 423.7c3.1 3.1 8.2 3.1 11.3 0l42.4-42.3c3.1-3.1 3.1-8.2 0-11.3L290 236.4zm352.7 187.3c3.1 3.1 8.2 3.1 11.3 0l133.7-133.6 43.7 43.7a8.01 8.01 0 0013.6-4.7L863.9 169c.6-5.1-3.7-9.5-8.9-8.9L694.8 179c-6.6.8-9.4 8.9-4.7 13.6l43.9 43.9L600.3 370a8.03 8.03 0 000 11.3l42.4 42.4zM845 694.9c-.8-6.6-8.9-9.4-13.6-4.7l-43.7 43.7L654 600.3a8.03 8.03 0 00-11.3 0l-42.4 42.3a8.03 8.03 0 000 11.3L734 787.6l-43.9 43.9a8.01 8.01 0 004.7 13.6L855 864c5.1.6 9.5-3.7 8.9-8.9L845 694.9zm-463.7-94.6a8.03 8.03 0 00-11.3 0L236.3 733.9l-43.7-43.7a8.01 8.01 0 00-13.6 4.7L160.1 855c-.6 5.1 3.7 9.5 8.9 8.9L329.2 845c6.6-.8 9.4-8.9 4.7-13.6L290 787.6 423.7 654c3.1-3.1 3.1-8.2 0-11.3l-42.4-42.4z">
                </path>
              </svg>
            </span>
            <el-input slot="reference" @blur="handleJsonInput(feature.keyValue)"
              :disabled="isCheck || (statusIsRun() && !isYunTest && !isExtend)" v-model="feature.keyValue"
              class="json_edit" resize="none" size="large" type="textarea"
              placeholder='请输入0~10240字符，json格式{"comment":true}' maxlength="10240"></el-input>
          </div>
        </li>
      </ul>
    </div>
    <edit-json-dialog v-if="isShowDialog" :dialogVisible.sync="isShowDialog" @handleJsonSucceed="handleJsonSucceed"
      @handleJsonClose="handleJsonClose" :jsonValue="jsonValue"></edit-json-dialog>
    <el-dialog title="图片预览" :visible.sync="previewDialogVisible" width="80%">
      <section style="max-width: 80vw; max-height: 60vh; overflow: auto;">
        <img :src="version.picUrl" style="width: 100%;height: 100%;" />
      </section>
    </el-dialog>
  </el-collapse-item>
</template>
<script>
const actions = {
  local: '/testAddress/earthworm/mis/upload/uploadimg',
  test: '/earthworm/mis/upload/uploadimg',
  server: '/earthworm/mis/upload/uploadimg'
};
const colorMap = ['#42c57a', 'rgba(30, 144, 255, 1)', 'rgba(0, 206, 209, 1)', 'rgba(255, 69, 0, 1)', 'rgba(255, 140, 0, 1)', 'rgba(255, 215, 0, 1)', 'rgba(144, 238, 144, 1)', 'rgba(0, 206, 209, 1)', 'rgba(199, 21, 133, 1)', 'rgba(255, 69, 0, 0.68)', 'rgba(199, 21, 133, 0.46)', 'rgba(255, 120, 0, 1)'];
import { mapGetters } from 'vuex';
import EditJsonDialog from '../edit-json-dialog.vue';
export default {
  props: ['version', 'index', 'length'],
  components: {
    EditJsonDialog,
  },
  data() {
    return {
      jsonValue: '',
      indexs: [],
      isShowDialog: false,
      isYunTest: this.$route.query.ex === '5' || false,
      isCodeTest: this.$route.query.ex === '1' || false,
      hasCorrectId: !!this.$route.query.correctId, // 从固化新建实验
      isExtend: this.$route.query.isExtend === '1',
      isConfirm: false,
      previewDialogVisible: false
    };
  },
  computed: {
    ...mapGetters(['addData',  'booleanList']),
    isCheck() {
      return this.$route.query.type === 'check';
    },
    uploadConfig() {
      return {
        action: this.computedEnv()
      };
    },
  },
  methods: {
    previewImage() {
      // 组件内dialog展示预览图片
      this.previewDialogVisible = true;
    },
    deleteImage() {
      this.version.picUrl = '';
    },
    handleImageSuccess(version, res, file) {
      version.picUrl = res.data.url;
    },
    computedEnv() {
      let hostName = location.hostname;
      let url = '';
      if (/^localhost$/gi.test(hostName)) {
        url = actions.local;
      } else if (/docker|test/gi.test(hostName)) {
        url = actions.test;
      } else {
        url = actions.server;
      }
      return url;
    },
    changeItemExpand(version) {
      this.$set(version, 'hidden', !version.hidden);
    },
    getBgColorByIndex(index) {
      const remainder = index % 12;
      return colorMap[remainder];
    },
    versionInputShow(version) {
      this.$set(version, 'editName', version.displayName || version.fixedName);
      this.$set(version, 'editNameStatus', true);
    },
    versionInputHide(version) {
      console.log('button click');
      this.isConfirm = true;
      setTimeout(() => {
        this.isConfirm = false;
      }, 200);
      if (!version.editName) {
        this.$message({
          type: 'error',
          message: '请输入版本名称'
        });
        return;
      }
      const name = version.editName;
      version.displayName = name;
      version.fixedName = name;
      version.editName = '';
      version.editNameStatus = false;
    },
    statusAwaitRun() {
      return this.statusIsAwaitPub() || this.statusIsDebug();
    },
    statusIsRun() {
      return this.statusIsPub() || this.statusIsPause() || this.statusIsDongJie();
    },
    statusIsDraft() { // 草稿状态
      return this.addData.status === 1;
    },
    statusIsDebug() { // 调试状态
      return this.addData.status === 2;
    },
    statusIsPub() { // 发布状态
      return this.addData.status === 3;
    },
    statusIsStop() { // 结束状态
      return this.addData.status === 4;
    },
    statusIsDongJie() {
      return this.addData.status === 7;
    },
    statusIsAwaitPub() { // 待发布状态
      return this.addData.status === 5;
    },
    statusIsPause() { // 暂停状态
      return this.addData.status === 6;
    },
    blurInput(version) {
      console.log('blur trigger');
      setTimeout(() => {
        if (this.isConfirm) {
          return;
        }
        version.editName = '';
        version.editNameStatus = false;
      }, 150);
    },
    handleDeleteVersion(index) {
      if (this.statusIsDebug() ||
        (this.statusIsRun() && !this.isExtend) ||
        this.isCheck
      ) {
        return;
      } else if (this.statusIsAwaitPub() || this.isExtend) {
        const versions = this.addData.versionList,
          trueVersions = versions.slice(1),
          trueVerLen = trueVersions.length;
        if (trueVerLen == 2) {
          this.$message.warning('至少保留两个版本');
          return;
        }
        this.$store.commit('deleteVersion', { index });
      } else {
        this.$store.commit('deleteVersion', { index });
      }
    },
    isJson(str) {
      if (typeof str == 'string') {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
            return false;
          }
        } catch (e) {
          console.log('error：' + str + '!!!' + e);
          return false;
        }
      } else {
        this.$message.error('请输入字符串');
      }
    },
    handleJsonClose() { },
    /**
     * @description: json编辑成功
     * @param {*} e
     * @return {*}
     */
    handleJsonSucceed(e) {
      let verIndex = this.indexs[0];
      let feaIndex = this.indexs[1];
      this.addData.versionList[verIndex].featureList[feaIndex].keyValue = JSON.stringify(e);
    },
    /**
     * @description: 校验输入值合法性
     * @param {*} index
     * @param {*} idx
     * @return {*}
     */
    handleJsonInput(val) {
      if (!val) {
        return;
      } else if (!this.isJson(val)) {
        this.$message.error('请输入正确的JSON字符串');
      }
      // index version 编号 idx feature 编号
      // 数据结构是竖着的
      // console.log("index, idx", index, idx);
    },
    /**
 * @description: icon点击
 * @param {*} index
 * @param {*} idx
 * @return {*}
 */
    handleEditJson(index, idx) {
      if (this.isCodeTest && this.statusIsRun()) {
        return;
      }
      this.indexs = [index, idx];
      let currentVal = this.addData.versionList[index].featureList[idx].keyValue;
      if (currentVal && !this.isJson(currentVal)) {
        this.$message.error('请输入正确的JSON字符串');
        return;
      }
      if (currentVal) {
        this.jsonValue = JSON.parse(currentVal);
      } else {
        this.jsonValue = {};
      }
      this.isShowDialog = true;
    },
    beforeImageUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      const isImg = 'png,jpg,jpeg'.indexOf(file.type.split('/')[1]) !== -1;

      if (!isImg) {
        this.$message.error('上传图片只能是 JPG、JPEG、PNG 格式!');
      }

      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isLt2M && isImg;
    },
  }
};
</script>
<style>
.cascader-item-name {
  max-width: 88px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  vertical-align: middle;
}

.el-icon-check {
  /* width: 32px; */
  /* height: 32px; */
  /* background-color: blue; */
  width: 36px !important;
  height: 36px !important;
  line-height: 36px !important;
  margin-left: 10px !important;
  color: white !important;
}
</style>