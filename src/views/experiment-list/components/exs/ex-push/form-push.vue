<template>
  <el-form ref="form" :model="formData" label-width="84px" label-suffix="：">
    <el-form-item label="实验名称">
      <el-input
        v-model="formData.displayName"
        :disabled="formData.displayName === '对照版本' || disabled"
      ></el-input>
    </el-form-item>

    <el-form-item label="是否推送">
      <el-switch v-model="formData.isPush" :disabled="disabled"></el-switch>
    </el-form-item>

    <template v-if="formData.isPush">
      <el-form-item label="推送通道">
        <el-select
          v-model="formData.pushWay"
          class="select_pushWay"
          placeholder="请选择"
          :disabled="disabled"
        >
          <el-option
            v-for="item in pushOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="推送平台">
        <el-checkbox-group v-model="formData.pushPlatform" :disabled="disabled">
          <el-checkbox label="android">Android</el-checkbox>
          <el-checkbox label="ios">iOS</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item v-if="formData.afterAction === 1" label="推送标题">
        <el-input
          v-model="formData.pushTitle"
          placeholder="请填写推送标题"
          maxlength="40"
          :disabled="disabled"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item v-if="formData.afterAction === 1" label="推送内容">
        <el-input
          v-model="formData.pushContent"
          placeholder="请填写推送内容"
          type="textarea"
          maxlength="100"
          show-word-limit
          rows="2"
          :disabled="disabled"
        ></el-input>
      </el-form-item>

      <el-form-item v-if="formData.afterAction === 2" label="描述">
        <el-input
          v-model="formData.pushDesc"
          placeholder="只用于后续查找，类似备注"
          type="textarea"
          maxlength="100"
          show-word-limit
          rows="2"
          :disabled="disabled"
        ></el-input>
      </el-form-item>

      <el-form-item label="后续动作">
        <el-radio-group v-model="formData.afterAction" :disabled="disabled">
          <el-radio :label="1">启动应用</el-radio>
          <el-radio :label="2">自定义行为</el-radio>
        </el-radio-group>
        <div v-if="formData.afterAction === 2" class="card_box">
          <div class="card_title">自定义数据</div>
          <div v-for="(item, index) in formData.fields" :key="index" class="field_box">
            <el-input
              class="input_field"
              v-model="item.name"
              placeholder="字段名"
              :disabled="disabled"
            ></el-input>
            <el-input
              class="input_field"
              v-model="item.value"
              placeholder="字段取值"
              :disabled="disabled"
            ></el-input>
            <i
              v-if="formData.fields.length > 1 && !disabled"
              class="el-icon-delete delete-icon"
              @click="handleDelete(index)"
            ></i>
            <div v-else :style="{ 'min-width': '14px' }"></div>
          </div>
          <div v-if="!disabled" class="card_btn_add" @click="handeldd">+ 增加参数队</div>
        </div>
      </el-form-item>

      <el-form-item v-if="formData.pushWay.length" label="通知提醒">
        <div v-if="android" class="card_box">
          <div class="card_title">Android</div>
          <div class="card_line">
            <el-checkbox-group v-model="formData.notice.way" :disabled="disabled">
              <el-checkbox :label="1">响铃</el-checkbox>
              <el-checkbox :label="2">震动</el-checkbox>
            </el-checkbox-group>
          </div>
          <div class="card_line">
            <span class="label">通知渠道重要性：</span>
            <el-select
              v-model="formData.notice.important"
              class="select_important"
              :disabled="disabled"
            >
              <el-option
                v-for="item in [0, 1, 2, 3, 4]"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </div>
          <el-checkbox v-model="formData.notice.clear" :disabled="disabled">通知可清除</el-checkbox>
        </div>

        <div v-if="ios" class="card_box">
          <div class="card_title">iOS</div>
          <span class="label">角标数字：</span>
          <el-input-number
            v-model="formData.notice.mark"
            class="input_mark"
            controls-position="right"
            :disabled="disabled"
          ></el-input-number>
        </div>
      </el-form-item>
    </template>

    <el-form-item label="频控规则">
      <el-radio-group v-model="formData.frequency" :disabled="disabled">
        <el-radio :label="1">全局频控</el-radio>
        <el-radio :label="2">无频控</el-radio>
      </el-radio-group>
      <div v-if="formData.frequency === 1" class="frequency_tips">
        每个用户1天内，最多能接受
        <span>20</span>
        条推送消息，且每小时最多
        <span>1</span>
        条
      </div>
    </el-form-item>

    <el-form-item label="推送时机">
      <el-radio-group v-model="formData.pushTimeWay" :disabled="disabled">
        <el-radio :label="1">单次立即推送</el-radio>
        <el-radio :label="2">定时推送</el-radio>
      </el-radio-group>
      <el-alert
        v-if="formData.pushTimeWay === 1"
        title="单次立即推送，预计在1分钟后触达终端用户。"
        type="info"
        :closable="false"
      ></el-alert>
    </el-form-item>

    <template v-if="formData.pushTimeWay === 2">
      <el-form-item label="重复例行">
        <el-switch v-model="formData.isRepeat" :disabled="disabled"></el-switch>
      </el-form-item>

      <el-form-item v-if="!formData.isRepeat" label="时间">
        <div class="flex_box">
          <el-date-picker
            v-model="formData.pushTime"
            class="picker_hours"
            type="datetime"
            placeholder="选择日期时间"
            :clearable="false"
            :disabled="disabled"
          ></el-date-picker>
        </div>
      </el-form-item>

      <el-form-item v-if="formData.isRepeat" label="触发周期">
        <div class="flex_box">
          <el-select v-model="formData.pushCycle" class="select_cycle" :disabled="disabled">
            <el-option
              v-for="item in cycleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <el-time-picker
            v-model="formData.pushTime"
            class="picker_hours"
            placeholder="请选择时间"
            :clearable="false"
            :disabled="disabled"
          ></el-time-picker>
        </div>
        <el-select
          v-if="['week', 'month'].includes(formData.pushCycle)"
          v-model="formData.pushCycleValue"
          class="select_pushCycleValue"
          placeholder="请选择"
          multiple
          :disabled="disabled"
        >
          <el-option
            v-for="item in cycleValueOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-if="formData.isRepeat" label="起止时间">
        <el-date-picker
          v-model="formData.pushDay"
          class="picker_pushDay"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="false"
          :disabled="disabled"
        ></el-date-picker>
      </el-form-item>
    </template>
  </el-form>
</template>

<script>
export default {
  props: {
    formData: Object,
    status: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      pushOptions: [
        {
          value: '选项1',
          label: '黄金糕'
        },
        {
          value: '选项2',
          label: '双皮奶'
        },
        {
          value: '选项3',
          label: '蚵仔煎'
        },
        {
          value: '选项4',
          label: '龙须面'
        },
        {
          value: '选项5',
          label: '北京烤鸭'
        }
      ],
      cycleOptions: [
        {
          label: '每日',
          value: 'day'
        },
        {
          label: '每周',
          value: 'week'
        },
        {
          label: '每月',
          value: 'month'
        }
      ],
      weeks: [
        {
          label: '星期一',
          value: 1
        },
        {
          label: '星期二',
          value: 2
        },
        {
          label: '星期三',
          value: 3
        },
        {
          label: '星期四',
          value: 4
        },
        {
          label: '星期五',
          value: 5
        },
        {
          label: '星期六',
          value: 6
        },
        {
          label: '星期日',
          value: 7
        }
      ]
    };
  },
  computed: {
    isCheck() {
      return this.$route.query.type === 'check';
    },
    disabled() {
      return this.status === 3 || this.isCheck;
    },
    android() {
      const { afterAction, pushPlatform } = this.formData;
      return afterAction === 1 && pushPlatform.includes('android');
    },
    ios() {
      const { pushPlatform } = this.formData;
      return pushPlatform.includes('ios');
    },
    cycleValueOptions() {
      if (this.formData.pushCycle === 'week') return this.weeks;
      const opts = [];
      for (let i = 1; i <= 31; i++) {
        opts.push({
          label: `${i}日`,
          value: i
        });
      }
      return opts;
    }
  },
  watch: {
    'formData.pushCycle'() {
      this.formData.pushCycleValue = [1];
    }
  },
  methods: {
    handeldd() {
      this.formData.fields.push({
        name: '',
        value: ''
      });
    },
    handleDelete(index) {
      this.formData.fields.splice(index, 1);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.el-form-item__label {
  font-weight: 700;
}

/deep/.el-textarea .el-input__count {
  background: transparent;
  bottom: 0;
}

.frequency_tips {
  color: #606266;
  font-size: 13px;

  span {
    padding: 5px 10px;
    margin: 0 5px;
    border: 1px solid #e7e9f5;
    border-radius: 4px;
  }
}

.card_box {
  border: 1px dashed #6bb8ef;
  border-radius: 4px;
  background-color: #e9f6fd;
  padding: 12px 24px;
  line-height: 1;
  background: #42c57a14;
  border-color: #42c57a;

  &:first-child {
    margin-bottom: 24px;
  }
}

.card_title {
  color: @zyb-green-1;
  margin-bottom: 12px;
}

.card_line {
  margin-bottom: 12px;
}

.card_btn_add {
  color: #606266;
  margin-right: 26px;
  background-color: #fff;
  border: 1px dashed #e7e9f5;
  text-align: center;
  height: 32px;
  line-height: 32px;
  box-sizing: border-box;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    color: @zyb-green-hover;
    border-color: @zyb-green-hover;
  }
}

.field_box {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.input_field {
  & + & {
    margin: 0 12px 0 24px;
  }
}

.delete-icon {
  cursor: pointer;
  color: @zyb-green-1;

  &:hover {
    color: @zyb-green-hover;
  }
}

.label {
  margin-right: 12px;
  color: #606266;
}

.select_pushWay {
  width: 100%;
}

.select_pushCycleValue {
  width: 100%;
  margin-top: 12px;
}

.picker_pushDay {
  width: 100% !important;
}

.select_important,
.input_mark {
  width: 120px;
}

.flex_box {
  display: flex;
}

.select_cycle {
  width: 80px;
  margin-right: 24px;
}

.picker_hours {
  flex: 1;
}
</style>