<template>
  <div class="ex_push">
    <div class="left">
      <div class="tabs_action">
        <el-dropdown size="default" @command="handleCommand">
          <div :class="['tabs_action_btn', { btn_hide: !scrollable }]">
            <i class="el-icon-more" />
          </div>
          <el-dropdown-menu slot="dropdown" class="dropdown">
            <el-dropdown-item
              v-for="item in versionList"
              :key="item.tabName"
              :command="item.tabName"
              :disabled="current === item.tabName"
            >
              {{ item.displayName }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <div class="tabs_action_btn" @click="handleTabsAdd">
          <i class="el-icon-plus" />
        </div>
      </div>
      <el-tabs
        ref="tabs"
        v-model="current"
        type="card"
        :class="{ hide_delete: versionList.length <= 2 }"
        @tab-remove="handleTabsRemove"
        closable
      >
        <el-tab-pane
          v-for="item in versionList"
          :key="item.tabName"
          :name="item.tabName"
          :label="beautySub(item.displayName)"
        >
          <form-push :form-data="item" :status="addData.status" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div :class="['right', { right_hide: !showRight }]">
      <img class="iphone" :src="iphone" />
      <div class="iphone_title">{{ title }}</div>
      <div class="iphone_content">{{ content }}</div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Iphone from '@/static/images/ex_iphone.png';
import FormPush from './form-push.vue';
import { debounce } from 'lodash';

export default {
  components: { FormPush },
  data() {
    return {
      iphone: Iphone,
      current: '',
      scrollable: false
    };
  },
  computed: {
    ...mapGetters(['addData']),
    versionList() {
      return this.addData.versionList.slice(1);
    },
    currentVersion() {
      return this.versionList.find((f) => f.tabName === this.current) || {};
    },
    showRight() {
      return this.currentVersion.isPush;
    },
    title() {
      return this.currentVersion.pushTitle || '标题';
    },
    content() {
      return this.currentVersion.pushContent || '内容';
    },
    aboutScrollable() {
      return {
        num: this.versionList.length,
        names: this.versionList.map((v) => v.displayName)
      };
    }
  },
  watch: {
    aboutScrollable: {
      handler(newVal) {
        this.setScrollable();
      },
      immediate: true
    }
  },
  methods: {
    setScrollable: debounce(function () {
      this.scrollable = this.$refs.tabs.$refs.nav.scrollable;
    }, 200),
    handleCommand(command) {
      this.current = command;
    },
    handleTabsAdd() {
      this.$store.commit('addVersion', {
        callback: (data) => {
          this.current = data.tabName;
        }
      });
    },
    handleTabsRemove(tabName) {
      this.$confirm('确认删除此版本吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let tabs = this.versionList;
          const index = tabs.findIndex((f) => f.tabName === tabName);
          if (this.current === tabName) {
            const nextTab = tabs[index + 1] || tabs[index - 1];
            this.current = nextTab.tabName;
          }
          this.$store.commit('deleteVersion', { index: index + 1 });
        })
        .catch(() => {});
    },
    beautySub(str, len = 6) {
      let reg = /[\u4e00-\u9fa5]/g,
        slice = str.substring(0, len),
        chineseCharNum = ~~(slice.match(reg) && slice.match(reg).length),
        realen = slice.length * 2 - chineseCharNum;
      const result = str.substr(0, realen) + (realen < str.length ? '...' : '');
      return result || '待命名';
    }
  },
  created() {
    // 设置默认tab
    if (this.$route.query.type === 'add') {
      this.current = this.versionList[0].tabName;
    } else {
      const unWatch = this.$watch('versionList', (newVal) => {
        this.current = newVal[0].tabName;
        unWatch();
      });
    }
  }
};
</script>

<style lang="less" scoped>
.ex_push {
  display: flex;
  justify-content: center;
}

.left {
  min-width: 400px;
  max-width: 600px;
  flex: 1;
  position: relative;
}

.tabs_action {
  height: 40px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tabs_action_btn {
  border: 1px solid #d3dce6;
  height: 18px;
  width: 18px;
  line-height: 18px;
  border-radius: 3px;
  text-align: center;
  color: #d3dce6;
  cursor: pointer;
  font-size: 12px;
  margin-left: 12px;
  transition: all 0.3s;
  overflow: hidden;

  &:hover {
    color: @zyb-green-hover;
  }
}

.btn_hide {
  width: 0;
  border: 0;
}

.dropdown {
  max-height: 300px;
  overflow-y: auto;
}

/deep/.el-tabs__item:first-child,
.hide_delete /deep/.el-tabs__item {
  &:hover {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }

  .el-icon-close {
    display: none;
  }
}

/deep/.el-tabs__nav-wrap {
  margin-right: 64px;
}

.right {
  padding-left: 100px;
  box-sizing: border-box;
  position: relative;
}

.right_hide {
  visibility: hidden;
}

.iphone {
  width: 300px;
  height: 600px;
}

.iphone_title {
  width: 225px;
  color: #606266;
  font-size: 14px;
  font-weight: bold;
  position: absolute;
  top: 242px;
  left: 135px;
  z-index: 10;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.iphone_content {
  width: 225px;
  color: #606266;
  font-size: 12px;
  line-height: 1.5;
  position: absolute;
  top: 262px;
  left: 135px;
  z-index: 10;
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>