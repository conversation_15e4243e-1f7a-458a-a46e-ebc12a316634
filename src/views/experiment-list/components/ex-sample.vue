<template>
  <div class="steps-content" @click="handleDocumentClick('metrics')">
    <h3>关注指标</h3>
    <div class="version-create">
      <el-button type="primary" @click="addMetric" v-if="!isCheck" :disabled="isCheck">添加指标</el-button>
    </div>
    <div class="metric-con">
      <span class="el-icon-warning-outline"></span>
      关注的指标可以衡量实验成功与否，实验开始后，在实验报告页面可以看到关注指标在实验版本和对照版本的对比。
    </div>
    <div class="ex-table metric-con" v-if="isAdd">
      <el-table :data="tableData">
        <el-table-column label="指标名称" align="center" prop="name"></el-table-column>
        <el-table-column label="指标描述" align="center" prop="description"></el-table-column>
        <el-table-column label="指标类型" align="center" prop="type">
          <template slot-scope="scope">
            <template v-if="scope.row.type">
              {{ scope.row.type }}
            </template>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column label="是否为核心指标" align="center" prop="isCore">
          <template slot="header">
            <el-tooltip effect="light" placement="top">
              <div slot="content">
                <p style="font-size: 14px">
                  核心指标，指的是用来决策实验功能是否符合预期的「直接效果指标」
                  ，也叫「成功指标」。一个实验只可设置1个核心指标，可在实验报告查看该指标的数据。
                </p>
              </div>
              <span class="core-span" style="font-size: 14px">
                是否为核心指标
                <i class="el-icon-question table-icon"></i>
              </span>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <template v-if="scope.row.isCore">-</template>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button class="op-btn" :disabled="isCheck" type="text" @click="handleRemove(scope.row.id)">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="ex-table metric-con" v-if="isEdit || isCheck">
      <el-table :data="tableData1">
        <el-table-column label="指标名称" align="center" prop="name"></el-table-column>
        <el-table-column label="指标描述" align="center" prop="description"></el-table-column>
        <el-table-column label="指标类型" align="center" prop="type">
          <template slot-scope="scope">
            <template v-if="scope.row.type">
              {{ scope.row.type }}
            </template>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column label="是否为核心指标" align="center" prop="isCore">
          <template slot="header">
            <el-popover trigger="hover" placement="top" width="230">
              <p class="popoverContent" style="font-size: 14px">
                核心指标，指的是用来决策实验功能是否符合预期的「直接效果指标」
                ，也叫「成功指标」。一个实验只可设置1个核心指标，可在实验报告查看该指标的数据。
              </p>
              <span slot="reference" class="core-span" style="font-size: 14px">
                是否为核心指标
                <i class="el-icon-question table-icon"></i>
              </span>
            </el-popover>
          </template>
          <template slot-scope="scope">
            <template v-if="scope.row.isCore">-</template>
            <template v-else>-</template>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button class="op-btn" :disabled="scope.row.status == 4 || isCheck" type="text" @click="handleRemove(scope.row.id)">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="metric-con ci-con">
      <p>置信水平</p>
      <el-popover trigger="hover" placement="top" width="230">
        <p class="popoverContent" style="font-size: 14px">
          置信水平（也称置信度、置信系数、统计显著性），是指实验组与对照组之间存在真正性能差异的概率。例如在置信水平是95%的情况下，如果某个实验指标的置信度p值&lt;0.05，则说明这个指标相比对照组，是有显著(超过置信水平)差异的。
        </p>
        <i slot="reference" class="el-icon-question basic-set-icon-question"></i>
      </el-popover>
      <span class="ci-val">{{ CI }}</span>
    </div>
    <modify-dialog v-if="modifyVisible" :metricId="metricId" :dialog-visible.sync="modifyVisible" :businessOptions="appList" @fetchMmetricList="fetchMmetricList" />
    <choose-metric-dialog v-if="chooseMetricDialog" :visible.sync="chooseMetricDialog" :list="metricData" :selectedList="isAdd ? addData.selectedMetric : seltedData" @update="updateSelectedMetric"></choose-metric-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import ModifyDialog from './metric-dialog/index.vue';
import ChooseMetricDialog from './choose-metric-dialog.vue';
export default {
  name: 'Exmetric',
  components: {
    ModifyDialog,
    ChooseMetricDialog
  },
  props: {
    exType: Number
  },
  data() {
    return {
      chooseMetricDialog: false,
      seltedData: [],
      // tableData1: [],
      metricData1: [],
      modifyVisible: false,
      metricId: 0,
      CI: '95%',
      tableList: [],
      hasOpAdded: false,
      labelPosition: 'top',
      options: [], // 受众规则
      isAdd: this.$route.query.type === 'add',
      isEdit: this.$route.query.type === 'edit',
      isCheck: this.$route.query.type === 'check'
    };
  },
  computed: {
    ...mapGetters(['addData', 'metricData', 'appList'], 'message'),
    tableData() {
      if (this.isAdd) {
        const data = [];
        //debugger;
        this.addData.selectedMetric &&
          this.addData.selectedMetric.forEach((item) => {
            this.metricData.forEach((met) => {
              if (item === met.id) {
                data.push(met);
              }
            });
          });
        return data;
      }
    },
    tableData1() {
      if (this.isEdit || this.isCheck) {
        const data = [];
        this.seltedData &&
          this.seltedData.forEach((item) => {
            this.metricData.forEach((met) => {
              if (item === met.id) {
                data.push(met);
              }
            });
          });
        return data;
      }
    }
  },
  created() {},
  watch: {
    'addData.indicators': {
      handler(a, b) {
        if ((this.isEdit && a) || (this.isCheck && a)) {
          const selectedMetric = []; // 选中的id
          for (let i = 0, len = this.addData.indicators.length; i < len; i++) {
            const curr = this.addData.indicators[i];
            selectedMetric.push(curr.indId); // select选中的
          }
          this.seltedData = selectedMetric;
        }
      }
    },
  },
  mounted() { },
  methods: {
    handleDocumentClick(key) {
      this.$eventbus.$emit('exp-document', key);
    },
    addMetric() {
      this.chooseMetricDialog = true;
    },
    updateSelectedMetric(list) {
      console.log('updateSelectedMetric', list);
      if (this.isAdd) {
        this.addData.selectedMetric = [...list];
      } else {
        this.seltedData = [...list];
      }
    },
    statusIsPub() { // 发布状态
      return this.addData.status === 3;
    },
    editMtric(id) {
      this.metricId = id;
      this.modifyVisible = true;
    },
    createMetric() {
      this.metricId = 0;
      this.modifyVisible = true;
    },
    fetchMmetricList() {
      this.$store.dispatch('getMetricList');
    },
    handleComfirm() { },
    // 删除指标项
    removeTag(val) {
      this.handelRemoveTagDetail(val);
    },
    // 移除指标的具体逻辑
    handelRemoveTagDetail(val) {
      console.log(val);
      let temp = [];
      this.metricData.forEach((item) => {
        if (item.isMust === 1) {
          temp.push(item.id);
        }
      });
      if (this.isAdd) {
        const selectedMetric = this.addData.selectedMetric;
        // 兼容在select删除
        if (temp.includes(val) && !selectedMetric.includes(val)) {
          //debugger;
          this.$message.warning('该指标为必看指标,不可删除');
          const pos = temp.indexOf(val);
          this.addData.selectedMetric.splice(pos, 0, val); // 此处是为了把删除值插入回去
          return;
          //debugger;
        }
        // 兼容从列表中直接删除
        if (temp.includes(val)) {
          //debugger;
          this.$message.warning('该指标为必看指标,不可删除');
          return;
          //debugger;
        }
        //debugger;
        this.$store.commit('delMetricTableData', val);
        // if (!temp.includes(val) && selectedMetric.length === temp.length) {
        //   //debugger;
        //   this.$message.warning('至少需要一个可以计算的置信指标作为核心指标');
        //   this.addData.selectedMetric.push(val);
        // }
      } else {
        const selectedMetric = this.seltedData;
        // 兼容在select删除
        if (temp.includes(val) && !selectedMetric.includes(val)) {
          //debugger;
          this.$message.warning('该指标为必看指标,不可删除');
          const pos = temp.indexOf(val);
          this.seltedData.splice(pos, 0, val); // 此处是为了把删除值插入回去
          return;
          //debugger;
        }
        // 兼容从列表中直接删除
        if (temp.includes(val)) {
          //debugger;
          this.$message.warning('该指标为必看指标,不可删除');
          return;
          //debugger;
        }
        //debugger;
        selectedMetric.forEach((item, index) => {
          if (item === val) {
            selectedMetric.splice(index, 1);
          }
        });
        // this.tableData1.forEach((item,index)=>{
        //   if(item.id == val){
        //     this.tableData1.splice(index,1)
        //   }
        // })
      }
    },
    //
    handleRemove(val) {
      this.handelRemoveTagDetail(val);
    }
  }
};
</script>

<style lang="less" scoped>
.steps-content {
  margin-top: 40px;
  padding: 24px 20px;
  border: 1px solid #e1e2e6;
  border-radius: 2px;

  >h3 {
    padding: 0 0 12px 0;
    font-size: 16px;
    font-weight: 700;
  }

  .add-con {
    display: flex;
    justify-content: flex-end;
  }

  .version-create {
    display: flex;
    overflow: hidden;
    padding: 0 35px;
  }

  .s-slect {
    min-width: 500px;
  }

  .metric-con {
    margin: 20px 0 0;
    padding: 0 35px;
    font-size: 14px;
  }

  .create-metric {
    margin-left: 10px;
  }

  /dee/ .el-table th .core-span {
    font-size: 14px;
  }

  /deep/ .el-tag.is-disabled {
    //background-color: transparent;
    border-color: #f0f1f5;
    color: #999;
    cursor: not-allowed;
  }

  /deep/ .op-add .el-button.is-disabled,
  /deep/ .op-delete .el-button.is-disabled {
    background-color: transparent;
  }

  /deep/ .el-textarea.is-disabled .el-textarea__inner,
  /deep/ .el-input.is-disabled .el-input__inner {
    //background-color: transparent;
    border-color: #e1e2e6;
    color: #999;
    //cursor: not-allowed;
  }

  /deep/ .el-tooltip__popper.is-light {
    div {
      font-size: 14px;
    }
  }

  .ci-con {
    display: flex;
  }

  .ci-val {
    margin: 0 2px;
  }

  /deep/ .op-switch.is-disabled .el-switch__core,
  /deep/ .op-switch.is-disabled .el-switch__label {
    background-color: rgb(155, 156, 158);
    border-color: rgb(155, 156, 158);
    cursor: pointer;
  }

  /deep/ .el-switch.is-disabled.is-checked .el-switch__core,
  /deep/ .op-switch.is-disabled.is-checked .el-switch__label {
    border-color: #42c57a;
    background-color: #42c57a;
  }

  /deep/ .op-btn span {
    font-size: 14px;
  }
}

/deep/ .el-table {
  font-size: 14px;
}

/deep/ .el-select .el-tag {
  font-size: 14px;
}
</style>