<template>
  <section style="margin: 0 16px">
    <el-form>
      <el-form-item label="实验类型:">
        <el-radio-group v-model="formData.type" @change="typeChange">
          <el-radio :label="0">无权限控制</el-radio>
          <el-radio :label="1">公共实验</el-radio>
          <el-radio :label="2">私有实验</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.type === 0">
        <el-alert
          title="无权限控制：所有有该业务线权限的人员皆有查看/协作权限。"
          type="warning"
          show-icon
          :closable="false"
        ></el-alert>
      </el-form-item>
      <el-form-item v-if="formData.type === 1">
        <el-alert
          title="公共实验，即拥有实验列表查看权限的用户均可查看该实验，管理员及实验负责人拥有该实验的编辑权限，如需添加某用户为该实验协作者，将该用户设置为该实验负责人或添加协作者权限即可。"
          type="warning"
          show-icon
          :closable="false"
        ></el-alert>
      </el-form-item>
      <el-form-item v-if="formData.type === 2">
        <el-alert
          title="私有实验，即无权限的用户和部门无法在实验列表查看该实验，如需查看/编辑实验信息，请分配对应权限或将用户设置为实验负责人。"
          type="warning"
          show-icon
          :closable="false"
        ></el-alert>
      </el-form-item>
    </el-form>
    <el-row v-if="formData.type !== 0">
      <el-col :span="24"></el-col>
      <el-col :span="24">
        <el-tabs v-model="activeType">
          <el-tab-pane label="用户" name="list"></el-tab-pane>
          <el-tab-pane label="部门" name="deptList"></el-tab-pane>
        </el-tabs>
        <el-button type="primary" size="small" @click="add" style="margin-bottom: 12px">
          添加协作者
        </el-button>

        <el-table max-height="193px" :data="formData[activeType]">
          <el-table-column prop="uname" label="用户" v-if="activeType === 'list'">
            <template slot-scope="scope">
              <template v-if="scope.row.id">
                <span>{{ scope.row.realName || scope.row.uname }}</span>
              </template>
              <template v-else>
                <el-select
                  v-model="scope.row.uname"
                  filterable
                  remote
                  placeholder="请选择用户"
                  :remote-method="remoteMethod"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.uname"
                    :label="item.uname"
                    :value="item.uname"
                  ></el-option>
                </el-select>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="部门" v-if="activeType === 'deptList'">
            <template slot-scope="scope">
              <template v-if="scope.row.id">
                <span>{{ scope.row.deptShortName }}</span>
              </template>
              <template v-else>
                <el-select
                  v-model="scope.row.deptId"
                  filterable
                  remote
                  placeholder="请输入部门ID"
                  :remote-method="remoteQueryDep"
                  @change="handleDepChange(scope.row)"
                >
                  <el-option
                    v-for="item in depOptions"
                    :key="item.deptId"
                    :label="item.deptShortName"
                    :value="item.deptId"
                  ></el-option>
                </el-select>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="authLevel" label="查看权限" width="80" v-if="formData.type === 2">
            <template slot-scope="scope">
              <el-switch :value="true" :disabled="true"></el-switch>
            </template>
          </el-table-column>
          <el-table-column
            prop="authLevel"
            label="协作人权限"
            width="90"
            v-if="formData.type === 2"
          >
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.authLevel"
                :active-value="2"
                :inactive-value="1"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" min-width="170">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime ? dateTimeFormat(scope.row.createTime) : '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="操作" width="100">
            <template slot-scope="scope">
              <el-button type="text" @click="remove(scope.$index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <section style="margin-top: 24px; height: 32px;">
      <section :span="24" style="float: right;">
        <el-button @click="close()">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </section>
    </section>
  </section>
</template>
<script>
import dayjs from 'dayjs';

export default {
  name: 'ex-permission',
  components: {},
  data() {
    return {
      activeType: 'list',
      formData: {
        type: 0,
        list: [],
        oldType: 0,
        oldList: [],
        deptList: []
      },
      options: [],
      depOptions: []
    };
  },
  props: {
    id: {
      type: Number
    }
  },
  created() {
    this.getDetail();
  },
  methods: {
    typeChange() {
      if (this.formData.type === this.formData.oldType) {
        this.formData.list = this.formData.oldList.slice();
      } else {
        this.formData.list = [];
      }
    },
    getDetail() {
      this.$service
        .get('GETEXPERMISSION', {
          id: this.id
        })
        .then(res => {
          const { list = [], type = 0, deptList = [] } = res;
          this.formData = {
            type: type,
            list: list,
            oldType: type,
            oldList: list,
            deptList
          };
        });
    },
    remove(index) {
      const listKey = this.activeType;
      const list = this.formData[listKey];
      list.splice(index, 1);
    },
    add() {
      const listKey = this.activeType;
      const list = this.formData[listKey];
      list.unshift({
        uname: '',
        realName: '',
        deptId: '',
        deptShortName: '',
        authLevel: this.formData.type === 1 ? 2 : 1
      });
    },
    remoteMethod(query) {
      this.$service.get('GETUNAMEINFO', { uname: query }).then(res => {
        this.options = res || [];
      });
    },
    remoteQueryDep(query) {
      this.$service.get('GETDEPTINFO', { deptName: query }).then(res => {
        this.depOptions = res || [];
      });
    },
    handleDepChange(row) {
      const dept = this.depOptions.find(item => item.deptId === row.deptId);
      row.deptShortName = dept.deptShortName;
    },
    dateTimeFormat(value) {
      const res = dayjs.unix(value).format('YYYY-MM-DD HH:mm:ss');
      return res;
    },
    formatFormData(data) {
      const formData = new FormData();
      for (const key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    close(refresh = false) {
      this.$emit('close', refresh);
    },
    confirm() {
      const formData = this.formatFormData({
        ...this.formData,
        id: this.id
      });
      this.$service
        .post('UPDATEEXPERMISSION', formData, { allback: 0, needLoading: true })
        .then(res => {
          this.close(true);
        });
    }
  }
};
</script>
