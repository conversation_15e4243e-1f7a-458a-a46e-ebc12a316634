<template>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="500px" @close="handleClose" class="cron-dialog">
        <!-- Tips alert -->
        <el-alert v-if="tips" :title="tips" type="warning" :closable="false" show-icon />

        <!-- Form content -->
        <el-form ref="form" :model="form" label-width="100px" class="cron-form">
            <!-- Start type selection -->
            <el-form-item :label="text + '方式'">
                <el-radio-group v-model="form.type" @change="handleTypeChange">
                    <el-radio :label="1">立即{{ text }}</el-radio>
                    <el-radio :label="2">指定时间{{ text }}</el-radio>
                </el-radio-group>
            </el-form-item>

            <!-- Time picker for scheduled start -->
            <el-form-item :label="text + '时间'" v-if="form.type === 2">
                <el-date-picker v-model="form.taskTime" type="datetime" value-format="timestamp"
                    :placeholder="'选择' + text + '时间'" :picker-options="pickerOptions" style="width: 100%" />
            </el-form-item>
        </el-form>

        <!-- Dialog footer -->
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </span>
    </el-dialog>
</template>

<script>
export default {
    name: 'ExCron',
    props: {
        // Dialog visibility
        visible: {
            type: Boolean,
            default: false
        },
        // Custom title
        title: {
            type: String,
            default: '定时任务设置'
        },
        // Tips content
        tips: {
            type: String,
            default: ''
        },
        text: {
            type: String,
            default: '开始'
        }
    },
    data() {
        return {
            dialogVisible: this.visible,
            form: {
                type: 1, // 1: 立即开始, 2: 指定时间开启
                taskTime: new Date().getTime()
            },
            pickerOptions: {
                disabledDate(time) {
                    // Disable past dates
                    return time.getTime() < Date.now() - 8.64e7;
                }
            }
        };
    },
    watch: {
        visible(newVal) {
            this.dialogVisible = newVal;
            if (newVal) {
                // Reset form when dialog opens
                this.resetForm();
            }
        }
    },
    methods: {
        handleClose() {
            this.dialogVisible = false;
            this.$emit('update:visible', false);
        },
        handleConfirm() {
            // Validate form before submitting
            if (this.form.type === 2 && !this.form.taskTime) {
                this.$message.warning('请选择开启时间');
                return;
            }

            // Emit confirm event with form data
            this.$emit('confirm', {
                taskTime: Math.floor(this.form.taskTime / 1000)
            });
            this.handleClose();
        },
        handleTypeChange() {
            this.form.taskTime = this.form.type === 1 ? 0 : new Date().getTime();
        },
        resetForm() {
            this.form = {
                type: 1,
                taskTime: 0
            };
        }
    }
};
</script>

<style scoped>
.cron-form {
    margin-top: 20px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}
</style>
<style lang="less">
.cron-dialog {
    .el-dialog__body {
        padding: 0 20px!important;
    }
    .el-dialog__footer{
        padding-top: 0!important;
    }
}
</style>