<template>
  <div>
    <el-drawer
      :before-close="handleClose"
      :visible.sync="showDrawer"
      :with-header="false"
      direction="rtl"
      size="50%"
    >
      <div class="history-page">
        <section class="header">
          <p style="font-size: 16px; font-weight: 500;  line-height: 1.5">
            查看血缘
          </p>
          <i class="el-icon-close close-icon" @click.stop="handleClose"></i>
        </section>

        <div class="parent-child-container flex justify-center items-center">
          <div class="parent-experiment">
            <el-popover placement="top" trigger="hover">
              <div>
                <p style="margin-bottom: 8px; font-weight: bold">父实验信息</p>
                <p style="line-height: 24px;">
                  实验ID: {{ parentExperiment ? parentExperiment.id : '' }}
                </p>
                <p style="line-height: 24px;">
                  实验名称: {{ parentExperiment ? parentExperiment.displayName : '' }}
                </p>
                <p style="line-height: 24px;">实验状态: {{ handleStatus(parentExperiment) }}</p>
                <p style="line-height: 24px;">
                  运行时长: {{ parentExperiment ? parentExperiment.timeStr : '' }}
                </p>
              </div>
              <div
                slot="reference"
                class="experiment-box"
                @click="navigateToExperiment(parentExperiment)"
              >
                {{ parentExperiment ? parentExperiment.displayName : '父实验名称' }}
              </div>
            </el-popover>
          </div>

          <!-- Connection line with arrows -->
          <div class="connection-container flex justify-center">
            <div class="connection-line"></div>
            <div class="children-container flex gap-y-4">
              <div
                v-for="experiment in childExperiments"
                :key="experiment.id"
                class="experiment-box-wrapper"
              >
                <div class="arrow-line"></div>

                <el-popover placement="top" trigger="hover">
                  <div>
                    <p style="margin-bottom: 8px; font-weight: bold">子实验信息</p>
                    <p style="line-height: 24px;">实验ID: {{ experiment ? experiment.id : '' }}</p>
                    <p style="line-height: 24px;">
                      实验名称: {{ experiment ? experiment.displayName : '' }}
                    </p>
                    <p style="line-height: 24px;">实验状态: {{ handleStatus(experiment) }}</p>
                    <p style="line-height: 24px;">
                      运行时长: {{ experiment ? experiment.timeStr : '' }}
                    </p>
                  </div>
                  <div
                    slot="reference"
                    class="experiment-box"
                    :class="{ 'is-current': isCurrentExperiment(experiment) }"
                    @click="navigateToExperiment(experiment)"
                  >
                    {{ experiment.displayName }}
                  </div>
                </el-popover>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <el-tooltip class="item" effect="dark" content="血缘关系" placement="top">
      <img
        :src="Relation"
        alt="血缘关系"
        style="width: 15px; height: 15px; margin-right: 5px; margin-left: 5px; cursor: pointer"
        @click="openDrawer"
      />
    </el-tooltip>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Relation from '@/static/images/relation.png';
import { getUrlByLocation } from '../../../utils/util';
export default {
  name: 'ExRelation',
  components: {},
  data() {
    return {
      showDrawer: false,
      Relation
    };
  },
  computed: {
    ...mapGetters(['addData', 'statusMap'], 'message'),

    graphData() {
      // In production
      const list = this.addData.relationFlowMap || [];
      return list;
    },
    currentExpId() {
      return this.addData.id;
    },
    parentExperiment() {
      return this.graphData.length > 0 ? this.graphData[0] : null;
    },
    childExperiments() {
      // Return all experiments except the first one (parent)
      return this.graphData.length > 1 ? this.graphData.slice(1) : [];
    }
  },
  methods: {
    handleClose() {
      this.showDrawer = false;
    },
    openDrawer() {
      this.showDrawer = true;
    },

    isCurrentExperiment(experiment) {
      return experiment && experiment.id === this.currentExpId;
    },

    navigateToExperiment(experiment) {
      if (!experiment || this.isCurrentExperiment(experiment)) return;

      // Open in new window
      const hash = '/exp-manage/list/edit';
      const params = {
        id: experiment.id,
        type: 'check',
        ex: experiment.type || 1
      };
      if (experiment.parentId / 1) {
        params.parentId = experiment.parentId;
      }
      const url = getUrlByLocation(hash, params);
      window.open(url);
    },
    handleStatus(row) {
      const statusVal = this.statusMap[row.status];
      return statusVal;
    }
  }
};
</script>

<style lang="less" scoped>
.history-btn {
  cursor: pointer;
}

.history-page {
  overflow-y: auto;
  box-sizing: border-box;
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    i {
      cursor: pointer;
      font-size: 18px;
    }
  }
}

/* 血缘关系图表样式 */
.lineage-chart {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 20px;
  overflow-y: auto;
  max-height: 80vh;
}

.parent-child-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  flex-grow: 1;
}

.connection-container {
  display: flex;
  flex-direction: column;
  position: relative;
}

.connection-line {
  position: absolute;
  left: -2px;
  top: 14px;
  bottom: 14px;
  width: 2px;
  background-color: #42c57a;
}

.parent-experiment {
  position: relative;
  margin-right: 24px;
  &::after {
    content: '';
    position: absolute;
    right: -24px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 2px;
    background-color: #42c57a;
  }
}
.children-container {
  display: flex;
  flex-direction: column;
  margin-left: 20px;
  row-gap: 2rem;
}

.experiment-box-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.arrow-line {
  position: absolute;
  left: -20px;
  width: 20px;
  height: 2px;
  background-color: #42c57a;

  &::after {
    content: '';
    position: absolute;
    right: 0;
    top: -3px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 4px 0 4px 6px;
    border-color: transparent transparent transparent #42c57a;
  }
}

.experiment-box {
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #e2e4e8;
  transition: all 0.3s;
  min-width: 150px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
  position: relative;

  &:hover:not(.is-current) {
    border-color: #42c57a;
    color: #42c57a;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  &.is-current {
    background-color: #42c57a;
    color: white;
    border: 1px solid #42c57a;
    cursor: default;
  }

  .close-icon {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #909399;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover .close-icon {
    opacity: 1;
  }

  &.is-current .close-icon {
    color: white;
  }
}

/* 提示框样式 */
/deep/ .experiment-tooltip-popper {
  padding: 0 !important;

  .tooltip-custom {
    padding: 12px;
    min-width: 200px;

    .tooltip-title {
      font-weight: bold;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
    }

    .tooltip-content {
      p {
        margin: 5px 0;
        font-size: 12px;
      }

      .version-info {
        margin-left: 10px;
      }
    }
  }
}
</style>

<style lang="less">
.edge-line {
  width: 4px;
  background-color: blue;
}
</style>
