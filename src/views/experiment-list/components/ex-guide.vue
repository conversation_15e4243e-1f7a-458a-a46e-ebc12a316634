<template>
    <section justify="center">
        <t-guide :current.sync="current" :steps="realSteps" @finish="handleFinish" :highlightPadding="4"
            @skip="handleSkip" :skipButtonProps="{ content: '取消' }">
            <template #body>
                <div>
                    <h2 style="color: red;font-weight: bold;">
                        变更前：
                        <span v-if="currentStepData.name === 'rule'">原口径</span>
                        <span v-else>{{ currentStepData.oldValue }}</span>
                    </h2>
                    <p>{{ currentStepData.desc }}</p>
                </div>
            </template>
        </t-guide>
    </section>
</template>

<script>
import { Guide as TGuide } from 'tdesign-vue';
import { mapGetters } from 'vuex';

export default {
    components: {
        TGuide
    },
    data() {
        return {
            preData: null,
            current: -1,
            steps: [
                {
                    element: '.ex-item-duration',
                    title: '实验时长提示',
                    show: false,
                    desc: "",
                    oldValue: "",
                    placement: 'right-top',
                },
                {
                    element: '.processInput',
                    title: '实验缩量提示',
                    oldValue: '40%',
                    desc: '当前操作存在缩量操作，实验流量将从100%缩量到40%。原实验是进组不出组，只会影响未进过组的用户。已经进组的用户会继续命中原来的实验组，不受缩量的影响。实验缩量将影响实验报告科学性，导致实验报告数据不可用，请谨慎操作。',
                    placement: 'right-top',
                    show: false,
                },
                {
                    element: '.rule-container',
                    title: '选取受众提示',
                    name: 'rule',
                    desc: '当前实验为进组不出组，已命中实验的用户仍然保持命中状态，未进组用户将按照新口径逻辑进行分流，请谨慎操作。',
                    placement: 'right-top',
                    show: false,
                },
                {
                    element: '.exp-list',
                    // element: '.version-create',
                    title: '调整分组流量提示',
                    oldValue: '40%',
                    desc: '当前实验为进组不出组，已进组用户保持命中状态，后续新进组用户会按照新的流量占比进行分流，请谨慎操作',
                    placement: 'right-top',
                    show: false,
                },
            ],
        };
    },
    computed: {
        ...mapGetters(
            ['addData'],
            'message'
        ),
        realSteps() {
            const steps = this.steps.filter(item => item.show);
            return steps
        },
        currentStepData() {
            return this.realSteps[this.current];
        },
    },
    watch: {
        addData(val) {
            if (!this.preData && val.id) {
                this.preData = {
                    ...val
                }
            }
        },
    },
    methods: {
        checkDuration() {
            // 检查是否修改了实验时长
            if (!this.preData || this.addData.duration === this.preData.duration) {
                return false;
            }

            // 检查必要的字段是否存在
            if (!this.addData.startTime || !this.addData.duration) {
                return false;
            }

            // 计算实验已运行的天数和小时数
            const currentTime = Date.now(); // 当前时间（毫秒）
            const startTime = this.addData.startTime * 1000; // 实验开始时间（转换为毫秒）
            const runningTime = currentTime - startTime; // 运行时间（毫秒）

            // 确保运行时间为正数
            if (runningTime >= 0) {
                // 计算运行的整天数
                const runningDays = Math.floor(runningTime / (24 * 60 * 60 * 1000));

                // 计算运行的小时数（不足一天的部分）
                const runningHours = Math.floor((runningTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));

                // 检查修改后的实验时长是否等于当前运行整天数+1
                const targetDuration = runningDays + 1;
                if (this.addData.duration === targetDuration) {
                    // 计算剩余小时数
                    const remainingHours = 24 - runningHours;

                    // 更新引导步骤的描述信息
                    const durationStep = this.steps.find(step => step.element === '.ex-item-duration');
                    if (durationStep) {
                        durationStep.desc = `您设置的实验时长为${this.addData.duration}天，当前实验运行时长为${runningDays}天${runningHours}小时，修改后试验将于${remainingHours}小时后结束，请谨慎操作。`;
                        durationStep.oldValue = `${this.preData.duration}天`;
                        durationStep.show = true
                    }
                }
            }
        },
        checkFlow() {
            // 检查是否修改了流量且流量调小
            if (!this.preData || this.addData.flow >= this.preData.flow) {
                return false;
            }
            // 计算流量百分比
            const currentFlowPercent = this.addData.flow;
            const originalFlowPercent = this.preData.flow;

            // 更新引导步骤的描述信息
            const flowStep = this.steps.find(step => step.element === '.processInput');
            if (flowStep) {
                // 判断实验类型：0=进组不出组，1=进组出组
                const isResetUser = this.addData.isResetUser;

                if (isResetUser === 0) {
                    // 进组不出组实验
                    flowStep.desc = `当前操作存在缩量操作，实验流量将从${originalFlowPercent}%缩量到${currentFlowPercent}%。原实验是进组不出组，只会影响未进过组的用户。已经进组的用户会继续命中原来的实验组，不受缩量的影响。实验缩量将影响实验报告科学性，导致实验报告数据不可用，请谨慎操作。`;
                } else {
                    // 进组出组实验
                    flowStep.desc = `当前操作存在缩量操作，实验流量将从${originalFlowPercent}%缩量到${currentFlowPercent}%。原实验是进组出组，会影响所有的用户，已经进过组的用户可能会发生出组。实验缩量将影响实验报告科学性，导致实验报告数据不可用，请谨慎操作。`;
                }

                flowStep.oldValue = `${originalFlowPercent}%`;
                flowStep.show = true;
            }
        },
        checkRule() {
            // 检查是否修改了受众规则
            if (!this.preData || !this.addData.filterRule || !this.preData.filterRule) {
                return false;
            }

            // 深度比较受众规则是否发生变化
            const currentRuleStr = JSON.stringify(this.addData.filterRule);
            const originalRuleStr = JSON.stringify(this.preData.filterRule);

            if (currentRuleStr === originalRuleStr) {
                return false;
            }

            // 更新引导步骤的描述信息
            const ruleStep = this.steps.find(step => step.element === '.rule-container');
            if (ruleStep) {
                // 判断实验类型：0=进组不出组，1=进组出组
                const isResetUser = this.addData.isResetUser;

                if (isResetUser === 0) {
                    // 进组不出组实验
                    ruleStep.desc = '当前实验为进组不出组，已命中实验的用户仍然保持命中状态，未进组用户将按照新口径逻辑进行分流，请谨慎操作。';
                } else {
                    // 进组出组实验
                    ruleStep.desc = '当前实验为进组出组，所有用户均按照新的受众规则进行分流，历史已进组的用户可能出现出组，导致之前能看到实验效果的用户后续无法再看到实验效果，影响实验数据科学性，请谨慎操作。';
                }

                ruleStep.show = true;
                return true;
            }

            return false;
        },
        handelGuideCheck() {
            // 检查是否为运行中实验
            if (this.addData.status !== 3) {
                return false;
            }

            // 检查实验时长变化
            this.checkDuration();

            // 检查流量变化
            this.checkFlow();

            // 检查受众规则变化
            this.checkRule();

            if (this.realSteps.length) {
                this.handleStart();
                return true;
            }
            return false;
        },
        handleStart() {
            this.current = 0;
        },
        handleFinish() {
            this.current = -1;
        },
        handleSkip() {
            this.current = -1;
        }
    }
};
</script>

<style lang="less">
.t-guide__highlight.t-guide__highlight--popup,
.t-guide__reference {
    max-width: 550px;
}

.t-guide__popup {
    max-width: 300px;

    .t-guide__tooltip {
        padding: 6px;
    }

    .t-popup__content {
        background: #FFEFD6;

        .t-button {
            background: #42C57A;
            min-width: 50px;
            border: none;
            color: white;
            --ripple-color: #42C57A !important;
        }
    }

    .t-popup__arrow {
        &::before {
            background: #FFEFD6;
        }
    }
}

.guide-container {
    max-width: 600px;
    padding: 40px;
}

.title-major {
    color: var(--td-text-color-primary);
    font-size: 36px;
    font-weight: 700;
    line-height: 44px;
}

.title-sub {
    margin-top: 8px;
    color: var(--td-text-color-secondary);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
}

.field {
    margin-top: 50px;
}

.label {
    margin-bottom: 8px;
    color: var(--td-text-color-primary);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
}

.action {
    display: inline-flex;
    margin-top: 50px;
}

.action button:first-child {
    margin-right: 10px;
}
</style>
