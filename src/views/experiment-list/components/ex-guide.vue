<template>
    <section justify="center">
        <t-guide :current.sync="current" :steps="steps" @finish="handleFinish" :highlightPadding="4" @skip="handleSkip"
            :skipButtonProps="{ content: '取消' }">
            <template #body>
                <div>
                    <h2 style="color: red;font-weight: bold;">
                        变更前：
                        <span v-if="currentStepData.name === 'rule'">原口径</span>
                        <span v-else>{{ currentStepData.oldValue }}</span>
                    </h2>
                    <p>{{ currentStepData.desc }}</p>
                </div>
            </template>
        </t-guide>
    </section>
</template>

<script>
import { Guide as TGuide } from 'tdesign-vue';
import { mapGetters } from 'vuex';

export default {
    components: {
        TGuide
    },
    data() {
        return {
            preData: null,
            current: -1,
            steps: [
                {
                    element: '.ex-item-duration',
                    title: '实验时长提示',
                    desc: '您设置的实验时长为30天，当前实验运行时长为29天20小时，修改后试验将于4小时后结束，请谨慎操作。',
                    oldValue: '40天',
                    placement: 'right-top',
                },
                {
                    element: '.processInput',
                    title: '实验缩量提示',
                    oldValue: '40%',
                    desc: '当前操作存在缩量操作，实验流量将从100%缩量到40%。原实验是进组不出组，只会影响未进过组的用户。已经进组的用户会继续命中原来的实验组，不受缩量的影响。实验缩量将影响实验报告科学性，导致实验报告数据不可用，请谨慎操作。',
                    placement: 'right-top',
                },
                {
                    element: '.rule-container',
                    title: '选取受众提示',
                    name: 'rule',
                    desc: '当前实验为进组不出组，已命中实验的用户仍然保持命中状态，未进组用户将按照新口径逻辑进行分流，请谨慎操作。',
                    placement: 'right-top',
                },
                {
                    element: '.exp-list',
                    // element: '.version-create',
                    title: '调整分组流量提示',
                    oldValue: '40%',
                    desc: '当前实验为进组不出组，已进组用户保持命中状态，后续新进组用户会按照新的流量占比进行分流，请谨慎操作',
                    placement: 'right-top',
                },
            ],
        };
    },
    computed: {
        ...mapGetters(
            ['addData'],
            'message'
        ),
        currentStepData() {
            return this.steps[this.current];
        }
    },
    watch: {
        addData(val) {
            if (!this.preData) {
                this.preData = {
                    ...val
                }
            }
        },
    },
    methods: {
        handelGuideCheck() {
            console.log('adds', this.preData)
            return true
        },
        handleStart() {
            this.current = 0;
        },
        handleFinish({ e, current, total }) {
            this.current = -1;
        },
        handleSkip() {
            this.current = -1;
        }
    }
};
</script>

<style lang="less">
.t-guide__highlight.t-guide__highlight--popup,
.t-guide__reference {
    max-width: 550px;
}

.t-guide__popup {
    max-width: 300px;

    .t-guide__tooltip {
        padding: 6px;
    }

    .t-popup__content {
        background: #FFEFD6;

        .t-button {
            background: #42C57A;
            min-width: 50px;
            border: none;
            color: white;
            --ripple-color: #42C57A !important;
        }
    }

    .t-popup__arrow {
        &::before {
            background: #FFEFD6;
        }
    }
}

.guide-container {
    max-width: 600px;
    padding: 40px;
}

.title-major {
    color: var(--td-text-color-primary);
    font-size: 36px;
    font-weight: 700;
    line-height: 44px;
}

.title-sub {
    margin-top: 8px;
    color: var(--td-text-color-secondary);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
}

.field {
    margin-top: 50px;
}

.label {
    margin-bottom: 8px;
    color: var(--td-text-color-primary);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
}

.action {
    display: inline-flex;
    margin-top: 50px;
}

.action button:first-child {
    margin-right: 10px;
}
</style>
