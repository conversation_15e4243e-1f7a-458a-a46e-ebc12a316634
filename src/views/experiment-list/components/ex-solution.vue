<template>
  <div class="steps-content">
    <h3>实验版本设置</h3>
    <div class="white-list-con" @click="handleDocumentClick('hitCondition')">
      <span>白名单命中条件：</span>
      <el-radio-group v-model="addData.isWhitelistRule" :disabled="statusIsRun() || statusIsAwaitPub() || isCheck">
        <el-radio :label="0">无需满足目标受众规则</el-radio>
        <el-radio :label="1">需满足目标受众规则</el-radio>
      </el-radio-group>
    </div>
    <exs />
    <h3 style="margin-top: 16px"  @click="handleDocumentClick('versionFlowRatio')">各版本流量分配</h3>
    <div class="white-list-con" @click="handleDocumentClick('versionFlowRatio')">
      <span>流量均匀分配：</span>
      <el-switch v-model="addData.isFlowAvg" :disabled="isCheck" @change="handleAverage"></el-switch>
    </div>
    <div @click="handleDocumentClick('versionFlowRatio')" :class="['exp-list', { 'exp-list-child': isChild }]">
      <div class="exp-list-box">
        <div v-for="(item, index) in addData.versionList" :key="index">
          <div v-if="index !== 0" class="ex-box">
            <div class="ex-item">
              <div>{{ item.displayName }}</div>
              <div class="perint">
                <template v-if="!addData.isFlowAvg">
                  <div>
                    <el-input-number v-model="item.flow" controls-position="right" :max="100" :min="0" :step="0.1"
                      :precision="1" :disabled="isCheck" @change="flowChange(index, item.flow)" />
                    <span class="symbol-con">%</span>
                  </div>
                </template>
                <template v-else>
                  <span>均匀分配</span>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template v-if="isChild">
      <h3 style="margin-top: 32px" class="relation-container"  @click="handleDocumentClick('parentVersion')">
        关联父实验版本
        <el-alert title="父实验版本效果等同于受众规则，在满足前面的受众规则之后，用户需要满足能够命中所选父实验版本才会向下进行，否则直接返回未命中子实验。" type="success"
          :closable="false"></el-alert>
      </h3>
      <div class="relation-father" @click="handleDocumentClick('parentVersion')">
        <el-table :data="realList" border>
          <el-table-column prop="displayName" label="子实验版本名称" align="center" width="250px"></el-table-column>
          <el-table-column prop="" label="关联父实验版本" align="center">
            <template slot-scope="scope">
              <el-select multiple filterable clearable v-model="scope.row.relationIds" placeholder="请选择"
                class="relation-father-select" :disabled="(isCheck || statusIsPub()) && !(isExtend && isChild)">
                <el-option v-for="item in fatherVersinList" :key="item.versionId" :label="item.displayName"
                  :value="item.versionId"></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </template>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import ShowExampleCode from './show-example-code.vue';
import EditJsonDialog from './edit-json-dialog.vue';
import Exs from './exs/index.vue';
import { cloneDeep } from 'lodash';

const actions = {
  local: '/testAddress/earthworm/mis/upload/uploadimg',
  test: '/earthworm/mis/upload/uploadimg',
  server: '/earthworm/mis/upload/uploadimg'
};
export default {
  name: 'Exsolution',
  props: {},
  components: {
    ShowExampleCode,
    EditJsonDialog,
    Exs
  },
  data() {
    return {
      jsonValue: '',
      indexs: [],
      isShowDialog: false,
      options: [], // 受众规则
      isCheck: this.$route.query.type === 'check' || false,
      isEdit: this.$route.query.type === 'edit' || false,
      isAdd: this.$route.query.type === 'add' || false,
      isYunTest: this.$route.query.ex === '5' || false,
      isCodeTest: this.$route.query.ex === '1' || false,
      isExtend: this.$route.query.isExtend === '1' || false,
      isCopy: this.$route.query.subType === 'copy' || false,
      relationFlowMap: []
    };
  },
  computed: {
    ...mapGetters(
      ['addData', 'fatherData', 'paramTypes', 'mutuList', 'booleanList', 'filerList', 'ipList'],
      'message'
    ),
    fatherVersinList() {
      const list = this.fatherData.versionList || [];
      return list.length ? list.filter((v, i) => i !== 0) : list;
    },
    isChild() {
      return !!this.$route.query.parentId;
    },
    realList() {
      return this.addData.versionList.filter((v, index) => index);
    }
  },
  created() {
    this.$store.dispatch('getIpList');
  },
  watch: {
    'addData.exclusiveId': {
      handler: function (newVal, oldVal) {
        if (!this.isAdd && newVal) {
          this.mutuType = 1;
        }
      }
    },
    'addData.newExclusiveId': {
      handler: function (newVal, oldVal) {
        if (!this.isAdd && newVal) {
          this.mutuType = 1;
        }
      }
    },
    'addData.versionList': {
      // 当版本有变化的时候需要重新计算
      handler: function (newVal, oldVal) {
        console.log('newVal>>>>>>', newVal, oldVal);
        if (this.isAdd) {
          this.handleAverage();
        }
        // 如果是草稿,待发布,继承,复制状态下且是均匀分配，当新增版本后重新计算流量
        if (
          (this.statusIsDraft() || this.statusIsAwaitPub() || this.isExtend || this.isCopy) &&
          this.addData.isFlowAvg
        ) {
          this.handleAverage();
        }
      }
    },
    'addData.relationFlowMap'(newVal) {
      if (!this.isChild && newVal && newVal.length) {
        this.relationFlowMap = cloneDeep(newVal);
      }
    },
    'addData.mutuList': {
      handler: function (newVal, oldVal) {
        // debugger;
        if (this.isEdit || this.isCheck) {
          this.freeFlowChange();
          if (
            (this.addData.exclusiveId && this.statusIsPub()) ||
            (this.addData.exclusiveId && this.statusIsAwaitPub())
          ) {
            this.freeFlowChange1();
          }
        }
        // 当切换实验第一步中的实验类型、账号体系、业务线的时候需要重置
        if (this.isAdd && newVal && this.current != 3) {
          this.mutuType = 0;
          this.maxFlow = 100;
          this.lastFlow = 0;
        }

        if (this.isChild && this.relationFlowMap.length && this.addData.newExclusiveId) {
          // 查找当前编辑子实验 互斥组名称
          if (this.isAdd || this.statusIsDraft()) {
            const obj = this.mutuList.find(f => f.id == this.addData.newExclusiveId) || {};
            obj.displayName &&
              (this.relationFlowMap[this.relationFlowMap.length - 1].exclusiveDisplayName =
                obj.displayName);
          }
        }
      }
    },
    // 子实验 新增 编辑 查看
    fatherData: {
      handler(newVal) {
        if (!newVal.id) return;
        // 还没有子实验
        if (!newVal.relationFlowMap.length) {
          this.relationFlowMap = [newVal.selfFlowMap];
        } else {
          this.relationFlowMap = cloneDeep(newVal.relationFlowMap);
        }
        if (this.statusIsDraft()) {
          const newData = cloneDeep(this.addData);
          // const obj = this.addData.mutuList.find((f) => f.id == newVal.newExclusiveId) || {};
          let exclusiveDisplayName = '';
          for (let i = 0; i < this.mutuList.length; i++) {
            let current = this.mutuList[i];
            if (current.id === newData.exclusiveId) {
              exclusiveDisplayName = current.displayName;
              break;
            }
          }
          console.log('newData', newData);
          let versionList = newData.versionList
            .filter((f, i) => i != 0)
            .map(v => {
              v.flow = (v.flow * 10 * newData.flow * 10) / 1000;
              v.flowRate = v.flow / 10 + '%';
              return v;
            });
          versionList.push({
            displayName: '未使用流量',
            flow: 1000 - newData.flow * 10,
            flowRate: 100 - newData.flow + '%'
          });
          this.relationFlowMap.push({
            edit: true,
            status: '草稿',
            displayName: newData.displayName,
            exclusiveDisplayName: exclusiveDisplayName || '正交实验层',
            versionList: versionList
          });
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.$route.query.type === 'add') {
      this.handleAverage();
    }
  },
  methods: {
    handleDocumentClick(key) {
      this.$eventbus.$emit('exp-document', key);
    },
    flowChange(idx, flow) {
      const verLen = this.addData.versionList.length;
      const current = this.addData.versionList[idx];
      current.flow = flow;
      const flowAll = this.addData.versionList.slice(1).reduce((pre, curr, index) => {
        return pre + curr.flow;
      }, 0);
      var diff = (flowAll - 100).toFixed(1);
      for (var i = verLen - 1; i >= 0; i--) {
        if (i === idx) {
          continue;
        }
        const item = this.addData.versionList[i];
        if (diff !== 0) {
          const a = (item.flow - diff).toFixed(1);
          if (a >= 0 && a <= 100) {
            item.flow = a;
            diff = 0;
          } else if (a < 0) {
            diff = diff - item.flow;
            item.flow = 0;
          } else if (a > 100) {
            diff = 100 - a;
            item.flow = 100;
          }
        }
      }
    },
    handleAverage() {
      const versioList = this.addData.versionList;
      const verLen = this.addData.versionList.length - 1; // 实际版本数据不包括第一个
      const min = parseInt(1000 / verLen);
      const maxNum = 1000 % verLen;
      versioList.map((version, index) => {
        if (index && index <= maxNum) {
          version.flow = (min + 1) / 10;
          return version;
        } else if (index) {
          version.flow = min / 10;
          return version;
        }
      });
    },
    statusAwaitRun() {
      return this.statusIsAwaitPub() || this.statusIsDebug();
    },
    statusIsRun() {
      return this.statusIsPub() || this.statusIsPause() || this.statusIsDongJie();
    },
    statusIsDraft() {
      // 草稿状态
      return this.addData.status === 1;
    },
    statusIsDebug() {
      // 调试状态
      return this.addData.status === 2;
    },
    statusIsPub() {
      // 发布状态
      return this.addData.status === 3;
    },
    statusIsStop() {
      // 结束状态
      return this.addData.status === 4;
    },
    statusIsAwaitPub() {
      // 待发布状态
      return this.addData.status === 5;
    },
    statusIsPause() {
      // 暂停状态
      return this.addData.status === 6;
    },
    statusIsDongJie() {
      return this.addData.status === 7;
    },
    /* 是否允许编辑: true 允许, false 不允许*/
    isAllowedEdit() {
      // 3 发布, 5 待发布, 6 暂停 7 冻结
      let isNonRunEditCode =
        this.isCodeTest && this.isEdit && ![3, 5, 6, 7].includes(this.addData.status);
      return (
        this.isAdd || // 添加的时候允许
        (this.isYunTest && this.isEdit) || // 云控实验编辑时允许
        isNonRunEditCode || // 非待发布和发布状态下编辑编程实验
        this.isExtend
      ); // 继承时允许
    },
    /**
     * @description: json编辑成功
     * @param {*} e
     * @return {*}
     */
    handleJsonSucceed(e) {
      let verIndex = this.indexs[0];
      let feaIndex = this.indexs[1];
      this.addData.versionList[verIndex].featureList[feaIndex].keyValue = JSON.stringify(e);
    },
    /**
     * @description: 校验输入值合法性
     * @param {*} val
     * @return {*}
     */
    handleJsonInput(val) {
      if (!val) {
        return;
      } else if (!this.isJson(val)) {
        this.$message.error('请输入正确的JSON字符串');
      }
      // index version 编号 idx feature 编号
      // 数据结构是竖着的
      // console.log("index, idx", index, idx);
    },
    /**
     * @description: icon点击
     * @param {*} index
     * @param {*} idx
     * @return {*}
     */
    handleEditJson(index, idx) {
      this.indexs = [index, idx];
      let currentVal = this.addData.versionList[index].featureList[idx].keyValue;
      if (currentVal && !this.isJson(currentVal)) {
        this.$message.error('请输入正确的JSON字符串');
        return;
      }

      if (currentVal) {
        this.jsonValue = JSON.parse(currentVal);
      } else {
        this.jsonValue = {};
      }
      this.isShowDialog = true;
    },
    isJson(str) {
      if (typeof str == 'string') {
        try {
          const obj = JSON.parse(str);
          return !!(typeof obj == 'object' && obj);
        } catch (e) {
          console.log('error：' + str + '!!!' + e);
          return false;
        }
      } else {
        this.$message.error('请输入字符串');
      }
    },
    reset(flist) {
      flist.keyType = '';
      flist.keyValue = [];
    },
    // 组装受众规则操作类型
    assembleSymbolList(symbolList, symbols) {
      if (!Array.isArray(symbolList) || !symbolList.length || !symbols || !symbols.length)
        return [];
      let result = [];
      symbols.forEach(sy => {
        symbolList.forEach(list => {
          if (sy == list.type) {
            result.push(list);
          }
        });
      });
      //debugger;
      return result;
    },
    // 适配文件上传环境地址
    computedEnv() {
      let hostName = location.hostname;
      let url = '';
      if (/^localhost$/gi.test(hostName)) {
        url = actions.local;
      } else if (/docker|test/gi.test(hostName)) {
        url = actions.test;
      } else {
        url = actions.server;
      }
      return url;
    }
  }
};
</script>

<style lang="less" scoped>
.steps-content {
  margin-top: 40px;
  padding: 24px 20px;
  border: 1px solid #e1e2e6;
  border-radius: 2px;
  overflow: auto;

  >h3 {
    padding: 0 0 12px 0;
    font-size: 16px;
    font-weight: 700;
  }

  .white-list-con {
    padding: 0;
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    span {
      font-size: 14px;
      display: inline-block;
      width: 130px;
      text-align: right;
      padding-right: 10px;
      box-sizing: border-box;
    }

    .el-select {
      width: 150px;
    }
  }

  .relation-father {
    margin-left: 130px;

    .relation-father-select {
      width: 90%;
    }

    .el-table {
      width: 650px;

      /deep/ .el-table__header-wrapper {
        thead {
          th {
            background: #42c57a;
            color: white;
            border-right: 1px solid #42c57a;
          }
        }
      }
    }
  }

  .relation-container {
    position: relative;

    .el-alert {
      position: absolute;
      top: -12px;
      left: 130px;
      width: 800px;
      padding: 8px 0;

      /deep/ .el-alert__title {
        font-size: 12px;
      }
    }
  }

  .exp-list {
    background-color: #f8f8fc;
    padding: 15px 20px;
    margin-left: 130px;

    .ex-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      height: 30px;
      font-size: 14px;

      &>div {
        font-weight: bold;

        &>span {
          font-weight: bold;
        }
      }

      .symbol-con {
        margin-left: 10px;
      }
    }
  }
}
</style>
