<template>
  <Page class="container">
    <section class="header">
      <section>
        <i class="el-icon-arrow-left" @click="handleGoBack"></i>
        <span class="page-title">{{ `「${displayName}」` }}实验的诊断工具</span>
      </section>
    </section>
    <div class="diag-container">
      <el-form size="medium" class="diag-form" label-position="left" label-width="150px">
        <el-form-item label="诊断类型：">
          <el-radio-group v-model="diagnosisType" @change="changeDiagnosisType">
            <el-radio :label="1">缓存命中</el-radio>
            <el-radio :label="2">当前命中</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="accountSystem == 1" label="cuid：">
          <el-input placeholder="请输入cuid" v-model="formData.cuid" clearable maxlength="50" show-word-limit></el-input>
        </el-form-item>
        <el-form-item v-if="accountSystem == 2" label="uid：">
          <el-input placeholder="请输入uid" v-model="formData.cuid" clearable maxlength="50" show-word-limit></el-input>
        </el-form-item>
        <el-form-item v-if="diagnosisType === 2 && filterRules.length" label="用户参数：">
          <div v-for="(param, index) in paramsList" :key="index" class="param-box">
            <el-input v-model="param.keyName" disabled></el-input>
            <el-input v-model="param.key" disabled></el-input>
            <el-input v-model="param.keyType" disabled></el-input>
            <el-input v-model="param.value"></el-input>
          </div>
        </el-form-item>
        <el-form-item v-if="diagnosisType === 2 && filterRules.length" label="受众规则：">
          <section class="target-cu-container">
            <div v-for="(frule, index) in filterRules" :key="index">
              <div class="line-con" v-if="index !== 0">
                <span>或</span>
              </div>
              <div v-if="frule.length">
                <div class="rule-con">
                  <p class="and-sign" v-show="frule.length > 1">且</p>
                  <div class="detail-set" :class="[frule.length > 1 ? 'detail-set-b' : '']" v-for="(flist, idx) in frule" :key="idx">
                    <el-input v-model="flist.keyName" disabled></el-input>
                    <el-input v-model="flist.compareType" disabled></el-input>
                    <template>
                      <el-input class="s-w s-slect el-select u-input" placeholder="请输入" type="text" v-model="flist.keyValue" :disabled="true"></el-input>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="medium" @click="handlediagnosis">开始诊断</el-button>
        </el-form-item>
      </el-form>
      <p class="diag-title diag-title1">
        诊断结果
        <el-tooltip class="item" effect="dark" content="Top Center 提示文字" placement="right">
          <div slot="content">
            <section class="tool-tips">
              <p>未命中原因</p>
              1.不符合样本总量：该用户分流结果不属于该实验的流量区间，当前实验分配总流量为<实验赋予流量百分比>，该用户属于剩余流量空间。
                <br />
                2.实验已暂停：该实验处于暂停状态，所有用户均出组，无法命中。
                <br />
                3.实验已冻结：该实验处于冻结状态，该用户冻结前未曝光实验，无法命中。
                <br />
                4.不符合受众规则：该用户不满足受众过滤条件，无法命中。
                <br />
                5.既不符合当前版本受众又不符合Base版本受众：该用户不满足当前运行版本受众过滤条件，同时不满足base版本受众过滤条件，无法命中。
            </section>
          </div>
          <el-button type="text" icon="el-icon-warning-outline" style="font-size: 15px;"></el-button>
        </el-tooltip>
      </p>
      <el-table :data="tableData" style="width: 100%" :show-header="false" border>
        <el-table-column prop="name" label="" align="center" width="210"></el-table-column>
        <el-table-column prop="value" label="" align="center">
          <template slot-scope="scope">
            <JsonEditor :value="scope.row.value" :options="options" v-if="scope.row.prop === 'json'"></JsonEditor>
            <span v-else>{{ scope.row.value }}</span>
          </template>
        </el-table-column>

        <!-- <el-table-column label="val" align="center" width="400">
          <template slot-scope="scope">
            <JsonEditor :value="scope.row.json" :options="options"></JsonEditor>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <div class="go-back-container">
      <el-button @click="goBack" type="primary" size="medium">返回</el-button>
    </div>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import JsonEditor from '@/components/tools/json-edit.vue';
import { cloneDeep } from 'lodash';
import { mapGetters } from 'vuex';

export default {
  name: 'diagnosis',
  components: {
    Page,
    JsonEditor
  },
  data() {
    return {
      loading: false,
      formData: {
        cuid: '',
        params: [],
      },
      diagnosisType: 1,
      tableData: [],
      filterRules: [],
      paramsList: [],
      accountSystem: this.$route.query.accSystem,
      displayName: decodeURIComponent(this.$route.query.name),
      expId: this.$route.query.id,
      isAdd: this.$route.query.type === 'add' ? true : false,
      isEdit: this.$route.query.type === 'edit' ? true : false,
      isCheck: this.$route.query.type === 'check' ? true : false,
      isEditYun: this.$route.query.ex == 5 && this.$route.query.type === 'edit' ? true : false,
      options: {
        mode: 'view',
        border: false
      }
    };
  },

  computed: {
    ...mapGetters(['filerList'], 'message')
  },
  watch: {},
  created() {
    this.init();
  },
  methods: {
    changeDiagnosisType(val) {
      if (this.diagnosisType !== 2) {
        this.filterRules = [];
        this.formData.params = [];
        return;
      }
      this.init();
    },
    init() {
      this.$store.dispatch('getFilterList');
      const params = {
        experimentId: this.expId,
      };
      this.$service.get("GETEXPFILTER", { ...params }, { needLoading: true }).then(res => {
        this.filterRules = res.filterRuleList;
        this.paramsList = res.paramsList || [];
      });
    },
    // 诊断
    handlediagnosis() {
      if (!this.formData.cuid) {
        this.$message.warning('请输入CUID');
        return;
      }
      const userParams = this.paramsList.filter(item=>item.value).map(item=>({
        k: item.key,
        v:item.value
      }));
      const params = {
        id: this.expId,
        cuid: this.formData.cuid,
        params: JSON.stringify(userParams),
      };
      const apiName = this.diagnosisType === 1 ? 'GETENGINERET' : 'GETENGINERES';
      this.$service.get(apiName, { ...params }, { needLoading: true }).then(res => {
        // debugger;
        let result = res.engineRets;
        if (Array.isArray(result) && result.length) {
          const item = result[0];
          const keyMap = [{
            prop: 'requestKey',
            name: '参数名'
          }, {
            prop: 'versionId',
            name: '命中的版本ID'
          }, {
            prop: 'versionName',
            name: '命中的版本名称'
          }, {
            prop: 'rtntype',
            name: '未命中的原因/命中的原因'
          }, {
            prop: 'json',
            name: '返回的json'
          }];
          result = keyMap.map(key => {
            const { prop } = key;
            const res = {
              ...key
            };
            switch (prop) {
              case 'json':
                res.value = cloneDeep(item);
                break;
              case 'rtntype':
                res.value = item.versionId ? item.rtntype : item.value;
                break;
              default:
                res.value = item[key.prop] || '-';
                break;
            }
            return res;
          });
          console.log('result', result);
        } else {
          this.$message.warning("未命中任何版本");
        }
        this.tableData = result;
      });
    },
    // 返回
    goBack() {
      this.$router.back(-1);
    },
    // 返回列表页面
    handleGoBack() {
      this.$router.push({
        path: '/exp-manage/list'
      });
    },

  },
  beforeDestroy() {
  }
};
</script>

<style lang="less" scoped>
.container {
  margin-top: 24px;

  /deep/ .page-container {
    padding-top: 16px;
  }

  /deep/ .jsoneditor-box {
    height: auto !important;
    width: 100% !important;

    .jsoneditor-menu,
    .jsoneditor-navigation-bar {
      display: none;
    }

    .jsoneditor-tree>tbody>tr:hover {
      * {
        background-color: #f5f7fa;
      }
    }

    div.jsoneditor tr {
      border: none !important;

      td {
        border: none;
      }
    }
  }

  .el-table {
    /deep/ .el-table_1_column_1 {
      background-color: #f8f8fa;
      font-size: 15px;
      color: black;
      font-weight: 600;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    section {
      display: flex;
      align-items: center;
    }

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin: 0 8px;
    }

    i {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.param-box{
  display: flex;
  max-width: 680px;
  margin-bottom: 8px;
  & > *{
    margin-right: 12px;
  }
}

.tool-tips {
  line-height: 22px;
  max-width: 450px;

  p {
    font-size: 15px;
    margin-bottom: 5px;
  }
}

.diag-title {
  padding: 0 0 30px 0;
  font-weight: 600;
  font-size: 16px;

  span {
    font-weight: 600;
    font-size: 16px;
  }
}

.target-cu-set {
  padding: 0 0 30px 0;
  font-size: 13px;
}

.target-cu-container {
  margin-bottom: 24px;
  max-width: 650px;
}

.rule-con {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  padding: 0;
  position: relative;
}

.and-sign {
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
}

.line-con {
  position: relative;
  // padding: 6px 35px;
  border-bottom: 1px solid #e1e2e6;
  margin: 25px 0px;

  span {
    position: absolute;
    bottom: -9px;
    left: 47%;
    width: 45px;
    height: 25px;
    background-color: #fff;
    display: inline-block;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
  }
}

.detail-set {
  padding: 2px 0 7px;
  margin: 0 0 0 20px;

  .el-input {
    width: 230px;
  }
}

.detail-set-b {
  border-left: 2px solid #42c57a;
}

.detail-set .el-select,
.detail-set .el-input {
  margin: 0 8px;
  width: 180px;

  /deep/ .el-input {
    width: 100%;
  }
}

.detail-set:hover .sigle-de {
  display: inline-block;
}

.diag-title1 {
  padding: 20px 0 20px 9px;
}

.diag-form {
  padding-left: 9px;
  margin-top: 12px;
}

/deep/ .s-w {
  width: 100px;
}

/deep/ .el-input {
  width: 500px;
}

/deep/ .el-table {
  font-size: 14px;
}

/deep/ .el-button>span {
  font-size: 14px;
}

.go-back-container {
  display: flex;
  flex-direction: row-reverse;
  padding: 20px;
}
</style>
