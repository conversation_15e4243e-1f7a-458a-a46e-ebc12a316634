<template>
  <section>
    <iframe :src="url" frameborder="0" ref="frame"></iframe>
  </section>
</template>
<script>
import Layout from '@/components/layout';
import qs from 'qs';
const hashMap = {
  '/system/app/configManager': '/system-manage/business-manage',
  '/system/intimeManager': '/system-manage/realtime-permission/list',
  '/admin/easyQuery': '/quick-query',
  '/admin/task/restartManager': '/restart-manage',
  '/costs/center': '/cost-manage/cost-center',
  '/costs/monitor': '/cost-manage/cost-monitor',
  '/costs/budgetManager': '/cost-manage/bus-budget-manage',
  '/app-group-manage': '/app-group-manage/list'
};
export const getUrlByLocation = (hash, params) => {
  let realHash = hashMap[hash] || hash;
  if (realHash.startsWith('/frame')) {
    realHash = realHash.replace('/frame', '');
  }
  const str = qs.stringify(params);
  const origin = location.origin;
  const url = `${origin}/static/fe-ab-test/#${realHash}`;
  return str ? `${url}?${str}` : url;
};
export default {
  name: 'layout',
  components: {
    Layout
  },
  data() {
    const route = this.$route;
    const { path = 'traffic-control/exp/list', query = {} } = route;
    const url = getUrlByLocation(path, query);
    console.log('url', url);
    return {
      url: url
    };
  }
};
</script>
<style lang="less" scoped>
section {
  height: calc(100vh - 52px);

  iframe {
    width: 100%;
    height: 100%;
  }
}
</style>
