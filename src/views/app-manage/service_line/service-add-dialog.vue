<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-05 10:52:36
 * @LastEditors: zhuyue
 * @LastEditTime: 2021-08-11 15:32:49
 * @Description: 新建/编辑参数
-->
<template>
  <el-dialog width="45%" :title="title" :visible="dialogVisible" :before-close="dialogClose" class="arg_style_dialog" top="80px">
    <el-form ref="argFormRef" :model="formData" label-width="130px" :rules="rules">
      <el-form-item label="key：" prop="appKey">
        <el-input :disabled="isDisabled" class="f-width-c" type="text" v-model="formData.appKey" maxlength="20" placeholder="请输入业务线key"></el-input>
      </el-form-item>
      <el-form-item label="业务线名称：" prop="displayName">
        <el-input class="f-width-c" type="text" v-model="formData.displayName" show-word-limit placeholder="请输入业务线名称"></el-input>
      </el-form-item>
      <el-form-item label="描述：" prop="description">
        <el-input type="textarea" v-model="formData.description" class="f-width-c" rows="3" resize="vertical" show-word-limit placeholder="请输入描述信息"></el-input>
      </el-form-item>
      <el-form-item label="业务单元：" prop="appGroupId">
        <el-select v-model="formData.appGroupId" placeholder="请选择业务单元">
          <el-option v-for="item in appgroupList" :key="item.id" :label="item.displayName" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <!-- 确定取消按钮组 -->
    <section slot="footer" class="dialog-footer">
      <el-button @click="dialogClose">取 消</el-button>
      <el-button type="primary" @click="handleSure">确定</el-button>
    </section>
  </el-dialog>
</template>
<script>
import { formatFormData } from '@/utils/util';
export default {
  name: 'create-dialog',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    editId: {
      type: Number | String
    },
    argId: {
      type: Number | String
    },
    type: {
      type: String
    },
    appgroupList: {
      type: Array
    }
  },
  computed: {
    isDisabled() {
      return this.argId != '';
    },
    isLocationDisabled() {
      return this.formData.location !== '' && this.argId !== '';
    },
    isViewDisabled() {
      return this.type === 'view';
    }
  },
  data() {
    return {
      ownerList: [],
      keyValueEnumsOpytion: [],
      symbolOptions: [],
      appOptionsData: [],
      formData: {
        displayName: '',
        description: '',
        appKey: '', // 业务线
        appGroupId: ''
      },
      locationTypes: [{
        value: 'mainland',
        label: '国内'
      },
      {
        value: 'usaeast',
        label: '美东'
      }],
      dauPoint: [],
      logData: [
        {
          name: '盘古',
          logObj: {
            ab: '',
            log: '',
            dau: '',
            abRely: '',
            logRely: '',
            dauRely: ''
          }
        },
        {
          name: '毕方',
          logObj: {
            ab: '',
            log: '',
            dau: '',
            abRely: '',
            logRely: '',
            dauRely: ''
          }
        }
      ],

      checkAll: false,
      symbolValue: [], // checkbox选择的值
      symbollist: [], // checkbox的option
      isIndeterminate: true,
      isAdanced: false,
      title: '添加业务线',
      rules: {
        displayName: [
          {
            required: true,
            message: '请输入业务线名称',
            trigger: 'change'
          }
        ],
        appKey: [
          {
            required: true,
            message: '请输入业务线key',
            trigger: 'change'
          }
        ],
        appGroupId: [
          {
            required: true,
            message: '请选择业务单元',
            trigger: 'change'
          }
        ],
      }
    };
  },
  created() {
    if (this.argId) {
      this.title = '编辑业务线';
    } else {
      this.title = '添加业务线';
    }
    if (this.type === 'view') {
      this.title = '业务线详情';
    }
  },
  mounted() {
    console.log('this.argId', this.argId);
    if (this.argId) {
      this.getArgDetail();
    }
  },
  methods: {
    getOwnerList(query) {
      if (query) {
        const data = formatFormData({
          busnEmail: query
        });
        this.$service
          .post('OWNERLIST', data, { needLoading: true })
          .then((res) => {
            this.ownerList = res || [];
          })
          .catch((err) => {
            console.log(err);
          });
      } else {
        this.ownerList = [];
      }
    },
    /**
     * @description: 获取详情
     * @param {*}
     * @return {*}
     */
    getArgDetail() {
      this.$service
        .get('APPVIEW', { id: this.argId }, { needLoading: true })
        .then((res) => {
          this.formData = {
            displayName: res.displayName,
            description: res.description,
            appKey: res.appKey,
            appGroupId: res.appGroupId
          };
        })
        .catch((err) => {
          console.log(err);
        });
    },
    /**
     * @description: 提交保存
     * @param {*}
     * @return {*}
     */
    handleSure() {
      this.$refs.argFormRef.validate((valid) => {
        if (valid) {
          const params = {
            ...this.formData,
          };
          let url = 'APPADD';
          if (this.argId) {
            // 更新
            url = 'APPUPDATA';
            params.id = this.argId;
          } else {
            url = 'APPADD';
          }

          this.$service
            .post(url, this.formatFormData(params), { needLoading: true, allback: 1 })
            .then((res) => {
              if (res.errNo === 0) {
                this.$message.success('保存成功');
                // this.isDisabledSure = false;
                this.$emit('update:dialogVisible', false);
                this.$emit('succeed');
              } else {
                this.$message.error(res.errStr);
              }
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        console.log(key);
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
      // this.defaultFormat();
    }
  },
  beforeDestroy() {
    this.$emit('resetEditId');
  }
};
</script>

<style lang="less" scoped>
/deep/ .el-select--small {
  width: 100%;
}

.arg_style_dialog {
  /deep/ .el-dialog__body {
    padding-top: 15px;
    // height: 450px;
    // overflow: auto;
  }
}

.warn_style {
  margin-top: 10px;
  color: red;
  font-size: 13px;
  margin-left: 7px;
}

/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 760px;
  min-width: 30%;
}

.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}

.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;

  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }

  .detail-value {
    width: 200px;

    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }

  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;

    span {
      padding: 0 11px;
    }
  }

  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }

  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}

/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}

/deep/ .divider-con {
  margin: 20px 0;
}

.tip {
  padding: 10px 10px 20px;
}

/deep/ .el-radio__label {
  font-size: 14px;
}

.log-content {
  margin-top: 20px;
  margin: 20px 0px 20px 60px;
  padding-bottom: 20px;
  border-bottom: 1px solid #dcdfe6;

  .tit-line {
    display: flex;
    padding: 10px 0 10px 10px;
  }

  .tit-line p:nth-child(1) {
    width: 10%;
  }

  .tit-line p:nth-child(2),
  .tit-line p:nth-child(3) {
    width: 45%;
    text-align: center;
  }

  .log-item {
    display: flex;
    align-items: center;
    padding-left: 20px;
    padding-bottom: 10px;

    p {
      width: 10%;
    }
  }
}

.log-content:last-child {
  border-bottom: none;
}
</style>
