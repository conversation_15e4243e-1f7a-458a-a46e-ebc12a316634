<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
    <template #header-action>
      <el-button class="btn_add" type="default" icon="el-icon-circle-plus" size="small" @click="handleRegisterArg">添加业务线</el-button>
    </template>
    <div class="table-content">
      <el-form ref="searchForm" inline>
        <el-form-item label="">
          <el-input class="key-world-input" placeholder="请输入业务线名称/id/key" v-model="formData.keyword" clearable @change="handleSearch" style="width:345px"></el-input>
        </el-form-item>
        <el-form-item label="状态：">
          <el-select v-model="formData.status" style="width: 150px" placeholder="请选择状态" @change="handleSearch">
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务单元：">
          <el-select v-model="formData.appGroupId" style="width: 150px" placeholder="请选择业务单元" @change="handleSearch" clearable>
            <el-option v-for="item in appgroupList" :key="item.id" :label="item.displayName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template-table :hasIndex="false" class="detail-table" ref="multipleTableParent" :table-column="tableColumns" :table-list="tableList" :tableBtns="tableBtns" @operationEvent="operationEvent">
      </template-table>
      <template-pagination :pagination-init="paginationInit" @changePage="changePage" @pageSizeChange="pageSizeChange">
      </template-pagination>
      <service-add-dialog v-if="isShowDialog" :dialogVisible.sync="isShowDialog" :argId="argId" :type="type" @succeed="handleSucceed" :appgroupList="appgroupList"></service-add-dialog>
    </div>
  </Page>
</template>

<script>
import { formatDate } from '@/common/utils';
import Page from '@/components/common/page/index.vue';
import TemplatePagination from '@/components/tools/template-pagination.vue';
import TemplateTable from '@/components/tools/template-table.vue';
import { formatFormData } from '@/utils/util';
import ServiceAddDialog from './service-add-dialog.vue';

export default {
  name: 'ServiceLine',
  components: {
    Page,
    TemplateTable,
    TemplatePagination,
    ServiceAddDialog
  },
  data() {
    return {
      appgroupList: [],
      statusList: [{
        value: 0,
        label: '全部'
      }, {
        value: 1,
        label: '使用中'
      }, {
        value: 2,
        label: '已停用'
      }],
      appOptionsData: [],
      argId: '',
      isShowDialog: false,
      breadcrumbList: ['ServiceLine'],
      paginationInit: {
        total: 0,
        pageSize: 10,
        currentPage: 1
      },
      tableBtns: [
        {
          label: '编辑',
          role: 'edit'
        },
        {
          label: '停用',
          role: 'stop',
          showHandler: (index, row) => {
            return !row.status || row.status === 1;
          }
        },
        {
          label: '启用',
          role: 'start',
          showHandler: (index, row) => {
            return !row.status || row.status === 2;
          }
        }
        // {
        //   label: '删除',
        //   role: 'del',
        //   disabledHandler: (index, row) => {
        //     // 修改按钮是否可点击
        //     if (row.isDelete === 0) return true;
        //   }
        // }
      ],
      formData: {
        status: 0,
        keyword: '',
        appGroupId: ''
      },
      total: 0,
      tableList: [],
      tableColumns: [
        {
          label: '业务线ID',
          name: 'id',
        },
        {
          label: '业务线名称',
          name: 'displayName',
        },
        {
          label: 'key',
          name: 'appKey',
        },
        {
          label: '业务单元',
          name: 'appGroupName',
        },
        {
          label: '状态',
          name: 'status',
          render: (h, { row }) => {
            const { status } = row;
            const text = status === 1 ? '使用中' : status === 2 ? '已停用' : '-';
            return <p>{text}</p>;
          }
          // 'min-width': '105px'
        },
        {
          label: '创建人',
          name: 'createUser',
          'min-width': '105px'
        },
        {
          label: '更新人',
          name: 'updateUser',
          'min-width': '105px',
          render: (h, { row }) => {
            const { updateUser } = row;
            const text = updateUser ? updateUser : '-';
            return <p>{text}</p>;
          }
        },
        {
          label: '创建时间',
          name: 'createTime',
          'min-width': '105px',
          render: (h, { row }) => {
            const { createTime } = row;
            return <p>{formatDate(createTime * 1000)}</p>;
          }
        },
        {
          label: '更新时间',
          name: 'updateTime',
          'min-width': '105px',
          render: (h, { row }) => {
            const { updateTime } = row;
            return <p>{formatDate(updateTime * 1000)}</p>;
          }
        }
      ],
      businessOptions: [],
      metricId: {
        flight: 0,
        history: 0,
        modify: 0,
      },
      flightVisible: false,
      historyVisible: false,
      modifyVisible: false,
      pagination: {
        pn: 1,
        rn: 10,
      },
      type: ''
    };
  },
  computed: {},
  mounted() {
    this.handleSearch();
    this.getAppGroupList();
  },
  methods: {
    getAppGroupList() {
      this.$service
        .get('APPGROUPLIST', { rn: 10000 }, { needLoading: true })
        .then((res) => {
          this.appgroupList = res.list || [];
        });
    },
    handleSucceed() {
      this.paginationInit.currentPage = 1;
      this.getList();
    },
    handleName(row) {
      this.type = 'view';
      this.argId = row.id;
      this.isShowDialog = true;
    },
    handleRegisterArg() {
      this.type = 'add';
      this.argId = '';
      this.isShowDialog = true;
    },
    pageSizeChange(pageSize) {
      this.paginationInit.pageSize = pageSize;
      this.paginationInit.currentPage = 1;
      this.getList();
    },
    changePage(page) {
      this.paginationInit.currentPage = page;
      this.getList();
    },
    operationEvent(row, btnRole) {
      switch (btnRole) {
        case 'edit':
          this.isShowDialog = true;
          this.argId = row.id;
          this.type = 'edit';
          break;
        case 'stop':
          this.$confirm(`确认停用业务线：${row.displayName}？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              const formData = formatFormData({ id: row.id, status: 2 });
              this.$service
                .post('CONTROL_APP_STATUS', formData, { needLoading: true })
                .then(() => {
                  this.$message({
                    type: 'success',
                    message: '已停用'
                  });
                  this.getList();
                });
            });
          break;
        case 'start':
          this.$confirm(`确认启用业务线：${row.displayName}？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              const formData = formatFormData({ id: row.id, status: 1 });
              this.$service
                .post('CONTROL_APP_STATUS', formData, { needLoading: true });
            })
            .then(() => {
              this.$message({
                type: 'success',
                message: '已启用'
              });
              this.getList();
            });
          break;
      }
    },
    // 查询列表数据
    handleSearch() {
      this.paginationInit.currentPage = 1;
      this.getList();
    },
    getList() {
      let params = {
        pn: this.paginationInit.currentPage, // 第几页
        rn: this.paginationInit.pageSize,
        notall: 1,
        ...this.formData
      };
      this.$service
        .get('FEATUREAPPLIST', { ...params }, { needLoading: true })
        .then((res) => {
          this.tableList = res.list || [];
          this.paginationInit.total = res.total;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 重置
    handleReset() {
      this.paginationInit.currentPage = 1;
      this.formData = {
        appKey: '',
        keyword: ''
      };
      this.getList();
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .btn_add {
  i {
    color: #42c57a;
  }
}


.description {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search {
  position: relative;
  // display: flex;
  // justify-content: space-between;

  .el-select {
    width: 175px;
  }
}

.keyword_input {
  width: 270px;
  margin-left: -1px;

  /deep/ .el-input__suffix {
    right: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    cursor: pointer;
  }
}

/deep/ .el-table {
  font-size: 14px;
}

.table-pagination {
  margin-top: 20px;
  text-align: right;
}

.table_description {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.related_flights_text {
  color: rgb(70, 96, 239);
  cursor: pointer;
}

.table_tag {
  display: inline-block;
  border-radius: 4px;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  color: #2f2f3f;
  background: #f8f8fc;
  border: 1px solid #e7e9f5;
}

.table_tag_required {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
}

.action_edit {
  margin-right: 10px;
}

/deep/ .op-switch.is-disabled .el-switch__core,
/deep/ .op-switch.is-disabled .el-switch__label {
  background-color: rgb(155, 156, 158);
  border-color: rgb(155, 156, 158);
  cursor: pointer;
}

/deep/ .el-switch.is-disabled.is-checked .el-switch__core,
/deep/ .op-switch.is-disabled.is-checked .el-switch__label {
  border-color: #42c57a;
  background-color: #42c57a;
}

/deep/ .el-button--text-primary>span {
  font-size: 14px;
}
</style>
