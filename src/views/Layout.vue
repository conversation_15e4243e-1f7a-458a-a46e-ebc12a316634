<!--
 * @Author: wang<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-02-29 20:44:26
 * @LastEditors: wanglijuan01
 * @LastEditTime: 2020-08-10 14:09:21
 * @Description: 内容组件
 -->
 <template>
  <Layout :hideMenu="hideMenu" :hideWrap="hideWrap">
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive" :key="$route.fullPath"></router-view>
    </keep-alive>
    <router-view v-if="!$route.meta.keepAlive" :key="$route.fullPath"></router-view>
  </Layout>
</template>
<script>
import Layout from '@/components/layout';
export default {
  name: 'layout',
  components: {
    Layout
  },
  data() {
    return {
      hideWrap: false,
    };
  },
  computed: {
    hideMenu(){
      return this.$route.meta.hideMenu || false;
    }
  },
};
</script>
