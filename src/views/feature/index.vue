<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
    <div class="feature-container">
      <div class="feature-search">
        <el-form inline>
          <el-form-item>
            <el-input
              v-model="displayName"
              class="search-input"
              placeholder="输入固化名称搜索/id/参数/创建人"
              prefix-icon="el-icon-search"
              @change="handleInputChange"
            ></el-input>
          </el-form-item>
          <el-form-item label="业务线：">
            <el-select placeholder="请选择" v-model="appKey" @change="handleAppKeyChange">
              <el-option
                v-for="(item, index) in appKeyList"
                :key="index"
                :label="item.displayName"
                :value="item.appKey"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间：">
            <el-date-picker
              v-model="createTime"
              @change="handleChange"
              type="daterange"
              value-format="timestamp"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
          <el-checkbox v-model="isMe" @change="handleChange">我创建的</el-checkbox>
          </el-form-item>
        </el-form>

        <el-button v-if="false"
                   class="btn_add"
                   icon="el-icon-plus"
                   type="primary"
                   size="small"
                   @click="handleAddFea">新建</el-button>
      </div>
      <section style="position: relative;height: 32px;">
        <el-popover placement="left-start" trigger="click">
          <el-checkbox-group @change="handleCheckedChange" v-model="checkedColumns" class="">
            <el-checkbox class="checkbox" v-for="item in checkedColumnsOptions" :label="item" :key="item" />
          </el-checkbox-group>
          <svg-icon icon-class="op" slot="reference"></svg-icon>
        </el-popover>
      </section>
      <zyb-table :table-data="columnDataList" :table-columns="_tableColumns">
        <template v-slot:custom="{ data: { item, scope } }">
          <template v-if="item.prop === 'displayName'">
            <zyb-text @click="handleHistoryList(scope.row)">{{ scope.row.displayName }}</zyb-text>
          </template>

          <template v-else-if="item.prop === 'relateExp'">
            <!-- {{scope.row.experimentId}} -->
            <zyb-text v-if="scope.row.experimentId != 0" @click="showRelevance(scope.row)">
              <i class="el-icon-plus"></i>
              已关联1实验
            </zyb-text>
            <zyb-text v-if="scope.row.experimentId == 0" @click="goExperiment(scope.row)">
              <i class="el-icon-plus"></i>
            </zyb-text>
          </template>

          <template v-else-if="item.prop === 'action'">
            <a style="margin-right:10px;" @click="handleVersionManagement(scope.row)" v-if="false">版本管理</a>
            <el-button type="text" v-if="scope.row.status === 2" @click="removeFeatrue(scope.row)">删除</el-button>
            <el-button type="text" v-if="scope.row.canRelease" @click="publishFeatrue(scope.row)">发布</el-button>
          </template>
        </template>
      </zyb-table>

      <relevance-dialog
        :visible.sync="relevanceVisible"
        :relevanceData="relevanceData"
        v-if="relevanceVisible"
        @handleRelevanceDialogClose="handleRelevanceDialogClose"
      ></relevance-dialog>
      <div class="pagination-container">
        <el-pagination
          v-show="total"
          layout="total,sizes, prev, pager, next, jumper"
          :page-size="pagination.rn"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handlePageSizeChange"
          :current-page="pagination.pn"
          :total="total"
          @current-change="handlePageNoChange"
        ></el-pagination>
      </div>
    </div>
    <progress-dialog
        v-if="progressDialogVisible"
        :visible.sync="progressDialogVisible"
        :rollBackVisble="false"
        :featureName="currentData.displayName"
        :currentProgressVersion="{
          ...currentData,
          correctVersionId: currentData.releaseVersionId
        }"
        @handleProgressDialogClose="handleProgressDialogClose"
      ></progress-dialog>
  </Page>
</template>
<script>
import Page from '@/components/common/page/index.vue';
import ZybTable from '@/components/zyb-table/index.vue';
import ZybText from '@/components/zyb-text/index.vue';
import { formatDate } from "@common/utils";
import { mapGetters } from 'vuex';
import relevanceDialog from './components/relevance-dialog.vue';
import { CORRECT_PUBLISH_STATUS, FEATRUE_STATUS, KEYTYPELIST, STATUSOPTIONS } from './contants';
import ProgressDialog from './version-manage/components/progress-dialog.vue';

export default {
  components: {
    Page,
    relevanceDialog,
    ZybTable,
    ZybText,
    ProgressDialog
  },
  data() {
    const tableColumns = [
        {
          label: '固化id',
          prop: 'id'
        },
        {
          label: '固化名称',
          prop: 'displayName',
          title: true,
          custom: true
        },
        {
          label: '开关状态',
          prop: 'status',
          title: true,
          render: (scope) => {
            const target = FEATRUE_STATUS.find(item=>item.key === +scope.row.status) || {};
            return target.label || '-';
          }
        },
        {
          label: '发布状态',
          prop: 'publishStatus',
          title: true,
          render: (scope) => {
            const target = CORRECT_PUBLISH_STATUS.find(item=>item.key === +scope.row.publishStatus) || {};
            return target.label || '-';
          }
        },
        {
          label: 'key',
          prop: 'keyName'
        },
        {
          label: '业务线',
          prop: 'appKey',
          title: true,
          render: (scope) => {
            return this.getAppkeyLabel(scope.row.appKey);
          }
        },
        {
          label: '变体类型',
          prop: 'variantType',
          render: (scope) => {
            return KEYTYPELIST[scope.row.keyType];
          }
        },
        {
          label: '创建人',
          prop: 'createUser'
        },
        {
          label: '创建时间',
          prop: 'createTime',
          render: (scope) => {
            return formatDate(scope.row.createTime * 1000);
          }
        },
        {
          label: '关联实验',
          prop: 'relateExp',
          custom: true
        },
        {
          label: '操作',
          prop: 'action',
          custom: true
        }
    ];
    const localStorageColumns = localStorage.getItem('feature-list-checked-column');
    return {
      progressDialogVisible: false,
      currentData: {}, // 当前发布进度版本数据
      breadcrumbList: ['固化列表'],
      statusList: STATUSOPTIONS,
      appKeyList: [], //应用列表
      appKey: 'all',
      status: '',
      displayName: '',
      isMe: false,
      createTime: [],
      columnDataList: [], // table数据
      pagination: {
        rn: 10,
        pn: 1
      },
      total: 0,
      relevanceVisible: false,
      relevanceData: {},
      tableColumns,
      checkedColumns: localStorageColumns ? JSON.parse(localStorageColumns) : tableColumns.map(item=>item.label)
    };
  },
  created() {
    const param = {
      pn: 1,
      rn: 100000
    };
    const prevParams = localStorage.getItem('featrue_list_params');
    if (prevParams) {
      const params = JSON.parse(prevParams);
      const { isMe,createTime,displayName,appKey } = params;
      this.isMe = isMe;
      this.createTime = createTime;
      this.displayName = displayName;
      this.appKey = appKey;
    }
    this.getAppList(param);
  },
  methods: {
    goReport(row) {
      this.$router.push({
        path: '/app/manage/feature-report',
        query: {
          id: row.id,
        }
      });
      console.log('row', row);
    },
    handleAddFea() {
      this.$router.push({
        path: '/feat/list/detail',
        query: {
          opType: 'add',
          appkey: '',
          id: '',
          layerId: ''
        }
      });
    },
    handlePageSizeChange(val) {
      this.pagination.rn = val;
      this.getList();
    },
    handlePageNoChange(val) {
      this.pagination.pn = val;
      this.getList();
    },
    getList(){
      const params = {
        pn: this.pagination.pn,
        rn: this.pagination.rn,
        appKey: this.appKey,
        displayName: this.displayName,
        createUser: this.isMe ? this.userInfo.uname : '',
      };
      if(Array.isArray(this.createTime) && this.createTime.length === 2){
        params.createTimeStart = this.createTime[0] / 1000;
        params.createTimeEnd = this.createTime[1] / 1000 +86399;
      }
      this.getFeatureList(params);
    },
    // 获取固化列表
    getFeatureList(param) {
      this.$service
        .get('NEWFEATURELIST', param, { allback: 0, needLoading: true })
        .then((res) => {
          this.columnDataList = res.list;
          this.total = res.total;
          localStorage.setItem('featrue_list_params', JSON.stringify({
            appKey: this.appKey,
            displayName: this.displayName,
            createTime: this.createTime,
            isMe: this.isMe
          }));
        })
        .catch(() => {});
    },
    // 输入查询
    handleInputChange(val) {
      this.pagination.pn = 1;
      this.pagination.rn = 10;
      this.getList();
    },
    // 状态变化
    handleStatusChange() {},
    // appKey变化
    handleAppKeyChange(val) {
      this.pagination.pn = 1;
      this.getList();
    },
    handleChange(){
      this.pagination.pn = 1;
      this.getList();
    },
    getAppkeyLabel(param) {
      let result = '';
      for (let i = 0; i < this.appKeyList.length; i++) {
        const current = this.appKeyList[i];
        if (param == current.appKey) {
          result = current.displayName;
          break;
        }
      }
      return result;
    },
    // 关闭关联实验dialog
    handleRelevanceDialogClose(val) {
      this.relevanceVisible = val;
    },
    // 确认删除 - 暂时不做
    confirmDelete() {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
        })
        .catch(() => {});
    },
    // 跳转到版本管理
    handleVersionManagement(val) {
      this.$router.push({
        path: '/feat/list/manage',
        query: {
          id: val.id
        }
      });
    },
    // 跳转到版本管理
    handleHistoryList(val) {
      this.$router.push({
        path: '/feat/list/manage',
        query: {
          id: val.id
        }
      });
    },
    // 获取APP列表
    getAppList(param) {
      this.$service
        .get('FEATUREAPPLIST', param, { allback: 0, needLoading: false })
        .then((res) => {
          this.appKeyList = res.list;
          this.$nextTick(() => {
            const params = {
              pn: this.pagination.pn,
              rn: this.pagination.rn,
              appKey: this.appKey || 'homework',
              displayName: this.displayName,
              createUser: this.isMe ? this.userInfo.uname : '', 
            };
            this.getFeatureList(params);
          });
        })
        .catch(() => {});
    },
    // 展示关联实验
    showRelevance(val) {
      // console.log('关联数据：', val);
      this.relevanceData = val;
      this.relevanceVisible = true;
    },
    goExperiment(row) {
      const path = {
        path: '/exp-manage/list/edit',
        query: {
          // id: row.id,
          type: 'add',
          ex: 1,
          correctId: row.id,
        }
      };
      this.$router.push(path);
    },
    handleCheckedChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.checkedColumnsOptions.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.checkedColumnsOptions.length;
      localStorage.setItem('feature-list-checked-column', JSON.stringify(value));
    },
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    removeFeatrue(data){
      const { id, displayName } = data;
      this.$confirm(`确定删除固化${displayName}？删除后将无法恢复。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = this.formatFormData({
          id: id,
        });
        this.$service.get('DELETEFEATURE',{
          id: id,
        }, { allback: 0, needLoading: true }).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.getList();
        });
      }).catch(() => {
      });
    },
    publishFeatrue(row){
      console.log('publishFeatrue', row);
      this.progressDialogVisible= true;
      this.currentData = row;
    },
    handleProgressDialogClose() {
      this.progressDialogVisible = false;
      this.getList();
    },
  },
  computed: {
    ...mapGetters(['userInfo']),
    _tableColumns() {
      const str = this.checkedColumns.toString();
      return this.tableColumns.filter((f) => this.checkedColumns.some((sf) => sf === f.label));
    },
    checkedColumnsOptions(){
      const res = [];
      this.tableColumns.forEach(item => {
        if(item.prop !== 'action'){
          res.push(item.label);
        }
      });
      return res;
    }
  }
};
</script>
<style scoped lang="less">

.btn_add {
  position: absolute;
  top: -2px;
  right: 0;
}

.feature-container {
  position: relative;
  width: 100%;
}
.feature-search {
  display: flex;
  justify-content: flex-start;

  /deep/ .el-form--inline .el-form-item {
    margin-right: 14px;
  }
}

.checkbox {
  display: block;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.search-input {
  width: 240px;
}
/deep/ .el-button--small {
  font-size: 14px;
}

/deep/ .el-button--text-primary > span {
  font-size: 14px;
}
</style>

