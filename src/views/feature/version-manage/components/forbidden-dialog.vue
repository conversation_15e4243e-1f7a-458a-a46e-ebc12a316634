<template>
  <el-dialog
    size="small"
    :visible.sync="dialogVisible"
    @close="$emit('handleForbiddenDialogClose', false)"
  >
    <!--  dialog标题 -->
    <div slot="title" class="dialog-title">
      <span
        role="img"
        aria-label="exclamation-circle"
        class="anticon anticon-exclamation-circle"
        style="color: rgb(250, 173, 20); margin-right: 8px"
      >
        <svg
          viewBox="64 64 896 896"
          focusable="false"
          class=""
          data-icon="exclamation-circle"
          width="1em"
          height="1em"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
          ></path>
        </svg>
      </span>
      <span>提示</span>
    </div>
    <!-- dialog内容 -->
    <div class="dialog-notice">
      <p>您已触发禁用版本，禁用后不可编辑、发布，亦无法解禁。请确认是否禁用？</p>
      <el-form class="dialog-form" :rules="rules" :model="dialogData" ref="dialogData">
        <el-form-item label="说明原因：" required prop="textarea">
          <el-input
            ref="textarea"
            type="textarea"
            placeholder="请输入内容"
            v-model="dialogData.textarea"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template slot="footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="setForbiddenReason('dialogData')">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
export default {
  name: 'forbidden-dialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentForbiddenVersion: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogData: {
        textarea: ''
      },
      featureId: this.$route.query.id,
      dialogVisible: this.visible,
      currentForbiddenVersionData: this.currentForbiddenVersion,
      rules: {
        textarea: [{ required: true, message: '请输入禁用原因', trigger: 'blur' }]
      }
    };
  },
  watch: {
    visible() {
      this.dialogVisible = this.visible;
    }
  },
  methods: {
    // 禁用
    setForbiddenReason(formData) {
      this.$refs[formData].validate((valid) => {
        if (valid) {
          if (this.dialogData.textarea) {
            const param = {
              id: this.featureId,
              correctVersionId: this.currentForbiddenVersionData.correctVersionId,
              reason: this.dialogData.textarea
            };
            this.$service
              .get('NEWDISABLE', param, {
                allback: 0,
                needLoading: false
              })
              .then(() => {
                this.$emit('handleForbiddenDialogClose', false);
                this.$message({
                  type: 'success',
                  message: '禁用成功!'
                });
              })
              .catch(() => {});
          }
        }
      });
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .el-dialog__headerbtn {
  padding: 20px;
}
/deep/ .el-dialog__header {
  padding-bottom: 0;
}
/deep/ .el-dialog__body {
  padding-bottom: 0;
}
/deep/ .el-dialog__footer {
  padding-right: 36px;
}
/deep/ .el-form-item__error {
  left: 88px;
}
/deep/ .el-textarea {
  width: 80%;
  border-radius: 4px;
}

.dialog-title {
  padding: 20px;
  text-align: left;
  font-size: 14px;
  font-weight: 700;
  overflow: hidden;
}
.dialog-notice {
  padding: 0 20px;
  color: rgb(139, 139, 166);
}
.dialog-form {
  padding-top: 10px;
}
</style>


