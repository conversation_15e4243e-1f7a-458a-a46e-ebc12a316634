<template>
  <el-dialog
    width="60%"
    min-height="200px"
    :visible.sync="dialogVisible"
    @close="$emit('handleProgressDialogClose', false)"
  >
    <!--  dialog标题 -->
    <div slot="title" class="dialog-title">
      <p class="dialog-title-item">「{{ featureNameData }}」</p>
      <p class="dialog-title-item">V{{ currentProgressVersionData.currentVersion }}版本</p>
      <p class="dialog-title-item">发布计划</p>
      <p
        class="dialog-title-item not-release"
        v-show="currentProgressVersionData.publishStatus === 1"
      >
        未发布
      </p>
      <p
        class="dialog-title-item on-release"
        v-show="currentProgressVersionData.publishStatus === 2 && currentProgressVersionData.flow < 1000"
      >
        发布中
      </p>
      <p
        class="dialog-title-item on-full"
        v-show="(currentProgressVersionData.publishStatus === 2 && currentProgressVersionData.flow === 1000) || currentProgressVersionData.publishStatus === 5"
      >
        已全量
      </p>
      <p
        class="dialog-title-item on-roll-back"
        v-show="currentProgressVersionData.publishStatus === 3"
      >
        已回滚
      </p>
      <p
        class="dialog-title-item on-forbidden"
        v-show="currentProgressVersionData.publishStatus === 4"
      >
        已禁用
      </p>
    </div>
    <div class="roll-back-notice" v-if="!!currentProgressVersionData.releasedVersion">
      <span
        role="img"
        aria-label="exclamation-circle"
        class="anticon anticon-exclamation-circle"
        style="color: rgb(250, 173, 20); margin-right: 8px"
      >
        <svg
          viewBox="64 64 896 896"
          focusable="false"
          class=""
          data-icon="exclamation-circle"
          width="1em"
          height="1em"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
          ></path>
        </svg>
      </span>
      <span style="color: red">
        您已触发覆盖发布，V{{ currentProgressVersionData.currentVersion }}
        版本将会覆盖当前 V{{ currentProgressVersionData.releasedVersion.currentVersion }} 已全量的版本，是否覆盖？
      </span>
      <el-button type="text-primary" @click="handleShowRelease">确认覆盖</el-button>
    </div>
    <div v-if="showRelease">
      <!-- dialog内容 -->
      <div class="dialog-notice">
        <el-form
          class="dialog-form"
          label-position="right"
          label-width="120px"
          :rules="rules"
          :model="dialogData"
          ref="dialogData"
        >
          <el-form-item label="发布方案" required>
            <el-select v-model="releaseType" disabled>
              <el-option
                v-for="(item, index) in releaseTypeList"
                :key="index"
                :label="item.label"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="流量控制" required style="margin-right: 25px">
            <el-slider
              v-model="flow"
              show-input
              :step="0.1"
              v-if="flowChange"
              :disabled="flowDisabled"
            ></el-slider>
            <span class="percentUnit">%</span>
          </el-form-item>
          <div class="roll-back-notice" v-if="showRollBack">
            <span
              role="img"
              aria-label="exclamation-circle"
              class="anticon anticon-exclamation-circle"
              style="color: rgb(250, 173, 20); margin-right: 8px"
            >
              <svg
                viewBox="64 64 896 896"
                focusable="false"
                class=""
                data-icon="exclamation-circle"
                width="1em"
                height="1em"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                ></path>
              </svg>
            </span>
            <span>
              您已触发回滚，请选择历史已全量的版本，其中[本地默认]会关闭当前固化生效本地默认值。
            </span>
            <el-button type="text-primary" @click="handleCancelRollBack">取消回滚</el-button>
          </div>
          <el-form-item label="回滚版本" required prop="rollbackVersionId" v-if="showRollBack">
            <el-select v-model="dialogData.rollbackVersionId">
              <el-option
                v-for="(item, index) in rollbackVersionList"
                :key="index"
                :label="item.label"
                :value="item.key"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="回滚原因" required prop="textarea" v-if="showRollBack">
            <el-input
              ref="textarea"
              type="textarea"
              v-model="dialogData.textarea"
              placeholder="请输入50个字符以内的回滚原因"
              maxlength="50"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <!-- 按钮 -->
      <div style="display: flex; justify-content: flex-end; margin-right: 20px">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleRollBack('dialogData')"
          v-if="rollBackVisble"
          v-show="currentProgressVersionData.publishStatus === 2 || currentProgressVersionData.publishStatus === 5"
        >
          回滚
        </el-button>
        <el-button
          type="primary"
          @click="handleIncrement"
          v-if="currentProgressVersionData.publishStatus === 1"
        >
          发布
        </el-button>
        <el-button
          type="primary"
          @click="handleIncrement"
          :disabled="rollBackDisabled"
          v-show="(currentProgressVersionData.publishStatus === 2 || currentProgressVersionData.publishStatus === 5) && currentProgressVersionData.flow <= 1000"
        >
          增量发布
        </el-button>
      </div>
    </div>
    <template v-slot:footer>
      <span/>
    </template>
  </el-dialog>
</template>
<script>
import { RELEASETYPELISTOPTION } from '../../contants';
export default {
  name: 'process-dialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rollBackVisble: {
      type: Boolean,
      default: true
    },
    currentProgressVersion: {
      type: Object,
      default: () => ({})
    },
    featureName: {
      type: String,
      default: ''
    },
    versionList: {
      type: Array,
      default: ()=>[]
    }
  },
  data() {
    return {
      overRelease: !!this.currentProgressVersion.releasedVersion, // 是否覆盖发布
      showRelease: !this.currentProgressVersion.releasedVersion, // 是否显示流量控制项
      flowChange: true, // 控制流量条的显示
      dialogData: {
        rollbackVersionId: '',
        textarea: ''
      },
      rules: {
        textarea: [{ required: true, message: '请输入回滚原因', trigger: 'blur' }],
        rollbackVersionId: [{required: true, message: '请选择回滚版本', trigger: 'change'}]
      },
      releaseType: 2, // 发布方式，暂时只支持手动发布
      releaseTypeList: RELEASETYPELISTOPTION, // 发布方式列表
      dialogVisible: this.visible,
      currentProgressVersionData: this.currentProgressVersion, //当前版本数据
      featureNameData: this.featureName,
      flow: this.currentProgressVersion.flow / 10,
      showRollBack: false,
      disabled:
        this.currentProgressVersion.publishStatus === 3 ||
        this.currentProgressVersion.publishStatus === 4,
      rollBackDisabled: false,
      rollbackVersionDefault: {label: '本地默认', key: -1},
    };
  },
  computed: {
    // 流量进度条的控制
    flowDisabled() {
      return this.disabled || this.rollBackDisabled;
    },
    rollbackVersionList() {
      // 获取回滚状态并且历史上已全量的版本
      let list = this.versionList
        .filter(v => v.publishStatus === 3 && v.flow === 1000)
        .map(v => ({label: `V${v.currentVersion}`, key: v.correctVersionId}));
      list.push(this.rollbackVersionDefault);
      return list;
    }
  },
  watch: {
    // 在增量发布时监听流量值，不可选择比当前流量更小的值，暴力处理
    flow(newVal) {
      if ((this.overRelease || this.currentProgressVersionData.publishStatus === 2)
        && newVal < this.currentProgressVersion.flow / 10) {
        this.flowChange = false;
        this.flow = this.currentProgressVersion.flow / 10;
        this.$nextTick(() => {
          this.flowChange = true;
        });
      }
    }
  },
  methods: {
    handleShowRelease() {
      this.showRelease = true;
    },
    // 回滚
    handleRollBack(formData) {
      this.showRollBack = true;
      this.rollBackDisabled = true;
      this.$refs[formData].validate((valid) => {
        if (valid) {
          if (this.dialogData.textarea && this.dialogData.rollbackVersionId) {
            const param = {
              id: this.$route.query.id,
              correctVersionId: this.currentProgressVersionData.correctVersionId,
              reason: this.dialogData.textarea,
              releaseVersionId: this.dialogData.rollbackVersionId
            };
            this.$service
              .get('NEWROLLBACK', param, {
                allback: 0,
                needLoading: false
              })
              .then(() => {
                this.$emit('handleProgressDialogClose', false);
                this.$message({
                  type: 'success',
                  message: '回滚成功!'
                });
              })
              .catch(() => {});
          }
        }
      });
    },
    // 取消回滚
    handleCancelRollBack() {
      this.showRollBack = false;
      this.rollBackDisabled = false;
      this.dialogData.rollbackVersion = '';
      this.dialogData.textarea = '';
    },
    // 发布和增量发布
    handleIncrement() {
      // 常规发布需要检查 新设置流量值 大于 原有流量值，覆盖发布都是100%，无需检查
      if(!this.overRelease && this.flow < this.currentProgressVersionData.flow / 10) {
        this.$message.warning(`无效流量：${this.flow}%，新流量值必须大于原有流量值${this.currentProgressVersionData.flow / 10}%`);
        return;
      }
      const rollbackVersionId = this.overRelease ? this.currentProgressVersionData.releasedVersion.correctVersionId : -1;
      const param = {
        id: this.$route.query.id || this.currentProgressVersionData.id,
        correctVersionId: this.currentProgressVersionData.correctVersionId ,
        flow: this.flow * 10,
        rollbackVersionId: rollbackVersionId, // 覆盖发布时，需要回滚的版本
      };
      this.$service.get('NEWRELEASE', param, {
        allback: 0,
        needLoading: false
      })
        .then(() => {
          this.$emit('handleProgressDialogClose', false);
          this.$message({
            type: 'success',
            message: '发布成功!'
          });
        })
        .catch(() => {});
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .el-input-number--small .el-input__inner {
  padding: 0;
}
/deep/ .el-dialog__headerbtn {
  padding: 20px;
}
/deep/ .el-dialog__header {
  padding-bottom: 0;
}
/deep/ .el-dialog__body {
  padding-bottom: 0;
}
/deep/ .el-dialog__footer {
  padding-right: 36px;
}
/deep/ .el-textarea {
  width: 80%;
  border-radius: 4px;
}
.percentUnit {
  position: absolute;
  right: -15px;
  top: 5px;
}
.dialog-title {
  font-size: 14px;
  display: flex;
  justify-content: flex-start;
  padding: 20px;
  font-weight: 700;
  overflow: hidden;
}
.dialog-notice {
  padding: 0;
  color: rgb(139, 139, 166);
}
.dialog-form {
  padding-top: 10px;
}

.dialog-title-item {
  margin-right: 3px;
  padding: 5px;
}
.on-release {
  background-color: rgb(87, 161, 89);
  color: #ffffff;
  border-radius: 4px;
}
.on-full {
  background-color: rgb(10,106,245);
  color: #ffffff;
  border-radius: 4px;
}
.not-release {
  background-color: rgb(118, 122, 138);
  color: #ffffff;
  border-radius: 4px;
}
.on-forbidden {
  background-color: rgb(79, 79, 91);
  color: #ffffff;
  border-radius: 4px;
}
.on-roll-back {
  background-color: rgb(228, 146, 72);
  color: #ffffff;
  border-radius: 4px;
}
.roll-back-notice {
  margin-left: 40px;
}
</style>
