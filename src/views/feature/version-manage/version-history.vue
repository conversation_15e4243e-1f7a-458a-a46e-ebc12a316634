<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
    <section class="header">
      <section>
        <i class="el-icon-arrow-left" @click="handleGoBack()"></i>
        <span class="page-title">固化版本管理</span>
      </section>
    </section>
    <div class="feature-container">
      <h2 class="title">「{{ featureName }}」历史版本</h2>
      <el-timeline class="all-highlight history-timeline">
        <template v-for="(item, index) in versionList">
          <el-timeline-item :key="index">
            <div class="left-container">
              <p class="time" v-if="item.startTime && !item.endTime">生效时间: {{ item.startTime }} ~ 至今</p>
              <p class="time" v-if="item.startTime && item.endTime">生效时间: {{ item.startTime }} ~ {{ item.endTime }}</p>
              <p class="time">
                {{ item.createUser }} 创建于 {{ item.createTime }}
                <span class="onEffect" v-if="item.publishStatus === 2">生效中</span>
              </p>
            </div>
            <div class="right-container">
              <div class="right-container-left-content">
                <el-button type="text" @click="handleFeatureCheck(item)">
                  V{{ item.currentVersion }}
                </el-button>
                <!-- 状态图标 -->
                <!-- 编辑 -->
                <span
                  v-show="item.publishStatus === 1"
                  class="feature-version-item-content-start-circle"
                  style="background-color: rgb(118, 122, 138)"
                >
                  <span
                    role="img"
                    aria-label="form"
                    class="anticon anticon-form feature-version-status-color"
                  >
                    <svg
                      viewBox="64 64 896 896"
                      focusable="false"
                      class=""
                      data-icon="form"
                      width="1em"
                      height="1em"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        d="M904 512h-56c-4.4 0-8 3.6-8 8v320H184V184h320c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V520c0-4.4-3.6-8-8-8z"
                      ></path>
                      <path
                        d="M355.9 534.9L354 653.8c-.1 8.9 7.1 16.2 16 16.2h.4l118-2.9c2-.1 4-.9 5.4-2.3l415.9-415c3.1-3.1 3.1-8.2 0-11.3L785.4 114.3c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-415.8 415a8.3 8.3 0 00-2.3 5.6zm63.5 23.6L779.7 199l45.2 45.1-360.5 359.7-45.7 1.1.7-46.4z"
                      ></path>
                    </svg>
                  </span>
                </span>
                <!-- 发布中 -->
                <span
                  v-show="item.publishStatus === 2 && item.flow < 1000"
                  class="feature-version-item-content-start-circle"
                  style="background-color: rgb(87, 161, 89)"
                >
                  <span
                    role="img"
                    aria-label="clock-circle"
                    class="anticon anticon-clock-circle feature-version-status-color"
                  >
                    <svg
                      viewBox="64 64 896 896"
                      focusable="false"
                      class=""
                      data-icon="clock-circle"
                      width="1em"
                      height="1em"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm176.5 585.7l-28.6 39a7.99 7.99 0 01-11.2 1.7L483.3 569.8a7.92 7.92 0 01-3.3-6.5V288c0-4.4 3.6-8 8-8h48.1c4.4 0 8 3.6 8 8v247.5l142.6 103.1c3.6 2.5 4.4 7.5 1.8 11.1z"
                      ></path>
                    </svg>
                  </span>
                </span>
                <span
                  v-show="item.publishStatus === 2 && item.flow === 1000"
                  class="feature-version-item-content-start-circle"
                  style="background-color: rgb(10,106,245)"
                >
                  <span
                    role="img"
                    aria-label="clock-circle"
                    class="anticon anticon-clock-circle feature-version-status-color"
                  >
                    <svg
                      viewBox="64 64 896 896"
                      focusable="false"
                      class=""
                      data-icon="clock-circle"
                      width="1em"
                      height="1em"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm176.5 585.7l-28.6 39a7.99 7.99 0 01-11.2 1.7L483.3 569.8a7.92 7.92 0 01-3.3-6.5V288c0-4.4 3.6-8 8-8h48.1c4.4 0 8 3.6 8 8v247.5l142.6 103.1c3.6 2.5 4.4 7.5 1.8 11.1z"
                      ></path>
                    </svg>
                  </span>
                </span>
                <!-- 回滚 -->
                <span
                  v-show="item.publishStatus === 3"
                  class="feature-version-item-content-start-circle"
                  style="background-color: rgb(228, 146, 72)"
                >
                  <span
                    role="img"
                    aria-label="reload"
                    class="anticon anticon-reload feature-version-status-color"
                  >
                    <svg
                      viewBox="64 64 896 896"
                      focusable="false"
                      class=""
                      data-icon="reload"
                      width="1em"
                      height="1em"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        d="M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"
                      ></path>
                    </svg>
                  </span>
                </span>
                <!-- 禁用 -->
                <span
                  v-show="item.publishStatus === 4"
                  class="feature-version-item-content-start-circle"
                  style="background-color: rgb(79, 79, 91)"
                >
                  <span
                    role="img"
                    aria-label="stop"
                    class="anticon anticon-stop feature-version-status-color"
                  >
                    <svg
                      viewBox="64 64 896 896"
                      focusable="false"
                      class=""
                      data-icon="stop"
                      width="1em"
                      height="1em"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"
                      ></path>
                    </svg>
                  </span>
                </span>
                <!-- 状态文字 -->
                <span class="icon icon-right" v-show="item.publishStatus === 1">未发布</span>
                <span class="icon icon-right" v-show="item.publishStatus === 2 && item.flow < 1000">发布中</span>
                <span class="icon icon-right" v-show="item.publishStatus === 2 && item.flow === 1000">已全量</span>
                <span class="icon icon-right" v-show="item.publishStatus === 3">已回滚</span>
                <span class="icon icon-right" v-show="item.publishStatus === 4">已禁用</span>
                <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <span class="icon icon-right" v-show="item.publishStatus === 2 || item.publishStatus === 3">发布进度: {{item.flow / 10}}%</span>
              </div>
              <div class="right-container-right-content">
                <el-tooltip
                  effect="light"
                  placement="top"
                  content="编辑"
                  v-if="item.publishStatus === 1 || item.publishStatus === 2"
                >
                  <span class="icon-circle icon" @click="handleEdit(item)">
                    <span role="img" aria-label="edit" class="anticon anticon-edit">
                      <svg
                        viewBox="64 64 896 896"
                        focusable="false"
                        class=""
                        data-icon="edit"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          d="M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"
                        ></path>
                      </svg>
                    </span>
                  </span>
                </el-tooltip>
                <el-tooltip
                  effect="light"
                  placement="top"
                  :content="getContent(item)"
                  v-if="item.publishStatus === 1"
                >
                  <span class="icon-circle icon">
                    <span
                      @click="onRelease(item)"
                      :class="{ disabled: !isRelease(item) }"
                      role="img"
                      aria-label="send"
                      class="anticon anticon-send"
                    >
                      <svg
                        viewBox="64 64 896 896"
                        focusable="false"
                        class=""
                        data-icon="send"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          d="M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"
                        ></path>
                      </svg>
                    </span>
                  </span>
                </el-tooltip>
                <el-tooltip effect="light" placement="top" :content="getForbiddenContent(item)">
                  <span
                    class="icon-circle icon"
                    v-if="item.publishStatus === 1 || item.publishStatus === 3"
                  >
                    <span
                      role="img"
                      aria-label="stop"
                      class="anticon anticon-stop"
                      :class="{ disabled: forbiddenDisabled }"
                      @click="handleForbidden(item)"
                    >
                      <svg
                        viewBox="64 64 896 896"
                        focusable="false"
                        class=""
                        data-icon="stop"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"
                        ></path>
                      </svg>
                    </span>
                  </span>
                </el-tooltip>
                <el-tooltip
                  effect="light"
                  placement="top"
                  content="查看发布进度"
                  v-if="item.publishStatus === 2 || item.publishStatus === 3"
                >
                  <span class="icon-circle icon" @click="checkProgress(item)">
                    <span role="img" aria-label="history" class="anticon anticon-history">
                      <svg
                        viewBox="64 64 896 896"
                        focusable="false"
                        class=""
                        data-icon="history"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          d="M536.1 273H488c-4.4 0-8 3.6-8 8v275.3c0 2.6 1.2 5 3.3 6.5l165.3 120.7c3.6 2.6 8.6 1.9 11.2-1.7l28.6-39c2.7-3.7 1.9-8.7-1.7-11.2L544.1 528.5V281c0-4.4-3.6-8-8-8zm219.8 75.2l156.8 38.3c5 1.2 9.9-2.6 9.9-7.7l.8-161.5c0-6.7-7.7-10.5-12.9-6.3L752.9 334.1a8 8 0 003 14.1zm167.7 301.1l-56.7-19.5a8 8 0 00-10.1 4.8c-1.9 5.1-3.9 10.1-6 15.1-17.8 42.1-43.3 80-75.9 112.5a353 353 0 01-112.5 75.9 352.18 352.18 0 01-137.7 27.8c-47.8 0-94.1-9.3-137.7-27.8a353 353 0 01-112.5-75.9c-32.5-32.5-58-70.4-75.9-112.5A353.44 353.44 0 01171 512c0-47.8 9.3-94.2 27.8-137.8 17.8-42.1 43.3-80 75.9-112.5a353 353 0 01112.5-75.9C430.6 167.3 477 158 524.8 158s94.1 9.3 137.7 27.8A353 353 0 01775 261.7c10.2 10.3 19.8 21 28.6 32.3l59.8-46.8C784.7 146.6 662.2 81.9 524.6 82 285 82.1 92.6 276.7 95 516.4 97.4 751.9 288.9 942 524.8 942c185.5 0 343.5-117.6 403.7-282.3 1.5-4.2-.7-8.9-4.9-10.4z"
                        ></path>
                      </svg>
                    </span>
                  </span>
                </el-tooltip>
                <el-tooltip
                  effect="light"
                  placement="top"
                  content="设为最新版本"
                  v-if="item.publishStatus === 3"
                >
                  <span class="icon-circle icon" @click="setNewVersion(item)">
                    <span role="img" aria-label="desktop" class="anticon anticon-desktop">
                      <svg
                        viewBox="64 64 896 896"
                        focusable="false"
                        class=""
                        data-icon="desktop"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          d="M928 140H96c-17.7 0-32 14.3-32 32v496c0 17.7 14.3 32 32 32h380v112H304c-8.8 0-16 7.2-16 16v48c0 4.4 3.6 8 8 8h432c4.4 0 8-3.6 8-8v-48c0-8.8-7.2-16-16-16H548V700h380c17.7 0 32-14.3 32-32V172c0-17.7-14.3-32-32-32zm-40 488H136V212h752v416z"
                        ></path>
                      </svg>
                    </span>
                  </span>
                </el-tooltip>
                <el-tooltip
                  effect="light"
                  placement="top"
                  content="查看发布计划"
                  v-if="item.publishStatus === 4"
                >
                  <span
                    class="icon-circle icon"
                    @click="checkPlan(item)"
                    v-if="item.publishStatus === 4 && item.flow !== 0"
                  >
                    <span role="img" aria-label="calendar" class="anticon anticon-calendar">
                      <svg
                        viewBox="64 64 896 896"
                        focusable="false"
                        class=""
                        data-icon="calendar"
                        width="1em"
                        height="1em"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                        ></path>
                      </svg>
                    </span>
                  </span>
                </el-tooltip>
              </div>
            </div>
          </el-timeline-item>
        </template>
      </el-timeline>
      <forbidden-dialog
        v-if="forbiddenDialogVisible"
        :visible.sync="forbiddenDialogVisible"
        :currentForbiddenVersion="currentForbiddenVersion"
        @handleForbiddenDialogClose="handleForbiddenDialogClose"
      ></forbidden-dialog>
      <progress-dialog
        v-if="progressDialogVisible"
        :visible.sync="progressDialogVisible"
        :featureName="featureName"
        :currentProgressVersion="currentProgressVersion"
        :versionList="versionList"
        @handleProgressDialogClose="handleProgressDialogClose"
      ></progress-dialog>
    </div>
  </Page>
</template>
<script>
import Page from '@/components/common/page/index.vue';
import { formatDate } from "@common/utils";
import forbiddenDialog from './components/forbidden-dialog.vue';
import progressDialog from './components/progress-dialog.vue';

export default {
  name: 'version-history',
  components: {
    forbiddenDialog,
    progressDialog,
    Page
  },
  data() {
    return {
      breadcrumbList: [],
      forbiddenDisabled: true,
      appkey: '',
      versionList: [],
      layerId: '', // 固化层ID
      id: '', // 固化ID
      featureName: '', // 固化名称
      forbiddenDialogVisible: false,
      currentForbiddenVersion: {}, //当前禁用版本数据
      progressDialogVisible: false,
      currentProgressVersion: {} // 当前发布进度版本数据
    };
  },
  created() {
    const id = this.$route.query.id; // 固化ID
    const param = {
      id
    };
    this.getFeatureHistory(param);
  },
  methods: {
    handleGoBack(flag = false) {
      this.$router.push({
        path: '/feat/list'
      });
    },
    // 版本历史
    getFeatureHistory(param) {
      this.$service
        .get('NEWVERSIONLIST', param, { allback: 0, needLoading: true })
        .then((res) => {
          if (res && res.versionList.length > 0) {
            this.versionList = res.versionList;
            this.layerId = res.layerId;
            this.id = res.id;
            this.featureName = res.displayName;
            this.appkey = res.appKey ? res.appKey : '';
            this.versionList.forEach((item) => {
              if (item.createTime) {
                item.createTime = formatDate(item.createTime * 1000);
              }
              if (item.startTime) {
                item.startTime = formatDate(item.startTime * 1000);
              }
              if (item.endTime) {
                item.endTime = formatDate(item.endTime * 1000);
              }
            });
          }
        })
        .catch(() => {});
    },
    // 跳转固化详情
    handleFeatureCheck(item) {
      console.log('edit', item);

      this.$router.push({
        path: '/feat/list/detail',
        query: {
          appkey: this.appkey,
          id: this.id,
          correctVersionId: item.correctVersionId,
          type: 'detail'
        }
      });
    },
    // 跳转固化编辑
    handleEdit(item) {
      // debugger
      this.$router.push({
        path: '/feat/list/detail',
        query: {
          appkey: this.appkey,
          id: this.id,
          correctVersionId: item.correctVersionId
        }
      });
    },
    // 禁用
    handleForbidden(item) {
      this.currentForbiddenVersion = item;
      this.forbiddenDialogVisible = true;
    },
    // 关闭禁用dialog
    handleForbiddenDialogClose(param) {
      //  刷新列表
      this.getFeatureHistory({
        id: this.$route.query.id
      });
      this.forbiddenDialogVisible = param;
    },
    // 查看发布进度
    checkProgress(item) {
      this.progressDialogVisible = true;
      this.currentProgressVersion = item;
    },
    // 关闭发布进度dialog
    handleProgressDialogClose(param) {
      //  刷新列表
      this.getFeatureHistory({
        id: this.$route.query.id
      });
      this.progressDialogVisible = param;
    },
    // 查看发布计划
    checkPlan(item) {
      this.progressDialogVisible = true;
      this.currentProgressVersion = item;
    },
    // 设为最新版本
    setNewVersion(val) {
      this.$router.push({
        path: '/feat/list/detail',
        query: {
          isNewVersion: 1,
          appkey: this.appkey,
          id: this.$route.query.id,
          correctVersionId: val.correctVersionId,
          // layerId: val.layerId
        }
      });
    },
    // 发布
    onRelease(item) {
      this.currentProgressVersion = item;
      //如果是已全量再发布，设置flow默认为1000（只有发布中并且已经全量才能再次进行发布，并且只能是flow只能是全量，同时设置overRelease为true）
      let version = this.versionList.find((item) => item.publishStatus === 2 && item.flow === 1000);
      if(version) {
        this.currentProgressVersion.releasedVersion = version;
        this.currentProgressVersion.flow = 1000;
      }
      this.progressDialogVisible = true;
    },
    // 是否禁止发布的content
    getContent(val) {
      const version = this.versionList.find((item) => item.publishStatus === 2);
      const curVersion = version? version.currentVersion: 'unknown';
      return this.isRelease(val) ? '发布' : `V${curVersion} 版本待发布/发布中，暂不支持设置发布计划`;
    },
    //是否可以发布，canRelease === 1 或者当前发布版本全量时 可以发布
    isRelease(val) {
      return val.canRelease === 1 ||
        this.versionList.some((item) => item.publishStatus === 2 && item.flow === 1000);
    },
    getForbiddenContent(val) {
      const canDisable = val.canDisable;
      if (canDisable === 1) {
        this.forbiddenDisabled = false;
      }
      return canDisable === 1 ? '禁用' : '当前feature只剩下1个版本，不支持禁用，您可前往feature列表删除该feature';
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .el-timespot__right.is-small {
  width: 80%;
}

.header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e1e3e9;
  padding-bottom: 6px;

  section {
    display: flex;
    align-items: center;
    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin: 0 8px;
    }

    i {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.title {
  font-size: 14px;
  font-weight: 700;
}
.feature-container {
  padding: 15px;
  background: #ffffff;
  font-size: 14px;
}
.left-container {
  display: flex;
  flex-direction: column;
  justify-content: left;
  font-size: 14px;
  // margin-left: 30px;
}
.right-container {
  display: flex;
  // margin-left: 30px;
  margin-bottom: 12px;
  border: 1px solid #e2e4e8;
  border-radius: 7px;
  padding: 18px 40px 18px 15px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 14px;
  box-sizing: border-box;
}
.history-timeline {
  margin: 24px 20px 0 12px;
}

.icon {
  margin-left: 15px;
}
.icon-right {
  padding: 0 5px;
}
.icon-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f4f4f4;
  color: #424242;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.feature-version-item-content-start-tag {
  background: #f4f6f9;
  border: 1px solid #e7e9f5;
  box-sizing: border-box;
  border-radius: 12px;
  padding: 4px 8px;
  max-width: 80px;
  margin-left: 10px;
}
.feature-version-item-content-start-circle {
  margin-left: 20px;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.feature-version-status-color {
  color: #ffffff;
  font-weight: 700;
}
.onEffect {
  color: rgb(87, 161, 89);
  margin-left: 9px;
}

.disabled {
  cursor: pointer;
  pointer-events: none;
}

.time {
  margin-bottom: 12px;
}

/deep/ .el-timeline-item__content {
  padding-top: 3px;
}
</style>

