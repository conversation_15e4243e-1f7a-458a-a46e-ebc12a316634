<template>
  <div :class="['release-audience', isDetail ? 'edit' : 'detail']">
    <h3 class="basic-title">3、发布受众</h3>
    <el-row class="release-audience-container">
      <el-col :span="1">
        <p class="white-list-if">if</p>
      </el-col>
      <el-col :span="23">
        <div class="white-list">
          <p class="white-list-title">受众白名单</p>
          <i class="el-icon-question"></i>
          <p>发布范围优先判断白名单</p>
          <!-- =={{audienceData.versionList}} -->
        </div>
        <!--TOOD 设置固定高度，然后下拉边框-->
        <el-scrollbar class="scroll-container">
          <div class="white-list-container">
            <div class="white-list-item" v-for="(item, index) in audienceData.versionList" :key="index">
              <div class="white-list-item-box"></div>
              <div class="white-list-item-name">
                <el-popover trigger="hover" placement="top">
                  <p>{{ item.displayName }}</p>
                  <p slot="reference">{{ item.displayName }}</p>
                </el-popover>
              </div>
              <!-- {{item}} -->
              <div class="white-list-item-name-tag" v-if="false"> // 不显示变体value
                <el-popover trigger="hover" placement="top">
                  <!-- <p>{{ item.featureList[0].variant.keyValue }}</p> -->
                  <!-- <p>{{ item.keyValue }}</p> -->
                  <p>{{ item.keyValue }}</p>
                  <p slot="reference">
                    <!-- {{ item.featureList[0].variant.keyValue }} -->
                    <!-- {{ item.keyValue }} -->
                    {{ item.keyValue }}
                  </p>
                </el-popover>
              </div>
              <!-- value-key="cuid" -->
              <!-- 变体 白名单下拉 -->
              <el-select :disabled="disabled" size="mini" multiple class="setVariant-select-whitelist" v-model="item.whitelist" value-key="cuid" filterable default-first-option @change="(val) => {
    whiteChange(val, index);
  }
    ">
                <!-- <el-option
                  v-for="(item, ind) in whiteListOption"
                  :key="item.cuid"
                  :label="item.displayName"
                  :value="item"
                  :disabled="item.selected"
                ></el-option> -->
                <el-option v-for="(it, ind) in whiteListOption" :key="it.cuid" :label="it.displayName" :value="it" :disabled="it.selected"></el-option>
              </el-select>
              <!-- <i class="el-icon-close icon-close delVar_style" @click="deleteWhite(index)"></i> -->
            </div>
          </div>
          <!-- <div class="white-list-container"></div> -->
        </el-scrollbar>
      </el-col>
    </el-row>
    <el-row class="release-audience-container">
      <el-col :span="1">
        <div class="white-list-elseif">elseif</div>
      </el-col>
      <el-col :span="23">
        <div class="white-list">
          <p class="white-list-title">自定义受众规则</p>
          <i class="el-icon-question"></i>
          <p>白名单完成命中后按照自上而下判断发布规则</p>
          <!-- {{audienceData.filterRule}} -->
        </div>
        <div class="white-list-container">
          <div class="audience-rules">
            <el-row>
              <el-col :span="21">
                <draggable v-model="audienceData.filterRule" animation="500">
                  <div v-for="(item, index) in audienceData.filterRule" :key="`filterRule${index}`">
                    <div v-if="item.filterList.length === 0">
                      <el-tag type="info">IF TRUE</el-tag>
                      <el-alert class="audience-rules-alert" show-icon type="error" title="无过滤条件时，当前剩余流量将会按发布范围全部上线，后续设定的定向规则失效"></el-alert>
                    </div>
                    <div class="filterList">
                      <div class="add-rule-qie" v-if="item.filterList.length > 1">且</div>
                      <div v-for="(solist, indexs) in item.filterList" :key="`filterList${indexs}`" class="filterListStyle">
                        <!-- 过滤条件名称(最右) -->
                        <el-select :disabled="disabled || solist.filterConfId === 0" size="mini" filterable class="setVariant-select" :popper-append-to-body="false" v-model="solist.filterConfId"
                          @change="(val) => handleChangeFilterKeyName(val, solist)">
                          <el-option v-for="item in getFilterList(solist)" :key="item.id" :label="item.displayName" :value="item.id">
                            <el-popover :open-delay="300" placement="right" trigger="hover">
                              <div>
                                <div style="min-width: 200px">
                                  <h3 style="font-size: 14px; font-weight: 600">参数名: {{ item.displayName }}</h3>
                                  <p style="padding: 20px 0">属性名：{{ item.keyName }}</p>
                                  <p style="font-size: 14px" v-if="item.keyType === 1">值类型: String</p>
                                  <p style="font-size: 14px" v-if="item.keyType === 2">值类型: Boolean</p>
                                  <p style="font-size: 14px" v-if="item.keyType === 4">值类型: Float</p>
                                </div>
                              </div>
                              <p slot="reference">{{ item.displayName }}</p>
                            </el-popover>
                          </el-option>
                        </el-select>
                        <!-- 过滤条件操作选项(中间) 随着过滤条件1的改变而改变 -->
                        <el-select :disabled="solist.filterConfId === '' || disabled" size="mini" class="setVariant-select" v-model="solist.keyType" placeholder="请选择" @change="(val) => handleChangeFilterKeyType(val, solist)">
                          <el-option v-for="(item, index) in solist.symbolList" :key="index" :label="item.displayName" :value="item.type"></el-option>
                        </el-select>
                        <!-- 过滤条件的值(最左) -->
                        <!-- 当keyType选择了等于或不等于的时候 -->
                        <template v-if="solist.keyType === 1 || solist.keyType === 2">
                          <!-- 人群包 -->
                          <template v-if="solist.useEnum && solist.filterType === 2">
                            <el-select :disabled="disabled || solist.filterConfId === '' || !solist.keyType" v-model="solist.keyValue" multiple filterable class="setVariant-select-tags" size="mini" placeholder="请选择">
                              <el-option v-for="(item, kIndex) in solist.keyValueEnums" :key="kIndex" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                          </template>
                          <!-- 自定义受众规则 -->
                          <template v-else>
                            <el-select :disabled="disabled || solist.filterConfId === '' || !solist.keyType" v-model="solist.keyValue" :multiple="solist.keyValueType !== 2" filterable class="setVariant-select-tags" size="mini"
                              :allow-create="solist.keyValueType !== 2" default-first-option placeholder="请选择或创建后选择" @change="(val) => handleChangeFilterKeyValue(val, solist)">
                              <el-option v-for="item in solist.keyValueEnums" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                          </template>
                        </template>
                        <template v-else>
                          <el-input :disabled="disabled || solist.filterConfId === '' || !solist.keyType" class="filterListStyle-input" size="mini" v-model="solist.keyValue" placeholder="请输入" type="text"
                            @change="(val) => handleChangeFilterKeyValue(val, solist)"></el-input>
                        </template>
                        <!-- 删除按钮 -->
                        <i :class="isDetail ? 'edit' : 'iconClose'" class="el-icon-close icon-close" @click="deleteRules(index, indexs)"></i>
                      </div>
                    </div>
                    <!-- 增加过滤条件按钮 -->
                    <el-button :disabled="disabled" class="add-rule-btn" @click="addRules(index, 1)" icon="el-icon-plus" type="text">
                      过滤条件
                    </el-button>
                    <el-button :disabled="disabled" class="add-rule-btn" @click="addRules(index, 2)" icon="el-icon-plus" type="text">
                      添加用户分群
                    </el-button>
                    <div class="audience-range-container">
                      <label>发布范围</label>
                      <el-select :disabled="disabled" size="mini" @change="(val) => changeRange(val, index)" class="setVariant-select-range" :ref="`audienceRange${index}`" v-model="item.range" placeholder="请选择变体">
                        <el-option v-for="(range, index) in rangeOption" :key="index" :value="range.id" :label="range.displayName"></el-option>
                      </el-select>
                    </div>
                    <div v-if="item.range === -2">
                      <div>
                        <i class="el-icon-question"></i>
                        <span>
                          下方可调配各个变体的流量权重，多个变体的发布流量和≤100%，与发布计划设置的流量为乘积的关系，总流量≤100%，未命中用户按最终默认规则生效。
                        </span>
                        <!-- {{item}} -->
                      </div>
                      <!-- 流量权重 -->
                      <!--TOOD 设置固定高度，然后下拉边框-->
                      <div class="audience-range-variant">
                        <!--
                          流量进度
                          注意最外层循环的是filterRule
                          processWidth 前端计算并且加上
                        -->
                        <!-- =={{audienceData}} -->
                        <!-- ==={{item.versionList}}<br/> -->
                        <!-- +++{{audienceData}} -->
                        <div class="audience-range-variant-title">
                          <div class="audience-range-variant-flow" v-for="(version, ind) in item.versionList" :key="ind" :style="{
    width: `calc(${version.processWidth} - 2px)`
  }">
                            <div v-if="version.flow > 0">
                              <el-popover trigger="hover" placement="top">
                                <p>
                                  {{ getAudienceVersionName(version.versionId, version.curIndex) }}: {{ version.flow }}%
                                </p>
                                <p class="audience-range-variant-name" slot="reference">
                                  {{ getAudienceVersionName(version.versionId, version.curIndex) }}
                                </p>
                              </el-popover>
                              <div class="audience-range-variant-slider" :style="{
    background: getBackgroundColor(ind, item.versionList.length),
    width: '100%'
  }"></div>
                            </div>
                          </div>

                          <div class="audience-range-variant-slider-remaining" :style="{ width: item.processWidth }"></div>
                        </div>
                        <div class="audience-range-variant-num">
                          <p>0%</p>
                          <p>100%</p>
                        </div>
                        <!-- 流量按钮 -->
                        <el-scrollbar class="scroll-container-variant-value">
                          <div class="audience-range-variant-value">
                            <div class="audience-range-variant-value-item" v-for="(flowData, inde) in item.versionList" :key="`versionList${inde}`">
                              <div class="audience-range-variant-value-item-container">
                                <div class="audience-range-variant-1">
                                  <div :style="{ background: getBackgroundColor(inde, item.versionList.length) }" class="audience-range-variant-value-item-box"></div>
                                  <el-tooltip :content="getAudienceVersionName(flowData.versionId, flowData.curIndex)" placement="top">
                                    <p class="audience-range-variant-value-item-name">
                                      {{ getAudienceVersionName(flowData.versionId, flowData.curIndex) }}
                                    </p>
                                  </el-tooltip>
                                </div>
                                <div class="input-number">
                                  <el-input-number v-if="!reComputed" :disabled="disabled" v-model="flowData.flow" size="small" :precision="1" :step="1" :max="100" :min="0" @change="(val) => changeFlow(index)"></el-input-number>
                                  <span class="input-number-unit">%</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </el-scrollbar>
                      </div>
                    </div>
                    <div class="deleteFilterRule">
                      <el-button v-if="isDetail" type="text" icon="el-icon-delete" @click="deleteFilterRule(index)">
                        删除
                      </el-button>
                    </div>
                    <!-- 分割线 -->
                    <div class="divider" v-if="index < audienceData.filterRule.length - 1 &&
    audienceData.filterRule.length > 1
    ">
                      <el-divider class="divider-line"></el-divider>
                      <p>或</p>
                      <el-divider class="divider-line"></el-divider>
                    </div>
                    <el-divider class="divider" v-if="audienceData.filterRule.length === 1"></el-divider>
                  </div>
                </draggable>
                <div class="addFilterRule">
                  <el-button icon="el-icon-plus" :disabled="disabled" @click="addFilterRule">
                    增加受众规则
                  </el-button>
                </div>
              </el-col>
              <!-- 维度筛选 -->
              <el-col :span="3"></el-col>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 最终规则 -->
    <el-row class="release-audience-container">
      <el-col :span="1">
        <p class="white-list-if">else</p>
      </el-col>
      <el-col :span="23">
        <div class="white-list">
          <p class="white-list-title">默认最终规则</p>
          <i class="el-icon-question"></i>
          <p>白名单和自定义受众规则未命中的用户自动发布最终规则</p>
          <!-- ==={{audienceData.defaultVersionIndex}} -->
        </div>
        <div class="white-list-container">
          <el-select :disabled="disabled" size="mini" class="setVariant-select-range" v-model="audienceData.defaultVersionIndex">
            <el-option v-for="(item, index) in defaultRangeOption" :key="index.id" :value="item.id" :label="item.displayName"></el-option>
          </el-select>
        </div>
      </el-col>
    </el-row>

    <!-- 示例代码 -->
    <!-- <show-example-code
      :portType="portType"
      :variantType="variantType"
      :audienceList="audienceList"
    >
    </show-example-code> -->
  </div>
</template>
<script>
import _ from 'lodash';
import draggable from 'vuedraggable';
import { mapGetters } from 'vuex';

export default {
  props: {
    featureData: {
      type: Object,
      default: () => ({})
    },
    whiteList: {
      type: Array,
      default: () => {
        [];
      }
    },
    filterList: {
      type: Array,
      default: () => {
        [];
      }
    },
    portType: {
      type: Number
    },
    audienceInfo: {
      type: Object,
      default: () => ({}),
    },
    accountSystem: {
      type: Number
    },
  },
  components: {
    draggable,
  },
  data() {
    return {
      variantType: [],
      audienceList: [],
      filterRuleInit: false,
      filterRuleList: [], // 储存受众规则
      isDetail: !this.$route.query.type,
      // appKey: this.featureData.appkey,
      appKey: '',
      range: '', // 发布范围
      disabled: !!this.featureData.type,
      audienceData: {
        versionList: [],
        filterRule: [],
        defaultVersionIndex: -1 //默认最终规则受众版本为-1:不下发参数
      },
      reComputed: false,
      colorList: [
        'rgb(0, 0, 255)',
        'rgb(0, 255, 0)',
        'rgb(0, 255, 255)',
        'rgb(255, 0, 0)',
        'rgb(255, 0, 255)',
        'rgb(255, 255, 0)',
        'rgb(255, 255, 255)',
        'rgb(0, 0, 0)',
      ],
      whiteListOption: this.whiteList,
      filterListOption: [], // 过滤条件1
      symbolList: [], // 过滤条件2
      filterListKeyType: [], // 过滤条件2
      rangeOption: [], // 发布范围
      defaultRangeOption: [], // 默认受众规则
      isDisabled: false
    };
  },
  created() {
    this.$store.dispatch('getIpList');
  },
  computed: {
    ...mapGetters(['ipList'], 'message')
  },
  watch: {
    featureData: {
      handler(val1) {
        const val = _.cloneDeep(val1);
        this.appKey = val.appKey;
        this.disabled = !!val.type;
        if (!this.filterRuleInit) {
          if (val.filterRule.length > 0) {
            // 存储一份接口返回的受众规则
            this.filterRuleList = JSON.parse(JSON.stringify(val.filterRule));
            this.audienceData.filterRule = val.filterRule;
          }
          this.audienceData.versionList = val.versionList;
          this.audienceData.defaultVersionIndex = val.defaultVersionIndex;
          this.filterRuleInit = true;
        } else {
          //对比新旧versionList，删除减少的变体，一定要逆向遍历才能保证下标不变
          let length = this.audienceData.versionList.length;
          for (let oldIndex = length - 1; oldIndex >= 0; oldIndex--) {
            let oldVer = this.audienceData.versionList[oldIndex];
            let find = val.versionList.some((newVer) => {
              return newVer.curIndex === oldVer.curIndex;
            });
            if (!find) {
              this.audienceData.versionList.splice(oldIndex, 1);
              if (oldVer.whitelist && oldVer.whitelist.length > 0) {
                this.whiteListOption.forEach((item) => {
                  oldVer.whitelist.forEach((li) => {
                    if (li.cuid === item.cuid) {
                      item.selected = false;
                    }
                  });
                });
              }
              // 删除规则中版本
              this.audienceData.filterRule.forEach((rule) => {
                let vLength = rule.versionList ? rule.versionList.length : 0;
                for (let idx = vLength - 1; idx >= 0; idx--) {
                  if (rule.versionList[idx].curIndex === oldVer.curIndex) {
                    rule.versionList.splice(idx, 1);
                  }
                }
              });
              this.filterRuleList.forEach((rule) => {
                let vLength = rule.versionList ? rule.versionList.length : 0;
                for (let idx = vLength - 1; idx >= 0; idx--) {
                  if (rule.versionList[idx].curIndex === oldVer.curIndex) {
                    rule.versionList.splice(idx, 1);
                  }
                }
              });
            }
          }
          //对比新旧versionList，添加新增的变体，更新已有的变体
          val.versionList.forEach((newVer) => {
            let find = this.audienceData.versionList.some((oldVer) => {
              return newVer.curIndex === oldVer.curIndex;
            });
            if (find) {
              //修改displayName和keyValue
              this.audienceData.versionList.forEach((oldVer) => {
                if (newVer.curIndex === oldVer.curIndex) {
                  oldVer.displayName = newVer.displayName;
                  oldVer.keyValue = newVer.keyValue;
                }
              });
            } else {
              // 版本增加，同时多变体发布范围需要新增变体
              this.audienceData.versionList.push(newVer);
              newVer.flow = 0;
              this.audienceData.filterRule.forEach((rule) => {
                rule.range === -2 && rule.versionList &&
                  rule.versionList.push(JSON.parse(JSON.stringify(newVer)));
              });
              this.filterRuleList.forEach((rule) => {
                rule.range === -2 && rule.versionList &&
                  rule.versionList.push(JSON.parse(JSON.stringify(newVer)));
              });
            }
          });
        }
        // rangeOption发布范围：包括本地添加和请求数据,数据是动态响应的
        this.rangeOption = [];
        this.audienceData.versionList.forEach((version) => {
          this.rangeOption.push({ id: version.curIndex, displayName: version.displayName });
        });
        this.defaultRangeOption = [...this.rangeOption, { displayName: '不下发参数', id: -1 }];
        this.rangeOption.push({ displayName: '多变体', id: -2 });
        // 遍历每个filterRule中range取值不在rangeOption中需要置空，避免出现回显数字问题
        this.audienceData.filterRule.forEach((rule) => {
          let isExist = this.rangeOption.some((range) => { return range.id === rule.range; });
          if (!isExist) {
            rule.range = '';
          }
        });
        let isExist = this.defaultRangeOption.some((range) => { return range.id === this.audienceData.defaultVersionIndex; });
        if (!isExist) {
          this.audienceData.defaultVersionIndex = '';
        }

        // 新建变量之后 versionList 和 filterRule 值不统一导致计算有问题
        // 计算进度条宽度 编辑时候计算一次宽度
        if (this.audienceData.filterRule.length > 0) {
          this.audienceData.filterRule.forEach((rule, index) => {
            if (rule.versionList && rule.versionList.length > 0) {
              this.computedWidth(index);
              this.computedWidth(index, -1);
            }
          });
        }
      },
      deep: true,
    },
    audienceData: {
      handler(val) {
        this.audienceList = val;
        this.$emit('audienceData', JSON.parse(JSON.stringify(val)));
      },
      deep: true
    },
    opType: {
      handler(val) {
        if (val === 'add') {
          this.isDisabled = true;
        }
      },
      immediate: true
    },
    appKey: {
      handler(val) {
        this.getWhiteListAndFilterList();
      }
    },
    accountSystem: {
      handler(val) {
        this.getWhiteListAndFilterList();
      }
    }
  },
  methods: {
    // 获取白名单和过滤条件1
    getWhiteListAndFilterList() {
      let params = { pn: 0, rn: 100000, appKey: this.appKey, accountSystem: this.accountSystem };
      this.getWhiteList(params);
      params = { pn: 1, rn: 100000, appKey: this.appKey, accountSystem: this.accountSystem };
      if (params.appKey) {
        this.getFilterListOption(params);
      }
    },
    getColorNumber(rgb) {
      let result = [0, 0, 0], color = rgb;
      if (/^(rgb|RGB)/.test(color)) {
        result = color.replace(/(?:\(|\)|rgb|RGB|\s)*/g, "").split(",").map((v) => {
          return Number(v);
        });
      }
      return result;
    },
    getBackgroundColor(index, variantNum) {
      // index范围[0, variantNum)
      let step = Math.ceil(variantNum / this.colorList.length), //每个区间元素个数
        num = (step - Math.floor(index / this.colorList.length)) % step, //获取index在区间第几个元素，逆向获取增加颜色区分度
        fromIndex = index % this.colorList.length, //获取区间开始颜色
        toIndex = (fromIndex + 1) % this.colorList.length, //获取区间结束颜色
        fromColor = this.getColorNumber(this.colorList[fromIndex]),
        toColor = this.getColorNumber(this.colorList[toIndex]);
      let result = fromColor.map((v, i) => {
        return Math.round(v + (toColor[i] - v) / step * num);
      }).join(", ");
      return "rgb(" + result + ")";
    },
    getAudienceVersionName(versionId, curIndex) {
      let version = this.audienceData.versionList.find((value) => {
        return versionId ? value.id === versionId : value.curIndex === curIndex;
      });
      return version ? version.displayName : "未知";
    },
    // 删除过滤条件
    deleteRules(index, indexs) {
      //this.audienceData.filterRule[index].filterList.splice(indexs, 1);
      const filterArr = this.audienceData.filterRule[index].filterList;
      // if (filterArr.length === 1) {
      //   // this.deleteFilterRule(index);
      // } else {
      // }
      filterArr.splice(indexs, 1);
    },
    // 增加过滤条件
    addRules(index, type) {
      const item = {
        keyName: '',
        keyType: '',
        keyValue: [],
        filterConfId: type === 2 ? 0 : ''
      };
      if (type === 2) {
        this.getChangeFilterKeyName(0, item);
      }
      this.audienceData.filterRule[index].filterList.push(item);
    },
    getChangeFilterKeyName(val, flist) {
      for (let i = 0, len = this.filterListOption.length; i < len; i++) {
        const curr = this.filterListOption[i];
        if (curr.id === val) {
          flist.keyValueType = curr.keyType;
          flist.filterType = curr.filterType;
          flist.keyName = curr.keyName;
          flist.useEnum = curr.useEnum;
          flist.keyValueEnums = curr.keyType === 2 ? [{ label: 'true', value: 'true' }, { label: 'false', value: 'false' }] : (curr.keyValueEnums ? curr.keyValueEnums : []);
          flist.symbolList = curr.symbols && curr.keyType !== 2 ? curr.symbols : [{ type: 1, displayName: '等于' }, { type: 2, displayName: '不等于' }];
          break;
        }
      }
    },
    getFilterList(item) {
      const index = item.filterConfId === 0 ? 0 : 1;
      return this.filterListOption.slice(index);
    },
    // 白名单选择变化时
    whiteChange(val, index) {
      const whitelist = [];
      this.audienceData.versionList.forEach((item) => {
        whitelist.push(...item.whitelist);
      });
      this.whiteListOption.forEach((item) => {
        item.selected = false;
        whitelist.forEach((itm, index) => {
          if (itm.cuid === item.cuid) {
            item.selected = true;
          }
        });
      });
    },
    // 增加受众规则
    addFilterRule() {
      this.audienceData.filterRule.push({
        filterList: [{
          keyName: '',
          keyType: '',
          keyValue: [],
          filterConfId: ''
        }],
        versionList: [],
        //默认选择第一个变体，没有则置为空
        range: this.audienceData.versionList.length > 0 ? this.audienceData.versionList[0].curIndex : '',
      });
      let index = this.audienceData.filterRule.length - 1;
      this.filterRuleList[index] = {
        filterList: [{
          keyName: '',
          keyType: '',
          keyValue: [],
          filterConfId: ''
        }],
        versionList: [],
        range: '',
      };
    },
    // 删除受众规则
    deleteFilterRule(index) {
      this.audienceData.filterRule.splice(index, 1);
    },
    changeFlow(index) {
      // console.log('index+++', index);
      this.reComputed = true;
      let isFlow = false;
      let total = 0;
      this.audienceData.filterRule[index].versionList.forEach((item, idx) => {
        if (total + item.flow > 100) {
          item.flow = 100 - total;
          isFlow = true;
        } else {
          total += item.flow;
        }
      });
      this.$nextTick(() => {
        this.reComputed = false;
      });
      this.computedWidth(index);
      this.computedWidth(index, -1);
    },
    // 发布范围变化时 会计算值 新建的规则在这计算
    changeRange(val, index) {
      // 获取index位置更改之前的range值，以及老数据index所在的range值
      let preVal = this.$refs[`audienceRange${index}`][0].value;
      let ovRange = this.filterRuleList[index].range;
      if (val === -2 && ovRange !== -2) {
        // 切换至多变体且老数据规则为非变体，使用audienceData.versionList赋值新数据
        const versionList = [];
        this.audienceData.versionList.forEach((item) => {
          versionList.push({
            flow: 0,
            versionId: item.id || 0,
            curIndex: item.curIndex
          });
        });
        this.audienceData.filterRule[index].versionList = versionList;
        if (ovRange === '') {
          // 当新增规则时候，同时赋值给老数据，设置老数据range为当前值
          this.filterRuleList[index].versionList = this.audienceData.filterRule[index].versionList;
          this.filterRuleList[index].range = val;
        }
      } else if (val === -2 && ovRange === -2) {
        // 切换至多变体且老规则为多变体，使用老数据直接赋值
        this.audienceData.filterRule[index].versionList = this.filterRuleList[index].versionList;
      } else if (val !== -2) {
        if (ovRange === '') {
          // 当新增规则时候,设置老数据range为当前值
          this.filterRuleList[index].range = val;
        }
        // 切换至非多变体，如果之前是多变体则缓存至老数据中，同时清空当前规则的versionList
        if (preVal === -2) {
          this.filterRuleList[index].versionList = this.audienceData.filterRule[index].versionList;
          this.filterRuleList[index].range = preVal;
        }
        this.audienceData.filterRule[index].versionList = [];
      }
      this.computedWidth(index);
      this.computedWidth(index, -1);
    },
    // 计算进度条宽度
    // index 当前filterRule里面对应的对象的索引
    // 每个index执行两遍 分别传入 index参数 和 index, -1参数

    // this.computedWidth(index, -1);
    // 计算最最外面的总的占比剩余 因为会根据versionList里面的所有值 计算剩余外层的值。
    // this.computedWidth(index);
    // 计算versionList里面的每一个的占比
    computedWidth(index, deep) {
      let processWidth = 0;
      let total = 0;
      this.$nextTick(() => {
        if (deep === -1) {
          processWidth = 100;
          this.audienceData.filterRule[index].versionList.forEach((item) => {
            processWidth = processWidth - item.flow;
            if (processWidth < 0) {
              processWidth = 0;
            }
          });
        } else {
          this.audienceData.filterRule[index].versionList.forEach((item, index) => {
            if (total <= 100) {
              if (total + item.flow < 100) {
                item.processWidth = `${item.flow}%`;
              } else {
                item.processWidth = `${100 - total}%`;
              }
              total += item.flow;
            } else {
              item.processWidth = 0;
              this.$message.error('变体权重总和不能超过100%');
            }
          });
        }
        this.$set(this.audienceData.filterRule[index], 'processWidth', `${processWidth}%`);
      });
    },
    // 获取白名单
    getWhiteList(param) {
      this.$service.get('WHITE_LIST', param, { allback: 0, needLoading: false }).then((res) => {
        // 白名单option
        this.whiteListOption = [];
        res.list.forEach((item) => {
          item.whitelist.forEach(it => {
            this.whiteListOption.push(it);
          });
        });
        // 设置versionList里面whitelist
        if (this.audienceData.versionList.length > 0) {
          this.audienceData.versionList.forEach((item) => {
            if (item.whitelist && item.whitelist.length > 0) {
              const newWhiteList = [];
              item.whitelist.forEach((ite) => {
                this.whiteListOption.forEach((option) => {
                  if (option.cuid === ite.cuid) {
                    option.setId = ite.setId ? ite.setId : ite.id;
                    newWhiteList.push(option);
                  }
                });
              });
              item.whitelist = newWhiteList;
            }
          });
        }
        // 白名单互斥
        this.whiteChange();
      }).catch(() => { });
    },
    // 过滤条件1
    getFilterListOption(param) {
      this.$service.get('FILTER_LIST', param, { allback: 0, needLoading: false }).then((res) => {
        this.filterListOption = res.filterList;
        this.symbolList = res.symbolList;
        this.audienceData.filterRule.forEach((rule) => {
          rule.filterList.forEach((list) => {
            let find = false;
            for (let i = 0; i < this.filterListOption.length; i++) {
              const current = this.filterListOption[i];
              find = list.filterConfId === current.id || (list.filterType === 2 && current.keyName === '__crowd__');
              if (find) {
                list.filterConfId = current.id;
                list.keyValueType = current.keyType;
                list.useEnum = current.useEnum;
                list.symbolList = current.symbols && current.keyType !== 2 ? current.symbols : [{ type: 1, displayName: '等于' }, { type: 2, displayName: '不等于' }];

                const isCrowd = (list.filterConfId === 0 && current.useEnum === 1) || (list.filterType === 2 && current.keyName === '__crowd__');
                if (isCrowd) { // 清理人群包受众规则value里不存在的人群包
                  list.keyValue = list.keyValue.filter((kv) => current.keyValueEnums.some((e) => e.value === kv));
                }

                const temp = current.keyType === 2 ? [{ label: 'true', value: 'true' }, { label: 'false', value: 'false' }] : (current.keyValueEnums ? current.keyValueEnums : []);
                const arr = list.keyValue.map(value => { return { label: value, value }; });
                list.keyValueEnums = this.clearDuplicate(current.keyType === 2 || isCrowd ? temp : [...temp, ...arr]);
                break;
              }
            }
            //如果没有找到对应filter conf id，则清空该过滤条件
            if (!find) {
              list.keyName = '';
              list.keyType = '';
              list.keyValue = '';
              list.filterConfId = '';
              list.symbolList = [];
            }
          });
        });
      }).catch(() => { });
    },
    // 数组对象去重复
    clearDuplicate(arr) {
      let map = new Map();
      for (let item of arr) {
        if (!map.has(item.label)) {
          map.set(item.label, item);
        }
      }
      return [...map.values()];
    },
    // 当改变受众规则的keyName时候
    handleChangeFilterKeyName(val, flist) {
      this.reset(flist);
      for (let i = 0, len = this.filterListOption.length; i < len; i++) {
        const curr = this.filterListOption[i];
        if (curr.id === val) {
          flist.keyValueType = curr.keyType;
          flist.filterType = curr.filterType;
          flist.keyName = curr.keyName;
          flist.useEnum = curr.useEnum;
          // flist.keyValueEnums = curr.keyType === 2 ? [{ label: 'true', value: 'true' }, { label: 'false', value: 'false' }] : (curr.keyValueEnums ? curr.keyValueEnums : []);
          flist.keyValueEnums = curr.keyName === 'ip' ? this.ipList : (curr.keyType === 2 ? [{ label: 'true', value: 'true' }, { label: 'false', value: 'false' }] : (curr.keyValueEnums ? curr.keyValueEnums : []));
          flist.symbolList = curr.symbols && curr.keyType !== 2 ? curr.symbols : [{ type: 1, displayName: '等于' }, { type: 2, displayName: '不等于' }];
          break;
        }
      }
    },
    // 当改变受众规则的keyType时候
    handleChangeFilterKeyType(val, flist) {
      flist.keyValue = [];
    },
    handleChangeFilterKeyValue(val, flist) {
      if (typeof val === 'object') {
        flist.keyValue = val;
      } else {
        flist.keyValue = [val + ''];
      }
    },
    reset(flist) {
      flist.keyType = '';
      flist.keyValue = [];
    },
    // 组装受众规则操作类型
    assembleSymbolList(symbolList, symbols) {
      if (!Array.isArray(symbolList) || !symbolList.length || !symbols || !symbols.length)
        return [];
      let result = [];
      symbols.forEach((sy) => {
        symbolList.forEach((list) => {
          if (sy == list.type) {
            result.push(list);
          }
        });
      });
      //debugger;
      return result;
    }
  }
};
</script>

<style scoped lang="less">
.delVar_style {
  display: none;
  padding-top: 6px;
  margin-left: 3px;
}

body {
  font-size: 14px;
}

p {
  font-size: 14px;
}

.detail {
  /deep/ .el-button.is-disabled {
    background-color: #ffffff;
  }
}

/deep/ .el-alert__closebtn {
  visibility: hidden;
}

/deep/ .el-alert__title {
  font-size: 14px;
}

/deep/ .el-input--mini .el-input__inner {
  font-size: 14px;
}

/deep/ .el-tag {
  font-size: 14px;
}

.el-select-dropdown__item {
  font-size: 14px;
}

// .el-icon-question-text {
//   // line-height: 20px;
// }

.basic-title {
  font-size: 16px;
  font-weight: 800;
  margin-bottom: 20px;
}

.iconClose {
  display: none;
}

.setVariant-select {
  margin-right: 8px;
}

.setVariant-select-whitelist {
  width: 50%;
}

.setVariant-select-range {
  margin-left: 8px;
}

.setVariant-select-tags {
  width: calc(100% - 425px);
}

.release-audience {
  font-size: 14px;
}

.release-audience-container {
  margin: 40px 0;
}

.white-list-if {
  background: rgb(223, 233, 250);
  color: rgb(51, 112, 255);
  text-align: center;
}

.white-list-elseif {
  background: rgb(250, 233, 237);
  color: rgb(255, 88, 129);
  text-align: center;
}

.white-list {
  display: flex;
  justify-content: flex-start;
  margin-left: 30px;
}

.white-list-title {
  margin-right: 30px;
}

.scroll-container {
  /deep/ .el-scrollbar__wrap {
    height: 100%;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  /deep/ .el-scrollbar__view {
    margin: 0 10px 10px 0;
  }
}

.scroll-container-variant-value {
  /deep/ .el-scrollbar__wrap {
    height: 100%;
    max-height: 250px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  /deep/ .el-scrollbar__view {
    margin: 0 10px 10px 0;
  }
}

.white-list-container {
  border: 1px solid #e7e9f5;
  margin-left: 30px;
  padding: 10px;
  margin-top: 10px;
}

.white-list-item {
  padding: 10px;
  display: flex;
  justify-content: flex-start;

  &:hover .delVar_style {
    display: block;
  }
}

.white-list-item-box {
  background: rgb(51, 112, 255);
  border-radius: 3px;
  width: 18px;
  height: 18px;
  margin: 5px 5px 0 0;
}

.white-list-item-name {
  margin: 0 5px;
  width: 70px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-top: 8px;
}

.white-list-item-name-tag {
  margin-right: 15px;
  width: 50px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-top: 8px;
}

.white-list-item-select {
  min-width: 450px;
  margin-left: 20px;
}

.add-white-btn {
  border-radius: 4px;
}

.audience-rules {
  padding: 10px;
}

.audience-rules-alert {
  margin: 8px 0;
}

.add-rule-btn {
  font-size: 14px;
  margin-left: 20px;
  height: 20px;
  border-radius: 0;
  border: none;
}

.add-rule-qie {
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
}

.audience-rules-type {
  width: 150px;
  margin-right: 10px;
}

.audience-rules-value {
  width: 120px;
  margin-right: 10px;
}

.audience-rules-tags {
  width: calc(100% - 325px);
}

.audience-range-container {
  margin: 20px 0;
}

.audience-range {
  width: 300px;
  margin-left: 10px;
  height: 24px;
  line-height: 24px;

  /deep/.el-input__inner {
    height: 24px;
  }
}

.audience-range-variant {
  padding: 20px 20px 30px;
  margin: 20px 0;
  background: #f8f8fc;
  border: 1px solid #f8f8fc;
}

.audience-range-variant-title {
  height: 30px;
}

.audience-range-variant-flow {
  float: left;
  margin-right: 2px;
}

.audience-range-variant-num {
  padding: 5px 0;
  display: flex;
  justify-content: space-between;
}

.audience-range-variant-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 14px;
  line-height: 22px;
  max-width: 70px;
}

.audience-range-variant-slider {
  height: 8px;
  background: blue;
  text-align: center;
  float: left;
}

.audience-range-variant-value-item {
  width: 47%;
  padding: 10px;
  display: inline-block;
}

.audience-range-variant-value-item-container {
  display: flex;
  justify-content: space-between;
  align-content: center;
}

.audience-range-variant-1 {
  display: flex;
  justify-content: flex-start;
}

.audience-range-variant-value-item-box {
  background: rgb(51, 112, 255);
  border-radius: 3px;
  width: 14px;
  height: 14px;
  margin-top: 5px;
  margin-right: 2px;
}

.audience-range-variant-value-item-name {
  margin: 0;
  padding-top: 7px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 70px;
}

.audience-range-variant-slider-remaining {
  margin-top: 22px;
  height: 8px;
  background: gainsboro;
  float: left;
}

.delete-text {
  color: rgb(24, 144, 255);
}

.default-final-rule {
  width: 490px;
}

.screening-latitude {
  background: #ffffff;
  padding: 14px 12px;
  margin-left: 10px;
  overflow-y: auto;
  position: absolute;
  top: -20px;
  right: -20px;
  height: calc(100% - 22px);
  width: 11%;
}

.screening-latitude-input-select {
  width: 100%;
  border-radius: 4px;
}

.latitude-list-item {
  border: 1px solid #e7e9f5;
  border-radius: 4px;
  margin-top: 6px;
  text-align: left;
  height: 28px;
  line-height: 28px;
  cursor: grab;
  padding-left: 12px;
  background: #ffffff;
}

.icon-close {
  cursor: pointer;
}

.input-number-unit {
  padding-left: 5px;
}

.addFilterRule {
  margin-top: 20px;
}

.divider {
  margin-top: 10px;
  width: 100%;
  display: flex;
}

.divider-line {
  width: 49.5%;
  padding-top: 5px;
}

.filterList {
  margin: 10px 0;
  position: relative;
}

.filterListStyle {
  margin-left: 20px;
  padding: 5px 0;

  .filterListStyle-input {
    width: calc(100% - 425px);
  }
}

/deep/ .el-select-dropdown__item {
  max-width: 400px;
  padding-right: 16px;
}

/deep/ .el-tooltip {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
