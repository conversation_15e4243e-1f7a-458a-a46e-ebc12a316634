<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
    <section class="page-header">
      <section>
        <i class="el-icon-arrow-left" @click="handleGoBack(true)"></i>
        <span class="page-title">固化详情</span>
      </section>
    </section>
    <div class="feature-container">
      <div class="feat_wrapper">
        <el-tabs tab-position="top" @tab-click="handleTabChange" v-model="activeName">
          <el-tab-pane label="基本设置" name="firstTab"></el-tab-pane>
          <el-tab-pane label="设置变体" name="secondTab"></el-tab-pane>
          <el-tab-pane label="发布受众" name="thirdTab"></el-tab-pane>
          <basic-set
              class="tab-pane-item"
              ref="firstTab"
              :opType="opType"
              :featureData="featureData"
              :variateInfo="variateInfo"
              @basicData="handleBasicData"
          ></basic-set>
          <set-variant
              :opType="opType"
              :variantMaxNum="variantMaxNum"
              class="tab-pane-item"
              ref="secondTab"
              :featureData="featureData"
              :variateInfo="variateInfo"
              @variantData="handleVariantData"
          ></set-variant>
          <release-audience
              class="tab-pane-item"
              ref="thirdTab"
              :portType="portType"
              :featureData="featureData"
              :whiteList="whiteListOption"
              :filterList="filterListOption"
              :audienceInfo="audienceInfo"
              :accountSystem="basicData.accountSystem"
              @audienceData="handleAudienceData"
          ></release-audience>
        </el-tabs>
      </div>
      <div class="feature-btns">
        <el-button type="primary" @click="reback">返回</el-button>
        <el-button type="primary" @click="saveFeature" v-if="isDetail">保存</el-button>
      </div>
    </div>
  </Page>
</template>
<script>
import Page from '@/components/common/page/index.vue';
import basicSet from './basic-set.vue';
import releaseAudience from './release-audience.vue';
import SetVariant from './set-variant.vue';


let mockData =
    {
      "displayName": "111识字付费转化路径AB实2222",
      "description": "2222识字付费转化路径AB实验8888",
      "clientType": 2,
      "picUrl": "",
      "accountSystem": 2,
      "currentVersion": 1,
      "feature": {
        "keyName": "uopk",
        "keyType": 1,
        "id": 1
      },
      versionList: [
        {
          "id": 1945,
          "displayName": "对照版本111",
          "description": "55",
          "keyValue": "A1pp",
          // "isNeedEdit": false,
          whitelist: [
            {
              "id": 791,
              "cuid": null,
              "whitelistId": 1,
              "displayName": '邓璨荣'
            }
          ]
        },
        {
          "id": 1945,
          "displayName": "对照版本222",
          "description": "55",
          "keyValue": "B1kk",
          // "isNeedEdit": false,
          whitelist: [
            {
              "id": 791,
              "cuid": null,
              "whitelistId": 1,
              "displayName": '邓璨荣'
            }
          ]
        },
        {
          "id": 1945,
          "displayName": "对照版本333",
          "description": "55",
          "keyValue": "A1",
          // "isNeedEdit": false,
          whitelist: [
            {
              "id": 791,
              "cuid": null,
              "whitelistId": 1,
              "displayName": '邓璨荣'
            }
          ]
        },
        {
          "id": 1945,
          "displayName": "对照版本444",
          "description": "55",
          "keyValue": "A1",
          // "isNeedEdit": false,
          whitelist: [
            {
              "id": 791,
              "cuid": null,
              "whitelistId": 1,
              "displayName": '邓璨荣'
            }
          ]
        }
      ],
      "filterRule": [{
        "id": 1253,
        "filterList": [
          {
            keyName: '',
            keyType: '',  // 选项2
            keyValue: '',  // 输入值
            filterConfId: ''  // 选项1
          }
        ],
        "versionList": [{
          "flow": 250,
          "versionId": 1940
        }, {
          "flow": 210,
          "versionId": 1941
        }, {
          "flow": 250,
          "versionId": 1942
        }, {
          "flow": 240,
          "versionId": 1943
        }]
      }]
    };

export default {
  name: 'feature',
  components: {basicSet, SetVariant, releaseAudience, Page},
  data() {
    return {
      opType: "",
      portType: 1,
      breadcrumbList: [],
      isDetail: !this.$route.query.type,
      activeName: 'firstTab',
      featureData: {
        appKey: '',
        filterRule: []
      }, // 固化详情
      basicData: {
      }, // 基础设置数据
      variantMaxNum: 200, //变体个数最大限制
      variantData: {}, // 变体数据
      audienceData: {}, // 受众数据
      experimentId: 0, //实验id
      id: 0,
      correctVersionId: 0,
      isClick: true, // 防止多次点击
      appKey: '',
      whiteListOption: [], //白名单
      filterListOption: [],
      variateInfo: {},
      audienceInfo: {}
    };
  },
  created() {
    this.experimentId = this.$route.query.experimentId ? this.$route.query.experimentId : '';
    this.id = this.$route.query.id ? this.$route.query.id : '';
    this.correctVersionId = this.$route.query.correctVersionId ? this.$route.query.correctVersionId : '';

    if(this.$route.query.opType) {
      this.opType = this.$route.query.opType;
    }
    // console.log('opType', this.opType);
    if(this.opType !== 'add') {
      this.getFeatureDetail();
    }
  },
  methods: {
    handleGoBack() {
      this.$router.replace({
        path: '/feat/list/manage',
        query: {
          id: this.id
        }
      });
    },
    // 详情
    getFeatureDetail() {
      const param = {
        experimentId: this.experimentId,
        id: this.id,
        correctVersionId: this.correctVersionId,
      };
      this.$service
          .get('NEWFTETUREVIEW', {...param}, {allback: 0, needLoading: true})
          .then((res) => {
            this.featureData = res;
            // this.featureData = JSON.parse(JSON.stringify(mockData));
            this.variateInfo = res;
            this.$set(this.featureData, 'type', this.$route.query.type);
            if (this.featureData.filterRule.length > 0) {
              this.featureData.filterRule.forEach((item) => {
                item.filterList.forEach((ite) => {
                  let kvx = JSON.parse(ite.keyValue);
                  if (typeof kvx === 'object') {
                    //将keyValue转换为字符数组
                    ite.keyValue = kvx.map(item => '' + item);
                  } else {
                    ite.keyValue = [ite.keyValue + ''];
                  }
                });
                item.versionList.forEach((it, index) => {
                  it.flow = it.flow ? it.flow / 10 : 0;
                  //通过versionId从versionList数组中寻找对应的版本curIndex，如果找到则赋值，否则赋值为当前index
                  let verIndex = this.featureData.versionList.findIndex((ver) => ver.id === it.versionId);
                  it.curIndex = verIndex >= 0? this.featureData.versionList[verIndex].curIndex: index;
                });
              });
            }
          })
          .catch(() => {});
    },
    // 描点定位
    handleTabChange() {
      const activeName = this.activeName;
      const offsetTop = this.$refs[activeName].$el.offsetTop;
      // 匀速滚动
      const sp = 10;
      let count = Number.parseInt(offsetTop / sp);
      let timer = setInterval(() => {
        if (count > 0) {
          count--;
          document.documentElement.scrollTop = offsetTop - count * sp;
        } else {
          clearInterval(timer);
        }
      }, sp);
    },
    // 基础配置数据
    handleBasicData(param) {
      this.basicData = param;
      this.portType = this.basicData.clientType;
      this.featureData.appKey = param.appKey;
      // console.log('this.basicData', this.basicData);
    },
    // 设置变体数据 设置versionList
    handleVariantData(param) {
      this.$set(this.featureData, 'versionList', param.versionList);
      this.variantData = param;
      // console.log('featureData====', this.featureData.versionList);
    },
    // 发布受众
    handleAudienceData(param) {
      this.audienceData = param;
    },
    // 返回，如果有id则返回版本历史页，否则返回列表页
    reback() {
      const id = this.$route.query.id;
      this.$router.push({
        path: '/feat/list' + (id ? '/manage?id=' + id : ''),
      });
    },
    // 保存
    saveFeature() {
      // 判断变体名称是否重复
      let accdata = this.audienceData.versionList.reduce((acc, v) => {
        acc[v.displayName] = acc[v.displayName] ? ++acc[v.displayName] : 1;
        return acc;
      }, {});
      let dual = Object.keys(accdata).flatMap((k) => accdata[k] > 1? [k]: []).join(', ');
      if(dual) {
        this.$message.error(`变体名称:[${dual}]出现重复值，请重新设置`);
        return;
      }

      const query = this.$route.query;
      const params = JSON.parse(JSON.stringify(this.featureData));

      if (this.isClick) {
        this.isClick = false;
        params.isNewVersion = query.isNewVersion ? query.isNewVersion : 0; // 是否设为最新版本
        if (query.experimentId != null) {
          params.experimentId = query.experimentId;
        } // 实验ID
        params.id = query.id ? query.id : ''; //从固化版本添加时传递固化ID
        // params.fromcorrectVersionId = query.correctVersionId ? query.correctVersionId : ''; //从固化版本添加时，传递当前固化版本的correctVersionId
        params.correctVersionId = query.correctVersionId ? query.correctVersionId : ''; //从固化版本添加时，传递当前固化版本的correctVersionId
        params.feature = this.variantData.feature;
        if(this.variantData.feature.id) {
          params.feature.id = this.variantData.feature.id;
        }
        // 基础数据
        params.displayName = this.basicData.displayName;
        params.description = this.basicData.description;
        params.picUrl = this.basicData.picUrl ? this.basicData.picUrl : '';
        params.appKey = this.basicData.appKey;
        params.clientType = this.basicData.clientType;
        params.accountSystem = this.basicData.accountSystem;

        // 变体的数据全在versionList里面
        this.audienceData.versionList.forEach((item, index) => {
          params.versionList[index].whitelist = item.whitelist;
        });

        let filter = JSON.parse(JSON.stringify(this.audienceData.filterRule));
        // 将filterRule中每条规则range从curIndex更新为versionList下标
        filter.length && filter.forEach((item) => {
          if(item.range === 0 || item.range > 0) {
            let index = this.audienceData.versionList.findIndex((it) => it.curIndex === item.range);
            item.range = index === -1 ? '' : index;
          } else {
            item.range = item.range === -2? -2: '';
          }
          item.versionList.forEach((it, ind) => {
            it.curIndex = ind;
          });
        });
        params.filterRule = filter;
        //将defaultVersionIndex从curIndex更新为versionList下标
        if(this.audienceData.defaultVersionIndex === 0 || this.audienceData.defaultVersionIndex > 0) {
          let index = this.audienceData.versionList.findIndex((it) => it.curIndex === this.audienceData.defaultVersionIndex);
          params.defaultVersionIndex = index === -1 ? '' : index;
        } else {
          params.defaultVersionIndex = this.audienceData.defaultVersionIndex === -1? -1: '';
        }
        // 受众规则
        params.filterRule.forEach((item) => {
          item.filterList.forEach((ite) => {
            if (Array.isArray(ite.keyValue)) {
              ite.keyValue = JSON.stringify(Array.from(ite.keyValue).map((kv) => kv+''));
            } else {
              ite.keyValue = JSON.stringify([ite.keyValue+'']);
            }
            this.delRedundantKey(ite, 'symbolList');
            this.delRedundantKey(ite, 'useEnum');
            this.delRedundantKey(ite, 'keyValueEnums');
          });
          item.versionList.forEach((it) => {
            it.flow = it.flow * 10;
            this.delRedundantKey(it, 'processWidth');
          });
          this.delRedundantKey(item, 'processWidth');
        });
        // 白名单
        params.versionList.forEach((item, index) => {
          const whitelist = [];
          item.curIndex = index;
          item.whitelist.forEach((it, index) => {
            whitelist.push({
              cuid: it.cuid,
              whitelistId: it.id,
              id: it.setId ? it.setId : 0,
            });
          });
          item.whitelist = whitelist;
        });

        if (!params.feature.keyName) {
          this.$message.error('请输入Key！');
          this.isClick = true;
          return;
        }
        if (!params.displayName) {
          this.$message.error('请输入固化名称！');
          this.isClick = true;
          return;
        }
        if (params.filterRule.length === 0) {
          this.$message.error('至少包含一条受众规则！');
          this.isClick = true;
          return;
        }
        let ruleErrs = params.filterRule.flatMap((item, index) => {
          let inds = item.filterList.flatMap((it, ind) =>
              (it.keyName === ''|| it.keyType === '' || it.keyValue === '' || it.keyValue === '[]')? [ind + 1]: []
          );
          return inds.length > 0? [`第${index + 1}条规则的第${inds.join(', ')}条`]: [];
        });
        if(ruleErrs.length > 0) {
          this.$message.error(`请设置正确的过滤条件:\n ${ruleErrs.join('; ')} ！`);
          this.isClick = true;
          return;
        }
        ruleErrs = params.filterRule.flatMap((item, index) => !item.range && item.range !== 0? [index + 1]: []);
        if(ruleErrs.length > 0) {
          this.$message.error(`第 ${ruleErrs.join(', ')} 条规则请选择发布范围！`);
          this.isClick = true;
          return;
        }
        if (!params.defaultVersionIndex && params.defaultVersionIndex !== 0) {
          this.$message.error('请选择默认受众规则！');
          this.isClick = true;
          return;
        }
        // console.log('params===', params);
        // return

        const formData = this.getSubmitData(params);
        this.$service
          .post('NEWFEATUREADD', formData, {allback: 0, needLoading: true})
          .then((id) => {
            this.$router.push({
              path: '/feat/list/manage?id=' + id,
            });
            this.$message.success('添加固化成功');
            this.isClick = true;
          })
          .catch(() => {
            this.isClick = true;
          });
      }
    },
    // 删除冗余字段
    delRedundantKey(obj, key) {
      if (obj.hasOwnProperty(key)) {
        delete obj[key];
      }
    },
    // 转换数据格式为formData
    getSubmitData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    // 获取白名单
    getWhiteList(param) {
      this.$service
          .get('WHITE_LIST', param, {
            allback: 0,
            needLoading: false,
          })
          .then((res) => {
            this.whiteListOption = res.list;
            // 白名单互斥
            this.whiteChange();
          })
          .catch(() => {});
    },
    // 过滤条件1
    getFilterListOption(param) {
      this.$service
          .get('FILTER_LIST', param, {
            allback: 0,
            needLoading: false,
          })
          .then((res) => {
            this.filterListOption = res.list;
          })
          .catch(() => {});
    },
  },
};
</script>
<style scoped lang="less">
/deep/ .el-tabs__nav {
  width: 100%;
}
/deep/ .el-tabs__item {
  width: 33%;
  font-size: 17px;
}
/deep/ .el-tabs__item {
  font-size: 17px;
  font-weight: 800;
}

.tab-pane-item {
  margin: 10px 0 20px 0;
}

.feature-container {
  padding: 15px;
  background: #ffffff;
}
.feature-btns {
  display: flex;
  justify-content: flex-end;
}
.el-tabs-item {
  font-size: 18px;
}
</style>

