<template>
  <Page class="container" :breadcrumbList="breadcrumbList">
    <div class="report-page">
      <a style="color: inherit" class="box-content-title">
        「{{ summaryData.correct ? summaryData.correct.displayName : '' }}」
        的固化报告
      </a>

      <el-card>
        <h4 class="report-v">报告概览</h4>
        <div class="report-person">
          <div>
           <p style="text-align: center">总进流量</p>
           <div style="margin-top: 10px;">
             <el-progress
                :width="81"
                type="circle"
                :color="`${colorList[3]}`"
                :stroke-width="6"
                :percentage="summaryData.correctVersion.flowPersent ? summaryData.correctVersion.flowPersent: 0"
              ></el-progress>
           </div>
            <!-- <p class="report-person-num">
              {{ summaryData.correctVersion.flowPersent ? summaryData.correctVersion.flowPersent + '%': 0 }}
            </p> -->
          </div>

          <div style="margin-left: 30px;">
            <p>总进组用户数</p>
            <p class="report-person-num">
              {{ summaryData.totalNum ? summaryData.totalNum : 0 }}
            </p>
          </div>
        </div>
      </el-card>

      <el-card
        v-for="(item, index) in summaryData.versionList"
        :key="item.versionId"
        class="report-page-box"
      >
        <div class="report-version-item">
          <h4>{{ item.versionName }}</h4>
        </div>
        <div class="report-version-item-num">
          <p>进组人数</p>
          <p class="report-version-item-num-p count">{{ item.num }}</p>
        </div>

        <div class="report-version-item-num">
          <p>进组占比</p>
          <p class="report-version-item-num-p count">{{ item.numPersent }}%</p>
        </div>
        <div class="report-version-item-num report-desc">
          <p class="report-status-desc">
            <!-- {{item.intro ? item.intro : ''}} -->
          </p>
        </div>
      </el-card>
    </div>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import { mapGetters } from 'vuex';

let mockData = {
    "totalNum": 469,
    "versionList": [
      {
        "num": 168,
        "versionId": 20,
        "versionName": "对照版本",
        "intro": "",
        numPersent: 99
      },
      {
        "num": 116,
        "versionId": 21,
        "versionName": "实验版本1",
        "intro": ""
      },
      {
        "num": 185,
        "versionId": 22,
        "versionName": "实验名称",
        "intro": ""
      }
    ],
    "correct": {
      "displayName": "12",
      "appKey": "homework",
      "clientType": 1,
      "accountSystem": 1,
      "createTime": **********
    },
    "correctVersion": {
      "id": 11,
      "appKey": "homework",
      "currentVersion": "3",
      "publishStatus": 2,
      "flowPersent": "30",
      "startTime": **********,
      "createTime": **********,
      "updateTime": **********,
      "endTime": 0
    }
  };

export default {
  name: 'ExpReport',
  components: {
    Page,
  },
  data() {
    return {
      breadcrumbList: ['固化报告'],
      expId: this.$route.query.id,
      isFoundationAnalysis: true,
      isAdvanceAnalysis: false,
      summaryData: {},
      colorList: [
        'rgb(96, 96, 107)',
        'rgb(28, 164, 116)',
        'rgb(237, 133, 64)',
        'rgb(51, 112, 255)',
        'rgb(24, 175, 240)',
        'rgb(0, 208, 182)',
        'rgb(15, 191, 96)',
        'rgb(185, 219, 13)',
        'rgb(255, 205, 0)',
        'rgb(245, 133, 5)',
        'rgb(93, 120, 242)',
        'rgb(108, 48, 199)',
        'rgb(0, 137, 213)'
      ]
    };
  },
  computed: {
    ...mapGetters(['expReportData'], 'message')
  },
  created() {
    if(this.$route.query.id) {
      this.getReportList();
    }
    //this.$store.dispatch('getReportData', this.$route.query.id);
    // this.getSummaryData();
  },
  mounted() {},
  methods: {
    getReportList() {
      const params = {
        correctId: this.$route.query.id
      };
      this.$service
        .get('CTSUMMARY', {...params}, {allback: 0, needLoading: true})
        .then(res => {
          this.summaryData = res;
          // console.log(res);
          // this.$store.dispatch('getHistoryData', {id: this.expId});

        })
        .catch(err => {
          console.log(err);
        });
    },
    // 基础分析
    foundationAnalysis() {
      this.isFoundationAnalysis = true;
      this.isAdvanceAnalysis = false;
    },
    // 高级分析
    advanceAnalysis() {
      this.isAdvanceAnalysis = true;
      this.isFoundationAnalysis = false;
    },
    // 获取实验数据汇总数据
    getSummaryData() {
      const params = {
        experimentId: this.$route.query.id
      };
      this.$service
        .get('REPORT_FOUND_SUMMARY', {...params}, {allback: 0, needLoading: true})
        .then(res => {
          this.summaryData = res;
          console.log(res);
          this.$store.dispatch('getHistoryData', {id: this.expId});

        })
        .catch(err => {
          console.log(err);
        });
    },
  },
  beforeDestroy() {}
};
</script>

<style lang="less" scoped>
.report-page {
  border: #e7e9f5;
  min-height: 100%;
}
.report-page-box {
  box-sizing: border-box;
  width: 100%;
  //height: 180px;
  //border: 1px solid#e7e9f5;
  padding: 24px;
}
.report-v {
  padding-left: 28px;
}
/deep/ .report-page-box .el-card__body {
  display: flex;
}
.report-person {
  display: flex;
  font-size: 13px;
  padding: 12px 24px 12px 0;
  margin-left: 60px;
  p {
    padding-top: 15px;
  }
}
/deep/ .el-card h4 {
  font-size: 16px;
  font-weight: 600;
}
.report-person-num {
  margin-top: 25px !important;
  text-align: center;
  font-size: 24px;
  margin: 2px 0 0;
  font-weight: 700;
  color: #2f2f3f;
}

.report-notice {
  background: #fffbe6;
  padding: 8px 15px;
  margin-top: 5px;
  border-radius: 4px;
}

.report-notice-icon {
  margin-right: 10px;
}
.report-version {
  // border: 1px solid gainsboro;
  height: 159px;
  padding: 10px;
  border-top: none;
  overflow: hidden;
}

.report-version-item {
  height: 100%;
  width: 138px;
  float: left;
}
.report-version-item-num {
  height: 100%;
  min-width: 140px;
  max-width: 160px;
  float: left;
  margin-right: 2%;
  // margin-top: 20px;
}
.count {
  min-width: 140px;
  max-width: 160px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.report-version-item-num-p {
  font-size: 20px;
  font-weight: 700;
  padding-top: 20px;
}
.report-desc {
  width: 200px;
  line-height: 20px;
  margin-top: 15px;
}
.report-version-redio {
  height: 100%;
  width: 138px;
  float: left;
  margin: 0 20px;
}

.report-version-item-notice {
  margin-right: 2%;
  margin-top: 20px;
  text-align: left;
  height: 100px;
}
.box-content-title {
  font-size: 18px;
  display: block;
  font-weight: 500;
  border-bottom: none;
  padding: 0;
  margin-bottom: 20px;
}
/deep/ .el-card.is-always-shadow,
/deep/ .el-card.is-hover-shadow:focus,
/deep/ .el-card.is-hover-shadow:hover {
  box-shadow: none;
}
/deep/ .el-card {
  border-bottom: none;
}
/deep/ .el-card:last-child {
  border-bottom: 1px solid #f0f1f5;
}
.analysis-btns {
  margin-top: 40px;
  display: flex;
  justify-content: flex-start;
}
.basicBtn {
  font-size: 16px;
  color: #4f4f5b;
  margin-right: 25px;
  cursor: pointer;
}
.selectedAnalysis {
  font-size: 18px;
  font-weight: 600;
  color: #42c57a;
}
</style>
