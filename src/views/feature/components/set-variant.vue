<template>
  <div class="setVariant">
    <h3 class="basic-title">2.设置变体</h3>
    <!-- {{featureData}} -->
    <el-row class="basic-set-container">
      <el-form label-width="100px" size="mini" :model="variantData">
        <el-form-item required label="Key">
          <el-input :disabled="isDisabled" class="setVariant-input" v-model="variantData.feature.keyName"></el-input>
        </el-form-item>
        <!-- {{variantData}} -->
        <el-form-item label="变体类型" required>
          <el-select class="setVariant-input"
                     :disabled="isDisabled"
                     v-model="variantData.feature.keyType"
                     @change="handleChangeParamType"
          >
            <el-option
              v-for="item in paramType"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            >
              {{ item.label }}
            </el-option>
          </el-select>
        </el-form-item>
        <!-- {{ variantData.versionList }} -->
      </el-form>
    </el-row>
    <el-row class="basic-set-container">
      <!-- {{variantData.versionList}} -->
      <!-- {{ variantData.versionList.length }} -->
      <el-col :span="23" :offset="1">
        <el-scrollbar ref="scroll" class="scroll-container">
          <div class="variant" v-for="(item, index) in variantData.versionList" :key="index">
            <div class="variant-item">
              <div class="variant-item-box">
                <div class="variant-item-title">
                  <div class="variant-item-title-box"></div>
                  <div class="change-variant-name" v-if="item.isEdit || !isDisabled">
                    {{ item.displayName }}
                  </div>
                  <!-- -----{{item.isNeedEdit}} -->
                  <div class="change-variant-name-change" v-if="item.isNeedEdit">
                    <el-input
                      class="setVariant-input"
                      v-model="newVariant[index]"
                      :disabled="disabled"
                    ></el-input>
                    <el-button
                      type="text"
                      class="change-variant-name-btn"
                      @click="changeVariantYes(index)"
                    >
                      确定
                    </el-button>
                    <el-button
                      type="text"
                      class="change-variant-name-btn"
                      @click="changeVariantNo(index)"
                    >
                      取消
                    </el-button>
                  </div>
                  <!-- :disabled="disabled" -->
                  <!-- v-if="item.isEdit || opType == 'add'"
                    v-show="isDetail" -->
                  <i
                    class="el-icon-edit icon-edit"
                    @click="editVariantName(index)"
                  ></i>
                </div>
                <el-row class="set-variant">
                  <el-col :span="4">
                    <div class="set-variant-item">
                      <p class="set-variant-value">值</p>
                      <!--
                        <el-input
                          :disabled="disabled"
                          class="setVariant-input-value"
                          v-model="item.featureList[0].variant.keyValue"
                        ></el-input>
                      -->
                      <div v-if="varType === 1">
                        <el-input
                          class="setVariant-input-value"
                          :disabled="disabled"
                          v-model="item.keyValue"
                          size="large"
                          type="text"
                          maxlength="500"
                          placeholder="支持500个字符"
                        ></el-input>
                      </div>
                      <div v-if="varType === 2">
                        <el-select
                          class="setVariant-input-value"
                          :disabled="true"
                          v-model="item.keyValue"
                          placeholder="选择取值"
                        >
                          <el-option
                            v-for="(paramType, paramTypeIndex) in booleanList"
                            :key="paramTypeIndex"
                            :value="paramType.name"
                            :label="paramType.name"
                          ></el-option>
                        </el-select>
                      </div>
                      <div v-if="varType === 4">
                        <el-input
                          class="setVariant-input-value"
                          :disabled="disabled"
                          v-model="item.keyValue"
                          size="large"
                          type="number"
                          placeholder="请输入数字"
                        ></el-input>

                        <!-- <el-input-number
                          class="setVariant-input-value"
                          :disabled="disabled"
                          v-model="item.featureList[0].variant.keyValue"
                          :min="-9007199254740991"
                          :max="9007199254740991"
                          maxlength="10"
                        ></el-input-number> -->
                      </div>

                      <div style="position: relative" v-if="varType === 5">
                      <span class="edit_icon" @click="handleEditJson(item, index)">
                        <svg
                          viewBox="64 64 896 896"
                          focusable="false"
                          data-icon="fullscreen"
                          width="1em"
                          height="1em"
                          fill="currentColor"
                          aria-hidden="true"
                        >
                          <path
                            d="M290 236.4l43.9-43.9a8.01 8.01 0 00-4.7-13.6L169 160c-5.1-.6-9.5 3.7-8.9 8.9L179 329.1c.8 6.6 8.9 9.4 13.6 4.7l43.7-43.7L370 423.7c3.1 3.1 8.2 3.1 11.3 0l42.4-42.3c3.1-3.1 3.1-8.2 0-11.3L290 236.4zm352.7 187.3c3.1 3.1 8.2 3.1 11.3 0l133.7-133.6 43.7 43.7a8.01 8.01 0 0013.6-4.7L863.9 169c.6-5.1-3.7-9.5-8.9-8.9L694.8 179c-6.6.8-9.4 8.9-4.7 13.6l43.9 43.9L600.3 370a8.03 8.03 0 000 11.3l42.4 42.4zM845 694.9c-.8-6.6-8.9-9.4-13.6-4.7l-43.7 43.7L654 600.3a8.03 8.03 0 00-11.3 0l-42.4 42.3a8.03 8.03 0 000 11.3L734 787.6l-43.9 43.9a8.01 8.01 0 004.7 13.6L855 864c5.1.6 9.5-3.7 8.9-8.9L845 694.9zm-463.7-94.6a8.03 8.03 0 00-11.3 0L236.3 733.9l-43.7-43.7a8.01 8.01 0 00-13.6 4.7L160.1 855c-.6 5.1 3.7 9.5 8.9 8.9L329.2 845c6.6-.8 9.4-8.9 4.7-13.6L290 787.6 423.7 654c3.1-3.1 3.1-8.2 0-11.3l-42.4-42.4z"
                          ></path>
                        </svg>
                      </span>

                        <el-popover placement="top" width="200" trigger="hover">
                          <span>{{ item.keyValue }}</span>
                          <el-input
                            slot="reference"
                            @blur="handleJsonInput(item.keyValue)"
                            :disabled="disabled"
                            v-model="item.keyValue"
                            class="json_edit"
                            resize="none"
                            size="large"
                            type="textarea"
                            placeholder='请输入0~10240字符，json格式{"comment":true}'
                            maxlength="10240"
                          ></el-input>
                        </el-popover>

                      </div>
                      <!-- <el-input
                        :disabled="disabled"
                        class="setVariant-input-value"
                        v-model="item.featureList[0].variant.keyValue"
                      ></el-input> -->
                    </div>
                  </el-col>
                  <el-col :span="20">
                    <div class="set-variant-item" v-if="varType != 5">
                      <p class="set-variant-value">描述</p>
                      <el-input
                        :disabled="disabled"
                        class="setVariant-input-value"
                        v-model="item.description"
                        placeholder="支持1000个字符"
                      ></el-input>
                    </div>
                    <div class="set-variant-item" v-if="varType == 5">
                      <p class="set-variant-value">描述</p>
                      <el-input
                        type="textarea"
                        :disabled="disabled"
                        class="var_dec"
                        v-model="item.description"
                        placeholder="支持1000个字符"
                      ></el-input>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <!-- v-if="idx !== 0 && !isCheck && addData.status !== 3 && addData.status !== 1" -->
            </div>
            <i class="el-icon-circle-close close-btn"
               v-if="variantData.versionList.length > 1 && variantData.feature.keyType !== 2 && !disabled"
               @click="handleDeleteParams(index)"
            ></i>
          </div>
        </el-scrollbar>
        <el-button v-if="this.variantData.feature.keyType !== 2" :disabled="addVarDisabled || disabled"
                   class="btn_add" icon="el-icon-plus" type="primary" size="small"
                   @click="handleAddVar"
        >
          添加新变体
        </el-button>
      </el-col>
    </el-row>

    <edit-json-dialog
      v-if="isShowDialog"
      :dialogVisible.sync="isShowDialog"
      @handleJsonSucceed="handleJsonSucceed"
      :jsonValue="jsonValue"
    ></edit-json-dialog>
  </div>
</template>
<script>
import {paramTypeList} from '../contants';
import { mapGetters } from 'vuex';
import EditJsonDialog from './edit-json-dialog.vue';


import _ from 'lodash';
export default {
  name: 'setVariant',
  props: {
    featureData: {
      type: Object,
      default: () => ({}),
    },
    opType: {
      type: String
    },
    variateInfo: {
      type: Object,
      default: () => ({}),
    },
    variantMaxNum: {
      type: Number
    },
  },
  components: {
    EditJsonDialog
  },
  data() {
    return {
      addVarDisabled: false,
      indexs: '',
      jsonValue: '',
      isShowDialog: false,
      isDisabled: true,
      isDetail: !this.$route.query.type,
      // disabled: this.featureData.type ? true : false,
      disabled: false,
      paramType: paramTypeList,
      variantData: {
        feature: {
          keyName: '',
          keyType: 2,
        },
        // keyName: '',
        // keyType: 1,
        versionList: [],
        filterRule: []
      },
      newVariant: [], // 新的版本名
      variantTimes: 0, //变体添加次数
      varType: 1,
    };
  },
  mounted() {
    if(this.opType === 'add') {
      this.variantData.keyType = 2;
      this.varType = 2;
      this.addVariant("true");
      this.addVariant("false");
    }
  },
  computed: {
    ...mapGetters(['addData', 'paramTypes', 'booleanList'])
  },
  watch: {
    variateInfo: {
      handler(newVal, oldVal) {
        // console.log('newVal, oldVal=====', newVal, oldVal);
        this.variantData.feature.keyName = newVal.feature.keyName;
        this.variantData.feature.keyType = newVal.feature.keyType;
        this.variantData.feature.id = newVal.feature.id;

        this.varType = newVal.feature.keyType;
        this.disabled = !!newVal.type;
        this.variantData.versionList = newVal.versionList.length > 0 ? newVal.versionList : [];
        this.variantData.versionList.forEach((item) => {
          item.isEdit = true;
          item.isNeedEdit = false;
        });
      },
      deep: true
    },
    variantData: {
      handler(val) {
        this.addVarDisabled = val.versionList.length > this.variantMaxNum;
        //首次初始化取数组长度
        if(this.variantTimes === 0) {
          this.variantTimes = val.versionList.length;
        }
        this.$emit('variantData', val);
      },
      deep: true,
    },
    opType: {
      handler(val) {
        if(val === 'add') {
          this.isDisabled = false;
        }
      },
      immediate: true
    }
  },
  methods: {
    // 删除变体
    handleDeleteParams(index) {
      this.variantData.versionList.splice(index, 1);
      this.newVariant.splice(index, 1);
    },
    /**
     * @description: 校验输入值合法性
     * @param {*} index
     * @param {*} idx
     * @return {*}
     */
    handleJsonInput(val) {
      if (!val) {
        return;
      } else if (!this.isJson(val)) {
        this.$message.error('请输入正确的JSON字符串');
      }
      // index version 编号 idx feature 编号
      // 数据结构是竖着的
      // console.log("index, idx", index, idx);
    },
    /**
     * @description: json编辑成功
     * @param {*} e
     * @return {*}
     */
    handleJsonSucceed(e) {
      // this.variantData.versionList[this.indexs].featureList[0].variant.keyValue = JSON.stringify(e);
      this.variantData.versionList[this.indexs].keyValue = JSON.stringify(e);
    },
    // icon 点击
    handleEditJson(item, index) {
      this.indexs = index;
      // let currentVal = this.variantData.versionList[index].featureList[0].variant.keyValue;
      let currentVal = this.variantData.versionList[index].keyValue;

      if (currentVal && !this.isJson(currentVal)) {
        this.$message.error('请输入正确的JSON字符串');
        return;
      }

      if (currentVal) {
        this.jsonValue = JSON.parse(currentVal);
      } else {
        this.jsonValue = {};
      }

      this.isShowDialog = true;
    },
    isJson(str) {
      if (typeof str == 'string') {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
            return false;
          }
        } catch (e) {
          console.log('error：' + str + '!!!' + e);
          return false;
        }
      } else {
        this.$message.error('请输入字符串');
      }
    },
    handleChangeParamType(e) {
      this.varType = e;
      if(this.varType == 2) {
        let length = this.variantData.versionList.length;
        this.variantData.versionList.splice(0, length);
        this.newVariant.splice(0, this.newVariant.length);
        this.addVariant("true");
        this.addVariant("false");
      } else {
        this.variantData.versionList.forEach((item) => {
          item.keyValue = '';
        });
      }
    },
    // 添加新变体
    handleAddVar() {
      this.addVariant("");
    },
    //变体新增
    addVariant(keyValue) {
      ++this.variantTimes;
      const newVar = {
        "displayName": `变体${this.variantTimes}`, // 变体名称
        "description": "", //变体描述
        "keyValue": keyValue, //变体的值
        //"isNeedEdit" : false,  //是否需要编辑
        "whitelist": [], // 新增详情返回一致
        curIndex: this.variantTimes - 1,
      };

      this.variantData.versionList.push(newVar);
      this.$set(this.variantData, 'versionList', this.variantData.versionList);
    },
    // 编辑名字按钮
    editVariantName(index) {
      this.$set(this.variantData.versionList[index], 'isEdit', false);
      // this.$set(this.variantData.versionList[index], 'isNeedEdit', true);
      this.variantData.versionList[index].isNeedEdit = true;
      this.newVariant[index] = this.variantData.versionList[index].displayName;
      this.$forceUpdate();
    },
    // 点击修改确定按钮
    changeVariantYes(index) {
      let displayName = this.newVariant[index].trim();
      let dual = this.variantData.versionList.find((v, i) => {
        return i !== index && v.displayName === displayName;
      });
      if(dual) {
        this.$message.warning(`变体名称:[${displayName}]出现重复值，请重新设置`);
        return;
      }
      this.$set(this.variantData.versionList[index], 'displayName', displayName);
      this.$set(this.variantData.versionList[index], 'isEdit', true);
      this.$set(this.variantData.versionList[index], 'isNeedEdit', false);
      this.$forceUpdate();
    },
    // 点击修改取消按钮
    changeVariantNo(index) {
      this.variantData.versionList[index].isEdit = true;
      this.variantData.versionList[index].isNeedEdit = false;
      this.$forceUpdate();
    },
  },
};
</script>
<style scoped lang="less">

.close-btn {
  display: none;
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 24px;
  background-color: #fff;
  cursor: pointer;
}

/deep/ .var_dec .el-textarea__inner {
  height: 54px;
}

.edit_icon {
  position: absolute;
  top: 4px;
  right: 3px;
  z-index: 10;
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

.change-variant-name-change /deep/ .el-button {
  margin-left: 10px;
}
.change-variant-name-change /deep/ .el-button + .el-button {
  margin-left: 10px;
}
.basic-set-container /deep/ .el-form-item--mini .el-form-item__label {
  font-size: 14px;
}
.basic-set-container /deep/ .el-input--mini {
  font-size: 14px;
}
.basic-title {
  font-size: 16px;
  font-weight: 800;
  margin-bottom: 20px;
}
.basic-set-container {
  width: 100%;
  margin-top: 20px;
}
.scroll-container{
  margin-left: 30px;
  /deep/ .el-scrollbar__wrap {
    height: 100%;
    max-height: 640px;
    overflow-y: auto;
    overflow-x: hidden;
  }
  /deep/ .el-scrollbar__view {
    margin: 0 10px 10px 0;
  }
}
.setVariant-input {
  width: 300px;
  height: 24px;
  line-height: 24px;
  /deep/.el-input__inner {
    height: 24px;
  }
}
.setVariant-input-value {
  height: 24px;
  line-height: 24px;
  /deep/.el-input__inner {
    height: 24px;
    font-size: 14px;
  }
}

/deep/ .el-input--mini .el-input__inner {
  font-size: 14px;
}
/deep/ .el-select-dropdown__item {
  font-size: 14px;
}

.variant {
  position: relative;
  display: flex;
  justify-content: flex-start;
  width: 100%;
  &:hover .close-btn {
    display: block;
  }
}
.variant-item {
  width: 100%;
  border: 1px solid #e7e9f5;
  margin-bottom: 20px;
}
.variant-item-box {
  padding: 8px;
}

.variant-item-title-box {
  background: rgb(51, 112, 255);
  border-radius: 3px;
  width: 18px;
  height: 18px;
  margin: 4px 5px 0 0;
}
.variant-item-title {
  display: flex;
  justify-content: flex-start;
  align-content: center;
}
.change-variant-name {
  font-size: 14px;
  padding-top: 8px;
}
.change-variant-name-change {
  display: flex;
  justify-content: flex-start;
  align-content: center;
}
.change-variant-name-btn {
  font-size: 14px;
  border: none;
  height: 24px;
}
.icon-edit {
  padding: 8px 0 0 10px;
}
.set-variant-item {
  margin: 10px 8px 10px 0;
}
.set-variant-value {
  font-size: 14px;
  padding: 5px  0;
}
</style>

