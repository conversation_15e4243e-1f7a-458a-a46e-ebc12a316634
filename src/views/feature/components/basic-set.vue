<template>
  <div :class="[isDetail ? 'edit' : 'detail']">
    <h3 class="basic-title">1、基本配置</h3>
    <el-row class="basic-set-container">
      <el-col :span="14">
        <el-form :model="basicData" label-width="100px" size="mini">
          <el-form-item label="名称" required>
            <template>
              <el-input
                v-if="isDetail"
                v-model="basicData.displayName"
                :disabled="disabled"
                class="basic-item"
                placeholder="请输入名称"
              ></el-input>
            </template>
            <template>
              <div v-if="!isDetail">
                <span>{{ basicData.displayName }}</span>
                <span class="currectVersion">V{{ basicData.currentVersion }}</span>
              </div>
            </template>
          </el-form-item>
          <el-form-item label="描述:">
            <el-input
              :disabled="disabled"
              type="textarea"
              :rows="2"
              v-model="basicData.description"
              class="basic-item"
              placeholder="请输入描述信息"
              allow-clear
            />
          </el-form-item>


          <!-- style="width: 150px" -->
          <el-form-item label="业务线：">
            <el-select
              :disabled="isDisabled"
              v-model="basicData.appKey"
              placeholder="请选择appKey"
            >
              <el-option
                v-for="item in appList"
                :key="item.appKey"
                :label="item.displayName"
                :value="item.appKey"
              ></el-option>
            </el-select>
          </el-form-item>


          <!-- <el-form-item label="标签:">
            <el-select
              size="mini"
              v-model="basicData.tags"
              class="basic-item multiple-select"
              multiple
              filterable
              allow-create
              default-first-option
            ></el-select>
          </el-form-item> -->
          <el-form-item label="实验类型:" required>
            <el-radio-group v-model="basicData.clientType" :disabled="isDisabled">
              <el-radio :label="1">客户端</el-radio>
              <el-radio :label="2">服务端</el-radio>
            </el-radio-group>
            <el-popover trigger="hover" placement="top">
              <p class="popoverContent">
                客户端Featurslote，指通过客户端获取固化分组信息并控制配置生效的固化，如客户端交互功能、UI样式等都建议创建客户端固化；服务端固化，指通过服务端获取固化分组信息并控制配置生效或下发的固化，如内容分发算法&策略、由服务端逻辑控制产品功能的固化都是服务端固化。
                请根据你的业务类型和固化生效方式选择端类型。
              </p>
              <i slot="reference" class="el-icon-question basic-set-icon-question"></i>
            </el-popover>
          </el-form-item>
          <el-form-item label="账号体系：" required>
            <!-- 账号体系，cuid：1，uid: 2-->
            <el-radio-group v-model="basicData.accountSystem" :disabled="isDisabled">
              <el-radio :label="1">cuid</el-radio>
              <el-radio :label="2">uid</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="10">
        <div class="upload-box">
          <el-upload
            :disabled="disabled"
            :action="uploadConfig.action"
            :before-upload="beforeUpload"
            :on-success="handleAvatarSuccess"
            :show-file-list="false"
            class="avatar-uploader"
            v-model="basicData.picUrl"
          >
            <img v-if="basicData.picUrl" :src="basicData.picUrl" class="avatar" />
            <el-button v-else class="avatar-uploader-icon" type="primary">上传图片</el-button>
          </el-upload>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import api from '@/api/feature/index';
import Message from '@common/message';
import { mapGetters } from 'vuex';

const actions = {
  local: '/testAddress/earthworm/mis/upload/uploadimg',
  test: '/earthworm/mis/upload/uploadimg',
  server: '/earthworm/mis/upload/uploadimg'
};

export default {
  name: 'basic-set',
  props: {
    featureData: {
      type: Object,
      default: () => ({})
    },
    opType: {
      type: String
    },
    variateInfo: {
      type: Object,
      default: () => ({}),
    }
  },
  data() {
    return {
      isDisabled: true,
      isDetail: this.$route.query.type ? false : true,
      uploadApi: api.FEATUREUPLOADIMG,
      disabled: false,
      basicData: {
        currentVersion: '', //当前版本
        displayName: '', // 实验名称
        description: '', // 描述
        // tags: [],
        clientType: 1, // 端类型
        picUrl: '', //上传图片地址
        accountSystem: 1, //cuid：1，uid: 2
        appKey: ''
      }
    };
  },
  watch: {
    variateInfo: {
      handler(val1) {
        const val = val1;
        this.basicData.displayName = this.basicData.displayName
          ? this.basicData.displayName
          : val.displayName;

          this.basicData.currentVersion = this.basicData.currentVersion
          ? this.basicData.currentVersion
          : val.currentVersion;


        this.basicData.currectVersion = val.currectVersion;
        this.basicData.description = this.basicData.description
          ? this.basicData.description
          : val.description;


        this.basicData.picUrl = this.basicData.picUrl ? this.basicData.picUrl : val.picUrl;
        this.basicData.clientType = val.clientType ? val.clientType : 1;
        this.basicData.accountSystem = val.accountSystem ? val.accountSystem : 1;
        this.basicData.appKey = val.appKey ? val.appKey : '';

        this.disabled = val.type ? true : false;
      },
      deep: true
    },
    basicData: {
      handler(val) {
        // console.log('basicData===', val);
        this.$emit('basicData', val);
      },
      deep: true
    },
    appList: {
      handler(newVal, oldVal) {
        // console.log('????', newVal);
        if(this.opType === 'add' && newVal.length) {
          this.basicData.appKey = newVal[0].appKey;
        }
      },
      deep: true
    },
    opType: {
      handler(val) {
        if(val === 'add') {
          this.isDisabled = false;
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapGetters(['appList']),
    uploadConfig() {
      return {
        action: this.computedEnv()
      };
    }
  },
  created() {
    this.$store.dispatch('getAppList');
  },
  methods: {
    // 适配文件上传环境地址
    computedEnv() {
      let hostName = location.hostname;
      let url = '';
      if (/^localhost$/gi.test(hostName)) {
        url = actions.local;
      } else if (/docker|test/gi.test(hostName)) {
        url = actions.test;
      } else {
        url = actions.server;
      }
      return url;
    },

    // 上传图片之前
    beforeUpload(file) {
      if (file.size / 1024 / 1024 > 2) {
        Message.warning('上传图片大小不能超过2M');
      }
    },
    // 上传成功
    handleAvatarSuccess(file) {
      this.basicData.picUrl = file.data.url;
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .el-form-item--mini .el-form-item__label {
  font-size: 14px;
}
.detail {
  /deep/ .el-input.is-disabled .el-input__inner {
    // background-color: #ffffff;
    // border: none;
    font-size: 14px;
  }
  /deep/ .el-textarea.is-disabled .el-textarea__inner {
    // background-color: #ffffff;
    // border: none;
    font-size: 14px;
  }
}

/deep/ .el-textarea__inner {
  font-size: 14px;
}
/deep/ .el-input--mini {
  font-size: 14px;
}

.basic-set-container {
  font-size: 14px;
  display: flex;
  justify-content: center;
  width: 100%;
  /deep/.el-form-item--mini .el-form-item__label {
    font-size: 14px;
  }
}

/deep/.ant-upload.ant-upload-select-picture-card {
  height: 100%;
  width: 100%;
}
/deep/ .el-input--mini .el-input__inner {
  font-size: 14px;
}

/deep/ .el-textarea__inner {
  font-size: 14px;
}
/deep/ .el-radio__label {
  font-size: 14px;
}
.currectVersion {
  margin-left: 7px;
  border: 1px solid #00dcc3;
  color: #00dcc3;
  padding: 0 6px;
  border-radius: 5px;
  background-color: rgba(0, 220, 195, 0.3);
}

.basic-title {
  font-size: 16px;
  margin-bottom: 20px;
  font-weight: 800;
  float: left;
}
.upload-box {
  width: 100%;
  height: 100%;
  padding: 0 10px;
}
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 95%;
  height: 85%;
  img {
    position: absolute;
    left: calc(50% - 100px);
    object-fit: contain;
  }
}
.avatar {
  width: 178px;
  height: 110px;
  display: block;
}

.avatar-uploader-icon {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.basic-item {
  margin-left: 0;
}

.popoverContent {
  width: 300px;
  font-size: 10px;
}
.basic-set-icon-question {
  margin-left: 10px;
}
.multiple-select {
  width: 100%;
}
</style>
