<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-08-05 10:52:36
 * @LastEditors: zhuyue
 * @LastEditTime: 2021-08-26 11:10:30
 * @Description: 编辑json
-->
<template>
  <el-dialog
    width="450px"
    :title="title"
    :visible="dialogVisible"
    :before-close="dialogClose"
    class="arg_style_dialog"
    @close="handleDialogClose"
  >
    <json-edit
      :options="options"
      @input="handleInput"
      @error="handleError"
      :value="jsonValue"
    ></json-edit>
  </el-dialog>
</template>

<script>
import * as _ from 'lodash';
import { mapGetters } from 'vuex';
import JsonEdit from '@/components/tools/json-edit.vue';

export default {
  name: 'create-dialog',
  components: {
    JsonEdit
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    editId: {
      type: Number | String
    },
    argId: {
      type: Number | String
    },
    type: {
      type: String
    },
    jsonValue: {
      type: Object
    }
  },
  computed: {
  },
  data() {
    return {
      editValue: '',
      options: {
        onChange: (e) => {
          console.log(e);
        },
        onValidate: function (json) {
          console.log('onValidate', json);
          var errors = [];

          if (json && json.customer && !json.customer.address) {
            errors.push({
              path: ['customer'],
              message: 'Required property "address" missing.'
            });
          }

          return errors;
        },
        mode: 'code',
        // theme: 'ace/theme/draw'
      },
      title: '',
    };
  },
  created() {
  },
  mounted() {
  },
  methods: {
    handleDialogClose() {
      this.$emit('handleJsonClose');
    },
    handleInput(e) {
      this.$emit('handleJsonSucceed', e);
      console.log("e+++++", e);
    },
    handleError() {

    },
    dialogClose() {
      this.$emit('update:dialogVisible', false);
      // this.defaultFormat();
    },
  }
};
</script>

<style lang="less" scoped>

/deep/.el-dialog__header {
  padding: 10px;
}

/deep/ .el-select--small {
  width: 100%;
}

/deep/ .el-dialog__header {
  border: transparent !important;
}

.arg_style_dialog {
  /deep/ .el-dialog__body {
    padding-top: 15px;
      // height: 450px;
      // overflow: auto;
    }
}

/deep/ .el-dialog__headerbtn {
  right: 16px;
}

.warn_style {
  margin-top: 10px;
  color: red;
  font-size: 13px;
  margin-left: 7px;
}
/deep/ .zyb-dialog--default {
  min-height: 50% !important;
  width: 760px;
  min-width: 30%;
}
.exp-detail {
  padding-bottom: 20px;
  color: #2f2f3f;
}
.utils-detail {
  font-size: 12px;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  .detail {
    display: block;
    color: #8b8ba6;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28px;
    font-size: 14px;
  }
  .detail-value {
    width: 200px;
    span {
      display: inline-block;
      width: 50px;
      padding-left: 8px;
    }
  }
  .lang-content {
    float: right;
    width: 400px;
    padding-right: 15px;
    span {
      padding: 0 11px;
    }
  }
  .label-small {
    font-size: 12px;
    color: #8b8ba6;
    width: 400px;
    margin-top: 3px;
  }
  /deep/ .com-btn {
    color: #42c57a;
    border-color: #42c57a;
  }
}
/deep/ .el-dialog__header {
  border-bottom: 1px solid #f0f0f0;
}
/deep/ .divider-con {
  margin: 20px 0;
}
.tip {
  padding: 10px 10px 20px;
}
/deep/ .el-radio__label {
  font-size: 14px;
}
</style>
