<template>
  <el-dialog
    width="80%"
    min-height="200px"
    :visible.sync="dialogVisible"
    @close="$emit('handleRelevanceDialogClose', false)"
  >
    <!--  dialog标题 -->
    <div slot="title" class="dialog-title">
      <span class="dialog-title-item">
        {{ relevanceDialogData.displayName }}
      </span>
      <span>关联的实验</span>
    </div>
    <!-- dialog内容 -->
    <div class="dialog-content">
      <zyb-table :table-data="columnDataList" :table-columns="tableColumns">
        <template v-slot:custom="{ data: { item, scope } }">
          <template v-if="item.prop === 'experimentDisplayName'">
            <a @click="turnExp(relevanceData.experimentId)">
              {{ scope.row.experimentDisplayName }}
            </a>
          </template>
        </template>
      </zyb-table>
    </div>
  </el-dialog>
</template>
<script>
import { formatDate } from '@/common/utils';
import ZybTable from '@/components/zyb-table/index.vue';

export default {
  components: { ZybTable },
  name: 'forbidden-dialog',
  props: {
    relevanceData: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      relevanceDialogData: this.relevanceData,
      columnDataList: [], //table数据
      columData: {},
      statusName: {
        1: '草稿',
        2: '调试中',
        3: '发布中',
        4: '已结束'
      },
      tableColumns: [
        {
          label: '实验名称',
          prop: 'experimentDisplayName',
          title: true,
          custom: true
        },
        {
          label: '所在互斥组',
          prop: 'exclusiveName'
        },
        {
          label: '实验状态',
          prop: 'id',
          render: (scope) => {
            return this.statusName[scope.row.experimentStatus];
          }
        },
        {
          label: '创建者',
          prop: 'experimentCreateUser'
        },
        {
          label: '实验流量',
          prop: 'experimentFlow',
          render: (scope) => {
            return scope.row.experimentFlow / 10 + '%';
          }
        },
        {
          label: '实验开启时间',
          prop: 'experimentStartTime'
        }
      ]
    };
  },
  created() {
    this.columData.experimentDisplayName = this.relevanceData.experimentDisplayName;
    this.columData.exclusiveName = this.relevanceData.exclusiveName
      ? this.relevanceData.exclusiveName
      : '-';
    this.columData.experimentStatus = this.relevanceData.experimentStatus;
    this.columData.experimentCreateUser = this.relevanceData.experimentCreateUser;
    this.columData.experimentFlow = this.relevanceData.experimentFlow;
    this.columData.experimentStartTime = this.relevanceData.experimentStartTime
      ? formatDate(this.relevanceData.experimentStartTime * 1000, 'yyyy-MM-dd HH:mm')
      : '-';
    this.columData.currectVersion = this.relevanceData.currectVersion;
    this.columnDataList = [this.columData];
  },
  methods: {
    // 点击实验名跳转到实验详情
    turnExp(id) {
      this.$router.push({
        path: '/exp-manage/list/edit',
        query: {
          id,
          type: 'check'
        }
      });
    },
    // 所关联实验跳转到固化详情
    turnFeatureDetail() {
      this.$router.push({
        path: '/feat/list/detail',
        query: {
          id: this.relevanceData.id,
          layerId: this.relevanceData.layerId,
          appKey: this.relevanceData.appKey,
          type: 'detail'
        }
      });
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .el-dialog__headerbtn {
  padding: 20px;
}
/deep/ .el-dialog__header {
  padding-bottom: 0;
}
// /deep/ .el-dialog__body {
//   padding-bottom: 0;
// }
/deep/ .el-dialog__footer {
  padding-right: 36px;
}
/deep/ .el-textarea {
  width: 80%;
  border-radius: 4px;
}

.dialog-title {
  padding: 10px 20px 20px 20px;
  text-align: left;
  font-size: 16px;
  font-weight: 700;
  overflow: hidden;
}
.dialog-content {
  padding: 0 20px;
}
.dialog-form {
  padding-top: 10px;
}
.dialog-title-item {
  margin-right: 5px;
}

.status-2 {
  background-color: rgba(51, 112, 255, 0.2);
  color: rgb(51, 112, 255);
  padding: 3px 10px;
  font-size: 12px;
  width: 55px;
}
.status-3 {
  background-color: rgba(51, 112, 255, 0.2);
  color: rgb(51, 112, 255);
  padding: 3px 10px;
  font-size: 12px;
  width: 55px;
}
.status-4 {
  background-color: rgba(90, 206, 187, 0.2);
  color: rgb(90, 206, 187);
  padding: 3px 10px;
  font-size: 12px;
  width: 55px;
}
</style>


