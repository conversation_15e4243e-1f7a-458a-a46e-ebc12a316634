<template>
  <div>
    <div style="">
      <h3 class="example_title">示例代码</h3>
      <p class="example_content">选择查看您项目所用的SDK示例代码，在你的项目中复制并粘贴以下代码。</p>
    </div>
    <div class="nav_tabs" style="margin-left: 27px;">
      <el-tabs :class="{ line_style: isLineStyle }" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane v-for="(item, index) in ueseTabs " :key="index" :label="item.label" :name="item.value">
          <div class="code_wrapper">
            <div class="copy_btn">
              <i style="font-size: smaller; color: #42c57a;" class="el-icon-document-copy"></i>
              <span @click="handleCopy">复制</span>
            </div>
            <div class="code-viewer">
              <pre v-highlightjs="sourcecode">
              <code :class="languageType"></code>
            </pre>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>

</template>

<script>
import { isArray } from 'lodash';

export default {
  name: 'show-example-code',
  props: {
    portType: {
      type: Number,
      default: 1 // 1 客户端 2 服务端
    },
    addData: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  components: {
  },
  data() {
    return {
      isLineStyle: true,
      ueseTabs: [],
      serveTabs: [
        {
          label: 'PHP',
          value: 'php'
        },
        {
          label: 'GO',
          value: 'go'
        }
      ],
      clientTabs: [
        {
          label: 'IOS',
          value: 'ios'
        },
        {
          label: 'Android',
          value: 'android'
        },
        {
          label: 'JavaScript',
          value: 'javascript'
        }
      ],
      languageType: 'java',
      sourcecode: '',
      codeText: '',
      activeName: 'ios',
      targetOption: [], //指标
      sport: '',
      typeValue: '',
      experimentId: ''
    };
  },
  watch: {
    'portType': {
      handler(newUrl, oldUrl) {
        if (this.portType === 1) {
          this.ueseTabs = this.clientTabs;
          this.activeName = 'ios';
          this.sourcecode = this.getObjectC();
        } else {
          this.activeName = 'php';
          this.sourcecode = this.getPHP();
          this.ueseTabs = this.serveTabs;
        }
      },
      immediate: true
    },
    'addData.versionList': {
      handler(newUrl, oldUrl) {
        console.log('newUrl参数===', newUrl);
        if (this.portType === 1) {
          this.activeName = 'ios';
          this.sourcecode = this.getObjectC();
        } else {
          this.activeName = 'php';
          this.sourcecode = this.getPHP();
        }
      },
      deep: true
      // immediate: true
    },
    'addData.filterRule': {
      handler(newUrl, oldUrl) {
        console.log('newUrl受众规则修改===', newUrl);
        if (this.portType === 1) {
          this.activeName = 'ios';
          this.sourcecode = this.getObjectC();
        } else {
          this.activeName = 'php';
          this.sourcecode = this.getPHP();
        }
      },
      deep: true
    }
  },
  methods: {
    handleClick(tab, event) {
      this.isLineStyle = false;
      // 客户端 1 服务端 2
      console.log(this.portType);
      switch (tab.label) {
        case 'IOS':
          // this.languageType = 'Objective-C'
          this.languageType = 'JavaScript';
          this.sourcecode = this.getObjectC();
          break;
        case 'Android':
          this.languageType = 'java';
          this.sourcecode = this.getAndroid();
          break;
        case 'JavaScript':
          this.languageType = 'JavaScript';
          this.sourcecode = this.getJavaScript();
          break;
        case 'PHP':
          this.languageType = 'PHP';
          this.sourcecode = this.getPHP();
          break;
        case 'GO':
          this.languageType = 'Go';
          this.sourcecode = this.getGolang();
          break;
        default:
      }
      // 服务端
      console.log(tab.label);
      this.codeText = tab.label;
    },
    /**
     * @description: 实验参数
     * @param {*}
     * @return {*}
     */
    getTestArg() {
      //const selecFeatureObj = [];
      const versions = this.addData.versionList,
        params = versions[0].featureList,
        paramsLen = params.length,
        typeTest = /^\w+$/,
        trueVersions = versions.slice(1),
        trueVerLen = trueVersions.length;

      let tempArr = [],
        newArr = [],
        displayNameArr = [];
      //const selecFeatureVal = [];
      for (let j = 0; j < trueVerLen; j++) {
        const featureList = trueVersions[j].featureList;
        const name = trueVersions[j].displayName;
        displayNameArr.push(name);
        for (let k = 0, featLen = featureList.length; k < featLen; k++) {
          if (tempArr.indexOf(featureList[k].keyName) === -1) {
            newArr.push({
              keyName: featureList[k].keyName,
              keyType: featureList[k].keyType,
              list: [featureList[k].variant.keyValue]
            });
            tempArr.push(featureList[k].keyName);
          } else {
            for (let j = 0; j < newArr.length; j++) {
              if (newArr[j].keyName == featureList[k].keyName) {
                newArr[j].list.push(featureList[k].variant.keyValue);
              }
            }
          }
        }
      }

      return newArr;
    },
    // 规则数据
    getAllRules() {
      let allRules = [];
      if (this.addData.filterRule.length) {
        this.addData.filterRule.forEach((item) => {
          item.filterList.length && item.filterList.forEach((every) => {
            allRules.push({
              k: every.keyName,
              v: isArray(every.keyValue) ? every.keyValue.slice(0, 1).join() : every.keyValue
              // Value: every.keyValue.slice(0,1)
            });
          });
        });
      }
      return allRules;
    },
    setAllChoice(list) {
      // 网络ip keyName=ip 地址不需要 人群包 key=renqunbao
      let result = JSON.parse(list);
      let newResult = [];
      result.forEach((item) => {
        if (item.k != 'ip' && item.k != 'renqunbao') {
          newResult.push(item);
        }
      });

      // 去重
      var obj = {};
      newResult = newResult.reduce(function (item, next) {
        obj[next.k] ? '' : obj[next.k] = true && item.push(next);
        return item;
      }, []);
      return newResult;
    },
    getPHP() {
      const appld = this.addData.appKey;
      let testarg = this.getTestArg();
      // 受众规则
      let allRules = this.getAllRules();

      let zipStr = JSON.stringify(allRules);
      let arrArg = this.setAllChoice(zipStr);

      // 渲染模版
      let switchStr = (list) => {
        let ks = ``;
        list.forEach((item) => {
          ks += `  case "${item}"
          //${item}逻辑
          break;
    `;
        });
        return ks;
      };

      let argStr = ``;
      testarg.forEach((item) => {
        argStr += `
$data = Ablib_Interface_Sdk::Report(${item.keyName},${appld},'cuid',${JSON.stringify(arrArg)});
if(false !== $data && !empty($data)){
    $value = $data['value'];
      switch ret1.Value {
      ${switchStr(item.list)}
      }
}
        `;
      });

      return `
/*
* @synopsis  Report 获取策略结果
*
* @param $keyName string
* @param $appId   string
* @param $cuid    string
* @param $params  json      [{"k":"course","v":"语文"},{"k":"course","v":"英语"},{"k":"grade","v":"15"}]
*
* @returns   false/array
*     [
*         "key"          : $keyName,   //key名称
*         "type"         : 1,          //1:string 2:bool 3:float64
*         "versionId"    : 20,         //版本ID
*         "value"        : "a",        //命中版本值
*         "experimentId" : 9,          //实验Id
*     ]
*/
      ${argStr}
      `;
    },
    getGolang() {

      let testarg = this.getTestArg();
      // 受众规则
      let allRules = this.getAllRules();

      let zipStr = JSON.stringify(allRules);
      let arrArg = this.setAllChoice(zipStr);

      // 模版渲染
      let str = ``;
      arrArg.forEach((item) => {
        str += `
{
  Key: ${item.k},
  Value: ${item.v}
}
        `;
      });

      let switchStr = (list) => {
        let ks = ``;
        list.forEach((item) => {
          ks += ` case "${item}"
  fmt.Println("命中了${item}版本")
     break
    `;
        });
        return ks;
      };

      let argStr = ``;
      testarg.forEach((item) => {
        argStr += `
variantKey := ${item.keyName}

ret1, err := app1Ab.Report(ctx, cuid, variantKey, params)
if err != nil {
    fmt.Println("get variant value failed:", err)
}

//未命中
if err == ErrorMissAb {
  fmt.Println("未命中实验，默认命中结果需由业务方控制")
  ...
}

if err != nil {
  fmt.Println("report error: %s", err)
}

switch ret1.Value {
${switchStr(item.list)}
}
        `;
      });

      return `
import (
  "git.zuoyebang.cc/znzt/abtest-client/abtest"
  ...
)


var app1Ab *abtest.Client

func InitAbClient() {
   
    var err error
   
    //用户的使用环境和appId
    apiClient := &conf.Api.Abengine
    appId1 := "<your-app-id1>"

    app1Ab,err = abtest.Init(abtest.Config{
        ApiClient:apiClient,
        AppID:appId1,
    })
    if err != nil  {
        fmt.Println("init abtest1 conf failer:", err)
    }
}
func main() {
   
    //初始化
    InitAbClient()

//用户唯一设备号
cuid := "<cuid>"

params := []abtest.KVPair{{
${str}
${argStr}
}
      `;
      // const a1 = []
      // const b2 = []
      // const c3 = []

      // this.addData.versionList.forEach((argItem) => {
      //   argItem.featureList.forEach((featEvery) => {
      //     if(featEvery.keyName === 'aaa1') {
      //       a1.push({
      //         keyName: 'aaa1',
      //         keyValue: featEvery.variant.keyValue
      //       })
      //     } else if(featEvery.keyName === 'bbb2') {
      //       // b2.push({
      //       //   keyName: 'aaa1',
      //       //   keyValue: featEvery.variant.keyValue
      //       //   }
      //       // )
      //     } else if(featEvery.keyName === 'ccc3') {
      //       c3.push()
      //     }
      //   }) 
      // })
      // console.log('a1====', a1);
    },
    getObjectC() {
      return `#import <RangersAppLog/BDAutoTrack.h>
#import <RangersAppLog/BDAutoTrackConfig.h>

 (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {

    /* 初始化开始 */
    BDAutoTrackConfig *config = [BDAutoTrackConfig new];

    /*域名默认国内: BDAutoTrackServiceVendorCN,
      新加坡: BDAutoTrackServiceVendorSG,
      美东:BDAutoTrackServiceVendorVA,
      注意：国内外不同vendor服务注册的did不一样。由CN切换到SG或者VA，会发生变化，切回来也会发生变化。因此vendor的切换一定要慎重，随意切换导致用户新增和统计的问题，需要自行评估。另外，切换上报区域，需要同时将appid更换为对应区域的appid，这可能需要您在重新创建应用并在高级选项中选择对应区域。*/
    config.serviceVendor = BDAutoTrackServiceVendorCN;

    config.appID = @"10000022"; // 如不清楚请联系专属客户成功经理
    config.appName = @"your appName"; // 与您申请APPID时的app_name一致
    config.channel = @"App Store"; // iOS一般默认App Store
    config.abEnable = YES; // 开启ab测试，默认为YES

    config.showDebugLog = NO; // 是否在控制台输出日志，仅调试使用，需要同时设置logger。release版本请设置为 NO
    config.logger = ^(NSString * _Nullable log) {
        NSLog(@"%@",log);
    };
    config.logNeedEncrypt = YES; // 是否加密日志，默认加密。release版本请设置为 YES

    config.autoTrackEnabled = YES;
    [BDAutoTrack startTrackWithConfig:config];
    /* 初始化结束 */

    /*自定义 “用户公共属性”（可选，初始化后调用, key相同会覆盖）
      关于自定义 “用户公共属性” 请注意：1. 上报机制是随着每一次日志发送进行提交，默认的日志发送频率是1分钟，所以如果在一分钟内连续修改自定义用户公共属性，，按照日志发送前的最后一次修改为准， 2. 不推荐高频次修改，如每秒修改一次 */
    [BDAutoTrack setCustomHeaderBlock:^NSDictionary<NSString *,id> * _Nonnull{
        return @{};
    }];
}

...


NSString *newOne = [BDAutoTrack ABTestConfigValueForKey:@"one" defaultValue:@("")];
if ([newOne isEqualToString:@"name"]) {
  //
} else if ([newOne isEqualToString:@"type"]) {
  //
} else {
  //
} 
NSString *newTwo = [BDAutoTrack ABTestConfigValueForKey:@"two" defaultValue:@("")];
if ([newTwo isEqualToString:@"age"]) {
  //
} else if ([newTwo isEqualToString:@"bye"]) {
  //
} else {
  //
} 
NSNumber newThree = [BDAutoTrack ABTestConfigValueForKey:@"three" defaultValue:@(0)];
if ([newThree integerValue == 1]) {
  //
} else if ([newThree integerValue == 2]) {
  //
} else {
  //
} 
Boolean newFor = [BDAutoTrack ABTestConfigValueForKey:@"for" defaultValue:@(NO)];
if (newFor) {
  // A 版本分支
} else {
  // B 版本分支
}
     
NSDictionary *newFive = [BDAutoTrack ABTestConfigValueForKey:@"five" defaultValue:@{}];
if ([newFive isEqualToDictionary:@{"content":"ddd"}]) {
  //
} else if ([newFive isEqualToDictionary:@{"content":"222"}]) {
  //
} else {
  //
}
`;
    },
    getAndroid() {
      return `public class TheApplication extends Application {
  @Override
  public void onCreate() {
      super.onCreate();

      /* 初始化开始 */
      final InitConfig config = new InitConfig("10000022", "your_channel"); // appid和渠道，appid如不清楚请联系客户成功经理

      /* 域名默认国内: DEFAULT, 新加坡:SINGAPORE, 美东:AMERICA}
      注意：国内外不同vendor服务注册的did不一样。由DEFAULT切换到SINGAPORE或者AMERICA，会发生变化，切回来也会发生变化。因此vendor的切换一定要慎重，随意切换导致用户新增和统计的问题，需要自行评估。另外，切换上报区域，需要同时将appid更换为对应区域的appid，这可能需要您在重新创建应用并在高级选项中选择对应区域。*/
      config.setUriConfig(UriConfig.DEFAULT);

      // 是否在控制台输出日志，可用于观察用户行为日志上报情况，建议仅在调试时使用，release版本请设置为false
      AppLog.setEnableLog(false);

      // 开启圈选埋点
      config.setPicker(new Picker(this, config));

      // 开启AB测试
      config.setAbEnable(true);

      config.setAutoStart(true);
      AppLog.init(this, config);
      /* 初始化结束 */

      /* 自定义 “用户公共属性”（可选，初始化后调用, key相同会覆盖）
      关于自定义 “用户公共属性” 请注意：1. 上报机制是随着每一次日志发送进行提交，默认的日志发送频率是1分钟，所以如果在一分钟内连续修改自定义用户公共属性，，按照日志发送前的最后一次修改为准， 2. 不推荐高频次修改，如每秒修改一次 */
      Map<String,Object> headerMap = new HashMap<String, Object>();
      
      headerMap.put("$is_first_day", "$is_first_day's value");,
      headerMap.put("app_version", "app_version's value");,
      headerMap.put("package", "package's value");,
      headerMap.put("client_ip", "client_ip's value");,
      headerMap.put("cohort", "cohort's value");,
      
      headerMap.put("sdk_lib", "sdk_lib's value");
      AppLog.setHeaderInfo((HashMap<String, Object>)headerMap);
  }
}
...
// 在实验页面或代码处调用，请勿过早调用此方法，以免带来实验过度曝光

String newOne = AppLog.getAbConfig("one", "");
if (newOne.equals("name")) {
  //
} else if (newOne.equals("type")) {
  //
} else {
  //
} 
String newTwo = AppLog.getAbConfig("two", "");
if (newTwo.equals("age")) {
  //
} else if (newTwo.equals("bye")) {
  //
} else {
  //
} 
int newThree = AppLog.getAbConfig("three", 0);
if (newThree == 1) {
  //
} else if (newThree == 2) {
  //
} else {
  //
} 
boolean newFor = AppLog.getAbConfig("for", false);
if (newFor) {
  //
} else {
  //
}
     
Object newFive = AppLog.getAbConfig("five", );`;
    },
    getJavaScript() {
      let allChoice = this.getAllRules();
      const appld = this.addData.appKey || '';
      // if(this.addData.filterRule.length) {
      //   this.addData.filterRule.forEach( (item) => {
      //     item.filterList.length&&item.filterList.forEach( (every) => {
      //       allChoice.push({
      //         k: every.keyName,
      //         v: isArray(every.keyValue) ? every.keyValue.slice(0,1).join() : every.keyValue
      //         // Value: every.keyValue.slice(0,1)
      //       });
      //     });
      //   });
      // }


      let arrArg = [];
      if (allChoice.length) {
        let strAllChoice = JSON.stringify(allChoice);
        arrArg = this.setAllChoice(strAllChoice);
      }

      let featureKeyStr = '';
      const versions = this.addData.versionList;
      let params = versions[0].featureList;
      if (params.length) {
        featureKeyStr = params[0].keyName;
      }



      return `import ZptABTest from '@znzt_fe/abtest-sdk';

// 实例化
var test = new ZptABTest({
    url: 'https://abtest.zuoyebang.com', // 线上请求地址：https://abtest.zuoyebang.com；// 测试环境请求地址：http://abtest-base-e.suanshubang.cc；如果不传递：默认为线上地址
    appId:'${appld}',// 接入线自己的appId
});
// 调用getABData方法
test.getABData({
    cuid:'123456789',// 接入线自己的cuid：用户唯一设备号
    featureKey:'${featureKeyStr}', //featureKey即为实验参数：支持字母、数字、下划线
    params: ${JSON.stringify(arrArg)} //用户自定义参数，即为受众规则：以对象的格式传递
},function(res){
    console.log(res)
    // 如果命中实验，则返回一个对象，结构如下：
      
    //{
    // experimentId: 494
    // key: "height1"
    // type: 1
    // value: "1"
    // versionId: 1723
    //}


    // 如果没有命中实验，则返回null（此处需要注意）

  })`;
    },
    handleCopy() {
      this.$copy(this.sourcecode, event);
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .nav_tabs .el-tabs__item {
  width: auto;
  font-weight: normal !important;
  font-size: 14px;
}

/deep/ .line_style .el-tabs__active-bar {
  width: 25px !important;
}

.example_title {
  font-weight: bold;
  // font-weight: 500;
  font-size: 14px;
}

.example_content {
  color: rgb(93 144 114);
  margin-top: 20px;
  margin-bottom: 20px;
}

.code-viewer {
  height: 200px;
  position: relative;
  padding: 8px 0 0 8px;
  padding-top: 0;
  // height: 200px;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  overflow: auto;
}

.copy_btn {
  position: absolute;
  right: 20px;
  top: 13px;
  z-index: 1000;

  // position: ab;
  span {
    font-size: 14px;
    padding-left: 2px;
    color: #42c57a;
    cursor: pointer;
  }
}

.search {
  position: relative;
  display: flex;
  justify-content: space-between;

  .el-select {
    width: 175px;
  }
}

.go_back {
  float: right;
  margin-bottom: 10px;
}

.keyword_input {
  width: 270px;
  margin-left: -1px;

  /deep/ .el-input__inner {
    border-radius: 0;
  }

  /deep/ .el-input__suffix {
    right: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    cursor: pointer;
  }
}

/deep/ .el-table {
  font-size: 14px;
}

.btn_add {
  position: absolute;
  top: 0;
  right: 0;
}

.table-pagination {
  margin-top: 20px;
  text-align: right;
}

.table_description {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.related_flights_text {
  color: rgb(70, 96, 239);
  cursor: pointer;
}

.table_tag {
  display: inline-block;
  border-radius: 4px;
  padding: 0 7px;
  font-size: 12px;
  line-height: 20px;
  color: #2f2f3f;
  background: #f8f8fc;
  border: 1px solid #e7e9f5;
}

.table_tag_required {
  color: #f5222d;
  background: #fff1f0;
  border-color: #ffa39e;
}

.action_edit {
  margin-right: 10px;
}

/deep/ .op-switch.is-disabled .el-switch__core,
/deep/ .op-switch.is-disabled .el-switch__label {
  background-color: rgb(155, 156, 158);
  border-color: rgb(155, 156, 158);
  cursor: pointer;
}

/deep/ .el-switch.is-disabled.is-checked .el-switch__core,
/deep/ .op-switch.is-disabled.is-checked .el-switch__label {
  border-color: #42c57a;
  background-color: #42c57a;
}

/deep/ .el-button--text-primary>span {
  font-size: 14px;
}
</style>