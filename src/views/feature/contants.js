export const STATUSOPTIONS = [
  {
    name: '全部'
  },
  {
    name: '已开启'
  },
  {
    name: '已关闭'
  }
];
export const paramTypeList = [
  {
    label: 'boolean',
    key: 2
  },
  {
    label: 'string',
    key: 1
  },
  {
    label: 'float',
    key: 4
  },
  {
    label: 'json',
    key: 5
  }
];
// 变体类型
export const KEYTYPELIST = {
  2: 'boolean',
  1: 'string',
  4: 'float'
};
export const RELEASETYPELISTOPTION = [
  {
    label: '手动增量发布',
    key: 2
  },
  {
    label: '自动增量发布',
    key: 1
  }
];

export const FEATRUE_STATUS = [
  {
    label: '已开启',
    key: 1
  },
  {
    label: '未开启',
    key: 2
  }
];

/**
 * CORRECT_PUBLISH_STATUS_PART     int8 = 1 // 固化发布状态：未发布
CORRECT_PUBLISH_STATUS_PUB      int8 = 2 // 固化发布状态：发布中
CORRECT_PUBLISH_STATUS_ROLLBACK int8 = 3 // 固化发布状态：回滚
CORRECT_PUBLISH_STATUS_DISABLE  int8 = 4 // 固化发布状态：禁用
CORRECT_PUBLISH_STATUS_FULL     int8 = 5 // 固化发布状态：已全量
 */
export const CORRECT_PUBLISH_STATUS = [
  {
    label: '未发布',
    key: 1
  },
  {
    label: '发布中',
    key: 2
  },
  {
    label: '已回滚',
    key: 3
  },
  {
    label: '已禁用',
    key: 4
  },
  {
    label: '已全量',
    key: 5
  }
];
