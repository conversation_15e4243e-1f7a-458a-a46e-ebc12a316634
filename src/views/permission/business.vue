<template>
  <Page class="container">
    <section class="content">
      <UserInfo></UserInfo>
      <section class="form-container">
        <p class="title">申请详情</p>
        <el-form label-width="135px" label-position="left" :model="form" :rules="rules" ref="ruleForm">
          <el-form-item label="申请类型：" prop="type">
            <el-checkbox-group v-model="form.type">
              <el-checkbox :label="1">业务线接入</el-checkbox>
              <el-checkbox :label="2">数据接入</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="业务线名称：" prop="displayName" v-if="!isOnlyData">
            <el-input v-model="form.displayName" placeholder="请输入业务线名称" :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="业务线名称：" prop="business" v-if="isOnlyData">
            <el-select v-model="form.business" value-key="appKey" clearable filterable placeholder="请选择">
              <el-option v-for="option in businessList" :label="option.displayName" :value="option" :key="option.appKey">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="业务线appid：" prop="appKey" v-if="!isOnlyData">
            <el-input v-model="form.appKey" placeholder="请输入业务线appid" :maxlength="50">
            </el-input>
            <el-tooltip class="item" effect="dark" content="appId用于在集成ab sdk时传参使用，请和业务侧研发老师确认清楚！" placement="top">
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="服务部署位置：" prop="location" v-if="!isOnlyData" required>
            <el-select v-model="form.location" clearable placeholder="请选择">
              <el-option v-for="item in locationTypes" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="!isOnlyBusiness" label="是否接入客户端：" prop="appIn" required>
            <el-radio-group v-model="form.appIn">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="2">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="this.form.appIn === 1 && !isOnlyBusiness">
            <el-form-item label="数据源：" prop="ods">
              <el-input v-model="form.ods" placeholder="请输入ODS数据源"></el-input>
            </el-form-item>
            <el-form-item label="" prop="dwd">
              <el-input v-model="form.dwd" placeholder="请输入DWD数据源"></el-input>
              <el-tooltip class="item" effect="dark" content="接入客户端实验，需要写明客户端打点底层日志表，包含ODS和DWD！" placement="top">
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="数据上报点位：" prop="pointName">
              <el-input v-model="form.pointName" placeholder="请输入数据上报点位"></el-input>
              <el-tooltip class="item" effect="dark" content="客户端实验，默认上报点位为DT9_002,若不是，请填写正确点位！" placement="top">
                <i class="el-icon-warning"></i>
              </el-tooltip>
            </el-form-item>
          </template>
        </el-form>
      </section>
      <section class="form-container">
        <p class="title">申请人附言</p>
        <el-form label-width="130px" label-position="left">
          <el-form-item label="申请人附言：" prop="statement">
            <el-input type="textarea" v-model="form.statement" :autosize="{ minRows: 3, maxRows: 5 }"></el-input>
          </el-form-item>
        </el-form>
      </section>
    </section>
    <section>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit" :loading="submitDisable">提交</el-button>
    </section>
  </Page>
</template>
<script>
import Page from '@/components/common/page/index.vue';
import UserInfo from './userInfo.vue';
import Message from '@common/message';

export default {
  name: 'Permission',
  components: {
    Page,
    UserInfo
  },
  data() {
    return {
      submitDisable: false,
      form: {
        type: [1, 2],
        appIn: 1,
        displayName: '',
        appKey: '',
        ods: '',
        dwd: '',
        pointName: 'DT9_002',
        statement: '',
        location: location.href.includes('global-abtest') ? 'usaeast' : 'mainland'
      },
      businessList: [],
      rules: {
        type: [{
          required: true, message: '请选择申请类型', trigger: 'change'
        }],
        business: [
          { required: true, message: '请选择业务线', trigger: 'change' },
        ],
        displayName: [
          { required: true, message: '业务线名称不能为空', trigger: 'change' },
        ],
        appKey: [
          { required: true, message: '业务线appid不能为空	', trigger: 'change' }
        ],
        ods: [
          { required: true, message: 'ODS数据源不可为空', trigger: 'change' }
        ],
        dwd: [
          { required: true, message: 'DWD数据源不可为空', trigger: 'change' }
        ],
        pointName: [
          { required: true, message: '数据上报点位不可为空	', trigger: 'change' }
        ],
      },
      locationTypes: location.href.includes('global-abtest') ? 
      [{
          value: 'usaeast',
          label: '美东'
        }]: [{
          value: 'mainland',
          label: '国内'
        }],
    };
  },
  computed: {
    isOnlyData() {
      return this.form.type.length === 1 && this.form.type[0] === 2;
    },
    isOnlyBusiness() {
      return this.form.type.length === 1 && this.form.type[0] === 1;
    }

  },
  methods: {
    handleGoBack() {
      this.$router.push({
        path: '/exp-manage/list'
      });
    },
    // 转换成formData格式提交给后台
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.submitDisable = true;
          const form = this.form;
          const params = {
            dataAction: form.type.includes(2),
            applicationAction: form.type.includes(1),
            appIn: form.appIn === 1,
            statement: form.statement,
            location: form.location
          };
          if (params.applicationAction) {
            params.displayName = form.displayName;
            params.appKey = form.appKey;
          } else {
            params.displayName = form.business.displayName;
            params.appKey = form.business.appKey;
          }
          if (form.appIn === 1 && !this.isOnlyBusiness) {
            params.pointName = form.pointName;
            params.dataTable = {
              odsTable: form.ods,
              dwdTable: form.dwd
            };
          }
          const data = this.formatFormData(params);
          this.$service.post('NEWAPPLICATION', data).then(() => {
            this.submitDisable = false;
            Message.success('已提交');
            this.cancel();
          });
        }
      }, () => {this.submitDisable = false;});
    },
    cancel() {
      this.$router.push({
        path: `/permission`
      });
    },
    init() {
      this.getAppList();
    },
    getAppList() {
      this.$service.get('APPLICATIONLIST')
        .then(res => {
          this.businessList = res.list || [];
        });
    }
  },
  created() {
    this.init();
  }
};
</script>


<style lang="less" scoped>
.container {
  margin-top: 24px;

  height: calc(100vh - 74px);

  /deep/ .page-cont {
    height: 100%;

    .page-container {
      padding-top: 16px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }

    .page-container-pagination {
      display: none;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    section {
      display: flex;
      align-items: center;
    }

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin: 0 8px;
    }

    i {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .content {
    display: flex;
    flex: 1;
    flex-direction: column;

    /deep/ .form-container {
      margin-bottom: 18px;

      .title {
        font-size: 16px;
        font-weight: 500;
        line-height: 16px;
        position: relative;
        padding-left: 16px;
        margin-top: 12px;

        &::after {
          content: " ";
          position: absolute;
          left: 0px;
          top: -1px;
          background-color: #42c57a;
          bottom: 0;
          width: 4px;
        }
      }

      .el-form {
        font-size: 14px;
        padding-top: 12px;
        max-width: 420px;

        .el-form-item {
          margin-bottom: 0;
          margin-top: 16px;
        }

        .el-form-item__content {
          display: flex;
          align-items: center;
          min-height: 32px;

          * {
            font-weight: 500;
          }

          .el-input {
            max-width: 245px;
            margin-right: 12px;
          }

          i {
            color: #42c57a;
          }
        }
      }
    }
  }
}
</style>
