<template>
  <Page class="container">
    <section class="content">
      <UserInfo></UserInfo>
      <section class="form-container">
        <p class="title">申请详情
          <span class="req-prompt-btn" v-if="!hasPermission">此申请走的是帮帮堂申请,有两个审批单,先找直属leader审批</span>
          <span class="req-prompt-btn" v-if="hasPermission">此申请走的是帮帮堂申请,先找直属leader审批</span>
        </p>
        <el-form label-width="130px" label-position="left" :model="form" :rules="rules" ref="ruleForm">
          <el-form-item label="申请角色：" prop="role" required v-if="!hasPermission">
            <el-select v-model="form.role" placeholder="请选择">
              <el-option v-for="option in roleList" :label="option.roleName" :value="option.id" :key="option.id">
              </el-option>
            </el-select>
            <el-alert title="当前申请会生成两条审批流程，请关注这两个流程的审批进度！" type="warning" :closable="false" />
          </el-form-item>
          <el-form-item label="申请业务线：" prop="appKey">
            <el-select v-model="form.appKey" clearable filterable placeholder="请选择,支持多选" multiple>
              <el-option v-for="option in businessList" :label="option.displayName" :value="option.appKey" :key="option.appKey">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统审批人：" prop="admin">
            <el-select v-model="form.admin" clearable filterable placeholder="请选择,支持多选" multiple>
              <el-option v-for="option in adminList" :label="option.name" :value="option.id" :key="option.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="申请原因：" prop="statement">
            <el-input type="textarea" v-model="form.statement" :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </section>
      <section class="form-container">
        <p class="title">申请人附言</p>
        <el-form label-width="130px" label-position="left">
          <el-form-item label="申请人附言：" prop="remark">
            <el-input type="textarea" v-model="form.remark" :autosize="{ minRows: 3, maxRows: 5 }"></el-input>
          </el-form-item>
        </el-form>
      </section>
    </section>
    <section>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submit" :loading="submitDisable">提交</el-button>
    </section>
  </Page>
</template>
<script>
import { mapGetters } from 'vuex';
import Page from '@/components/common/page/index.vue';
import UserInfo from './userInfo.vue';
import Message from '@common/message';
export default {
  name: 'Permission',
  components: {
    Page,
    UserInfo
  },
  data() {
    return {
      isShip: location.href.includes('suanshubang.cc'),
      submitDisable: false,
      form: {
        role: '',
        appKey: [],
        admin: [],
        statement: '',
        remark: ''
      },
      businessList: [],
      adminList: [],
      roleList: [{
        roleName: '普通用户',
        id: '100000019'
      }],
      rules: {
        appKey: [
          { required: true, message: '请选择业务线', trigger: 'change' },
        ],
        admin: [
          { required: true, message: '请选择审批人', trigger: 'change' }
        ],
        statement: [
          { required: true, message: '请输入申请原因', trigger: 'change' }
        ],
      }
    };
  },
  computed: {
    ...mapGetters(['userInfo'], 'message'),
    hasPermission() {
      const res = !!(this.userInfo.roleInfo && this.userInfo.roleInfo.length);
      return res;
    }
  },
  methods: {
    checkFormData() {
      if (this.form.appKey.length === 0 ||
        this.form.admin.length === 0 ||
        this.form.statement === '' ||
        (!this.hasPermission && this.form.role === '')) {
        this.$message.warning("必填选项不能为空");
        return false;
      } else {
        return true;
      }
    },
    handleGoBack() {
      this.$router.push({
        path: '/exp-manage/list'
      });
    },
    // 转换成formData格式提交给后台
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    submit() {
      if (!this.checkFormData()) {
        return;
      }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.submitDisable = true;
          const params = this.formatFormData(this.form);
          this.$service.post('APPLYPERMISSION', params).then(() => {
            this.submitDisable = false;
            if (this.isShip) {
              Message.success('已添加成功');
            } else {
              Message.success('已提交');
            }
            this.cancel();
          }, () => { this.submitDisable = false; });
        }
      });
    },
    cancel() {
      this.$router.push({
        path: `/permission`
      });
    },
    init() {
      this.getadminList();
      this.getAppList();
      this.getRoleList();
    },
    getadminList() {
      this.$service.get('GETADMINLIST')
        .then(res => {
          this.adminList = res || [];
          console.log('GETADMINLIST', res);
        });
    },
    getAppList() {
      this.$service.get('APPLICATIONLIST')
        .then(res => {
          console.log('APPLICATIONLIST', res);
          this.businessList = res.list || [];
        });
    },
    getRoleList() {
      this.$service.get('ROLELIST')
        .then(res => {
          console.log('ROLELIST', res);
          this.roleList = res || [];
        });
    }
  },
  created() {
    this.init();
  }
};
</script>


<style lang="less" scoped>
.container {
  margin-top: 24px;

  height: calc(100vh - 74px);

  .el-alert {
    padding: 0;
    margin-left: 12px;
    vertical-align: middle;
    max-width: 400px;
    display: inline-block;
  }

  /deep/ .page-cont {
    height: 100%;

    .page-container {
      padding-top: 16px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }

    .page-container-pagination {
      display: none;
    }
  }

  .req-prompt-btn {
    margin-left: 10px;
    font-size: 14px;
    cursor: pointer;
    color: #FC1616;
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    section {
      display: flex;
      align-items: center;
    }

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin: 0 8px;
    }

    i {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .content {
    display: flex;
    flex: 1;
    flex-direction: column;

    /deep/ .form-container {
      margin-bottom: 18px;

      .title {
        font-size: 16px;
        font-weight: 500;
        line-height: 16px;
        position: relative;
        padding-left: 16px;
        margin-top: 12px;

        &::after {
          content: " ";
          position: absolute;
          left: 0px;
          top: -1px;
          background-color: #42c57a;
          bottom: 0;
          width: 4px;
        }
      }

      .el-form {
        font-size: 14px;
        padding-top: 12px;

        .el-form-item {
          margin-bottom: 0;
          margin-top: 16px;
        }

        .el-form-item__content {
          * {
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style>
