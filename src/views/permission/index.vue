<template>
  <Page class="container" :breadcrumbList="[]">
    <section class="header">
      <section>
        <i class="el-icon-arrow-left" @click="handleGoBack"></i>
        <span class="page-title">申请权限</span>
      </section>
    </section>
    <section class="content">
      <section class="card" @click="jump('business')" v-if="!isShip">
        <i class="el-icon-set-up"></i>
        <p class="title">业务线申请接入</p>
        <p class="desc">
        <p>新业务线接入：使用ab系统创建、发布实验。</p>
        <p>数据接入：分析ab实验命中数据。</p>
      </section>
      <section class="card" @click="jump('request')">
        <i class="el-icon-user"></i>
        <p class="title">权限申请</p>
        <p class="desc">
          ab系统访问权限，ab系统业务线权限
        </p>
      </section>
    </section>
  </Page>
</template>
<script>
import Page from '@/components/common/page/index.vue';

export default {
  name: 'Permission',
  components: {
    Page
  },
  data() {
    return {
      isShip: location.href.includes('suanshubang.cc'),
    };
  },
  computed: {
  },
  methods: {
    handleGoBack() {
      this.$router.push({
        path: '/exp-manage/list'
      });
    },
    jump(path){
      this.$router.push({
        path: `/permission/${path}`
      });
    }
  }
};
</script>


<style lang="less" scoped>
.container {
  margin-top: 24px;

  height: calc(100vh - 74px);

  /deep/ .page-cont {
    height: 100%;

    .page-container {
      padding-top: 16px;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    section {
      display: flex;
      align-items: center;
    }

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin: 0 8px;
    }

    i {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .content {
    justify-content: center;
    align-items: center;
    display: flex;
    flex: 1;

    .card {
      padding: 36px 24px;
      width: 300px;
      height: 230px;
      display: inline-flex;
      flex-direction: column;
      border: 1px solid #e1e3e9;
      align-items: center;
      margin-left: 24px;

      i {
        font-size: 45px;
        margin-bottom: 8px;
        color: #42c57a;
      }

      .title {
        font-weight: 500;
        font-size: 21px;
        margin-bottom: 12px;
      }

      p {
        font-size: 14px;
        line-height: 32px;
      }
    }
  }
}
</style>
