<template>
  <section class="form-container">
    <p class="title">申请人信息</p>
    <el-form label-width="130px" label-position="left">
      <el-form-item label="申请人：" >
        <span>{{ this.userInfo.realName }}</span>
      </el-form-item>
      <el-form-item label="申请人编号：" >
        <span>{{ this.userInfo.employeeId }}</span>
      </el-form-item>
      <el-form-item label="申请日期：" >
        <span>{{ getCurrentDate() }}</span>
      </el-form-item>
    </el-form>
  </section>
</template>
<script>
import { mapGetters } from 'vuex';
import dayjs from 'dayjs';
export default {
  name: 'Permission',
  components: {
  },
  data() {
    return {};
  },
  created(){
    setTimeout(() => {
      console.log('this.userinfo', this.userInfo);
    }, 1000);
  },
  computed: {
    ...mapGetters(['userInfo'], 'message'),
  },
  methods:{
    getCurrentDate(){
      return dayjs().format('YYYY-MM-DD HH:MM:ss'); // '25/01/2019'
    }
  }
};
</script>


<style lang="less" scoped>
</style>