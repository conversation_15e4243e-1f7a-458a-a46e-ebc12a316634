<template>
  <section>
    <iframe :src="url" frameborder="0" ref="frame"></iframe>
  </section>
</template>
<script>
import Layout from '@/components/layout';
import qs from 'qs';

export const getUrlByLocation = (hash, params) => {
  const str = qs.stringify(params);
  const url = `${location.origin}/static/fe-ab-test/#${hash}`;
  console.log('getUrlByLocation', url);
  return str ? url + "?" + str : url;
};
export default {
  name: 'layout',
  components: {
    Layout
  },
  data() {
    const route = this.$route;
    let { path = 'traffic-control/exp/list', query = '' } = route.query;
    if (query) {
      query = JSON.parse(query);
    }
    const url = getUrlByLocation(path, query);
    console.log('url', url);
    return {
      url: url
    };
  },
  beforeRouteLeave(to, from, next) {
    if (process.env.NODE_ENV === 'development') {
      next();
      return;
    }
    const frame = this.$refs.frame;
    const hash = frame.contentWindow.location.hash;
    if (hash && hash.includes('/edit')) {
      this.$confirm("本次编辑内容尚未保存，返回将放弃页面已填写内容，确定返回吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        distinguishCancelAndClose: false
      }).then(() => {
        next();
      });
    } else {
      next();
    }
  }
};
</script>
<style lang="less" scoped>
section {
  height: calc(100vh - 52px);

  iframe {
    width: 100%;
    height: 100%;
  }
}
</style>
