/*
 * @Author: wang<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-11-06 14:16:03
 * @LastEditTime: 2020-08-07 16:17:35
 * @LastEditors: wanglijuan01
 * @Description: In User Settings Edit
 */

// 生成通用的测试服务器地址
function getProxy(testAddress) {
  let _proxy;
  if (process.env.NODE_ENV !== 'production') {
    if (/test/.test(testAddress) || /docker/.test(testAddress)) {
      let url = /online/.test(testAddress) ? 'http://wxtools.zuoyebang.cc/' : `http://${testAddress }.suanshubang.com`;
      _proxy = {
        '/testAddress': {
          target: url, // 源地址
          changeOrigin: true, // 改变源
          ws: true,
          pathRewrite: {
            '^/testAddress': url // 路径重写
          }
        },
        '/opmis': {
          target: url, // 源地址
          changeOrigin: true, // 改变源
          ws: true,
          pathRewrite: {
            '^/opmis': '/opmis' // 路径重写
          }
        },
        '/wxmarket': {
          target: url, // 源地址
          changeOrigin: true, // 改变源
          ws: true,
          pathRewrite: {
            '^/wxmarket': '/wxmarket' // 路径重写
          }
        }
      };
    } else {
      domain = 'http://yapi.zuoyebang.cc/mock/';
      _proxy = {
        '/testdomain': {
          target: domain,
          changeOrigin: true,
          pathRewrite: {
            '^/testdomain': ''
          }
        }
      };
    }
  }
  return _proxy;
}
function getArgv() {
  let argv;
  try {
    argv = JSON.parse(process.env.npm_config_argv).original;
  } catch (e) {
    argv = process.argv;
  }
  if (argv) {
    return argv
      .toString()
      .split(',')
      .pop();
  }
}

module.exports = {
  getArgv,
  getProxy
};