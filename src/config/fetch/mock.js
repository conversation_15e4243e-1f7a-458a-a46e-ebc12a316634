/*
 * @Author:
 * @Date: 2020-04-29 16:04:46
 * @LastEditors: zhuyue
 * @LastEditTime: 2021-01-26 17:45:51
 * @Description: description
 */
export function getMockAddress(api) {
  const projectList = [
    {name: 'wxadmin', value: 248},
    {name: 'wxserver', value: 77},
    {name: 'wxqk', value: 1723},
    {name: 'wxmsg', value: 2203},
    {name: 'wxmessage', value: 1931},
    {name: 'wxsk', value: 1931},
    {name: 'qk-strategy', value: 1931},
    {name: 'risk', value: 2155},
    {name: 'csadmin', value: 4526},
    {name: 'earthworm', value: 5228}
  ];
  const currentPro = projectList.filter(item => {
    return api.split('/')[1] === item.name;
  });
  if (currentPro.length && currentPro[0].value) {
    return '/testdomain/' + currentPro[0].value;
  }
}
