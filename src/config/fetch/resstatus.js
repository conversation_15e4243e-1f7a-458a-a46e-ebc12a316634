/*
 * @Author:
 * @Date: 2020-02-17 15:18:58
 * @LastEditors: z<PERSON><PERSON>e
 * @LastEditTime: 2021-02-01 16:41:10
 * @Description:
 */
import Message from '@common/message';
import router from '@/router/index';
import { localStorage } from '@common/utils';
import Vue from 'vue';
// 数据过滤
export function checkState(res, resolve, reject) {
  console.log(res.errNo);
  switch (res.errNo) {
    case 0:
      resolve(res.data);
      break;
    case 70001:
      console.log('\x1b[36m\x1b[0m [LOG]: @#@#$@$@');
      Message.error('用户未登录, 请先登录~');
      if (/localhost/.test(window.location.host) || /127.0.0.1/.test(window.location.host)) {
        window.open(res.errstr);
        router.push({ path: '/login' });
      } else {
        window.location.href = res.errstr;
      }
      break;
    case 70003:
      Message.error('没有微信后台权限，请联系管理员~');
      router.push({ path: '/no-authority' });
      break;
    case -1005:
      window.location.href = res.data.loginUrl;
      break;
    default:
      // 【note】如果errNo不为0和特殊的值，返回所有数据.
      let len = res.errstr ? res.errstr.length : res.errStr.length;
      let dur = len > 20 ? 5000 : 3000;
      // 特殊状态码需要html格式
      Vue.prototype.$message({
        dangerouslyUseHTMLString: true,
        showClose: true,
        duration: dur,
        message: res.errStr || res.errstr, // 兼容接口返回的值
        type: 'error'
      });
      reject({}); // 兼容业务组件内的catch报错提示， {} 为了防止业务组件提示错误信息
      break;
  }
}
