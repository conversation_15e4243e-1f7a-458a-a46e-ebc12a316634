/*
 * @Author: wang<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019-11-05 11:51:48
 * @LastEditTime: 2020-08-11 14:06:32
 * @LastEditors: wanglijuan01
 * @Description: In User Settings Edit
 * @FilePath: 测试服务器地址配置
 */
const testServer = {
  receiver: {
    test83: 'http://**************:8020/fisreceiver.php',
    test44: 'http://**************:8020/fisreceiver.php',
    test25: 'http://**************:8020/fisreceiver.php',
    test58: 'http://**************:8020/fisreceiver.php',
    test16: 'http://**************:8020/fisreceiver.php',
    test84: 'http://**************:8020/fisreceiver.php',
    test108: 'http://***************:8020/fisreceiver.php',
    test155: 'http://***************:8020/fisreceiver.php',
    test20: 'http://**************:8020/fisreceiver.php',
    test22: 'http://***************:8020/fisreceiver.php',
    test133: 'http://***************:8020/fisreceiver.php',
    test148: 'http://***************:8020/fisreceiver.php',
    test288: 'http://***************:8020/fisreceiver.php',
    test203: 'http://***************:8020/fisreceiver.php',
    test252: 'http://**************:8020/fisreceiver.php',
    test184: 'http://192.168.240.231:8020/fisreceiver.php',
    test291: 'http://192.168.241.111:8020/fisreceiver.php',
    lxyymis: 'http://wxtools.zuoyebang.cc/fisreceiver.php',
    'ltwx-docker': 'http://zhibo.ltwx-docker.suanshubang.com/fisreceiver.php',
    'hpx-docker': 'http://zhibo.hpx-docker.suanshubang.com/fisreceiver.php',
    'zhaofeng-docker':'http://zhibo.zhaofeng-docker.suanshubang.com/fisreceiver.php',
    'wqks-docker': 'http://zhibo.wqks-docker.suanshubang.com/fisreceiver.php',
    'wzntf-docker': 'http://zhibo.wzntf-docker.suanshubang.com/fisreceiver.php',
    'juzi-docker': 'http://fwyymis.juzi-docker.suanshubang.com/fisreceiver.php',
    'xlx002-docker': 'http://zhibo.hpx-docker.suanshubang.com/fisreceiver.php',
    'zhibo.newrain-docker': 'http://zhibo.hpx-docker.suanshubang.com/fisreceiver.php',
    test296: 'http://192.168.241.116:8020/fisreceiver.php'
  },
  toPath: '/home/<USER>/webroot/static/fe-abtest-new' //修改模块目录
};
module.exports = testServer;
