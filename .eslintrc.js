/*
 * @Author: wang<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020-02-29 10:46:08
 * @LastEditors: wanglijuan01
 * @LastEditTime: 2020-08-10 17:32:23
 * @Description: eslint 规则
 */
module.exports = {
  root: true,
  parser: "vue-eslint-parser",
  parserOptions: {
    sourceType: 'module',
    parser: "babel-eslint"
  },
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  // https://github.com/standard/standard/blob/master/docs/RULES-en.md
  // extends: 'standard',
  // required to lint *.vue files
  plugins: [
    'html'
  ],
  // add your custom rules here
  'rules': {
    // allow paren-less arrow functions
    'arrow-parens': 0,
    'semi': [2, 'always'],
    'eol-last': 0,
    // allow async-await
    'no-console': 'off',
    'generator-star-spacing': 0,
    // "camelcase": ["error", {"allow": ["aa_bb"]}],
    // 'camelcase': [2, {'properties': 'never'}],
    'space-before-function-paren': [2, { 'anonymous': 'always', 'named': 'never' }],
    'no-debugger': 0
  }
}
